#!/bin/bash

# 检查是否提供了参数
if [ "$#" -ne 1 ]; then
    echo "使用方法: $0 文件或目录路径"
    exit 1
fi

# 获取并使用提供的路径
path="$1"

# 判断给定路径是一个文件还是一个目录
if [ -f "$path" ]; then
    # 如果是文件，直接替换该文件中的tab
    if [[ "$path" == *.go ]]; then
        sed -i '' 's/\t/    /g' "$path"
        echo "已完成文件 '$path' 中tab字符到4个空格的转换。"
    else
        echo "提供的文件 '$path' 不是.go文件，跳过处理。"
    fi
elif [ -d "$path" ]; then
    # 如果是目录，递归遍历目录中的所有.go文件并替换tab
    find "$path" -type f -name "*.go" -exec sed -i '' 's/\t/    /g' {} \;
    echo "已完成目录 '$path' 下的golang源文件中tab字符到4个空格的转换。"
else
    echo "'$path' 既不是文件也不是目录，无法处理。"
fi
