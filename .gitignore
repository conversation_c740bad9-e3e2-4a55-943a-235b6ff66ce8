# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
*.zip
*.prof

# Test binary, built with `go test -c`
*.test
index.db

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Mac file
.DS_Store

# Project file
.idea/*
.theia/*
.vscode/*
.qoder/*
*.iml
*.history/*
note/
out/
stress-test-linux-amd64

# ignore the vendor directory in the project root
/vendor/

cosy
DEV_VERSION

*.actual.*_chunk.yaml
*.actual.*_chunk.yml

chat/agents/deepwiki/deepwiki_output/
chat/agents/tool/wiki/output/
chat/agents/tool/wiki/questions/

__debug_bin*
sls/back_flow/results/
sls/back_flow/test-dir
*.pyc
logs
