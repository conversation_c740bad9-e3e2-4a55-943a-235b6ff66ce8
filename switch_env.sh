#!/usr/bin/env zsh

project_path=$(cd "$(dirname "${0}")"; pwd)
config_file="${HOME}/Library/Caches/.lingma/config.json"
local_endpoint="http://127.0.0.1:7001"
hangzhou_endpoint="https://devops.aliyun.com/search"
beijing_endpoint="https://lingma-api.tongyi.aliyun.com/algo"
touch "${config_file}"

function check_env() {
  version=$(cat "${config_file}" | grep '"big_model_cookie"' | grep -o 'version=[^"]*')
  endpoint=$(cat "${config_file}" | grep '"big_model_endpoint"' | grep -o 'http[s]*://[^"]*')
  if [ "${version}" = "version=ga_dev" ]; then
    echo "Using environment: \"daily\""
  elif [ "${version}" = "version=ga" ]; then
    echo "Using environment: \"prepub\""
  elif [ "${endpoint}" = "${local_endpoint}" ]; then
    echo "Using environment: \"local\""
  elif [[ "${endpoint}" = "" || "${endpoint}" = "${hangzhou_endpoint}" || "${endpoint}" = "${beijing_endpoint}" ]]; then
    echo "Using environment: \"production\""
  else
    echo "Using environment: \"unknown\""
  fi
}

function remote_env_config() {
  sed -i '' -e '/"big_model_endpoint"/d' "${config_file}"
  sed -i '' -e '/"big_model_cookie"/d' "${config_file}"
}

function fix_remote_config() {
  remote_env_config
  if [ "$(grep '\"remote_config\"' "${config_file}")" = "" ]; then
    if [ "$(grep '^{$' "${config_file}")" = "" ]; then
      cat <<EOF >"${config_file}"
{
  "remote_config": {
  }
}
EOF
    else
      sed -i '' "/^{$/a\\
  \"remote_config\": {\\
  },\\
" "${config_file}"
    fi
  fi
}

function fix_extra_comma() {
    sed -i '' -n -e '1h;1!H;${;x;s/,[ ]*\n\([ ]*\)}/\n\1}/g;p;}' "${config_file}"
}

function add_endpoint() {
  sed -i '' "/\"remote_config\"/a\\
    \"big_model_endpoint\": \"${1}\",\\
" "${config_file}"
}

function add_version_cookie() {
  sed -i '' "/\"remote_config\"/a\\
    \"big_model_endpoint\": \"${hangzhou_endpoint}\",\\
    \"big_model_cookie\": \"version=${1}\",\\
" "${config_file}"
}

function switch_to_local() {
  fix_remote_config
  add_endpoint "${local_endpoint}"
  fix_extra_comma
  echo "Switched to environment \"local\""
  zsh "${project_path}/restart_process.sh" --yes
}

function switch_to_daily() {
  fix_remote_config
  add_version_cookie ga_dev
  fix_extra_comma
  echo "Switched to environment \"daily\""
  zsh "${project_path}/restart_process.sh" --yes
}

function switch_to_prepub() {
  fix_remote_config
  add_version_cookie ga
  fix_extra_comma
  echo "Switched to environment \"prepub\""
  zsh "${project_path}/restart_process.sh" --yes
}

function switch_to_prod() {
  remote_env_config
  fix_extra_comma
  echo "Switched to environment \"production\""
  zsh "${project_path}/restart_process.sh" --yes
}

if [ "${1}" = "" ]; then
  check_env
elif [ "${1}" = "local" ]; then
  switch_to_local
elif [ "${1}" = "daily" ]; then
  switch_to_daily
elif [[ "${1}" = "prepub" || "${1}" = "pre" ]]; then
  switch_to_prepub
elif [[ "${1}" = "production" || "${1}" = "prod" ]]; then
  switch_to_prod
else
  printf "Usage: %s [local|daily|prepub|prod]\n" "${0}"
fi
