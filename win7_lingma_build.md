

```bash
## 公有云版本
zip -r  lingma-develop-0402-2.zip . -x ".git/*"  -x "out/*" 

## 专有云版本
zip -r  lingma-develop-on-premise-0311.zip . -x ".git/*"  -x "out/*" 

```


```bash 
# 公有云版构建    
go build -buildmode=pie -trimpath -ldflags "-s -w" -o out/LingmaWin7.exe

# 专有云版构建    
```bash
# main.expireTime  单位：时间秒 
# 1717171200 是 2024-06-01 00:00:00
go build -buildmode=pie -trimpath -ldflags \
 "-s -w -X main.trialEdition=建设银行 -X main.serverUrl=http://************/algo -X main.expireTime=1717171200  -X main.serverHost=lingma-api.tongyi.aliyun.com " \
  -o out/LingmaWin7.exe
```

#计算hash
certutil -hashfile out/LingmaWin7.exe MD5

#复制到指定目录
copy  out/LingmaWin7.exe C:/Users/<USER>/Desktop/LingmaWin7-${hash}.exe

copy  out/LingmaWin7.exe out/LingmaWin7-0129.exe

```