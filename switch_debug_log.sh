#!/usr/bin/env zsh

project_path=$(cd "$(dirname "${0}")"; pwd)
config_file="${HOME}/Library/Caches/.lingma/config.json"

echo "switch debug logfile: $config_file"

function fix_extra_comma() {
    sed -i '' -n -e '1h;1!H;${;x;s/,[ ]*\n\([ ]*\)}/\n\1}/g;p;}' "${config_file}"
}

if [[ "${1}" = "on" || "${1}" = "off" ]]; then
  sed -i '' -e '/"debug":/d' "${config_file}"
  if [ "${1}" = "on" ]; then
    sed -i '' "/^{$/a\\
  \"debug\": true,\\
" "${config_file}"
  fi
  fix_extra_comma
  zsh "${project_path}/restart_process.sh" --yes
else
  printf "Current debug log is: ["
  if [ "$(grep '"debug":' "${config_file}" | grep 'true')" = "" ]; then
    echo "OFF]"
  else
    echo "ON]"
  fi
  echo "Please specify \"on\" or \"off\" to switch"
fi
