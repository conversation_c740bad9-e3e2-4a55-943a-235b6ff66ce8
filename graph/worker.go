package graph

import (
	"context"
	"cosy/indexing/index_setting"
	"cosy/log"
	"cosy/server"
	"cosy/sls"
	cosy_storage "cosy/storage"
	"cosy/user"
	"cosy/util"
	"cosy/util/graph"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"runtime"
	"runtime/debug"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"

	"code.alibaba-inc.com/cosy/lingma-codebase-graph/parser"

	"code.alibaba-inc.com/cosy/lingma-codebase-graph/definition"
	cpp_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/cpp"
	csharp_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/csharp"
	golang_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/golang"
	java_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/java"
	javascript_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/javascript"
	kotlin_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/kotlin"
	python_parser "code.alibaba-inc.com/cosy/lingma-codebase-graph/parser/python"
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage"
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage/graphsqlite"
)

type GraphWorker struct {
	cosyServer *server.CosyServer
	lock       *GraphLock
}

// workerRunStat
// worker运行的监控信息
type workerRunStat struct {
	cost      int64  // 耗时
	language  string // 处理的语言
	isSuccess bool   // 是否处理成功
	ignore    bool   // 是否忽略监控信息
	stage     string // 任务处理阶段
}

type workerProcessConfig struct {
	ignoreErr         map[string]bool
	maxFailedCount    int
	singleFileTimeout int
}

func NewGraphWorker(cosyServer *server.CosyServer, lock *GraphLock) *GraphWorker {
	return &GraphWorker{
		cosyServer: cosyServer,
		lock:       lock,
	}
}

func (gw *GraphWorker) Run() {
	defer func() {
		if r := recover(); r != nil {
			panicStr := string(debug.Stack())
			data := make(map[string]string)
			data["panic_msg"] = panicStr
			data["function"] = "graph_worker"
			userType := user.GetUserType()
			data["user_type"] = userType
			log.Error("[codebase-graph] worker recover panic", panicStr)
			sls.Report(sls.EventTypeChatCodebaseGraphPanic, uuid.NewString(), data)
		}
	}()
	for {
		if !graph.GetGlobalGraphSwitch() {
			log.Infof("[codebase-graph] graph indexing is disabled")
			time.Sleep(1 * time.Minute)
			continue
		}
		processConfig := workerProcessConfig{
			ignoreErr:         graph.GetIgnoreGraphParseError(),
			maxFailedCount:    graph.GetMaxFailedFileCount(),
			singleFileTimeout: graph.GetWorkerSingleFileTimeout(),
		}

		idleTime := graph.GetWorkerIdleTime()
		infos, err := gw.cosyServer.GetWorkspaceInfos()
		if err != nil {
			log.Errorf("[codebase-graph] cosyServer.GetWorkspaceInfos error: %v", err)
			continue
		}
		if infos == nil {
			verboseLogf("[codebase-graph] all workspace is empty, sleep %vms", idleTime)
			time.Sleep(time.Duration(idleTime) * time.Millisecond)
			continue
		}
		emptyCount := 0
		for _, info := range infos {
			workspace := info.WorkspaceFolders[0].URI
			if _, ok := graph.GraphWorkspaceWorkerLock.Load(workspace); !ok {
				continue
			}
			graphStore := cosy_storage.GetGraphStore(workspace)
			if graphStore == nil {
				continue
			}
			if !index_setting.IndexEnable(workspace) {
				verboseLogf("[codebase-graph] index is not enable, skip this round")
				emptyCount += 1
				continue
			}
			runWorkspaceCount := gw.RunWorkspace(graphStore, workspace, processConfig)
			if runWorkspaceCount == 0 {
				emptyCount += 1
			}

		}
		time.Sleep(time.Duration(graph.GetWorkerScanInterval()) * time.Millisecond)
		if emptyCount == len(infos) {
			verboseLogf("[codebase-graph] all workspace is empty, sleep %vms", idleTime)
			time.Sleep(time.Duration(idleTime) * time.Millisecond)
		}
	}
}

func (gw *GraphWorker) RunWorkspace(graphStore storage.GraphStore, workspace string, config workerProcessConfig) int {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("[codebase-graph] RunWorkspace panic: %v", r)
		}
	}()

	fmtRecord := []string{}
	records, err := graphStore.ScanCanExecuteRecord(graph.GetWorkerScanBatchSize())
	if err != nil {
		log.Errorf("[codebase-graph] scan can execute file error: %v", err)
		return 0
	}

	workerRunStats := make([]workerRunStat, 0)
	for _, record := range records {
		fmtRecord = append(fmtRecord, record.Stage+"---"+record.FileAbsPath)
		stat := gw.RunOneRecord(graphStore, workspace, record, config)
		stat.stage = record.Stage
		workerRunStats = append(workerRunStats, stat)
	}

	if len(fmtRecord) > 0 {
		verboseLogf("[codebase-graph] batch process file: %v, %v", workspace, fmtRecord)
	}

	go reportSummaryInfo(workerRunStats, workspace)

	return len(records)
}

func (gw *GraphWorker) RunOneRecord(graphStore storage.GraphStore, workspace string, record storage.GraphFileRecord, config workerProcessConfig) workerRunStat {
	// 设置超时
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(config.singleFileTimeout)*time.Millisecond)
	defer cancel()

	// 使用channel来接收结果或超时
	done := make(chan workerRunStat, 1)
	go func() {
		startTime := time.Now()
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase-graph] RunOneRecord panic: %v", r)
				stack := debug.Stack()
				// panic情况下上报错误埋点，并返回失败状态
				errorReason := fmt.Sprintf("execute_panic: %v\n%s", r, stack)
				duration := time.Since(startTime)
				reportErrorResult(workspace, duration, "", errorReason, record.FileAbsPath)
				// panic情况下返回失败状态
				done <- workerRunStat{
					cost:      duration.Microseconds(),
					language:  "",
					isSuccess: false,
					ignore:    true,
				}
			}
		}()

		result := gw.runOneRecordInternal(graphStore, workspace, record, config)
		done <- result
	}()

	select {
	case result := <-done:
		return result
	case <-ctx.Done():
		log.Warnf("[codebase-graph] runOneRecord timeout for file: %s, %s", record.FileAbsPath, record.Stage)
		// 上报处理超时埋点
		reportErrorResult(workspace, time.Duration(config.singleFileTimeout), "", "execute_timeout", record.FileAbsPath)
		return workerRunStat{
			cost:      int64(time.Duration(config.singleFileTimeout) * time.Microsecond),
			language:  "",
			isSuccess: false,
			ignore:    true,
		}
	}
}

func (gw *GraphWorker) runOneRecordInternal(graphStore storage.GraphStore, workspace string, record storage.GraphFileRecord, config workerProcessConfig) (result workerRunStat) {
	// 统计信息记录
	startTime := time.Now()
	var errorReason string
	var language string
	isNeedReportErrResult := false

	// 初始化返回值变量，初始化为失败状态
	result = workerRunStat{
		cost:      0,
		language:  "",
		isSuccess: false,
		ignore:    false,
	}

	defer func() {
		// 计算最终耗时
		duration := time.Since(startTime)
		result.cost = duration.Microseconds()
		result.language = language

		if !isNeedReportErrResult {
			return
		}
		// 处理失败时单独上报处理失败埋点数据，明确失败原因
		reportErrorResult(workspace, duration, language, errorReason, record.FileAbsPath)
	}()

	key := ProcessFilePrefix + record.FileAbsPath
	if gw.lock.TryLock(key, time.Duration(config.singleFileTimeout)*time.Millisecond) {
		defer gw.lock.Unlock(key)

		// 推进到DOING
		condition1 := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileState, definition.OperatorIn, []interface{}{definition.INIT, definition.FAIL})
		condition2 := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileAbsPath, definition.OperatorEqual, record.FileAbsPath)
		updateField1 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordFileState, definition.DOING)
		updateField2 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordGmtModified, time.Now().UnixNano())
		updateCount, err := graphStore.UpdateFileRecord([]storage.GraphUpdateField{updateField1, updateField2}, []storage.GraphCondition{condition1, condition2})
		if err != nil {
			log.Errorf("[codebase-graph] update file(%s) record error: %v", record.FileAbsPath, err)
			errorReason = fmt.Sprintf("UpdateFileRecord_Error:%s", err.Error())
			isNeedReportErrResult = true
			return result
		}
		if updateCount == 0 {
			// 没有更新记录，可能是状态已被其他进程改变，算作成功
			result.isSuccess = true
			result.ignore = true
			return result
		}
		if record.Stage == definition.ProcessNode {
			gw.DeleteNodeAndEdgeRecord(graphStore, workspace, record.FileAbsPath)
		}

		if !gw.IsFileExist(record.FileAbsPath) {
			gw.DeleteFileRecord(graphStore, workspace, record.FileAbsPath)
			gw.DeleteNodeAndEdgeRecord(graphStore, workspace, record.FileAbsPath)
			// 文件不存在，清理完成算作成功
			result.isSuccess = true
			result.ignore = true
			return result
		}

		language = util.GetLanguageByFilePath(record.FileAbsPath)
		if language == definition.Python || language == definition.Java || language == definition.Golang ||
			language == definition.Kotlin || language == definition.CSharp ||
			language == definition.Vue || language == definition.JavaScript || language == definition.TypeScript ||
			language == definition.C || language == definition.Cpp || language == definition.C_Cpp {
			var graphParser parser.LangGraphParser
			if language == definition.Java {
				graphParser = java_parser.NewJavaLangGraphParser(context.Background(), workspace, graphStore)
			} else if language == definition.Python {
				graphParser = python_parser.NewPythonLangGraphParser(context.Background(), workspace, graphStore)
			} else if language == definition.Golang {
				moduleName, modulePath, err := graph.GetModulePathForFile(record.FileAbsPath, record.WorkspaceDir)
				if err != nil {
					gw.UpdateFileState(graphStore, workspace, definition.FAIL, record, config)
					isNeedReportErrResult = false
					result.ignore = true
					return result
				}
				ctx := context.Background()
				ctx = context.WithValue(ctx, graph.ContextKeyGolangModuleName, moduleName)
				ctx = context.WithValue(ctx, graph.ContextKeyGolangModulePath, modulePath)
				graphParser = golang_parser.NewGoGraphParser(ctx, workspace, graphStore)
			} else if language == definition.Kotlin {
				graphParser = kotlin_parser.NewKotlinLangGraphParser(context.Background(), workspace, graphStore)
			} else if language == definition.CSharp {
				graphParser = csharp_parser.NewCSharpLangGraphParser(context.Background(), workspace, graphStore)
			} else if language == definition.Vue || language == definition.JavaScript || language == definition.TypeScript {
				graphParser = javascript_parser.NewJsGraphParser(context.Background(), workspace, graphStore)
			} else {
				graphParser = cpp_parser.NewCppLangGraphParser(context.Background(), workspace, graphStore)
			}
			if graphParser == nil {
				gw.UpdateFileState(graphStore, workspace, definition.FAIL, record, config)
				log.Errorf("[codebase-graph] parser(%s) is nil", record.FileAbsPath)
				errorReason = "GraphParser_Nil_Error"
				isNeedReportErrResult = true
				return result
			}
			validFileCode, err := graphParser.IsValidFile(record.FileAbsPath)
			if err != nil {
				if judgeCanIgnoreError(err.Error(), config.ignoreErr) {
					gw.UpdateFileState(graphStore, workspace, definition.SUCCESS, record, config)
					// 处理成功
					result.isSuccess = true
					return result
				}
				gw.DeleteFileRecord(graphStore, workspace, record.FileAbsPath)
				log.Errorf("[codebase-graph] invalid file(%s) error: %v", record.FileAbsPath, err)
				// 无效文件，清理完成算作成功
				result.isSuccess = true
				result.ignore = true
				return result
			}

			if err := graphParser.ParseFile(record.FileAbsPath, string(validFileCode)); err != nil {
				if judgeCanIgnoreError(err.Error(), config.ignoreErr) {
					gw.UpdateFileState(graphStore, workspace, definition.SUCCESS, record, config)
					// 处理成功
					result.isSuccess = true
					return result
				}
				gw.UpdateFileState(graphStore, workspace, definition.FAIL, record, config)
				log.Errorf("[codebase-graph] parse file(%s) error: %v", record.FileAbsPath, err)
				errorReason = err.Error()
				isNeedReportErrResult = true
				return result
			}
			var unifiedGraph storage.UnifiedGraph
			var processErr error
			switch record.Stage {
			case definition.ProcessNode:
				unifiedGraph, processErr = graphParser.ProcessGraphNode()
			case definition.CompleteNode:
				unifiedGraph, processErr = graphParser.CompleteGraphNode()
			case definition.ProcessRelation:
				unifiedGraph, processErr = graphParser.ProcessGraphRelation()
			default:
				processErr = errors.New("unknown stage" + record.Stage + record.FileAbsPath)
			}
			if processErr == nil {
				gw.InsertToGraph(graphStore, workspace, unifiedGraph)
				gw.UpdateFileState(graphStore, workspace, definition.SUCCESS, record, config)
				// 处理成功
				result.isSuccess = true
			} else {
				if judgeCanIgnoreError(processErr.Error(), config.ignoreErr) {
					gw.UpdateFileState(graphStore, workspace, definition.SUCCESS, record, config)
					// 处理成功
					result.isSuccess = true
					return result
				}
				log.Errorf("[codebase-graph] parse file(%s) error: %v", record.FileAbsPath, processErr)
				gw.UpdateFileState(graphStore, workspace, definition.FAIL, record, config)
				errorReason = processErr.Error()
				isNeedReportErrResult = true
			}
		} else {
			log.Errorf("[codebase-graph] unknown language %s", record.FileAbsPath)
			gw.DeleteFileRecord(graphStore, workspace, record.FileAbsPath)
			// 不支持的语言，清理完成算作成功
			result.isSuccess = true
			result.ignore = true
		}
	} else {
		// 未获取锁，此种情况无需上报
		result.ignore = true
	}

	return result
}

func (gw *GraphWorker) DeleteFileRecord(graphStore storage.GraphStore, workspace string, filepath string) {
	condition := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileAbsPath, definition.OperatorEqual, filepath)
	if err := graphStore.DeleteFileRecord([]storage.GraphCondition{condition}); err != nil {
		log.Errorf("delete file record error: %v", err)
	}
}

func (gw *GraphWorker) DeleteNodeAndEdgeRecord(graphStore storage.GraphStore, workspace string, filepath string) {
	err := graphStore.RemoveNodeByFilePath(filepath)
	if err != nil {
		log.Errorf("[codebase-graph] remove node by file path error: %v", err)
	}
	err = graphStore.RemoveEdgeByFilePath(filepath)
	if err != nil {
		log.Errorf("[codebase-graph] remove edge by file path error: %v", err)
	}
}

func (gw *GraphWorker) InsertToGraph(graphStore storage.GraphStore, workspace string, unifiedGraph storage.UnifiedGraph) {
	for _, node := range unifiedGraph.Nodes {
		err := graphStore.AddNodeIfAbsent(node)
		if err != nil {
			log.Errorf("[codebase-graph] add node error: %v", err)
		}
	}
	err := graphStore.BatchAddEdges(unifiedGraph.Edges)
	if err != nil {
		log.Errorf("[codebase-graph] add edges error: %v", err)
	}
}

func (gw *GraphWorker) UpdateFileState(graphStore storage.GraphStore, workspace string, state string, record storage.GraphFileRecord, config workerProcessConfig) {
	// 要推进到FAIL状态，但是达到最大次数，因此直接推进到SUCCESS，后续会加一下状态，PENDING，下一次图升级再加
	if state == definition.FAIL {
		if judgeReachMaxFailedCount(record.FileAbsPath, workspace, record.Stage, config.maxFailedCount) {
			state = definition.SUCCESS
		}
	}
	condition := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileAbsPath, definition.OperatorEqual, record.FileAbsPath)
	updateField1 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordFileState, state)
	updateField2 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordNextExecuteTime, time.Now().UnixNano()+(10*1e9))
	updateField3 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordGmtModified, time.Now().UnixNano())
	_, err := graphStore.UpdateFileRecord([]storage.GraphUpdateField{updateField1, updateField2, updateField3}, []storage.GraphCondition{condition})
	if err != nil {
		log.Errorf("[codebase-graph] update file record error: %v", err)
	}
}

func (gw *GraphWorker) IsFileExist(filepath string) bool {
	_, err := os.Stat(filepath)
	if os.IsNotExist(err) {
		return false
	}
	return true
}

// verboseLogf 根据 LINGMA_GRAPH_WORKER_LOG_VERBOSE 环境变量控制debug日志输出
// 如果开启了 LINGMA_GRAPH_WORKER_LOG_VERBOSE 环境变量，则展示更多debug信息
func verboseLogf(format string, args ...interface{}) {
	if os.Getenv("LINGMA_GRAPH_WORKER_LOG_VERBOSE") != "" {
		log.Debugf("[codebase-graph-verbose] "+format, args...)
	}
}

// verboseLogf 根据 LINGMA_GRAPH_WORKER_LOG_VERBOSE 环境变量控制debug日志输出（无格式化参数）
func verboseLog(msg string) {
	if os.Getenv("LINGMA_GRAPH_WORKER_LOG_VERBOSE") != "" {
		log.Debugf("[codebase-graph-verbose] %s", msg)
	}
}

// reportErrorResult 上报worker处理错误埋点
func reportErrorResult(workspace string, cost time.Duration, language string, errorReason string, filepath string) {
	requestId := uuid.NewString()
	eventData := map[string]string{
		"workspace":   workspace,
		"filepath":    filepath,
		"language":    language,
		"cost":        strconv.FormatInt(cost.Microseconds(), 10),
		"errorReason": errorReason,
	}
	go sls.Report(sls.EventTypeChatCodebaseGraphIndexBuildResult, requestId, eventData)
}

// reportSummaryInfo 上报work处理的summary信息
// 按照stage和language分组计算评价耗时(cost的平均值)，成功数量(isSuccess为true)
// 统计时忽略ignore为true的信息
func reportSummaryInfo(stats []workerRunStat, workspace string) {
	defer func() {
		if r := recover(); r != nil {
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Errorf("Panic recovered in reportSummaryInfo: %v,%s", r, string(stack))
		}
	}()

	if len(stats) == 0 {
		return
	}

	// 用于分组统计的map，key为"stage_language"，value为统计数据
	type statSummary struct {
		totalCost    int64 // 总耗时
		count        int   // 总数量
		successCount int   // 成功数量
	}

	groupStats := make(map[string]*statSummary)

	// 遍历所有统计信息进行分组统计
	for _, stat := range stats {
		// 忽略ignore为true的信息
		if stat.ignore {
			continue
		}

		// 忽略language为空的情况
		if stat.language == "" {
			continue
		}

		// 构建分组key
		groupKey := fmt.Sprintf("%s_%s", stat.stage, stat.language)

		// 初始化或获取分组统计数据
		if groupStats[groupKey] == nil {
			groupStats[groupKey] = &statSummary{}
		}

		groupStat := groupStats[groupKey]
		groupStat.totalCost += stat.cost
		groupStat.count++
		if stat.isSuccess {
			groupStat.successCount++
		}
	}

	// 如果没有有效的统计数据，则不上报
	if len(groupStats) == 0 {
		return
	}

	// 构建所有分组的统计信息slice
	allGroupsStatInfo := make([]map[string]interface{}, 0)

	for groupKey, groupStat := range groupStats {
		// 解析stage和language
		parts := strings.Split(groupKey, "_")
		if len(parts) < 2 {
			continue
		}
		stage := parts[0]
		language := strings.Join(parts[1:], "_") // 处理language中可能包含下划线的情况

		// 计算统计数据
		avgCost := groupStat.totalCost / int64(groupStat.count)
		successRate := float64(groupStat.successCount) / float64(groupStat.count)

		// 构建 summaryInfo JSON 字符串
		summaryInfo := map[string]interface{}{
			"avg_cost":      avgCost,
			"success_count": groupStat.successCount,
			"total_count":   groupStat.count,
			"success_rate":  fmt.Sprintf("%.2f", successRate),
		}

		summaryInfoJSON, err := json.Marshal(summaryInfo)
		if err != nil {
			log.Errorf("[codebase-graph] marshal summaryInfo error: %v", err)
			continue
		}

		// 将该分组的统计信息添加到slice中
		groupStatInfo := map[string]interface{}{
			"stage":       stage,
			"language":    language,
			"summaryInfo": string(summaryInfoJSON),
		}
		allGroupsStatInfo = append(allGroupsStatInfo, groupStatInfo)
	}

	// 将所有分组信息序列化为JSON字符串
	statInfoJSON, err := json.Marshal(allGroupsStatInfo)
	if err != nil {
		log.Errorf("[codebase-graph] marshal statInfo error: %v", err)
		return
	}

	// 构建上报数据，只调用一次sls.Report
	requestId := uuid.NewString()
	eventData := map[string]string{
		"workspace": workspace,
		"statInfo":  string(statInfoJSON),
	}

	sls.Report(sls.EventTypeChatCodebaseGraphIndexBuildSummary, requestId, eventData)
}

func judgeCanIgnoreError(err string, canIgnoreError map[string]bool) bool {
	if _, ok := canIgnoreError[err]; ok {
		return true
	}
	return false
}

func judgeReachMaxFailedCount(filepath, workspace, stage string, maxFailedCount int) bool {
	res := false
	count := GetFailedFileCount(filepath, workspace, stage)
	if count >= maxFailedCount {
		res = true
	}
	AddFailedFileCount(filepath, workspace, stage, 1)
	return res
}
