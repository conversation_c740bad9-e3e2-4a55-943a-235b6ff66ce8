package graph

import (
	"fmt"
	"github.com/patrickmn/go-cache"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestCacheExpiredSuccess(t *testing.T) {
	testCache := cache.New(1*time.Minute, 1*time.Minute)

	ChangeFailedFileCache(testCache)

	for i := 0; i < 50; i++ {
		time.Sleep(500 * time.Millisecond)
		AddFailedFileCount("test", "test", "test", 1)
		count := GetFailedFileCount("test", "test", "test")
		fmt.Println("test success, count", count)
		assert.Equal(t, count, i+1)

		failedFilepathCount := GetFailedFileCount("test1", "test", "test")
		assert.Equal(t, failedFilepathCount, 0)
		failedFilepathCount = GetFailedFileCount("test", "test1", "test")
		assert.Equal(t, failedFilepathCount, 0)
		failedFilepathCount = GetFailedFileCount("test", "test", "test1")
		assert.Equal(t, failedFilepathCount, 0)
	}

	time.Sleep(1 * time.Minute)
	assert.Equal(t, GetFailedFileCount("test", "test", "test"), 0)
	AddFailedFileCount("test", "test", "test", 1)
	assert.Equal(t, GetFailedFileCount("test", "test", "test"), 1)
}
