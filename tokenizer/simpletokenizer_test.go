package tokenizer

import (
	"testing"
)

func TestSimpleTokenizer_Tokenize(t1 *testing.T) {
	//b, _ := ioutil.ReadFile("/Users/<USER>/Downloads/demo3.java")
	//code := string(b)
	//tokenizer := NewSimpleTokenizer()
	//re, _ := tokenizer.Tokenize(code)
	//for _, v := range re {
	//    log.Info(fmt.Sprintf("[%s]", v))
	//}
}

func TestSimpleMergeTokenizer_Tokenize(t1 *testing.T) {
	//b, _ := ioutil.ReadFile("/Users/<USER>/Downloads/java-client/src/main/java/com/wavefront/integrations/Main.java")
	//code := string(b)
	//tokenizer := NewSimpleMergeTokenizer()
	//re, _ := tokenizer.Tokenize(code)
	//for _, v := range re {
	//    log.Info(fmt.Sprintf("[%s]", v))
	//}
}
