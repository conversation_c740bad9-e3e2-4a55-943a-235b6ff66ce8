package tokenizer

import (
	"errors"
	"fmt"
	"log"
	"sync"

	"github.com/pkoukk/tiktoken-go"
)

var (
	// 定义一个全局的jieba分词器，避免重复加载词典
	tiktokenLock = sync.Mutex{}
	tik          *tiktoken.Tiktoken
)

type QwenTokenizer struct {
	tik *tiktoken.Tiktoken
	// 是否对分词后的token id 进行解码
	decodeTokenId bool
}

var _tokenizer *QwenTokenizer

func init() {
	qwenTokenizer, err := NewQwenTokenizer(false)
	if err != nil {
		log.Fatal("init qwen tokenizer fail", err)
		panic(err)
	}
	_tokenizer = qwenTokenizer
}

// NewQwenTokenizer 创建qwen解码器
// param decodeTokenId：是否对token解码，false时性能更好
func NewQwenTokenizer(decodeTokenId bool) (*QwenTokenizer, error) {
	tiktokenLock.Lock()
	defer tiktokenLock.Unlock()
	if tik == nil {
		var err error
		tik, err = tiktoken.GetEncoding(tiktoken.MODEL_QWEN_BASE)
		if err != nil {
			return nil, err
		}
	}
	return &QwenTokenizer{
		tik:           tik,
		decodeTokenId: decodeTokenId,
	}, nil
}

func (seg *QwenTokenizer) Tokenize(str string) ([]string, error) {
	if seg.tik == nil {
		return nil, errors.New("tiktoken not init")
	}
	tokens := seg.tik.Encode(str, []string{"all"}, nil)
	result := make([]string, len(tokens))
	for i, token := range tokens {
		if seg.decodeTokenId {
			result[i] = seg.tik.Decode([]int{token})
		} else {
			result[i] = fmt.Sprintf("%d", token)
		}
	}
	return result, nil
}

func (seg *QwenTokenizer) Untokenize(tokens string) (string, error) {
	return "", errors.New("untokenize not support")
}

func CalQwenTokenCount(text string) (int, error) {
	if _tokenizer == nil {
		return -1, errors.New("tokenizer not init")
	}
	tokens, err := _tokenizer.Tokenize(text)
	if err != nil {
		return -1, err
	}
	return len(tokens), nil
}

func GetQwenTokenSize(text string) int {
	count, err := CalQwenTokenCount(text)
	if err != nil {
		return len(text) / 3
	}
	return count
}
