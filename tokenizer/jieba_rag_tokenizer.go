package tokenizer

import (
	"strings"
)

type JiebaRagTokenizer struct {
	// 分割驼峰
	splitCamelCase bool
	// 保留原词
	keepOriginCamel bool

	jiebaTokenizer *JiebaBleveTokenizer
}

// NewJiebaRagTokenizer returns a JiebaRagTokenizer
func NewJiebaRagTokenizer() *JiebaRagTokenizer {
	return &JiebaRagTokenizer{
		splitCamelCase:  true,
		keepOriginCamel: true,
		jiebaTokenizer:  NewJiebaBleveTokenizer(),
	}
}

// Tokenize converts given code to a list of tokens
func (t *JiebaRagTokenizer) Tokenize(code string) (result []string, err error) {
	tokens := t.jiebaTokenizer.Tokenize([]byte(code))
	result = make([]string, 0, len(tokens))
	for _, jiebaToken := range tokens {
		token := string(jiebaToken.Term)
		if strings.Trim(token, " \r\n\t") == "" {
			continue
		}
		// 如果是前端标签，则跳过拆分驼峰阶段
		if t.splitCamelCase && !strings.HasPrefix(token, "<") {
			// 拆分驼峰单词
			camelCaseStat := CamelCaseTokenizer(token)
			if camelCaseStat != token && camelCaseStat != "" {
				camelTokens := strings.Split(camelCaseStat, " ")
				for _, camelToken := range camelTokens {
					result = append(result, camelToken)
				}
				if t.keepOriginCamel {
					// 保留原词
					result = append(result, token)
				}
			} else {
				result = append(result, token)
			}
		} else {
			result = append(result, token)
		}
	}
	return result, nil
}

// Untokenize recovers the list of tokens to the text
// By default, the given tokens are separated by whitespaces
func (t *JiebaRagTokenizer) Untokenize(tokens string) (string, error) {
	return tokens, nil
}
