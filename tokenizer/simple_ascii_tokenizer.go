package tokenizer

import "cosy/definition"

// GetTokenCountWithSimpleAsciiTokenizer 对于代码类型，ascii码占比高的场景，简单化获取chunk数
func GetTokenCountWithSimpleAsciiTokenizer(code string) int {
	if len(code) <= 3 {
		return 1
	}
	return len(code) / 3
}

func GetAsciiCountWithSimpleAsciiTokenizer(tokenCount int) int {
	return tokenCount * 3
}

func GetChunkCountWithFileSize(fileSize int) int {
	asciiCount := GetAsciiCountWithSimpleAsciiTokenizer(definition.DefaultChunkSize)
	if fileSize <= asciiCount {
		return 1
	}

	result := int(fileSize / asciiCount)
	if result*asciiCount < fileSize {
		result++
	}
	return result
}
