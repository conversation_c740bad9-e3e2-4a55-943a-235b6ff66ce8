package tokenizer

import (
	"fmt"
	"testing"
)

func TestPunctuationTokenizer(t *testing.T) {
	tests := []string{
		"",
		"#GoLangCode!$!",
		"regexp.Compile",
		"for _, s := range ",
		"TestPunctuationTokenizer(t",
		"TestPunctuationTokenizer(t.",
		// 本期直接移除掉所有问句中的中文
		"中文问句处理",
		"万一有中文呢？",
		"万一有中文呢？中文测试",
		"如何读取excel文件",
	}
	for _, test := range tests {
		fmt.Print(test, "\t\t=>\n", PunctuationTokenizer(test), "\n\n")
	}
}
