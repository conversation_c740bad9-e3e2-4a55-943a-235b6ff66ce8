package tokenizer

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestBpeTokenizer_Train1(t1 *testing.T) {
	//numSymbol := 32000
	//trainer := NewTrainer(*DefaultTrainerConfig())
	//trainer.outputFreq = true
	//data := readJavaFiles("/Users/<USER>/Projects/java/force")
	//fmt.Println("Number of files:", len(data))
	//tt := time.Now()
	//for i := 0; i < 1; i++ {
	//    model, vocab, _ := trainer.Train(data, numSymbol)
	//    trainer.WriteModel(model, vocab, "java_bpe_model.txt", "java_bpe_vocab.txt")
	//    //fmt.Println("Model size:", len(model))
	//    //for _, v := range model {
	//    //    fmt.Println(v)
	//    //}
	//    //fmt.Println("Vocab size:", len(vocab))
	//    //fmt.Println(vocab)
	//}
	//
	//fmt.Println(fmt.Sprintf("Training bpe model with %d bigrams from %d source code files costs: ", numSymbol, len(data)), time.Since(tt))
}

// Recursively read
func readJavaFiles(path string) []string {
	var result []string
	filepath.Walk(path, func(path string, info os.FileInfo, err error) error {
		if strings.HasSuffix(path, ".java") {
			content, err := ioutil.ReadFile(path)
			if err != nil {
				return err
			}
			result = append(result, string(content))
		}
		return nil
	})

	return result
}

// Recursively read
func readJsFiles(path string) []string {
	var result []string
	filepath.Walk(path, func(path string, info os.FileInfo, err error) error {
		if strings.HasSuffix(strings.ToLower(path), ".js") ||
			strings.HasSuffix(strings.ToLower(path), ".jsx") ||
			strings.HasSuffix(strings.ToLower(path), ".ts") ||
			strings.HasSuffix(strings.ToLower(path), ".tsx") {
			fmt.Println(path)
			content, err := ioutil.ReadFile(path)
			if err != nil {
				return err
			}
			result = append(result, string(content))
		} else {
		}
		return nil
	})

	return result
}
