package tokenizer

import (
	"regexp"
	"strings"
)

// Regex
var (
	ragWordsSplitRegex = regexp.MustCompile(`#\w+|[^a-zA-Z0-9]+?`)
)

type RagTokenizer struct {
	// 分割驼峰
	splitCamelCase bool
	// 保留原词
	keepOriginCamel bool
}

// NewRagTokenizer returns a RagTokenizer
func NewRagTokenizer() *RagTokenizer {
	return &RagTokenizer{
		splitCamelCase:  true,
		keepOriginCamel: true,
	}
}

// Tokenize converts given code to a list of tokens
func (t *RagTokenizer) Tokenize(code string) (result []string, err error) {
	tokens := ragWordsSplitRegex.Split(code, -1)
	result = make([]string, 0, len(tokens))
	for _, token := range tokens {
		if strings.Trim(token, " \r\n\t") == "" {
			continue
		}
		if t.splitCamelCase {
			// 拆分驼峰单词
			camelCaseStat := CamelCaseTokenizer(token)
			if camelCaseStat != token && camelCaseStat != "" {
				camelTokens := strings.Split(camelCaseStat, " ")
				for _, camelToken := range camelTokens {
					result = append(result, camelToken)
				}
				if t.keepOriginCamel {
					// 保留原词
					result = append(result, token)
				}
			} else {
				result = append(result, token)
			}
		} else {
			result = append(result, token)
		}
	}
	return result, nil
}

// Untokenize recovers the list of tokens to the text
// By default, the given tokens are separated by whitespaces
func (t *RagTokenizer) Untokenize(tokens string) (string, error) {
	return tokens, nil
}
