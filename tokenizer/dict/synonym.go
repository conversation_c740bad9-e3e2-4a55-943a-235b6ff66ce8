package dict

import "strings"

// SynonymMap 同义词词典
type SynonymMap interface {
	Load(dict string) error
	Add(key string, values []string) error
	Get(key string) ([]string, error)
}

// ElasticsearchSynonymMap 实现基于elasticsearch同义词词典格式的同义词词典加载
type ElasticsearchSynonymMap struct {
	synonyms        map[string][]string
	ignoreCaseUpper bool // 是否忽略大小写
}

func NewElasticsearchSynonymMap(ignoreCaseUpper bool) *ElasticsearchSynonymMap {
	return &ElasticsearchSynonymMap{
		synonyms:        make(map[string][]string),
		ignoreCaseUpper: ignoreCaseUpper,
	}
}

// Load 加载同义词词典
// 每行一条规则，包含两种规则
// 规则一: word1,word2,word3
// 规则二: word1,word2 => word3
// 当命中规则一中的单词时，将命中的规则一中的所有单词返回
// 当命中规则二时，将命中的规则二中的=>后的单词返回
func (s *ElasticsearchSynonymMap) Load(dict string) error {
	lines := strings.Split(dict, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		if strings.HasPrefix(line, "#") {
			continue
		}
		if strings.Contains(line, "=>") {
			// 规则二
			parts := strings.Split(line, "=>")
			if len(parts) != 2 {
				continue
			}
			key := strings.Split(strings.TrimSpace(parts[0]), ",")
			values := strings.Split(strings.TrimSpace(parts[1]), ",")
			for _, k := range key {
				err := s.Add(k, values)
				if err != nil {
					continue
				}
			}
		} else {
			// 规则一
			values := strings.Split(line, ",")
			for _, v := range values {
				err := s.Add(v, values)
				if err != nil {
					continue
				}
			}
		}
	}
	return nil
}

func (s *ElasticsearchSynonymMap) Add(key string, values []string) error {
	if s.ignoreCaseUpper {
		key = strings.ToLower(key)
		newValues := make([]string, len(values))
		for i, v := range values {
			newValues[i] = strings.ToLower(v)
		}
		values = newValues
	}
	s.synonyms[key] = values
	return nil
}

func (s *ElasticsearchSynonymMap) Get(key string) ([]string, error) {
	if s.ignoreCaseUpper {
		key = strings.ToLower(key)
	}
	if values, ok := s.synonyms[key]; ok {
		return values, nil
	}
	return nil, nil
}
