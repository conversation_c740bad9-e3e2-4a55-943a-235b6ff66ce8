package tokenizer

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestJiebaBleveTokenizer_Tokenize(t *testing.T) {
	jiebaTokenizer := NewJiebaBleveTokenizer()
	input := `<ButtonTooltip
        text
        className={styles.configDiff}
        onClick={() => setVisible(true)}
        onlyDisabled={false}
        tooltip={'查看更新内容'}
      >
        <YunxiaoIcon type="warning-fill" />
        存在更新
      </ButtonTooltip>

<setting-item name="请求方式 & URL 地址">
      <n-input-group></n-input-group>
      
    </setting-item>`
	//bytes := []byte(input)
	tokens := jiebaTokenizer.Tokenize([]byte(input))
	for _, token := range tokens {
		t.Log(string(token.Term), token.Position, token.Type, token.KeyWord, token.Start, token.End)
		//t.Log(string(bytes[token.Start:token.End]))
	}
}

func TestJiebaBleveTokenizer_StopWord(t *testing.T) {
	jiebaTokenizer := NewJiebaBleveTokenizer()
	assert.Equal(t, true, jiebaTokenizer.seg.IsStop("{"))
	assert.Equal(t, true, jiebaTokenizer.seg.IsStop("}"))
}
