package tokenizer

import (
	"cosy/log"
	"cosy/tokenizer/dict"
	"errors"
	"strings"
	"sync"

	"github.com/go-ego/gse"
)

var (
	// 定义一个全局的jieba分词器，避免重复加载词典
	globalSegment *gse.Segmenter
	synonymMap    dict.SynonymMap
	segmentLock   = sync.Mutex{}
)

type JiebaTokenizer struct {
	seg                *gse.Segmenter
	synonymMap         dict.SynonymMap
	enableSynonym      bool
	filterInvalidChars bool
}

func NewJiebaTokenizer(enableSynonym bool) *JiebaTokenizer {
	segmentLock.Lock()
	defer segmentLock.Unlock()
	if globalSegment == nil {
		var seg gse.Segmenter
		err := seg.LoadDictStr(dict.SegmentZhDict)
		if err != nil {
			log.Error("load dict error: %v", err)
		}
		err = seg.LoadStopStr(dict.StopZhDict)
		if err != nil {
			log.Error("load stop dict error: %v", err)
		}
		synonymMap = dict.NewElasticsearchSynonymMap(true)
		err = synonymMap.Load(dict.SynonymDict)
		if err != nil {
			log.Error("load synonym dict error: %v", err)
		}
		globalSegment = &seg
	}
	gse.ToLower = false
	return &JiebaTokenizer{
		seg:                globalSegment,
		synonymMap:         synonymMap,
		enableSynonym:      enableSynonym,
		filterInvalidChars: true,
	}
}

func (seg *JiebaTokenizer) Tokenize(code string) ([]Token, error) {
	if seg.seg == nil {
		return nil, errors.New("segmenter is nil")
	}
	mLen := int(float32(len(code))/3) + 1
	result := make([]Token, 0, mLen)
	offset := 0
	tokens := seg.seg.Cut(code)
	for i, token := range tokens {
		bytes := []byte(token)
		if seg.filterInvalidChars && !isValidToken(token) {
			offset += len(bytes)
			continue
		}
		extTokens := make([]string, 0, 2)
		extTokens = append(extTokens, token)
		extTokens = seg.synonym(extTokens, token)
		extTokens = seg.ngram(extTokens, token)
		duplicates := make(map[string]bool, len(extTokens))
		for _, extToken := range extTokens {
			lowerToken := strings.ToLower(extToken)
			if duplicates[lowerToken] {
				continue
			}
			duplicates[lowerToken] = true
			result = append(result, Token{
				Position: i + 1,
				Term:     extToken,
				Start:    offset,
				End:      offset + len(bytes),
			})
		}
		offset += len(bytes)
	}
	return result, nil
}

func (seg *JiebaTokenizer) TokenizeToString(code string) ([]string, error) {
	tokens, err := seg.Tokenize(code)
	if err != nil {
		return nil, err
	}
	result := make([]string, 0, len(tokens))
	for _, token := range tokens {
		result = append(result, string(token.Term))
	}
	return result, nil
}

func (seg *JiebaTokenizer) synonym(extTokens []string, token string) []string {
	if synonymMap != nil && seg.enableSynonym {
		if seg.IsStop(token) {
			return extTokens
		}
		if synonyms, err := seg.synonymMap.Get(strings.ToLower(token)); len(synonyms) > 0 && err == nil {
			extTokens = append(extTokens, synonyms...)
		} else {
			extTokens = append(extTokens, token)
		}
	}
	return extTokens
}

func (seg *JiebaTokenizer) ngram(extTokens []string, token string) []string {
	// for search ngram
	runes := []rune(token)
	for _, incr := range []int{2, 3} {
		if len(runes) <= incr {
			continue
		}

		var gram string
		for i := 0; i < len(runes)-incr+1; i++ {
			gram = string(runes[i : i+incr])
			v, _, ok := seg.seg.Find(gram)
			if ok && v > 0 {
				extTokens = append(extTokens, gram)
			}
		}
	}
	return extTokens
}

func (seg *JiebaTokenizer) IsStop(tokens string) bool {
	return seg.seg.IsStop(tokens)
}

func isValidToken(token string) bool {
	clearToken := strings.Trim(token, " \t\r\n")
	return len(clearToken) > 0
}
