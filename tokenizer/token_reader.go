package tokenizer

import (
	"errors"
	"strings"
)

type TokenReader interface {
	GetToken() (string, error)
	GetLine() int
	Next() bool
	GetLines() []string
	NextLine() bool
	GetLintTokens() []string
}

type SimpleTokenReader struct {
	code              string
	lines             []string
	currentLine       int
	currentTokenIndex int
	lineTokens        []string
	tokenizer         Tokenizer
}

func NewSimpleTokenReader(code string) *SimpleTokenReader {
	return &SimpleTokenReader{
		code:              code,
		lines:             strings.Split(code, "\n"),
		currentLine:       0,
		currentTokenIndex: -1,
		tokenizer:         NewJiebaRagTokenizer(),
	}
}

func (s *SimpleTokenReader) GetToken() (string, error) {
	if s.currentTokenIndex < 0 || s.currentTokenIndex >= len(s.lineTokens) {
		return "", errors.New("invalid token index")
	}
	return s.lineTokens[s.currentTokenIndex], nil
}

func (s *SimpleTokenReader) GetLine() int {
	return s.currentLine - 1
}

func (s *SimpleTokenReader) GetLines() []string {
	return s.lines
}

func (s *SimpleTokenReader) Next() bool {
	var err error
	if s.currentLine < len(s.lines) {
		if s.lineTokens == nil || s.currentTokenIndex+1 >= len(s.lineTokens) {
			var line string
			for {
				if s.currentLine >= len(s.lines) {
					break
				}
				line = s.lines[s.currentLine]
				line = strings.Trim(line, " \t")
				if line != "" {
					break
				}
				s.currentLine++
			}
			s.lineTokens, err = s.tokenizer.Tokenize(line)
			if err != nil {
				return false
			}
			s.currentTokenIndex = 0
			s.currentLine++
			return true
		} else if s.currentTokenIndex >= 0 && s.currentTokenIndex < len(s.lineTokens) {
			s.currentTokenIndex++
			return true
		}
	}
	return false
}

func (s *SimpleTokenReader) NextLine() bool {
	if s.currentLine < len(s.lines) {
		line := s.lines[s.currentLine]
		var err error
		s.lineTokens, err = s.tokenizer.Tokenize(line)
		if err != nil {
			return false
		}
		s.currentLine++
		return true
	}
	return false
}

func (s *SimpleTokenReader) GetLintTokens() []string {
	return s.lineTokens
}
