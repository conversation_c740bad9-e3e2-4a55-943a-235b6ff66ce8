package tokenizer

import (
	"fmt"
	"testing"
)

func TestCamelCaseTokenizer(t *testing.T) {
	tests := []string{
		"",
		"lowercase",
		"Class",
		"MyClass",
		"HTML",
		"PDFLoader",
		"AString",
		"SimpleXMLParser",
		"vimRPCPlugin",
		"GL11Version",
		"99Bottles",
		"May5",
		"BFG9000",
		"BöseÜberraschung",
		"Two  spaces",
		"BadUTF8\\xe2\\xe2\\xa1",
		"demo-abc-masd",
	}
	for _, test := range tests {
		fmt.Print(test, ": ", CamelCaseTokenizer(test), "\n")
	}
}
