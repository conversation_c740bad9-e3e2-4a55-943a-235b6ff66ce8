package tokenizer

import (
	"cosy/log"
	"fmt"
	"io/ioutil"
	"regexp"
	"strings"
	"time"
)

// Regex
var (
	blankCharRegex = regexp.MustCompile("\\s+")
	splitRegex     = regexp.MustCompile("\\$\\$VAR\\$\\$|\\$\\$STR\\$\\$|\\s?[a-zA-Z0-9_@/$]+| ?[^\\s\\w@_/$]+|\\s+")
)

type TokenizerConfig struct {
	wordEndIdentifier    string   // Identifier for the end of a word
	subwordIdentifier    string   // Identifier which indicates that the token is a subword
	whitespaceIdentifier string   // Identifier for whitespace
	glossaries           []string // Reserved words
	modelFile            string   // Bpe model filename, located in working directory
	vocabFile            string   // Vocabulary filename, located in working directory
	Wd                   string   // Working directory
}

type BpeTokenizer struct {
	TokenizerConfig
	// The key is the bigram, the value is bigram's order in model file.
	// Smaller value means higher frequency
	model map[Bigram]int
	// ReverseModel is used to reverse merge operation
	reverseModel map[string]Bigram
	// Optional vocabulary
	vocab map[string]bool
}

type Bigram struct {
	Prev string
	Next string
}

// NewBpe returns a bpe tokenizer
func NewBpe(config TokenizerConfig) (*BpeTokenizer, error) {
	start := time.Now()
	tokenizer := &BpeTokenizer{
		TokenizerConfig: config,
	}
	if err := tokenizer.Init(); err != nil {
		return nil, err
	}
	log.Info("Bpe tokenizer initialized, model size: ", len(tokenizer.model), ", time cost: ", time.Since(start))
	return tokenizer, nil
}

// NewBigram returns a Bigram
func NewBigram(prev string, next string) *Bigram {
	return &Bigram{
		Prev: prev,
		Next: next,
	}
}

func DefaultTokenizerConfig(wd string, modelFile string, vocabFile string) *TokenizerConfig {
	return &TokenizerConfig{
		wordEndIdentifier:    "🔚",
		subwordIdentifier:    "⫗",
		whitespaceIdentifier: "🀆",
		glossaries:           []string{"$$VAR$$", "$$STR$$"},
		Wd:                   wd,
		modelFile:            modelFile,
		vocabFile:            vocabFile,
	}
}

func EmptyTokenizerConfig() *TokenizerConfig {
	return &TokenizerConfig{
		wordEndIdentifier:    "🔚",
		subwordIdentifier:    "⫗",
		whitespaceIdentifier: "🀆",
		glossaries:           []string{"$$VAR$$", "$$STR$$"},
	}
}

// Tokenize converts given code to a list of tokens
func (t *BpeTokenizer) Tokenize(code string) (result []string, err error) {
	code = replaceBlankChars(code)
	if len(t.model) == 0 {
		if err := t.Init(); err != nil {
			log.Info("Initialization failed, exiting")
			return nil, err
		}
	}

	if result, err = Encode(code, t.model, t.reverseModel, t.vocab, t.TokenizerConfig); err != nil {
		return nil, err
	}
	return result, nil
}

// Untokenize recovers the list of tokens to the text
// By default, the given tokens are separated by whitespaces
func (t *BpeTokenizer) Untokenize(tokens string) (string, error) {
	return Decode(strings.Split(tokens, " ")), nil
}

func (t *BpeTokenizer) Init() error {
	var err error
	if t.model, t.vocab, err = Load(t.Wd+t.modelFile, t.Wd+t.vocabFile); err != nil && t.model == nil {
		log.Info("[ERROR] Cannot load BPE model:", err)
		return err
	}
	t.reverseModel = map[string]Bigram{}
	for b := range t.model {
		k := b.Prev + b.Next
		t.reverseModel[k] = b
	}
	log.Info("Model loaded, size:", len(t.model), ", vocab size:", len(t.vocab))
	return nil
}

// GetModelFromStr initializes the BPE model from model string
func GetModelFromStr(modelStr string) (model map[Bigram]int) {
	model = map[Bigram]int{}
	lines := strings.Split(modelStr, "\n")
	order := 1
	for _, line := range lines {
		words := strings.Split(line, " ")
		if len(words) != 2 && len(words) != 3 {
			log.Info(fmt.Sprintf("WARN: invalid bigram: [%s]", line))
			continue
		}
		// Ignore frequencies of bigrams now
		model[*NewBigram(words[0], words[1])] = order
		order++
	}
	return model
}

// Load reads the local trained bpe model
// The first return value is Bigram model, two words are separated by whitespace
// The second return value is vocab list
func Load(bigramFilename string, vocabFilename string) (model map[Bigram]int, vocab map[string]bool, err error) {
	model = map[Bigram]int{}
	vocab = map[string]bool{}
	var bytes []byte

	// Load model
	order := 1
	if bytes, err = ioutil.ReadFile(bigramFilename); err != nil {
		return nil, nil, err
	}
	bigrams := strings.Split(string(bytes), "\n")
	for _, bigramStr := range bigrams {
		words := strings.Split(bigramStr, " ")
		if len(words) != 2 && len(words) != 3 {
			log.Info(fmt.Sprintf("WARN: invalid bigram: [%s]", bigramStr))
			continue
		}
		// Ignore frequencies of bigrams now
		model[*NewBigram(words[0], words[1])] = order
		order++
	}

	// Load vocab
	if bytes, err = ioutil.ReadFile(vocabFilename); err != nil {
		return model, nil, err
	}
	for _, line := range strings.Split(string(bytes), "\n") {
		words := strings.Split(line, " ")
		if len(words) != 1 && len(words) != 2 {
			log.Info(fmt.Sprintf("WARN: invalid vocab: [%s]", line))
			continue
		}
		vocab[words[0]] = true
	}

	return model, vocab, nil
}

// Encode encodes text using given bpe model described by bigrams, vocab and reserveWord
func Encode(text string, model map[Bigram]int, reverseModel map[string]Bigram, vocab map[string]bool, config TokenizerConfig) (result []string, err error) {
	result = []string{}

	tokens := splitRegex.FindAllString(replaceBlankChars(text), -1)
	for i := range tokens {
		if tokens[i] != "" {
			tokens[i] = strings.ReplaceAll(tokens[i], " ", config.whitespaceIdentifier)
		}
	}

	for _, token := range tokens {
		// Skip invalid token
		if token == " " || token == "" {
			continue
		}

		words := processGlossaries(token, config.glossaries)
		for _, word := range words {
			// The word is already in vocab
			if vocab[word] == true {
				result = append(result, word)
				continue
			}

			// Tokenize word
			subwords := strings.Split(parseWord(word, config.wordEndIdentifier), " ")
			for len(subwords) > 1 {
				// Get all pairs to be merged
				var pairs []Bigram
				pairsPos := map[Bigram]int{}
				for i, subword := range subwords {
					if i > 0 {
						bigram := *NewBigram(subwords[i-1], subword)
						if model[bigram] > 0 {
							// If the pair is in the model, do merge later
							pairs = append(pairs, bigram)
							pairsPos[bigram] = i - 1
						}
					}
				}

				if len(pairs) <= 0 || pairs == nil {
					break
				}

				// Get first merge operation
				firstMergePairs := pairs[0]
				order := model[firstMergePairs]
				for _, p := range pairs {
					if order > model[p] && model[p] > 0 {
						firstMergePairs = p
						order = model[p]
					}
				}

				// Find start position of all pairs that we want to merges
				var positions []int
				for _, p := range pairs {
					if p == firstMergePairs {
						positions = append(positions, pairsPos[p])
					}
				}
				// Do merge
				idx := 0
				var newSubwords []string
				bigram := firstMergePairs.Prev + firstMergePairs.Next
				for _, pos := range positions {
					// Merges are invalid if they start before current position. This can happen if there are overlapping pairs: (x x x -> xx x)
					if pos < idx {
						continue
					}
					newSubwords = append(newSubwords, subwords[idx:pos]...)
					newSubwords = append(newSubwords, bigram)
					// Continue after merged pair
					idx = pos + 2
				}
				// Append rest subwords
				newSubwords = append(newSubwords, subwords[idx:]...)
				subwords = newSubwords
			}

			// Remove wordEndIdentifier from subwords
			lastSubword := subwords[len(subwords)-1]
			if lastSubword == config.wordEndIdentifier {
				subwords = subwords[:len(subwords)-1]
			} else if strings.HasSuffix(lastSubword, config.wordEndIdentifier) {
				subwords[len(subwords)-1] = strings.Trim(lastSubword, config.wordEndIdentifier)
			}

			// Split by vocab (if needed)
			if len(vocab) > 0 {
				subwords = splitByVocab(subwords, reverseModel, vocab, config)
			}

			for i := range subwords {
				if i < len(subwords)-1 {
					subwords[i] = subwords[i] + config.subwordIdentifier
				}
			}

			result = append(result, subwords...)
		}
	}
	return result, nil
}

func Decode(tokens []string) (result string) {
	result = strings.Join(tokens, "")
	result = strings.ReplaceAll(result, "⫗", "")
	result = strings.ReplaceAll(result, "🀆", " ")
	return result
}

func splitByVocab(subwords []string, reverseModel map[string]Bigram, vocab map[string]bool, config TokenizerConfig) []string {
	var result []string
	for i, subword := range subwords {
		// For last subword, do not append subword identifier
		if i == len(subwords)-1 {
			if vocab[subword] == true {
				result = append(result, subword)
			} else {
				result = append(result, recursiveSplit(subword, reverseModel, vocab, config, true)...)
			}
			continue
		}

		if vocab[subword+config.subwordIdentifier] == true {
			result = append(result, subword)
		} else {
			result = append(result, recursiveSplit(subword, reverseModel, vocab, config, false)...)
		}
	}
	return result
}

// recursiveSplit splits segment into smaller units (by reversing BPE merges) recursively
// Splitting stops if all units are either in-vocabulary, or cannot be split further.
func recursiveSplit(subword string, reverseModel map[string]Bigram, vocab map[string]bool, config TokenizerConfig, isFinal bool) []string {
	var result []string
	var bigram Bigram
	if isFinal {
		subword = subword + config.wordEndIdentifier
	}
	var ok bool
	if bigram, ok = reverseModel[subword]; !ok {
		subword = strings.Trim(subword, config.wordEndIdentifier)
		result = append(result, subword)
		return result
	}
	bigram.Next = strings.Trim(bigram.Next, config.wordEndIdentifier)

	if vocab[bigram.Prev+config.subwordIdentifier] == true {
		result = append(result, bigram.Prev)
	} else {
		result = append(result, recursiveSplit(bigram.Prev, reverseModel, vocab, config, false)...)
	}

	if (isFinal && vocab[bigram.Next] == true) || (!isFinal && vocab[bigram.Next+config.subwordIdentifier] == true) {
		result = append(result, bigram.Next)
	} else {
		result = append(result, recursiveSplit(bigram.Next, reverseModel, vocab, config, isFinal)...)
	}
	return result
}

// Process the glossary list
// Returns a list of subwords. In which all 'glossary' glossaries are isolated
// For example, if 'USA' is the glossary and '1934USABUSA' the word, the return value is:
// ['1934', 'USA', 'B', 'USA']
func processGlossaries(word string, glossaries []string) []string {
	subwords := []string{word}
	for _, gloss := range glossaries {
		var newSubwords []string
		for _, subword := range subwords {
			if subword == gloss {
				newSubwords = append(newSubwords, subword)
			} else {
				subword = strings.ReplaceAll(subword, gloss, " "+gloss+" ")
				subSubwords := strings.Split(subword, " ")
				for _, subSubword := range subSubwords {
					if subSubword != "" && subSubword != " " {
						newSubwords = append(newSubwords, subSubword)
					}
				}
			}
		}
		subwords = newSubwords
	}
	return subwords
}

// replaceBlankChars replaces all sequential blank chars using whitespace, including \n, \t, whitespace, etc
func replaceBlankChars(code string) string {
	return blankCharRegex.ReplaceAllLiteralString(code, " ")
}

// parseWord parses a word into char sequence separated by whitespace
// Note that a wordEndIdentifier is added following the last character
// For example, for input word "code" and wordEndIdentifier "<\w>", this function returns "c o d e<\w>"
func parseWord(word string, wordEndIdentifier string) string {
	parsedWord := ""
	for i, c := range word {
		if c == ' ' {
			continue
		}
		if i == 0 {
			parsedWord += string(c)
		} else {
			parsedWord = parsedWord + " " + string(c)
		}
		if i == len(word)-1 && c != '.' && c != '(' && c != '[' {
			parsedWord += wordEndIdentifier
		}
	}
	return parsedWord
}

func (b *Bigram) String() string {
	return b.Prev + " " + b.Next
}
