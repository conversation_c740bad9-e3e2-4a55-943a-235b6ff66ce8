package tokenizer

import (
	"cosy/global"
	"cosy/log"
	"fmt"
	"io/ioutil"
	"regexp"
	"sort"
	"strconv"
	"strings"
)

type Trainer struct {
	TrainerConfig
}

var (
	logRegex       = regexp.MustCompile("log\\.(error|info|debug|warn)[\\S ]+;")
	systemOutRegex = regexp.MustCompile("System\\.out[\\S ]+;")
)

type TrainerConfig struct {
	wordEndIdentifier    string   // Identifier for the end of a word
	whitespaceIdentifier string   // Identifier for whitespace
	reserveWords         []string // Reserved words
	minFrequency         int      // Minimal frequency required for a bigram to be added to the model
	outputFreq           bool     // Whether the trainer writes frequencies of bigrams
}

type wordFreq struct {
	word      string
	frequency int
}

type bpeChange struct {
	word    string
	newWord string
	freq    int
	idx     int
}

func NewTrainer(config TrainerConfig) *Trainer {
	return &Trainer{config}
}

// DefaultTrainerConfig returns a default  Trainer config
func DefaultTrainerConfig() *TrainerConfig {
	return &TrainerConfig{
		wordEndIdentifier:    "🔚",
		whitespaceIdentifier: "🀆",
		reserveWords:         []string{"$$VAR$$", "$$STR$$"},
		minFrequency:         2,
		outputFreq:           false,
	}
}

// Train does the training of bpe model for a set of texts
// The inputs are texts and upper limit of vocab
// Train returns bpe Bigram model and vocab string list if there is no error
func (t *Trainer) Train(texts []string, numSymbol int) (model []string, vocab []string, err error) {
	vocabCounter := map[string]int{}
	texts = t.Preprocess(texts)
	vocabulary := t.getVocabulary(texts)
	sortedVocab := t.getSortedVocab(vocabulary)
	stat, indices := t.getPairStat(sortedVocab)

	// Start merging
	for idx := 0; idx < numSymbol; idx++ {
		// Learn numSymbol new words/subwords
		if len(stat) <= 0 {
			break
		}

		// Sort bigrams by frequency and names
		var mostCommonBigram Bigram
		curMax := 0
		for k, v := range stat {
			if v > curMax {
				mostCommonBigram = k
				curMax = v
			}
			if v == curMax {
				if strings.Compare(k.String(), mostCommonBigram.String()) > 0 {
					mostCommonBigram = k
				}
			}
		}
		if stat[mostCommonBigram] < t.minFrequency {
			break
		}

		// Add to model
		vocabCounter[mostCommonBigram.Prev] += 1
		vocabCounter[mostCommonBigram.Next] += 1
		if t.outputFreq == true {
			model = append(model, fmt.Sprintf("%s %d", mostCommonBigram.String(), stat[mostCommonBigram]))
		} else {
			model = append(model, mostCommonBigram.String())
		}

		// Get changes
		changes := t.getChanges(mostCommonBigram, sortedVocab, indices)

		// Do replacement
		stat, indices = t.updatePairStatistics(mostCommonBigram, changes, stat, indices)
		stat[mostCommonBigram] = 0
	}

	// Apply model on inputs to get vocab
	resultVocab := map[string]int{}
	applyModel := GetModelFromStr(strings.Join(model, "\n"))
	reverseModel := map[string]Bigram{}
	for b := range applyModel {
		k := b.Prev + b.Next
		reverseModel[k] = b
	}
	for _, text := range texts {
		result, _ := Encode(text, applyModel, reverseModel, map[string]bool{}, *EmptyTokenizerConfig())
		for _, v := range result {
			resultVocab[v] += 1
		}
	}
	var sortedResultVocab []wordFreq
	for v, cnt := range resultVocab {
		sortedResultVocab = append(sortedResultVocab, wordFreq{
			word:      v,
			frequency: cnt,
		})
	}
	sort.Slice(sortedResultVocab, func(i, j int) bool {
		return sortedResultVocab[i].frequency > sortedResultVocab[j].frequency
	})

	for _, v := range t.reserveWords {
		vocabStr := v + " " + "999999"
		vocab = append(vocab, vocabStr)
	}

	for _, v := range sortedResultVocab {
		vocabStr := v.word + " " + strconv.Itoa(v.frequency)
		vocab = append(vocab, vocabStr)
	}

	return model, vocab, nil
}

func (t *Trainer) WriteModel(model []string, vocab []string, modelFilename string, vocabFilename string) {
	_ = ioutil.WriteFile(modelFilename, []byte(strings.Join(model, "\n")), 0666)
	_ = ioutil.WriteFile(vocabFilename, []byte(strings.Join(vocab, "\n")), 0666)
}

func (t *Trainer) Preprocess(texts []string) (result []string) {
	for _, t := range texts {
		// Remove code like log.error()
		t = logRegex.ReplaceAllLiteralString(t, "")
		// Remove code like System.out.xx
		t = systemOutRegex.ReplaceAllLiteralString(t, "")
		// Remove string literals
		t = global.StringLiteralRegex.ReplaceAllLiteralString(t, "$$STR$$")
		// Remove comments
		t = global.MultilineCommentRegex.ReplaceAllString(t, "")
		t = global.SingleLineCommentRegex.ReplaceAllString(t, "")
		result = append(result, t)
	}
	return result
}

func (t *Trainer) getVocabulary(texts []string) (vocab map[string]int) {
	splitRegex, err := regexp.Compile("\\s?[a-zA-Z0-9_@/]+| ?[^\\s\\w@_/$]+|\\s+")

	if err != nil {
		log.Error(err)
	}

	vocab = map[string]int{}
	for _, text := range texts {
		text = replaceBlankChars(text)
		tokens := splitRegex.FindAllString(text, -1)

		for _, token := range tokens {
			if token != "" && token != " " {
				token = strings.ReplaceAll(token, " ", t.whitespaceIdentifier)
				separatedToken := parseWord(token, t.wordEndIdentifier)
				vocab[separatedToken] = vocab[separatedToken] + 1
			}
		}
	}
	return vocab
}

func (t *Trainer) getPairStat(sortedVocab []wordFreq) (stats map[Bigram]int, indices map[Bigram]map[int]int) {
	stats = map[Bigram]int{}
	indices = map[Bigram]map[int]int{}
	// This vocab uses subwords (separated by whitespace) sequence as the key
	// For example, token "word" is actually "w o r d🂱" in the first run, pls see getVocabulary()
	for idx, wordWithCount := range sortedVocab {
		word := wordWithCount.word
		cnt := wordWithCount.frequency
		newWord := ""
		subwords := strings.Split(word, " ")
		for i, c := range subwords {
			if c == " " || c == "" {
				continue
			}
			if i == 0 {
				newWord = c
			}
			if i >= 1 {
				// Ensure that there exists prevChar
				prevChar := subwords[i-1]
				nextChar := c
				if i == len(word)-1 {
					// For last char, append wordEndIdentifier
					nextChar = nextChar + t.wordEndIdentifier
				}
				newWord = newWord + " " + nextChar
				b := *NewBigram(prevChar, nextChar)
				indices = t.updateIndices(indices, b, idx, 1)
				stats[b] = stats[b] + cnt
			}
		}
	}
	return stats, indices
}

func (t *Trainer) printIndices(indices map[Bigram]map[int]int) {
	for bigram, sub := range indices {
		log.Info(bigram.String())
		for k, v := range sub {
			log.Info("belongs to:", k, ", cnt:", v)
		}
		log.Info("--")
	}
	log.Info("len of indices:", len(indices))
}

func (t *Trainer) firstIndexOfSlice(slice []string, target string, start int) int {
	for i, str := range slice {
		if i >= start && str == target {
			return i
		}
	}
	return -1
}

func (t *Trainer) getSortedVocab(vocab map[string]int) []wordFreq {
	var sortedVocab []wordFreq
	for word, cnt := range vocab {
		sortedVocab = append(sortedVocab, wordFreq{
			word:      word,
			frequency: cnt,
		})
	}

	sort.Slice(sortedVocab, func(i, j int) bool {
		return sortedVocab[i].frequency > sortedVocab[j].frequency
	})

	return sortedVocab
}

func (t *Trainer) getChanges(mostCommonBigram Bigram, sortedVocab []wordFreq, indices map[Bigram]map[int]int) []bpeChange {
	var changes []bpeChange
	old := mostCommonBigram.String()
	replacer := mostCommonBigram.Prev + mostCommonBigram.Next
	for j, freq := range indices[mostCommonBigram] {
		if freq < 1 {
			continue
		}
		word := sortedVocab[j].word
		freq := sortedVocab[j].frequency
		newWord := strings.ReplaceAll(word, old, replacer)
		sortedVocab[j].word = newWord
		sortedVocab[j].frequency = freq
		changes = append(changes, bpeChange{
			word:    word,
			newWord: newWord,
			freq:    freq,
			idx:     j,
		})
	}
	return changes
}

func (t *Trainer) updatePairStatistics(mostCommonBigram Bigram, changes []bpeChange, stat map[Bigram]int, indices map[Bigram]map[int]int) (newStat map[Bigram]int, newIndices map[Bigram]map[int]int) {
	replacer := mostCommonBigram.Prev + mostCommonBigram.Next
	stat[mostCommonBigram] = 0
	indices[mostCommonBigram] = map[int]int{}
	for _, c := range changes {
		j := c.idx
		freq := c.freq
		oldSubwords := strings.Split(c.word, " ")
		subwords := strings.Split(c.newWord, " ")

		// Decrease counts
		i := 0
		for {
			i = t.firstIndexOfSlice(oldSubwords, mostCommonBigram.Prev, i)
			if i < 0 {
				break
			}
			if i < len(oldSubwords)-1 && oldSubwords[i+1] == mostCommonBigram.Next {
				// Reduce the frequency of previous bigrams.
				// For example, we have "A B C", if we are merging "B C", reduce the frequency of "A B" first
				if i > 0 {
					prev := *NewBigram(oldSubwords[i-1], oldSubwords[i])
					stat[prev] -= freq
					indices = t.updateIndices(indices, prev, j, -1)
				}
				if i < len(oldSubwords)-2 {
					// Assuming a symbol sequence "A B C B", if "B C" is merged, reduce the frequency of "C B".
					// However, skip this if the sequence is A B C B C, because the frequency of "C B" will be reduced by the later "B C"
					if oldSubwords[i+2] != mostCommonBigram.Prev || i >= len(oldSubwords)-3 || oldSubwords[i+3] != mostCommonBigram.Next {
						next := *NewBigram(oldSubwords[i+1], oldSubwords[i+2])
						stat[next] -= freq
						indices = t.updateIndices(indices, next, j, -1)
					}
				}
				i += 2
			} else {
				i += 1
			}
		}

		// Increase counts
		i = 0
		for {
			// Find new pair
			i = t.firstIndexOfSlice(subwords, replacer, i)
			if i < 0 {
				break
			}

			// Assuming a symbol sequence "A BC D", if "B C" is merged, increase the frequency of "A BC"
			if i > 0 {
				prev := *NewBigram(subwords[i-1], subwords[i])
				stat[prev] += freq
				indices = t.updateIndices(indices, prev, j, 1)
			}

			// Assuming a symbol sequence "A BC B", if "B C" is merged, increase the frequency of "BC B"
			// However, if the sequence is A BC BC, skip this step because the frequency of "BC BC" will be incremented by the previous code block
			if i < len(subwords)-1 && subwords[i+1] != replacer {
				next := *NewBigram(subwords[i], subwords[i+1])
				stat[next] += freq
				indices = t.updateIndices(indices, next, j, 1)
			}
			i += 1
		}
	}
	return stat, indices
}

func (t *Trainer) updateIndices(indices map[Bigram]map[int]int, b Bigram, idx int, increment int) map[Bigram]map[int]int {
	if indices[b] == nil {
		indices[b] = map[int]int{}
	}
	indices[b][idx] += increment
	return indices
}
