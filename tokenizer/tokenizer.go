package tokenizer

import "strings"

type DefaultTokenizer struct {
}

func NewDefaultTokenizer() *DefaultTokenizer {
	return &DefaultTokenizer{}
}

// Tokenize converts given code to a list of tokens
func (t *DefaultTokenizer) Tokenize(code string) ([]string, error) {
	return strings.Split(code, " "), nil
}

// <PERSON><PERSON><PERSON><PERSON> recovers the list of tokens to the text
// By default, the given tokens are separated by whitespaces
func (t *DefaultTokenizer) Untokenize(tokens string) (string, error) {
	return tokens, nil
}
