package tokenizer

import (
	"fmt"
	"testing"
)

func TestNewSimpleTokenReader(t *testing.T) {
	code := `print("Hello, World!")`
	reader := NewSimpleTokenReader(code)
	if reader.code != code {
		t.<PERSON><PERSON>("Expected code to match input code")
	}
}

func TestGetToken(t *testing.T) {
	code := `print("Hello, World!")`
	reader := NewSimpleTokenReader(code)
	reader.Next() // Initialize token reading
	token, err := reader.GetToken()
	if err != nil {
		t.Errorf("Did not expect an error, got %v", err)
	}
	if token != "print" {
		t.Errorf("Expected first token to be 'print', got %v", token)
	}
}

func TestGetToken_Chinese(t *testing.T) {
	expected := []string{"打印", "盖章", "初始", "程序", "print", "Hello", "World"}
	code := `
# 打印一个初始程序
print("Hello, World!")`
	tokens := make([]string, 0)
	reader := NewSimpleTokenReader(code)
	for reader.NextLine() {
		lineTokens := reader.GetLintTokens()
		tokens = append(tokens, lineTokens...)
	}
	fmt.Println(tokens)
	if len(tokens) != len(expected) {
		t.Errorf("Expected %v tokens, got %v", len(expected), len(tokens))
	} else {
		for i, token := range tokens {
			if token != expected[i] {
				t.Errorf("Expected token %v to be '%v', got '%v'", i, expected[i], token)
			}
		}
	}
}

func TestGetTokenError(t *testing.T) {
	code := ""
	reader := NewSimpleTokenReader(code)
	_, err := reader.GetToken()
	if err == nil {
		t.Errorf("Expected an error, got nil")
	}
}

func TestGetLine(t *testing.T) {
	code := "print(\"Hello, World!\")"
	reader := NewSimpleTokenReader(code)
	reader.Next()
	line := reader.GetLine()
	if line != 0 {
		t.Errorf("Expected line number to be 0, got %v", line)
	}
}

func TestNext(t *testing.T) {
	code := "print\n(\"Hello, World!\")"
	reader := NewSimpleTokenReader(code)
	if !reader.Next() {
		t.Errorf("Expected Next to return true, got false")
	}
	if reader.GetLine() != 0 {
		t.Errorf("Expected line number to be 1, got %v", reader.GetLine())
	}
	if reader.currentTokenIndex != 0 {
		t.Errorf("Expected currentTokenIndex to be 0, got %v", reader.currentTokenIndex)
	}
}

func TestNextAtEnd(t *testing.T) {
	code := "print(\"Hello, World!\")"
	reader := NewSimpleTokenReader(code)
	for reader.Next() {
		// Reading all tokens
	}
	if reader.Next() {
		t.Errorf("Expected Next to return false at the end, got true")
	}
}
