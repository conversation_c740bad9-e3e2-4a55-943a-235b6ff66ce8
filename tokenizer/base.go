package tokenizer

type Token struct {
	Term  string `json:"term"`
	Start int    `json:"start"`
	// End specifies the byte offset of the end of the term in the field.
	End      int `json:"end"`
	Position int `json:"position"`
}

// RichTokenizer interface
type RichTokenizer interface {
	// Tokenize converts given code to a list of tokens
	Tokenize(code string) ([]Token, error)
}

// Tokenizer interface
type Tokenizer interface {
	// Tokenize converts given code to a list of tokens
	Tokenize(code string) ([]string, error)

	// Untok<PERSON>ze recovers the list of tokens to the text
	// By default, the given tokens are separated by whitespaces
	Untokenize(tokens string) (string, error)
}
