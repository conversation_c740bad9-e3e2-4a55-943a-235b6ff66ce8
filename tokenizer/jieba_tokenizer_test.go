package tokenizer

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// 测试 editDistanceVerifier 函数
func TestJiebaTokenizer_Tokenize(t *testing.T) {
	testCases := []struct {
		name   string
		code   string
		expect []string
	}{
		{"TestCase 1", "SELECT * FROM table", []string{"SELECT", "*", "FROM", "table"}},

		{"TestCase 2: 中文场景", "从Date类型的时间中提取日期部分", []string{"从", "Date", "类型", "的", "时间", "中", "提取", "日期", "部分"}},

		{"TestCase 3: 更多字符", "SELECT * FROM table2 WHERE data > 0", []string{"SELECT", "*", "FROM", "table2", "WHERE", "data", ">", "0"}},

		{"TestCase 4: golang代码场景", `func isValidToken(token string) bool {
            clearToken := strings.Trim(token, " \t\r\n")
            return len(clearToken) > 0
        }`, []string{"func", "isValidToken", "(", "token", "string", ")", "bool", "{", "clearToken", ":", "=", "strings", ".", "Trim", "(", "token", ",", "\"", "\\", "t", "\\", "r", "\\", "n", "\"", ")", "return", "len", "(", "clearToken", ")", ">", "0", "}"}},

		{"TestCase 5: vue代码场景", `
    <template #dropdown>
      <!-- 这是一个标签前的注释 -->
      <el-dropdown-menu>
        <el-dropdown-item class="clearfix">
          comments
          <el-badge class="mark" :value="12" />
        </el-dropdown-item>
        <el-dropdown-item class="clearfix">
          replies
          <el-badge class="mark" :value="3" />
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>`, []string{"<", "template", "#", "dropdown", ">", "<", "!", "-", "-", "这", "是", "一个", "标签", "前", "的", "注释", "-", "-", ">", "<", "el", "-", "dropdown", "-", "menu", ">", "<", "el", "-", "dropdown", "-", "item", "class", "=", "\"", "clearfix", "\"", ">", "comments", "<", "el", "-", "badge", "class", "=", "\"", "mark", "\"", ":", "value", "=", "\"", "12", "\"", "/", ">", "<", "/", "el", "-", "dropdown", "-", "item", ">", "<", "el", "-", "dropdown", "-", "item", "class", "=", "\"", "clearfix", "\"", ">", "replies", "<", "el", "-", "badge", "class", "=", "\"", "mark", "\"", ":", "value", "=", "\"", "3", "\"", "/", ">", "<", "/", "el", "-", "dropdown", "-", "item", ">", "<", "/", "el", "-", "dropdown", "-", "menu", ">", "<", "/", "template", ">"}},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			jiebaTokenizer := NewJiebaTokenizer(false)

			actual, err := jiebaTokenizer.TokenizeToString(tc.code)
			assert.NoError(t, err)
			assert.Equal(t, tc.expect, actual)
		})
	}
}
