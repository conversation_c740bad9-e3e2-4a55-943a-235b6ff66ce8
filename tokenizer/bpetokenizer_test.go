package tokenizer

import (
	"fmt"
	"testing"
)

func TestTokenize(t *testing.T) {
	//data := readJavaFiles("/Users/<USER>/Downloads/EasPredictClient.java")
	//config := *DefaultTokenizerConfig("/Users/<USER>/Projects/rust/cosy-local/model/", "java_model.txt", "java_vocab.txt")
	//config.glossaries = append(config.glossaries, "[EOL]")
	////config.Wd = "/Users/<USER>/Alibaba/odps_clt_release_64/bin/"
	//config.whitespaceIdentifier = "Ġ"
	//config.wordEndIdentifier = "</w>"
	//config.subwordIdentifier = "@@"
	//
	//// Test Encode
	//tokenizer, _ := NewBpe(config)
	////fmt.Println(data[322])
	//s := time.Now()
	//result, err := tokenizer.Tokenize(data[0])
	//log.Info("tokenization costs: ", time.Since(s))
	//if err != nil {
	//    fmt.Println(err)
	//}
	//fmt.Println(result)
	//// Test Decode
	//fmt.Println("Decode result:")
	//fmt.Println(Decode(result))
}

func Test_processGlossaries(t *testing.T) {
	fmt.Println(processGlossaries("1934USABUSA", []string{"USA"}))
}
