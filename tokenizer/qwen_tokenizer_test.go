package tokenizer

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"os"
	"testing"
	"time"
)

func TestQwenTokenizer_Tokenize(t *testing.T) {
	// Initialize QwenTokenizer
	tokenizer, err := NewQwenTokenizer(true)
	assert.NoError(t, err)

	// Test input string
	input := "```json\n{\n    \"code_modify\": \"YES\",\n    \"refined_question\": \"Add gender attribute to pets\",\n    \"keywords\": [\"Pet\", \"gender\", \"add\", \"attribute\", \"field\"],\n    \"files_to_modify\": [\"src/main/java/org/springframework/samples/petclinic/model/Pet.java\", \"src/main/java/org/springframework/samples/petclinic/repository/jpa/JpaPetRepositoryImpl.java\", \"src/main/java/org/springframework/samples/petclinic/repository/springdatajpa/SpringDataPetRepository.java\", \"src/main/java/org/springframework/samples/petclinic/service/ClinicService.java\", \"src/main/java/org/springframework/samples/petclinic/web/PetController.java\", \"src/main/resources/db/*.sql\"]\n}\n```"

	// Expected output
	//expected := []string{"This", "is", "a", "test", "sentence."}

	// Tokenize the input string
	tokens, err := tokenizer.Tokenize(input)
	assert.NoError(t, err)
	fmt.Println(tokens)
}

func TestQwenTokenizer_Tokenize_Performance(t *testing.T) {
	// Initialize QwenTokenizer
	tokenizer, err := NewQwenTokenizer(false)
	assert.NoError(t, err)

	filePath := "/Users/<USER>/Documents/codes/projects/codeup-core/force-base/src/main/java/com/alibaba/force/base/codereview/impl/GitDiffChangesetParserImpl.java"
	codeBytes, err := os.ReadFile(filePath)
	assert.NoError(t, err)
	for i := 0; i < 10; i++ {
		startTime := time.Now()
		// Tokenize the input string
		tokens, _ := tokenizer.Tokenize(string(codeBytes))
		fmt.Println("time cost: ", time.Since(startTime), " len:", len(tokens))

	}
	assert.NoError(t, err)
}
