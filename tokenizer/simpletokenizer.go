package tokenizer

import (
	"regexp"
)

// Regex
var (
	simpleSplitRegex = regexp.MustCompile("\\$[a-zA-Z0-9_\\[\\].]*\\$|\\s+|[a-zA-Z0-9_@$]+|[^\\s\\w@_$]+|/")
	// merge start bracket with chars
	simpleMergeSplitRegex = regexp.MustCompile("\\$[a-zA-Z0-9_\\[\\].]*\\$|\\s+|[a-zA-Z0-9_@$]+\\(?|[^\\s\\w@_$]+|/")

	wordsSplitRegex = regexp.MustCompile("[a-zA-Z0-9]+")
)

type SimpleTokenizer struct {
	//whether merge start bracket and chars
	mergeSymbol bool
	// 去除符号，只保留英文及字母
	onlyWords bool
}

// NewSimpleTokenizer returns a SimpleTokenizer
func NewSimpleTokenizer() *SimpleTokenizer {
	return &SimpleTokenizer{
		mergeSymbol: false,
	}
}

// NewSimpleMergeTokenizer returns a SimpleTokenizer
func NewSimpleMergeTokenizer() *SimpleTokenizer {
	return &SimpleTokenizer{
		mergeSymbol: true,
	}
}

// NewSimpleWordsTokenizer returns a SimpleTokenizer
func NewSimpleWordsTokenizer() *SimpleTokenizer {
	return &SimpleTokenizer{
		onlyWords: true,
	}
}

// Tokenize converts given code to a list of tokens
func (t *SimpleTokenizer) Tokenize(code string) (result []string, err error) {
	if t.onlyWords {
		return wordsSplitRegex.FindAllString(code, -1), nil
	}
	if t.mergeSymbol {
		return simpleMergeSplitRegex.FindAllString(code, -1), nil
	} else {
		return simpleSplitRegex.FindAllString(code, -1), nil
	}
}

// Untokenize recovers the list of tokens to the text
// By default, the given tokens are separated by whitespaces
func (t *SimpleTokenizer) Untokenize(tokens string) (string, error) {
	return tokens, nil
}
