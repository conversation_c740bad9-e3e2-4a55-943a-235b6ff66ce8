#!/usr/bin/env zsh

#===============================================================================
# Qoder Environment Switcher
#===============================================================================
#
# 描述:
#   这是一个用于切换 Qoder IDE 环境配置的脚本。
#   支持切换到不同的地区和环境（开发、测试、生产环境）。
#
# 功能特性:
#   - 支持新加坡、美东、首尔三个地区的环境切换
#   - 每个地区支持不同的环境类型（daily/test/prod）
#   - 自动清理缓存和用户数据
#   - 自动终止已运行的 Qoder 进程
#   - 检查当前环境配置
#
# 使用方法:
#   ./qoder-switch-env.sh [ENVIRONMENT|check]
#
# 可用环境:
#   新加坡地区:
#     singapore-daily  - 新加坡日常开发环境
#     singapore-test   - 新加坡测试环境
#     singapore-prod   - 新加坡生产环境
#
#   美东地区:
#     us-east-test     - 美东测试环境
#     us-east-prod     - 美东生产环境
#
#   首尔地区:
#     seoul-test       - 首尔测试环境
#     seoul-prod       - 首尔生产环境
#
#   其他命令:
#     init            - 初始化默认配置
#     check           - 检查当前环境配置
#     (无参数)        - 显示帮助信息
#
# 使用示例:
#   # 初始化默认配置
#   ./qoder-switch-env.sh init
#
#   # 检查当前环境
#   ./qoder-switch-env.sh check
#
#   # 切换到新加坡测试环境
#   ./qoder-switch-env.sh singapore-test
#
#   # 切换到美东生产环境
#   ./qoder-switch-env.sh us-east-prod
#
#   # 显示帮助信息
#   ./qoder-switch-env.sh
#
# 注意事项:
#   1. 运行此脚本仅支持mac
#   2. 切换环境时会自动清理用户缓存数据
#   3. 切换环境时会自动终止正在运行的 Qoder 进程
#   4. 配置文件位置: /Applications/Qoder.app/Contents/Resources/app/resources/bin/env.json
#   5. 缓存数据位置: ~/Library/Application Support/Qoder/SharedClientCache
#   6. cosy IDE开发调试还需要配合环境变量
#      - "QODER_PROCESS_HOME": "/Applications/Qoder.app/Contents/Resources/app/resources"
#      - "QODER_HOME": "$HOME/Library/Application Support/Qoder/SharedClientCache"
#
# 作者: Qoder Team
# 版本: 1.0
# 更新时间: 2025
#===============================================================================

version="2025-08-11"

project_path=$(cd "$(dirname "${0}")"; pwd)
dataDir="$HOME/Library/Application Support/Qoder/SharedClientCache"
binDir="/Applications/Qoder.app/Contents/Resources/app/resources/bin"
env_config_file="${binDir}/env.json"
cache_config_file="${dataDir}/cache/config.json"

###########################################################

# 新加坡环境端点配置
singapore_daily_endpoint="https://daily-api2.qoder.sh"
singapore_test_endpoint="https://test-api2.qoder.sh"
singapore_prod_endpoint="https://api2.qoder.sh"

# 美东环境端点配置
us_east_test_endpoint="https://test-api1.qoder.sh"
us_east_prod_endpoint="https://api1.qoder.sh"

# 首尔环境端点配置
seoul_test_endpoint="https://test-api3.qoder.sh"
seoul_prod_endpoint="https://api3.qoder.sh"

# Remote Agent 端点配置
singapore_daily_remote_agent="https://daily-api2.qoder.sh"
singapore_test_remote_agent="https://test-qts.qoder.sh"
singapore_prod_remote_agent="https://qts2.qoder.sh"

us_east_test_remote_agent="https://test-qts.qoder.sh"
us_east_prod_remote_agent="https://qts1.qoder.sh"

seoul_test_remote_agent="https://test-qts.qoder.sh"
seoul_prod_remote_agent="https://qts3.qoder.sh"

###########################################################

# 公共端点
daily_base_endpoint="https://daily.qoder.ai"
test_base_endpoint="https://www.qoder.ai"
prod_base_endpoint="https://www.qoder.com"


###########################################################

# 第一步：定义模板
template=$(cat << 'EOF'
{
    "on_premise": "false",
    "remote_config": {
        "login_url": "${base_endpoint}/device/selectAccounts",
        "auth_login_url": "${base_endpoint}/users/sign_in",
        "auth_logout_url": "${base_endpoint}/users/logout",
        "big_model_endpoint": "${endpoint}/algo",
        "force_ai_endpoint": "",
        "server_proxy": "",
        "oss_model_root": "${base_endpoint}/algo/api/v1/model/download",
        "oss_model_env_root": "${base_endpoint}/algo/api/v1/model/env/download",
        "oss_plugin_root": "${base_endpoint}/algo/api/v1/ide/plugin/update"
    },
    "url_config": {
        "faq": "https://help.aliyun.com/zh/lingma/support/troubleshooting-guide",
        "help": "https://help.aliyun.com/document_detail/2590613.html",
        "homepage": "https://tongyi.aliyun.com/lingma",
        "feedback": "https://developer.aliyun.com/ask/new?excode=lingma&exdcode=coding",
        "join": "https://tongyi.aliyun.com/lingma",
        "survey_feedback": "https://survey.aliyun.com/apps/zhiliao/gLgsYL8mB",
        "privacy": "https://help.aliyun.com/document_detail/2590617.html",
        "network_error": "https://help.aliyun.com/document_detail/2671485.html",
        "access_key": "https://help.aliyun.com/zh/ram/user-guide/create-an-accesskey-pair",
        "access_token": "https://account-devops.aliyun.com/settings/personalAccessToken",
        "kb_help_url": "https://help.aliyun.com/document_detail/2796751.html",
        "url_developer_config": "https://help.aliyun.com/zh/lingma/user-guide/ai-developer-guidlines",
        "ai_rules_url": "https://help.aliyun.com/zh/lingma/user-guide/ai-rules"
    },
    "region_config": {
        "preferredCenterNode": {
            "endpoint": "${endpoint}",
            "latency": 86
        },
        "preferredDataNodeMap": {
            "codebase": {
                "endpoint": "${endpoint}",
                "latency": 0
            },
            "remote_agent": {
                "endpoint": "${remote_agent_endpoint}",
                "latency": 0
            }
        },
        "preferredInferenceNode": {
            "endpoint": "${endpoint}",
            "latency": 84
        }
    }
}
EOF
)

init_env_config=$(cat << 'EOF'
{
  "on_premise": "false",
  "url_config": {
    "faq": "https://docs.qoder.com/",
    "help": "https://docs.qoder.com/",
    "homepage": "https://qoder.com/",
    "privacy": "https://qoder.com/privacy-policy",
    "network_error": "https://docs.qoder.com/user-guide/configure-network-proxy",
    "join": "https://docs.qoder.com/",
    "terminal_doc": "https://docs.qoder.com/troubleshooting/terminal-execution-exceptions"
  }
}
EOF
)


function check_env() {
  endpoint=$(cat "${env_config_file}" | grep '"big_model_endpoint"' | grep -o 'http[s]*://[^"]*')
  
  echo "endpoint: ${endpoint}"

  if [ "${endpoint}" = "${singapore_daily_endpoint}/algo" ]; then
    echo "Using environment: \"singapore-daily\""
  elif [ "${endpoint}" = "${singapore_test_endpoint}/algo" ]; then
    echo "Using environment: \"singapore-test\""
  elif [ "${endpoint}" = "${singapore_prod_endpoint}/algo" ]; then
    echo "Using environment: \"singapore-prod\""
  elif [ "${endpoint}" = "${us_east_test_endpoint}/algo" ]; then
    echo "Using environment: \"us-east-test\""
  elif [ "${endpoint}" = "${us_east_prod_endpoint}/algo" ]; then
    echo "Using environment: \"us-east-prod\""
  elif [ "${endpoint}" = "${seoul_test_endpoint}/algo" ]; then
    echo "Using environment: \"seoul-test\""
  elif [ "${endpoint}" = "${seoul_prod_endpoint}/algo" ]; then
    echo "Using environment: \"seoul-prod\""
  elif [ "${endpoint}" = "" ]; then
    echo "endpoint not specific, Using default environment: \"prod\""
  else
    echo "Using environment: \"unknown\" (${endpoint}), "
  fi
}

function clear_env_config() {
  # 清空env.json文件
  if cat <<EOF >"${env_config_file}" 2>/dev/null; then
{
}
EOF
    echo "Successfully cleared env.json configuration"
  else
    echo "Warning: Failed to clear env.json configuration - permission denied"
    echo "File location: ${env_config_file}"
    echo "You may need to run this script with appropriate permissions or manually clear the file"
  fi
}

function clear_cache_user() {
  rm -rf "${dataDir}/cache/user"
  rm -rf "${dataDir}/cache/quota"
}

function clear_cache_region_config() {
  # 检查cache_config_file是否存在
  if [ -f "${cache_config_file}" ]; then
    # 使用jq工具清空region_config节点，如果jq不存在则使用备用方案
    if command -v jq >/dev/null 2>&1; then
      # 使用jq清空region_config节点，忽略stderr的错误但检查输出
      temp_file=$(mktemp)
      if jq '.region_config = {}' "${cache_config_file}" > "$temp_file" 2>/dev/null; then
        mv "$temp_file" "${cache_config_file}"
        echo "Cleared region_config from cache configuration"
      else
        # jq失败，尝试使用备用方案
        echo "jq failed, trying sed backup method..."
        # 先清理文件中可能的非JSON内容，然后替换region_config
        sed '/^```/d; /cache_config_file/d; /的内容如上所示/d; /在执行脚本切换环境时/d; /需要把.*region_config节点的内容清空/d; /\"region_config\"[[:space:]]*:[[:space:]]*{/,/^[[:space:]]*}[[:space:]]*$/c\
  "region_config": {}' "${cache_config_file}" > "$temp_file"
        mv "$temp_file" "${cache_config_file}"
        echo "Cleared region_config from cache configuration using sed"
      fi
    else
      # 备用方案：使用sed替换region_config节点内容
      temp_file=$(mktemp)
      # 先清理文件中可能的非JSON内容，然后替换region_config
      sed '/^```/d; /cache_config_file/d; /的内容如上所示/d; /在执行脚本切换环境时/d; /需要把.*region_config节点的内容清空/d; /\"region_config\"[[:space:]]*:[[:space:]]*{/,/^[[:space:]]*}[[:space:]]*$/c\
  "region_config": {}' "${cache_config_file}" > "$temp_file"
      mv "$temp_file" "${cache_config_file}"
      echo "Cleared region_config from cache configuration using sed"
    fi
  else
    echo "Cache config file not found: ${cache_config_file}"
  fi
}

function kill_qoder() {
  # 获取所有包含 "Qoder start" 的进程信息（排除 grep 本身）
  output=$(ps aux | grep "Qoder" | grep "start" | grep -v "grep" | grep -v "/Library/Developer")

  # 提取所有 PID（第二列），并存储到数组中
  pids=($(echo "$output" | tr -s ' ' | cut -d' ' -f2))

  # 检查是否找到任何进程
  if [ ${#pids[@]} -eq 0 ]; then
      echo "No processes found with 'Qoder start'"
      return
  fi

  # 遍历所有 PID 并终止进程
  for pid in "${pids[@]}"; do
      # 检查进程是否存在
      if kill -0 "$pid" > /dev/null 2>&1; then
          kill -9 "$pid"
          echo "Killed process with PID: $pid"
      else
          echo "Process $pid does not exist, skipping..."
      fi
  done
}

# 传入三个参数，一个是base_endpoint，一个是endpoint，一个是remote_agent_endpoint
# 三个参数分别将template中的对应变量进行替换，最终写入env_config_file文件中
function switch_env() {
  local base_endpoint="$1"
  local endpoint="$2"
  local remote_agent_endpoint="$3"
  
  # 检查参数是否提供
  if [ -z "$base_endpoint" ] || [ -z "$endpoint" ] || [ -z "$remote_agent_endpoint" ]; then
    echo "Error: base_endpoint, endpoint, and remote_agent_endpoint parameters are required"
    return 1
  fi
  
  # 先清空配置文件
  clear_env_config
  clear_cache_user
  clear_cache_region_config

  # 使用envsubst替换模板中的变量生成配置内容
  export base_endpoint endpoint remote_agent_endpoint
  local generated_config=$(echo "$template" | envsubst '$base_endpoint,$endpoint,$remote_agent_endpoint')
  
  # 尝试写入配置文件，如果失败则保存到内存变量
  local write_success=false
  if echo "$generated_config" > "${env_config_file}" 2>/dev/null; then
    write_success=true
    echo "Successfully updated env.json configuration"
  else
    echo "Warning: Failed to write env.json configuration - permission denied"
    echo "File location: ${env_config_file}"
    echo "You may need to run this script with appropriate permissions or manually update the file"
  fi
  
  kill_qoder

  # 打印生成的env.json配置内容
  echo ""
  echo "==================== Generated env.json Configuration ===================="
  if [ "$write_success" = true ] && [ -f "${env_config_file}" ]; then
    # 文件写入成功，从文件读取内容
    if command -v jq >/dev/null 2>&1; then
      # 使用jq格式化输出JSON
      jq '.' "${env_config_file}"
    else
      # 如果没有jq，直接输出文件内容
      cat "${env_config_file}"
    fi
  else
    # 文件写入失败或文件不存在，使用内存中的配置内容
    if command -v jq >/dev/null 2>&1; then
      # 使用jq格式化输出JSON
      echo "$generated_config" | jq '.'
    else
      # 如果没有jq，直接输出配置内容
      echo "$generated_config"
    fi
  fi
  echo "==========================================================================="
  echo ""

  # 清理环境变量
  unset base_endpoint endpoint remote_agent_endpoint
}

function switch_to_singapore_daily() {
  switch_env "${daily_base_endpoint}" "${singapore_daily_endpoint}" "${singapore_daily_remote_agent}"
  echo "Switched to environment \"singapore-daily\""
  echo "Endpoint: ${singapore_daily_endpoint}"
  echo "Remote Agent: ${singapore_daily_remote_agent}"
}

function switch_to_singapore_test() {
  switch_env "${test_base_endpoint}" "${singapore_test_endpoint}" "${singapore_test_remote_agent}"
  echo "Switched to environment \"singapore-test\""
  echo "Endpoint: ${singapore_test_endpoint}"
  echo "Remote Agent: ${singapore_test_remote_agent}"
}

function switch_to_singapore_prod() {
  switch_env "${prod_base_endpoint}" "${singapore_prod_endpoint}" "${singapore_prod_remote_agent}"
  echo "Switched to environment \"singapore-prod\""
  echo "Endpoint: ${singapore_prod_endpoint}"
  echo "Remote Agent: ${singapore_prod_remote_agent}"
}

function switch_to_us_east_test() {
  switch_env "${test_base_endpoint}" "${us_east_test_endpoint}" "${us_east_test_remote_agent}"
  echo "Switched to environment \"us-east-test\""
  echo "Endpoint: ${us_east_test_endpoint}"
  echo "Remote Agent: ${us_east_test_remote_agent}"
}

function switch_to_us_east_prod() {
  switch_env "${prod_base_endpoint}" "${us_east_prod_endpoint}" "${us_east_prod_remote_agent}"
  echo "Switched to environment \"us-east-prod\""
  echo "Endpoint: ${us_east_prod_endpoint}"
  echo "Remote Agent: ${us_east_prod_remote_agent}"
}

function switch_to_seoul_test() {
  switch_env "${test_base_endpoint}" "${seoul_test_endpoint}" "${seoul_test_remote_agent}"
  echo "Switched to environment \"seoul-test\""
  echo "Endpoint: ${seoul_test_endpoint}"
  echo "Remote Agent: ${seoul_test_remote_agent}"
}

function switch_to_seoul_prod() {
  switch_env "${prod_base_endpoint}" "${seoul_prod_endpoint}" "${seoul_prod_remote_agent}"
  echo "Switched to environment \"seoul-prod\""
  echo "Endpoint: ${seoul_prod_endpoint}"
  echo "Remote Agent: ${seoul_prod_remote_agent}"
}

function switch_to_init() {
  # 先清空配置文件并清理缓存
  clear_env_config
  clear_cache_user
  clear_cache_region_config

  # 尝试写入初始化配置文件
  local write_success=false
  if echo "$init_env_config" > "${env_config_file}" 2>/dev/null; then
    write_success=true
    echo "Successfully updated env.json with initialization configuration"
  else
    echo "Warning: Failed to write env.json configuration - permission denied"
    echo "File location: ${env_config_file}"
    echo "You may need to run this script with appropriate permissions or manually update the file"
  fi

  kill_qoder

  # 打印生成的env.json配置内容
  echo ""
  echo "==================== Generated env.json Configuration ===================="
  if [ "$write_success" = true ] && [ -f "${env_config_file}" ]; then
    # 文件写入成功，从文件读取内容
    if command -v jq >/dev/null 2>&1; then
      # 使用jq格式化输出JSON
      jq '.' "${env_config_file}"
    else
      # 如果没有jq，直接输出文件内容
      cat "${env_config_file}"
    fi
  else
    # 文件写入失败或文件不存在，使用内存中的配置内容
    if command -v jq >/dev/null 2>&1; then
      # 使用jq格式化输出JSON
      echo "$init_env_config" | jq '.'
    else
      # 如果没有jq，直接输出配置内容
      echo "$init_env_config"
    fi
  fi
  echo "==========================================================================="
  echo ""

  echo "Switched to environment \"init\""
  echo "Using default initialization configuration"
}

function show_usage() {
  printf "Usage: %s [ENVIRONMENT|check]\n" "${0}"
  printf "\nAvailable environments:\n"
  printf "\nSingapore:\n"
  printf "  singapore-daily  - Singapore Daily Environment (%s)\n" "${singapore_daily_endpoint}"
  printf "  singapore-test   - Singapore Test Environment (%s)\n" "${singapore_test_endpoint}"
  printf "  singapore-prod   - Singapore Production Environment (%s)\n" "${singapore_prod_endpoint}"
  printf "\nUS East:\n"
  printf "  us-east-test     - US East Test Environment (%s)\n" "${us_east_test_endpoint}"
  printf "  us-east-prod     - US East Production Environment (%s)\n" "${us_east_prod_endpoint}"
  printf "\nSeoul:\n"
  printf "  seoul-test       - Seoul Test Environment (%s)\n" "${seoul_test_endpoint}"
  printf "  seoul-prod       - Seoul Production Environment (%s)\n" "${seoul_prod_endpoint}"
  printf "\nOther:\n"
  printf "  init            - Initialize with default configuration\n"
  printf "  check           - Check current environment\n"
}

# 主逻辑
if [ "${1}" = "" ] || [ "${1}" = "check" ]; then
  check_env
  echo "version: $version"
  show_usage
elif [ "${1}" = "singapore-daily" ]; then
  switch_to_singapore_daily
elif [ "${1}" = "singapore-test" ]; then
  switch_to_singapore_test
elif [ "${1}" = "singapore-prod" ]; then
  switch_to_singapore_prod
elif [ "${1}" = "us-east-test" ]; then
  switch_to_us_east_test
elif [ "${1}" = "us-east-prod" ]; then
  switch_to_us_east_prod
elif [ "${1}" = "seoul-test" ]; then
  switch_to_seoul_test
elif [ "${1}" = "seoul-prod" ]; then
  switch_to_seoul_prod
elif [ "${1}" = "init" ]; then
  switch_to_init
else
  show_usage
fi