package main

// 这是一个测试索引的二进制文件
// 用于压力测试在模型各个条件下（限流、挂掉、ping不通）的情况下，是否能够如期完成索引
import (
	"cosy/client"
	"cosy/components"
	"cosy/config"
	"cosy/definition"
	"cosy/global"
	"cosy/indexing"
	"cosy/indexing/index_setting"
	"cosy/log"
	"cosy/regionha"
	"cosy/storage/factory"
	"cosy/tree"
	"cosy/user"
	"cosy/util"
	"encoding/json"
	"fmt"
	"os"
	osUser "os/user"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/BurntSushi/toml"
)

const (
	EnvAmerica   = "america"
	EnvKorea     = "korea"
	EnvSingapore = "singapore"
)

// ExpandTilde 将路径中的 ~ 替换为用户的主目录，并转换为绝对路径
func ExpandTilde(path string) (string, error) {
	if !strings.HasPrefix(path, "~") {
		// 如果路径中没有 ~，直接返回绝对路径
		return filepath.Abs(path)
	}

	// 获取用户的主目录
	var homeDir string
	if homeDir = os.Getenv("HOME"); homeDir == "" {
		usr, err := osUser.Current()
		if err != nil {
			return "", fmt.Errorf("无法获取用户主目录: %w", err)
		}
		homeDir = usr.HomeDir
	}

	// 替换 ~ 为用户的主目录
	expandedPath := strings.Replace(path, "~", homeDir, 1)
	absPath, err := filepath.Abs(expandedPath)
	if err != nil {
		return "", fmt.Errorf("无法转换为绝对路径: %w", err)
	}
	return absPath, nil
}

// Config 配置文件结构
type TestConfig struct {
	BaseRepoDir  string   `toml:"base_repo_dir"`
	RepoNameList []string `toml:"repo_name_list"`
	Env          string   `toml:"env"`
	QoderHome    string   `toml:"qoder_home"`
	MachineId    string   `toml:"machine_id"`
}

type CodebaseInfo struct {
	WorkspacePath   string `json:"workspace_path"`
	CodebaseId      string `json:"codebase_id"`
	FileNumber      int    `json:"file_number"`
	DirNumber       int    `json:"dir_number"`
	TotalNodeNumber int    `json:"total_node_number"`
	CostTimeSecond  int    `json:"cost_time_second"`
	ErrorMsg        string `json:"error_msg"`
}

type OutputData struct {
	CodebaseInfos []CodebaseInfo `json:"codebase_infos"`
}

// loadTOMLConfig 加载TOML格式的配置文件
func loadTOMLConfig(configFile string) (*TestConfig, error) {
	var testConfig TestConfig
	if _, err := toml.DecodeFile(configFile, &testConfig); err != nil {
		return nil, fmt.Errorf("failed to parse TOML config: %w", err)
	}
	var err error
	// 将所有涉及目录的部分都转换为绝对路径
	testConfig.BaseRepoDir, err = ExpandTilde(testConfig.BaseRepoDir)
	if err != nil {
		return nil, fmt.Errorf("failed to expand base repo dir: %w", err)
	}
	testConfig.QoderHome, err = ExpandTilde(testConfig.QoderHome)
	if err != nil {
		return nil, fmt.Errorf("failed to expand qoder home: %w", err)
	}
	return &testConfig, nil
}

func main() {
	// 检查命令行参数
	// 输入参数为配置文件路径
	// 如果输入参数为空，则使用默认配置文件
	var configFile string
	if len(os.Args) < 2 {
		configFile = "config.toml"
	} else {
		configFile = os.Args[1]
	}

	// 检查配置文件是否存在
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		panic(fmt.Sprintf("Config file not found: %s", configFile))
	}

	// 读取配置文件
	testConfig, err := loadTOMLConfig(configFile)
	if err != nil {
		panic(fmt.Sprintf("Failed to load config: %v", err))
	}

	if testConfig.Env != EnvAmerica && testConfig.Env != EnvKorea && testConfig.Env != EnvSingapore {
		panic(fmt.Sprintf("Invalid env: %s", testConfig.Env))
	}

	//COSY_DEV_VERSION=2.1.4.202503141346;COSY_ENABLE_POST_PROCESS_DEBUG=true;COSY_LOCAL_DEV=true
	// 设置环境变量
	os.Setenv("QODER_PROCESS_HOME", testConfig.QoderHome)
	os.Setenv("QODER_HOME", testConfig.QoderHome)
	os.Setenv("MOCK_BIG_REPO_AUTO_INDEX", "true")
	os.Setenv("COSY_DEV_VERSION", "2.1.4.202503141346")
	os.Setenv("COSY_ENABLE_POST_PROCESS_DEBUG", "true")
	os.Setenv("COSY_LOCAL_DEV", "true")

	db, err := factory.NewKvStore(os.TempDir(), factory.BBlotStore, true)
	if err != nil || db == nil {
		panic(fmt.Sprintf("Failed to initialize local storage, error: %v", err))
	}

	os.MkdirAll(testConfig.QoderHome, 0755)
	// 按照年-月-日-时-分-秒
	nowTime := time.Now().Format("2006-01-02-15-04-05")
	logFile := filepath.Join(testConfig.QoderHome, fmt.Sprintf("codebase_test_%s.log", nowTime))
	global.DebugMode = true
	log.UseTestFileLogger(testConfig.QoderHome, logFile)

	// 将QODER_HOME下的登录信息复制到对应正确的文件中
	// 复制quota和user文件
	originalUserFileName := strings.Join([]string{testConfig.Env, definition.UserFile}, "-")
	originalQuotaFileName := strings.Join([]string{testConfig.Env, definition.QuotaFile}, "-")
	originalEnvFileName := strings.Join([]string{testConfig.Env, "env.json"}, "-")
	originalUserFilePath := filepath.Join(testConfig.QoderHome, "data", originalUserFileName)
	originalQuotaFilePath := filepath.Join(testConfig.QoderHome, "data", originalQuotaFileName)
	originalEnvFilePath := filepath.Join(testConfig.QoderHome, "data", originalEnvFileName)

	targetCacheDir := filepath.Join(testConfig.QoderHome, "cache")
	targetBinDir := filepath.Join(testConfig.QoderHome, "bin")
	os.MkdirAll(targetCacheDir, 0755)
	os.MkdirAll(targetBinDir, 0755)

	targetUserFilePath := filepath.Join(targetCacheDir, definition.UserFile)
	targetQuotaFilePath := filepath.Join(targetCacheDir, definition.QuotaFile)
	targetEnvFilePath := filepath.Join(targetBinDir, "env.json")
	quotaFile, err := os.ReadFile(originalQuotaFilePath)
	if err != nil {
		panic(fmt.Sprintf("Failed to read quota file: %v", err))
	}
	userFile, err := os.ReadFile(originalUserFilePath)
	if err != nil {
		panic(fmt.Sprintf("Failed to read user file: %v", err))
	}
	envFile, err := os.ReadFile(originalEnvFilePath)
	if err != nil {
		panic(fmt.Sprintf("Failed to read env file: %v", err))
	}
	os.WriteFile(targetQuotaFilePath, quotaFile, 0644)
	os.WriteFile(targetUserFilePath, userFile, 0644)
	os.WriteFile(targetEnvFilePath, envFile, 0644)

	util.FixupMachineIdForTest(testConfig.MachineId)
	if util.GetMachineId(true) != testConfig.MachineId {
		panic(fmt.Sprintf("Now machineId is not match: %v, expect: %v", util.GetMachineId(true), testConfig.MachineId))
	}

	// cosy相关初始化
	config.InitLocalConfig()
	config.InitConfig()
	client.InitClients()
	global.InitCodebaseConfig()
	index_setting.InitTestCodebaseSettings()
	regionha.CheckUpdateUserDataRegion()
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		panic(fmt.Sprintf("userInfo is nil"))
	}
	// 判定workspace是否为合法的workspace
	_, err = os.Stat(testConfig.BaseRepoDir)
	if os.IsNotExist(err) {
		panic(fmt.Sprintf("workspace dir is not exist: %v", err))
	}
	// 获取根目录下的文件和目录列表
	entries, err := os.ReadDir(testConfig.BaseRepoDir)
	if err != nil {
		panic(fmt.Sprintf("read workspace path err: %v", err))
	}

	if global.IsQoderProduct() {
		log.Infof("~~~~~~~~~~~~ Qoder Product ~~~~~~~~~~~~")
	} else {
		panic("Cosy Product is not supported")
	}

	allWorkspacePaths := []string{}
	// 遍历所有条目，检查哪些是目录
	for _, entry := range entries {
		if entry.IsDir() {
			// 使用正则匹配name是否在testConfig.RepoList中
			for _, repo := range testConfig.RepoNameList {
				if matched, _ := regexp.MatchString(repo, entry.Name()); matched {
					allWorkspacePaths = append(allWorkspacePaths, filepath.Join(testConfig.BaseRepoDir, entry.Name()))
					break
				}
			}
		}
	}
	for _, workspacePath := range allWorkspacePaths {
		log.Infof("~~~~~~~~~~~~ workspacePath will be indexed: %v ~~~~~~~~~~~~", workspacePath)
	}
	log.Infof("workspaceCount: %v, all workspace path: %v", len(allWorkspacePaths), allWorkspacePaths)

	// 使用多线程并发处理工作空间索引
	wg := sync.WaitGroup{}
	workspaceCount := len(allWorkspacePaths)
	startTime := time.Now()
	for i := 0; i < workspaceCount; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			workspacePath := allWorkspacePaths[index]
			// 取出仓库名
			log.Infof("~~~~~~~~~~~~ WorkspacePath %v: Start to index task ~~~~~~~~~~~~", workspacePath)
			workspaceInfo := definition.NewWorkspaceInfo(workspacePath)
			indexer := indexing.NewProjectFileIndex(db, workspaceInfo)
			param := indexing.NewChatVectorIndexParam()
			indexer.IndexWorkspace(param, true)
			index_setting.SetVectorProgressForTest(workspacePath, index_setting.IndexProgressRunning)
			for {
				setting := index_setting.GetCodebaseIndexSetting(workspacePath)
				log.Infof("~~~~~~~~~~~~ WorkspacePath %v: Indexing progress: %v ~~~~~~~~~~~~", workspacePath, setting.Progress)
				if setting.VectorProgress == index_setting.IndexProgressFinish {
					log.Infof("~~~~~~~~~~~~ WorkspacePath %v: Indexing progress Finish ~~~~~~~~~~~~", workspacePath)
					break
				}
				time.Sleep(time.Duration(10) * time.Second)
			}
			log.Infof("~~~~~~~~~~~~ WorkspacePath %v: Build index task success ~~~~~~~~~~~~", workspacePath)
		}(i)
	}

	// 等待所有线程完成
	wg.Wait()

	// 将codebase遍历并写入到out_put_file中
	outputData := OutputData{
		CodebaseInfos: []CodebaseInfo{},
	}
	for _, workspacePath := range allWorkspacePaths {
		handle, err := components.NewServerHandle(workspacePath)
		if err != nil {
			log.Errorf("NewServerHandle failed: %v", err)
			outputData.CodebaseInfos = append(outputData.CodebaseInfos, CodebaseInfo{
				WorkspacePath: workspacePath,
				ErrorMsg:      fmt.Sprintf("NewServerHandle failed: %v", err),
			})
			continue
		}
		codebaseId, err := handle.GetCodebaseId()
		if err != nil {
			log.Errorf("GetCodebaseId failed: %v", err)
			outputData.CodebaseInfos = append(outputData.CodebaseInfos, CodebaseInfo{
				WorkspacePath: workspacePath,
				ErrorMsg:      fmt.Sprintf("GetCodebaseId failed: %v", err),
			})
			continue
		}
		clientTree := tree.NewWorkspaceMerkleTree(workspacePath)
		outputData.CodebaseInfos = append(outputData.CodebaseInfos, CodebaseInfo{
			WorkspacePath:   workspacePath,
			CodebaseId:      codebaseId,
			FileNumber:      clientTree.GetLeafNodeCount(),
			DirNumber:       clientTree.GetNotLeafNodeCount(),
			TotalNodeNumber: clientTree.GetAllNodeCount(),
			CostTimeSecond:  int(time.Since(startTime).Seconds()),
		})
	}
	// 将内容写入文件中
	data, err := json.Marshal(outputData)
	if err != nil {
		log.Errorf("Marshal outputData failed: %v", err)
		return
	}
	os.WriteFile(filepath.Join(testConfig.QoderHome, "result.json"), data, 0644)
	log.Infof("All workspace index task post finish, sleep finish")
}
