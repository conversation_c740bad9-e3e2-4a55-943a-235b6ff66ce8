package main

// 这是一个测试索引的二进制文件
// 用于压力测试在模型各个条件下（限流、挂掉、ping不通）的情况下，是否能够如期完成索引
import (
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/indexing"
	"cosy/indexing/manager"
	"cosy/lang/indexer/rag"
	"cosy/log"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// ExpandTilde 将路径中的 ~ 替换为用户主目录
func ExpandTilde(path string) string {
	if strings.HasPrefix(path, "~") {
		homeDir, err := os.UserHomeDir()
		if err != nil {
			panic(err)
		}
		// 将 ~ 替换为主目录路径
		path = filepath.Join(homeDir, path[1:])
	}
	return path
}
func main() {
	//COSY_DEV_VERSION=2.1.4.202503141346;COSY_ENABLE_POST_PROCESS_DEBUG=true;COSY_LOCAL_DEV=true
	// 设置环境变量
	os.Setenv("COSY_DEV_VERSION", "2.1.4.202503141346")
	os.Setenv("COSY_ENABLE_POST_PROCESS_DEBUG", "true")
	os.Setenv("COSY_LOCAL_DEV", "true")

	client.InitClients()
	config.InitLocalConfig()

	// 标准输入获取workspace
	// 创建一个扫描器以从标准输入读取数据
	var workspacePath string
	args := os.Args[1:]
	workspacePath = strings.Join(args, "")
	workspacePath = strings.Trim(workspacePath, " ")

	// 判定workspace是否为合法的workspace
	workspacePath = ExpandTilde(workspacePath)
	_, err := os.Stat(workspacePath)
	if os.IsNotExist(err) {
		panic("路径不存在")
	}

	// 取出仓库名
	workspaceName := workspacePath[strings.LastIndex(workspacePath, string(filepath.Separator))+1:]
	// 将日志目录设置为~/.lingma_test/workspaceName.log
	homeDir, err := os.UserHomeDir()
	if err != nil {
		panic(err)
	}

	rootDir := filepath.Join(homeDir, ".lingma_test")
	logFile := filepath.Join(rootDir, workspaceName+".log")

	// 修改日志输出路径
	log.UseTestFileLogger(rootDir, logFile)
	log.Info("~~~~~~~~~~~~ 开始建立索引 ~~~~~~~~~~~~")
	// 调用索引建立函数

	workspaceInfo := definition.NewWorkspaceInfo(workspacePath)
	indexer := indexing.NewProjectFileIndex(nil, workspaceInfo)

	param := indexing.NewChatVectorIndexParam()

	// 全量建立索引
	indexer.IndexWorkspace(param, true)
	time.Sleep(10 * time.Second)
	// 等待向量索引构建完毕
	manager.GlobalIndexBuilderManager.WaitForWorkspaceFinish(workspacePath)

	log.Infof("~~~~~~~~~~~~ 索引建立已完成 ~~~~~~~~~~~~")
	// 索引建立完毕
	vectorIndexer, ok := indexer.GetChatRetrieveFileVectorIndexer()
	if !ok {
		log.Errorf("【TEST】vector indexer not found: %v", err)
		return
	}

	engine, err := vectorIndexer.GetClientVectorRetrieveEngine()
	if err != nil {
		log.Errorf("【TEST】failed to get vector retrieve engine, err: %v", err)
		return
	}
	vectorEngine := engine.(*rag.SqliteVecRetrieveEngine)

	totalFileNum := vectorEngine.GetStorageFileNum()
	if totalFileNum >= 5000 {
		log.Warnf("【TEST】total file num is too large: %d", totalFileNum)
	}

	alright := true
	// 索引完成后，校验索引完整性
	indexer.WalkDirectoryForTest(workspacePath, func(path string) error {
		storageChunks, err := vectorEngine.GetStorageFileChunks(path)
		if err != nil {
			log.Errorf("【TEST】failed to get storage file chunks, err: %v", err)
			return err
		}
		if len(storageChunks) == 0 {
			if totalFileNum < 5000 {
				log.Infof("【TEST】no storage chunks found for file: %s", path)
			}
			return nil
		}

		storageMap := make(map[string]bool)
		for _, chunk := range storageChunks {
			//chunkEmbeddingSum := float32(0.0)
			//for idx, embedding := range chunk.Embedding {
			//	chunkEmbeddingSum += float32(idx) * embedding
			//}
			chunkIdentifier := fmt.Sprintf("%s%d%d%s", chunk.FilePath, chunk.StartLine, chunk.EndLine, chunk.FileName)
			storageMap[chunkIdentifier] = true
		}

		task := definition.NewTask(definition.NewVirtualFile(path), definition.VectorFileChangeIndexSource, workspacePath, 0)
		splitWrapper, err := vectorEngine.SplitFile(task, false)
		if err != nil {
			log.Errorf("【TEST】failed to split file, err: %v", err)
			return err
		}

		splitMap := make(map[string]bool)
		for _, chunk := range splitWrapper.Chunks {
			chunkIdentifier := fmt.Sprintf("%s%d%d%s", chunk.FilePath, chunk.StartLine, chunk.EndLine, chunk.FileName)
			splitMap[chunkIdentifier] = true
		}

		successCnt := 0
		for chunkIdentifier, _ := range storageMap {
			_, ok := splitMap[chunkIdentifier]
			if ok {
				successCnt += 1
			} else {
				log.Errorf("【TEST】chunkIdentifier not found in splitMap: %s", chunkIdentifier)
			}
		}
		if len(storageMap) != len(splitMap) || successCnt != len(storageMap) {
			log.Errorf("【TEST】storageMap and splitMap not equal, storageMap: %v, splitMap: %v, filepath: %s", storageMap, splitMap, path)
			alright = false
		}
		return nil
	})

	log.Infof("~~~~~~~~~~~~ 索引验证完毕 ~~~~~~~~~~~~")

	if alright {
		log.Infof("【TEST】【Check Success】")
	} else {
		log.Infof("【TEST】【Check Failed】")
	}
	return
}
