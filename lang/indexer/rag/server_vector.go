package rag

import (
	"cosy/components"
	"cosy/definition"
	"cosy/global"
	"cosy/indexing/index_setting"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/sls"
	"cosy/tokenizer"
	"cosy/tree"
	"cosy/user"
	"cosy/util"
	"cosy/util/collection"
	"cosy/util/rag"
	"errors"
	"os"
	"path"
	"path/filepath"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/google/uuid"

	merkletree "code.alibaba-inc.com/cosy/mtree"
)

// showVerboseLog 是否展示更多debug信息
// 默认不展示，通过环境变量 LINGMA_CLIENT_SERVER_VECTOR_LOG_VERBOSE 控制
var showVerboseLog = false

type ServerVecRetrieveEngine struct {
	WorkspacePath                  string
	Mutex                          sync.RWMutex
	FullIndexBuildRunning          *atomic.Bool
	IncrementalIndexBuildingNumber *atomic.Int32
	CompensationRunning            *atomic.Bool
	ServerHandle                   *components.ServerHandle
	StorageFileNumber              *atomic.Int32
	UserInfo                       *user.CachedUserInfo
}

// newChatSqliteVecRetrieveEngine
// 不希望外部随便new向量引擎
// 应该使用 GetClientChatVectorRetrieveEngine 来获取向量引擎
func newChatServerVecRetrieveEngine(workspacePath string) (*ServerVecRetrieveEngine, error) {
	if util.IsIllegalWorkspace(workspacePath) {
		return nil, errors.New("workspace path is illegal")
	}
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		log.Debugf("[codebase]-[server vector engine] get user info failed")
	}

	vecEngine := &ServerVecRetrieveEngine{
		WorkspacePath:                  workspacePath,
		Mutex:                          sync.RWMutex{},
		FullIndexBuildRunning:          &atomic.Bool{},
		IncrementalIndexBuildingNumber: &atomic.Int32{},
		CompensationRunning:            &atomic.Bool{},
		ServerHandle:                   nil,
		StorageFileNumber:              &atomic.Int32{},
		UserInfo:                       userInfo,
	}

	newHandle, err := components.NewServerHandle(vecEngine.WorkspacePath)
	if err != nil || newHandle == nil {
		log.Debugf("[codebase]-[server vector engine] init server handle failed, err: %v", err)
	} else {
		vecEngine.ServerHandle = newHandle
	}

	vecEngine.FullIndexBuildRunning.Store(false)
	vecEngine.CompensationRunning.Store(false)
	vecEngine.StorageFileNumber.Store(0)
	vecEngine.IncrementalIndexBuildingNumber.Store(0)
	vecEngine.Compensation()
	vecEngine.monitor()

	return vecEngine, nil
}

func (engine *ServerVecRetrieveEngine) monitor() {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[server vector engine] [user monitor] panic: %v", r)
				log.Debugf("[codebase]-[server vector engine] [user monitor] panic stack: %s", string(debug.Stack()))
			}
		}()

		for {
			// 持续监控用户变化，如果重新登录，就需要更换handle
			time.Sleep(time.Second * 1)
			cacheUserInfo := user.GetCachedUserInfo()
			oldUserInfo := engine.UserInfo

			// 1.用户登录状态监测，如果用户登录状态发生变化，则需要更换handle
			if cacheUserInfo == nil {
				// 新状态用户未登录，跳过
				if oldUserInfo != nil {
					// 用户已登录，需要删除已有状态handle
					log.Infof("[codebase]-[server vector engine] user logout")
					engine.Mutex.Lock()
					engine.ServerHandle = nil
					engine.UserInfo = nil
					engine.Mutex.Unlock()
				}
				continue
			} else if oldUserInfo == nil && cacheUserInfo != nil {
				// 旧状态未登录，新状态用户已登录，需要创建handle
				log.Infof("[codebase]-[server vector engine] user login success, create new server handle")
				newHandle, err := components.NewServerHandle(engine.WorkspacePath)
				if err != nil || newHandle == nil {
					log.Debugf("[codebase]-[server vector engine] user login success, create new server handle failed, err: %v", err)
					continue
				}
				engine.Mutex.Lock()
				engine.ServerHandle = newHandle
				engine.UserInfo = cacheUserInfo
				engine.Mutex.Unlock()
				continue
			} else if oldUserInfo.Uid != cacheUserInfo.Uid {
				// 旧状态用户已登录，新状态用户已登录，但并不是同一个用户
				// 需要切换登录状态
				log.Infof("[codebase]-[server vector engine] user switch, create new server handle")
				newHandle, err := components.NewServerHandle(engine.WorkspacePath)
				if err != nil || newHandle == nil {
					log.Debugf("[codebase]-[server vector engine] user switch, create new server handle failed, err: %v", err)
					continue
				}
				engine.Mutex.Lock()
				engine.ServerHandle = newHandle
				engine.UserInfo = cacheUserInfo
				engine.Mutex.Unlock()
				continue
			}

			// 2. server handle 监测，如果handle为空，则需要创建新的handle
			if cacheUserInfo != nil {
				// 用户正常登录，且是同一个用户
				handle := engine.getServerHandle()
				if handle == nil {
					log.Errorf("[codebase]-[server vector engine] server handle is nil, user switch, create new server handle")
					newHandle, err := components.NewServerHandle(engine.WorkspacePath)
					if err != nil || newHandle == nil {
						log.Debugf("[codebase]-[server vector engine] user switch, create new server handle failed, err: %v", err)
						continue
					}
					engine.Mutex.Lock()
					engine.ServerHandle = newHandle
					engine.UserInfo = cacheUserInfo
					engine.Mutex.Unlock()
				} else {
					// log.Debugf("[codebase]-[server vector engine] server handle is not nil, skip renew server handle")
				}
			}

		}
	}()
}

func (engine *ServerVecRetrieveEngine) autoIndexEnable() bool {
	return index_setting.GetAutoIndexSetting(engine.WorkspacePath)
}

func (engine *ServerVecRetrieveEngine) indexIsCanceled() bool {
	return index_setting.IndexIsCanceled(engine.WorkspacePath)
}

func (engine *ServerVecRetrieveEngine) getServerHandle() *components.ServerHandle {
	engine.Mutex.RLock()
	defer engine.Mutex.RUnlock()
	if engine.UserInfo == nil {
		// 用户未登录，跳过
		return nil
	}
	return engine.ServerHandle
}

func (engine *ServerVecRetrieveEngine) indexEnable() bool {
	engine.Mutex.RLock()
	defer engine.Mutex.RUnlock()
	if engine.UserInfo == nil {
		log.Debugf("[codebase]-[server vector engine] [index enable] user info is nil")
		return false
	}

	return index_setting.IndexEnable(engine.WorkspacePath)
}

func (engine *ServerVecRetrieveEngine) EngineType() string {
	return VectorEngineTypeServer
}

func (engine *ServerVecRetrieveEngine) Retrieve(queryCondition definition.QueryCondition) (RetrieveResult, error) {
	handle := engine.getServerHandle()
	if handle == nil {
		log.Errorf("[codebase]-[server vector engine] [retrieve] workspacePath: %s, server handle is nil", engine.WorkspacePath)
		return RetrieveResult{}, errors.New("server handle is nil")
	}
	response, err := handle.ServerRetrieve(queryCondition)
	if err != nil {
		log.Errorf("[codebase]-[server vector engine] [retrieve] workspacePath: %s, error: %v", engine.WorkspacePath, err)
		return RetrieveResult{}, err
	}

	if response == nil {
		log.Errorf("[codebase]-[server vector engine] [retrieve] workspacePath: %s, response is nil", engine.WorkspacePath)
		return RetrieveResult{}, errors.New("response is nil")
	}

	verboseLogf("[codebase]-[server vector engine] [retrieve] workspacePath: %s, response: %v", engine.WorkspacePath, response)

	retrieveResult := RetrieveResult{
		Source: ServerVectorRetrieveSource,
		Chunks: make([]RetrieveChunk, 0),
	}
	for _, chunk := range response.Chunks {
		filePath := filepath.Join(engine.WorkspacePath, chunk.FilePath)
		// fileContent, err := rag.GetFileContentByOffset(filePath, chunk.StartOffset, chunk.EndOffset)
		fileContent, err := rag.GetFileContentByLine(filePath, chunk.StartLine, chunk.EndLine)
		if err != nil || fileContent == "" {
			log.Errorf("[codebase]-[server vector engine] [retrieve] workspacePath: %s, get file content failed, filePath: %v, startOffset: %v, endOffset: %v", engine.WorkspacePath, filePath, chunk.StartOffset, chunk.EndOffset)
			continue
		}

		retrieveResult.Chunks = append(retrieveResult.Chunks, RetrieveChunk{
			CodeChunk: indexer.CodeChunk{
				FilePath:    filePath,
				Content:     fileContent,
				StartLine:   chunk.StartLine,
				EndLine:     chunk.EndLine,
				StartOffset: chunk.StartOffset,
				EndOffset:   chunk.EndOffset,
			},
			Score: chunk.Score,
		})

		if len(retrieveResult.Chunks) >= queryCondition.TopK {
			break
		}
	}

	return retrieveResult, nil
}

func (engine *ServerVecRetrieveEngine) ExecuteBatchTask(tasks definition.VectorBatchTask, enableCheckChange bool, limitRequire bool) bool {
	log.Debugf("[codebase]-[server vector engine] [execute batch task] not support")
	return false
}

// BatchDeleteIndex
// 服务端删除索引
func (engine *ServerVecRetrieveEngine) BatchDeleteIndex(virtualFiles []definition.VirtualFile, isDir bool) error {
	return nil
}

func (engine *ServerVecRetrieveEngine) GetStorageFileNum() int {
	return int(engine.StorageFileNumber.Load())
}

func (engine *ServerVecRetrieveEngine) GetStorageChunkNum() int {
	log.Infof("[codebase]-[server vector engine] [get storage chunk num] not support")
	return -1
}

func (engine *ServerVecRetrieveEngine) GetStorageFileChunks(filePath string) ([]definition.StorageChunk, error) {
	log.Infof("[codebase]-[server vector engine] [get storage file chunks] not support")
	return nil, errors.New("get storage file chunks not support")
}

func (engine *ServerVecRetrieveEngine) executeAllRepoBuildIndex() {
	engine.AsyncBuildIndex(nil, definition.VectorFullIndexSource)
}

func (engine *ServerVecRetrieveEngine) Compensation() {
	go func() {
		if !engine.CompensationRunning.CompareAndSwap(false, true) {
			log.Infof("[codebase]-[server vector engine] [compensation] is in progress, workspacePath: %s", engine.WorkspacePath)
			return
		}

		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[server vector engine] [compensation] workspacePath: %s, panic: %v", engine.WorkspacePath, r)
				log.Debugf("[codebase]-[server vector engine] [compensation] workspacePath: %s, panic stack: %s", engine.WorkspacePath, string(debug.Stack()))
			}
			engine.CompensationRunning.Store(false)
		}()

		log.Infof("[codebase]-[server vector engine] [compensation] start monitor, workspace: %s", engine.WorkspacePath)
		for {
			time.Sleep(definition.DefaultCompensationInterval)

			if !engine.autoIndexEnable() {
				continue
			}
			log.Infof("[codebase]-[server vector engine] [compensation] trigger, workspace: %s", engine.WorkspacePath)
			engine.executeAllRepoBuildIndex()
		}
	}()
}

func (engine *ServerVecRetrieveEngine) Close() {

}

func (engine *ServerVecRetrieveEngine) AsyncBuildIndex(virtualFiles []definition.VirtualFile, source string) {
	if source == definition.VectorFileChangeIndexSource {
		if !engine.autoIndexEnable() {
			log.Debugf("[codebase]-[server vector engine] [incremental index] workspacePath: %s, auto index is disabled, skip this vector index", engine.WorkspacePath)
			return
		}
	}
	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		wg.Done()
		engine.executeBuildServerIndex(virtualFiles, source)
	}()
	wg.Wait()
}

func (engine *ServerVecRetrieveEngine) executeBuildServerIndex(virtualFiles []definition.VirtualFile, source string) {
	if !engine.indexEnable() {
		log.Debugf("[codebase]-[server vector engine] [build server index] workspacePath: %s, index is disabled, skip this vector index", engine.WorkspacePath)
		return
	}

	if source == definition.VectorFullIndexSource {
		if !engine.FullIndexBuildRunning.CompareAndSwap(false, true) {
			// 同时只能有一个全量索引在执行
			log.Infof("[codebase]-[server vector engine] [full index] is in progress, workspacePath: %s", engine.WorkspacePath)
			return
		}

		// 执行全库索引时，执行补偿机制
		engine.Compensation()
	} else if source == definition.VectorFileChangeIndexSource {
		if engine.FullIndexBuildRunning.Load() {
			// 如果在执行全量索引，则不执行增量索引
			log.Infof("[codebase]-[server vector engine] [full index] is in progress, incremental index will be ignored, workspacePath: %s", engine.WorkspacePath)
			return
		}

		if engine.IncrementalIndexBuildingNumber.Load() >= definition.DefaultMaxParallelIncrementalBuildingNumber {
			log.Infof("[codebase]-[server vector engine] [incremental index] incremental building number is too large, workspacePath: %s, number: %v, skip this vector index", engine.WorkspacePath, engine.IncrementalIndexBuildingNumber.Load())
			return
		}

		if len(virtualFiles) == 0 {
			log.Debugf("[codebase]-[server vector engine] [incremental index] virtualFiles is empty, skip this vector index, workspacePath: %s", engine.WorkspacePath)
			return
		}
	}

	defer func() {
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[server vector engine] [build server index] workspacePath: %s, panic: %v", engine.WorkspacePath, r)
			log.Debugf("[codebase]-[server vector engine] [build server index] workspacePath: %s, panic stack: %s", engine.WorkspacePath, string(debug.Stack()))
		}

		if source == definition.VectorFullIndexSource {
			engine.FullIndexBuildRunning.Store(false)
		} else if source == definition.VectorFileChangeIndexSource {
			engine.IncrementalIndexBuildingNumber.Add(-1)
		}

		if !engine.indexIsCanceled() {
			// 调用finishBuildIndex来修改索引进度
			index_setting.FinishBuildIndex(engine.WorkspacePath, index_setting.IndexModeVector)
		}
	}()

	innerSource := source
	if source == definition.VectorFullIndexSource {
		// 未知源的全量索引，需要把source进行细化
		handle := engine.getServerHandle()
		if handle == nil {
			log.Errorf("[codebase]-[server vector engine] [build server index] workspacePath: %s, server handle is nil", engine.WorkspacePath)
			return
		}
		if handle.IsNew {
			// 全新库索引，初始化全量索引
			innerSource = definition.VectorInitialFullIndexSource
		} else {
			// 非全新库索引，初始化增量索引
			innerSource = definition.VectorSubsequentFullIndexSource
		}
	} else {
		engine.IncrementalIndexBuildingNumber.Add(1)
		innerSource = definition.VectorFileChangeIndexSource
	}

	log.Debugf("[codebase]-[server vector engine] [build server index] workspacePath: %s, start, index mode: %v", engine.WorkspacePath, source)

	clientOldTree := tree.NewWorkspaceMerkleTree(engine.WorkspacePath)
	if clientOldTree != nil && source == definition.VectorFullIndexSource {
		engine.StorageFileNumber.Store(int32(clientOldTree.GetLeafNodeCount()))
	}

	startTime := time.Now()
	actions, err := engine.GetActionNodes(virtualFiles, source)
	if actions == nil || err != nil {
		if err != nil {
			log.Errorf("[codebase]-[server vector engine] [get server diff nodes] workspacePath: %s, error: %v", engine.WorkspacePath, err)
		} else {
			log.Debugf("[codebase]-[server vector engine] [get server diff nodes] workspacePath: %s, actions is nil, index is canceled", engine.WorkspacePath)
		}
		return
	}

	if len(actions.AddNodes) == 0 &&
		len(actions.DeleteNodes) == 0 &&
		len(actions.UpdateNodes) == 0 &&
		len(actions.LeafNodes) == 0 {

		if source == definition.VectorFullIndexSource {
			displayer := NewIndexDisplayer()
			displayData := DisplayData{
				WorkspacePath: engine.WorkspacePath,
				FinishedNum:   0,
				TotalNum:      0,
				IndexType:     DisplayIndexTypeServerVectorFullSkipped,
				OutputMode:    DisplayOutputModeLog,
			}
			// 全量索引输出进度条和完成banner
			displayer.Display(displayData)
		}

		log.Infof("[codebase]-[server vector engine] [build server index] workspacePath: %s, no diff nodes, skip this vector index, mode: %s", engine.WorkspacePath, source)
		return
	}

	if source == definition.VectorFullIndexSource {
		engine.StorageFileNumber.Store(int32(actions.ServerFileNum))
		log.Debugf("[codebase]-[server vector engine] server storage file number will be: %v", actions.ServerFileNum)
	}

	relPathNotLeafNodeMap := make(map[string]*definition.MTreePairActionNodes)

	// 将节点加入map中
	for _, addNode := range actions.AddNodes {
		actionNode := &definition.MTreePairActionNodes{
			Node:       addNode,
			Operator:   definition.ActionNodeOperatorAdd,
			NodeStatus: definition.ActionNodeStatusPending,
		}
		relPathNotLeafNodeMap[addNode.New.RelativePath] = actionNode
	}

	for _, updateNode := range actions.UpdateNodes {
		actionNode := &definition.MTreePairActionNodes{
			Node:       updateNode,
			Operator:   definition.ActionNodeOperatorUpdate,
			NodeStatus: definition.ActionNodeStatusPending,
		}
		relPathNotLeafNodeMap[updateNode.New.RelativePath] = actionNode
	}

	deleteActionNodes := make([]*definition.MTreePairActionNodes, 0)
	for _, deleteNode := range actions.DeleteNodes {
		actionNode := &definition.MTreePairActionNodes{
			Node:       deleteNode,
			Operator:   definition.ActionNodeOperatorDelete,
			NodeStatus: definition.ActionNodeStatusPending,
		}
		deleteActionNodes = append(deleteActionNodes, actionNode)
		relPathNotLeafNodeMap[deleteNode.Old.RelativePath] = actionNode
	}

	leafActionNodes := make([]*definition.LeafActionNodes, 0, len(actions.LeafNodes))
	for _, leafNode := range actions.LeafNodes {
		leafActionNodes = append(leafActionNodes, &definition.LeafActionNodes{
			NodeStatus:          definition.ActionNodeStatusPending,
			MTreeActionNodeMeta: *leafNode,
		})
	}

	log.Debugf("[codebase]-[server vector engine] [get server diff nodes] leaf nodes cnt: %v", len(leafActionNodes))

	if !engine.indexEnable() {
		log.Infof("[codebase]-[server vector engine] [build server index] workspacePath: %s, index is disabled, skip this vector index", engine.WorkspacePath)
		return
	}

	isCanceled := false
	dealFileWg := sync.WaitGroup{}
	dealFileWg.Add(2)
	go func() {
		// 处理删除节点
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[server vector engine] [handle delete nodes] workspacePath: %s, delete panic: %v", engine.WorkspacePath, r)
				log.Debugf("[codebase]-[server vector engine] [handle delete nodes] workspacePath: %s, delete panic stack: %s", engine.WorkspacePath, string(debug.Stack()))
			}
			dealFileWg.Done()

		}()
		isCanceled = isCanceled || engine.HandleDeleteNodes(deleteActionNodes)
	}()

	go func() {
		// 处理上传文件
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[server vector engine] [upload file] workspacePath: %s, update panic: %v", engine.WorkspacePath, r)
				log.Debugf("[codebase]-[server vector engine] [upload file] workspacePath: %s, update panic stack: %s", engine.WorkspacePath, string(debug.Stack()))
			}
			dealFileWg.Done()
		}()
		isCanceled = isCanceled || engine.HandleUploadFilesV2(leafActionNodes)
	}()

	if !engine.indexEnable() || isCanceled {
		if isCanceled {
			log.Infof("[codebase]-[server vector engine] [build server index] workspacePath: %s, index is canceled, skip this vector index", engine.WorkspacePath)
		}
		return
	}
	// 等待文件上传完毕
	dealFileWg.Wait()
	log.Debugf("[codebase]-[server vector engine] [upload file] finish, start to sync node")

	dealMTreeNodeWg := sync.WaitGroup{}
	dealMTreeNodeWg.Add(1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[server vector engine] [handle sync server nodes] workspacePath: %s, panic: %v", engine.WorkspacePath, r)
				log.Debugf("[codebase]-[server vector engine] [handle sync server nodes] workspacePath: %s, panic stack: %s", engine.WorkspacePath, string(debug.Stack()))
			}
			dealMTreeNodeWg.Done()
		}()

		isCanceled = isCanceled || engine.HandleSyncServerNodesV2(relPathNotLeafNodeMap, leafActionNodes, source)
	}()

	dealMTreeNodeWg.Wait()
	if !engine.indexEnable() || isCanceled {
		return
	}

	serverHandle := engine.getServerHandle()
	codebaseId, err := serverHandle.GetCodebaseId()
	if err != nil {
		log.Errorf("[codebase]-[server vector engine] [build server index] workspacePath: %s, get codebase id failed: %v", engine.WorkspacePath, err)
	}
	costTime := time.Since(startTime).Milliseconds()
	log.Debugf("[codebase]-[server vector engine] [build server index] workspacePath: %s, cost time: %v, mode: %s", engine.WorkspacePath, costTime, innerSource)
	if serverHandle != nil && source == definition.VectorFullIndexSource {
		// 全量索引时，执行埋点上报，如果handle为空，没有埋点的意义
		go func() {
			mtree := clientOldTree.Clone()
			data := make(map[string]string)
			data["cost_time"] = strconv.FormatInt(costTime, 10)
			data["is_exceed_max_time"] = strconv.FormatBool(costTime > definition.DefaultMaxServerIndexTime.Milliseconds())
			data["index_mode"] = innerSource
			data["workspace_path"] = engine.WorkspacePath
			data["codebase_id"] = codebaseId
			data["total_index_file_cnt"] = strconv.FormatInt(int64(len(leafActionNodes)), 10)
			data["total_leaf_node_cnt"] = strconv.FormatInt(int64(mtree.GetLeafNodeCount()), 10)
			sls.Report(sls.EventTypeChatCodebaseServerFullVectorIndexFinish, uuid.NewString(), data)
		}()
	}
}

// HandleDeleteNodes
// 返回是否被cancel
func (engine *ServerVecRetrieveEngine) HandleDeleteNodes(deleteNodes []*definition.MTreePairActionNodes) bool {
	// 直接删除每个节点
	for len(deleteNodes) > 0 {
		if !engine.indexEnable() {
			return true
		}

		batchSize := definition.DefaultPerRequestMaxUpdateNotLeafNodeNum
		if batchSize > len(deleteNodes) {
			batchSize = len(deleteNodes)
		}
		toDeleteNodes := deleteNodes[:batchSize]
		deleteNodes = deleteNodes[batchSize:]

		batchDeleteNodes := make([]*definition.MTreePairNodes, 0)
		for _, node := range toDeleteNodes {
			batchDeleteNodes = append(batchDeleteNodes, node.Node)
		}
		log.Debugf("[codebase]-[server vector engine] [delete node] workspacePath: %s, batch delete nodes cnt: %v", engine.WorkspacePath, len(batchDeleteNodes))
		handle := engine.getServerHandle()
		if handle == nil {
			log.Errorf("[codebase]-[server vector engine] [delete node] workspacePath: %s, server handle is nil", engine.WorkspacePath)
			for _, node := range toDeleteNodes {
				node.NodeStatus = definition.ActionNodeStatusDiscard
			}
			continue
		}
		err := handle.UpdateServerMerkelNodes(batchDeleteNodes)
		if err != nil {
			log.Errorf("[codebase]-[server vector engine] [delete node] workspacePath: %s, error: %v", engine.WorkspacePath, err)
			for _, node := range toDeleteNodes {
				node.NodeStatus = definition.ActionNodeStatusDiscard
			}
		} else {
			for _, node := range toDeleteNodes {
				node.NodeStatus = definition.ServerFileStatusSynced
			}
		}
	}

	return false
}

// HandleUploadFiles
// 返回是否被cancel
func (engine *ServerVecRetrieveEngine) HandleUploadFiles(leafNodes []*definition.LeafActionNodes) bool {
	// 先批量获取当前新节点的服务端文件状态，以确定需要上传哪些文件
	leafNodeMap := make(map[string][]*definition.LeafActionNodes)
	for _, node := range leafNodes {
		leafNodeMap[node.Hash] = append(leafNodeMap[node.Hash], node)
	}
	log.Debugf("[codebase]-[server vector engine] [upload file] to upload file cnt: %v", len(leafNodeMap))
	// 此处是去重的FileIds
	toCheckFileIds := make([]string, 0)
	for fileId, _ := range leafNodeMap {
		toCheckFileIds = append(toCheckFileIds, fileId)
	}

	// 获取服务端文件状态
	checkStatusBatchFileIds := make([]string, 0)
	// 检查服务端文件状态，目的是拿到需要上传的文件
	toUploadFileIds := make([]string, 0)
	for len(toCheckFileIds) > 0 || len(checkStatusBatchFileIds) > 0 {
		if !engine.indexEnable() {
			return true
		}
		batchSize := definition.DefaultPerRequestMaxCheckFileStatusNum
		if len(toCheckFileIds) < batchSize {
			batchSize = len(toCheckFileIds)
		}

		checkStatusBatchFileIds = append(checkStatusBatchFileIds, toCheckFileIds[:batchSize]...)
		toCheckFileIds = toCheckFileIds[batchSize:]

		// 不为空时，检查一次文件状态
		if len(checkStatusBatchFileIds) >= 0 {
			fileIds := make([]string, 0)
			for _, fileId := range checkStatusBatchFileIds {
				fileIds = append(fileIds, fileId)
			}

			checkStatusBatchFileIds = make([]string, 0)

			verboseLogf("[codebase]-[server vector engine] [upload files] check file status cnt: %v", len(fileIds))
			// 执行check文件状态
			response, err := components.CheckServerFileStatus(fileIds)
			if err != nil || response == nil {
				// 失败时，丢弃这些文件
				for _, fileId := range fileIds {
					for _, node := range leafNodeMap[fileId] {
						node.NodeStatus = definition.ActionNodeStatusDiscard
					}
				}
				log.Errorf("[codebase]-[vector server engine] [upload files] check file status failed: %v", err)
			} else {
				for fileId, fileStatus := range response.FileStatuses {
					if fileStatus == definition.ServerFileStatusNotSynced {
						// 说明此时文件未同步，需要上传文件
						toUploadFileIds = append(toUploadFileIds, fileId)
					} else if fileStatus == definition.ServerFileStatusPending {
						// 说明此时文件已上传，服务端正在处理，不需要重复上传文件
						engine.StorageFileNumber.Add(int32(len(leafNodeMap[fileId])))
					} else if fileStatus == definition.ServerFileStatusSynced {
						// 说明此时文件已处理完毕，直接将节点状态置为文件处理成功
						for _, node := range leafNodeMap[fileId] {
							node.NodeStatus = definition.ActionLeafNodeStatusProcessSuccess
						}
						engine.StorageFileNumber.Add(int32(len(leafNodeMap[fileId])))
					} else {
						// 兜底逻辑，丢弃该文件
						for _, node := range leafNodeMap[fileId] {
							node.NodeStatus = definition.ActionNodeStatusDiscard
						}
					}
				}
			}
		}
	}

	log.Debugf("[codebase]-[server vector engine] [upload files] to upload file cnt: %v", len(toUploadFileIds))
	// 开始上传文件
	uploadBatchFileIds := make([]string, 0)
	uploadFileSizeSum := 0
	reachedMaxUploadFileSize := false
	totalFileCnt := len(toUploadFileIds)
	for len(toUploadFileIds) > 0 || len(uploadBatchFileIds) > 0 {
		if !engine.indexEnable() {
			return true
		}

		if len(toUploadFileIds) > 0 {
			// 查看当前上传队列 uploadBatchFileIds 是否仍然能够添加文件
			fileId := toUploadFileIds[0]
			for _, uploadFileId := range uploadBatchFileIds {
				if uploadFileId == fileId {
					// 说明此时文件已在等待上传，跳过即可
					toUploadFileIds = toUploadFileIds[1:]
					continue
				}
			}

			var filePath string
			if _, ok := leafNodeMap[fileId]; ok {
				// 确定存在某个节点，由于是上传文件
				filePath = filepath.Join(engine.WorkspacePath, leafNodeMap[fileId][0].RelativePath)
			} else {
				// 节点在map中不存在，跳过即可
				toUploadFileIds = toUploadFileIds[1:]
				for _, node := range leafNodeMap[fileId] {
					node.NodeStatus = definition.ActionNodeStatusDiscard
				}
				continue
			}

			stat, err := os.Stat(filePath)
			if err != nil {
				toUploadFileIds = toUploadFileIds[1:]
				for _, node := range leafNodeMap[fileId] {
					node.NodeStatus = definition.ActionNodeStatusDiscard
				}
				continue
			}

			fileSize := int(stat.Size())
			if fileSize > definition.DefaultMaxMTreeFileSize || fileSize <= definition.DefaultMinMTreeFileSize {
				// 单文件太大，直接跳过
				// 单文件太小，没有价值，直接跳过
				toUploadFileIds = toUploadFileIds[1:]
				for _, node := range leafNodeMap[fileId] {
					node.NodeStatus = definition.ActionNodeStatusDiscard
				}
				continue
			}

			if fileSize+uploadFileSizeSum < definition.DefaultPerRequestMaxUploadFileSize {
				// 添加文件后，依然小于最大上传文件限制，则取出文件添加到上传队列中
				uploadBatchFileIds = append(uploadBatchFileIds, fileId)
				uploadFileSizeSum += fileSize
				toUploadFileIds = toUploadFileIds[1:]
			} else {
				reachedMaxUploadFileSize = true
			}
		}

		if int(engine.StorageFileNumber.Load()) >= global.GetMaxServerStorageFileNum() {
			log.Infof("[codebase]-[server vector engine] [upload files] reached max storage file num, break")
			// 达到服务端存储上限,剩余文件不再上传
			break
		}

		if reachedMaxUploadFileSize ||
			len(uploadBatchFileIds) >= definition.DefaultPerRequestMaxUploadFileNum ||
			len(toUploadFileIds) == 0 {
			// reachedMaxUploadFileSize 代表到达了这一批上传文件的大小上限
			// len(toUploadFileIds) == 0 代表此时已经是最后一批上传的文件了
			// len(uploadBatchFileIds) >= definition.DefaultPerRequestMaxUploadFileNum 代表这一批上传文件的数量已达到上限
			var toUploadFilePaths []string
			for _, fileId := range uploadBatchFileIds {
				if _, ok := leafNodeMap[fileId]; !ok {
					continue
				}
				filePath := filepath.Join(engine.WorkspacePath, leafNodeMap[fileId][0].RelativePath)
				toUploadFilePaths = append(toUploadFilePaths, filePath)
			}
			uploadBatchFileIds = make([]string, 0)
			uploadFileSizeSum = 0
			reachedMaxUploadFileSize = false
			log.Debugf("[codebase]-[vector server engine] [upload files] upload cnt: %v", len(toUploadFilePaths))
			handle := engine.getServerHandle()
			if handle == nil {
				log.Errorf("[codebase]-[server vector engine] [upload files] server handle is nil")
				continue
			}
			codebaseId, err := handle.GetCodebaseId()
			if err != nil {
				log.Errorf("[codebase]-[server vector engine] [upload files] get codebase id failed: %v", err)
				continue
			}
			response, err := components.UploadFileToEmbedding(engine.WorkspacePath, toUploadFilePaths, totalFileCnt, codebaseId)
			if err != nil || response == nil {
				// 失败则丢弃这一批文件
				log.Errorf("[codebase]-[vector server engine] [upload files] error: %v", err)
			} else {
				for _, result := range response.Results {
					if result.Success {
						// 文件上传成功
						engine.StorageFileNumber.Add(int32(len(leafNodeMap[result.FileId])))
					} else {
						// 文件上传失败
						// 或者未从response找到该文件
						// 直接丢弃，等待下次索引
						for _, node := range leafNodeMap[result.FileId] {
							node.NodeStatus = definition.ActionNodeStatusDiscard
						}
						filePath := filepath.Join(engine.WorkspacePath, result.FilePath)
						log.Errorf("[codebase]-[vector server engine] [upload files] filePath: %s, error: %v", filePath, result.Error)
					}
				}
			}
		}
	}

	return false
}

func (engine *ServerVecRetrieveEngine) HandleSyncServerNodes(relPathNodeMap map[string]*definition.MTreePairActionNodes, leafNodes []*definition.LeafActionNodes, source string) bool {
	// 去重后的FileId
	leafNodeMap := make(map[string][]*definition.LeafActionNodes, len(leafNodes))
	for _, node := range leafNodes {
		leafNodeMap[node.Hash] = append(leafNodeMap[node.Hash], node)
	}

	toCheckFileIds := make([]string, 0)
	for fileId, _ := range leafNodeMap {
		toCheckFileIds = append(toCheckFileIds, fileId)
	}

	for _, node := range leafNodes {
		verboseLogf("[codebase]-[vector server engine] [handle server node] to sync leaf: %v", node.RelativePath)
	}

	toSyncLeafNodeCnt := len(leafNodes)
	log.Debugf("[codebase]-[vector server engine] [handle server node] %v leaf node waiting to sync", toSyncLeafNodeCnt)

	startTime := time.Now()
	// 获取服务端文件状态
	checkStatusBatchFileIds := make([]string, 0)
	continuousQueryNoUpdateNodeTimes := 0
	// 检查服务端文件状态，目的是拿到需要上传的文件
	displayer := NewIndexDisplayer()
	displayData := DisplayData{
		WorkspacePath: engine.WorkspacePath,
		FinishedNum:   0,
		TotalNum:      0,
		OutputMode:    DisplayOutputModeLog,
	}

	if source == definition.VectorFullIndexSource {
		displayData.IndexType = DisplayIndexTypeServerVectorFull
	} else {
		displayData.IndexType = DisplayIndexTypeServerVectorIncremental
	}

	for {
		if !engine.indexEnable() {
			return true
		}

		batchSize := definition.DefaultPerRequestMaxCheckFileStatusNum
		if len(toCheckFileIds) < batchSize {
			batchSize = len(toCheckFileIds)
		}
		checkStatusBatchFileIds = make([]string, 0, batchSize)
		checkStatusBatchFileIds = append(checkStatusBatchFileIds, toCheckFileIds[:batchSize]...)
		toCheckFileIds = toCheckFileIds[batchSize:]
		executeCheckStatusFileNum := len(checkStatusBatchFileIds)
		// 不为空时，检查一次文件状态
		if len(checkStatusBatchFileIds) > 0 {
			for _, fileId := range checkStatusBatchFileIds {
				for _, node := range leafNodeMap[fileId] {
					verboseLogf("[codebase]-[vector server engine] [handle server node] to check filepath: %v", node.RelativePath)
				}
			}

			// 执行check文件状态
			log.Debugf("[codebase]-[vector server engine] [handle server node] check file status cnt: %v", len(checkStatusBatchFileIds))
			response, err := components.CheckServerFileStatus(checkStatusBatchFileIds)
			if err != nil || response == nil {
				// 失败代表这些节点全部discard
				for _, fileId := range checkStatusBatchFileIds {
					for _, node := range leafNodeMap[fileId] {
						node.NodeStatus = definition.ActionNodeStatusDiscard
					}
				}
				log.Errorf("[codebase]-[vector server engine] [handle server node] check file status failed: %v", err)
				continue
			}
			for fileId, fileStatus := range response.FileStatuses {
				for _, node := range leafNodeMap[fileId] {
					verboseLogf("[codebase]-[vector server engine] [handle server node] check file status: %v, filePaths: %v, fileId: %v", fileId, node.RelativePath, fileStatus)
				}
				if fileStatus == definition.ServerFileStatusNotSynced {
					// 此前文件已经全部上传完毕
					// 说明此时文件未上传，直接将该节点设置为丢弃
					for _, node := range leafNodeMap[fileId] {
						node.NodeStatus = definition.ActionNodeStatusDiscard
					}
				} else if fileStatus == definition.ServerFileStatusPending {
					// 说明此时文件正在处理，等待即可
					// 仍然需要将该文件放回，下次继续check
					toCheckFileIds = append(toCheckFileIds, fileId)
				} else if fileStatus == definition.ServerFileStatusSynced {
					// 说明此时文件已处理完毕，但是节点未同步
					for _, node := range leafNodeMap[fileId] {
						node.NodeStatus = definition.ActionLeafNodeStatusProcessSuccess
					}
				} else {
					// 兜底逻辑，丢弃该文件
					for _, node := range leafNodeMap[fileId] {
						node.NodeStatus = definition.ActionNodeStatusDiscard
					}
				}
			}
		} else {
			log.Debugf("[codebase]-[vector server engine] [handle server node] no file to check")
		}

		// 获取pending leaf节点状态
		pendingLeafNodeNumber := 0
		discardLeafNodeNumber := 0
		processSuccessLeafNodeNumber := 0
		for _, sameFileIdNodes := range leafNodeMap {
			for _, node := range sameFileIdNodes {
				if node.NodeStatus == definition.ActionNodeStatusPending {
					pendingLeafNodeNumber++
				} else if node.NodeStatus == definition.ActionLeafNodeStatusProcessSuccess {
					processSuccessLeafNodeNumber++
				} else if node.NodeStatus == definition.ActionNodeStatusDiscard {
					discardLeafNodeNumber++
				}
			}
		}
		log.Debugf("[codebase]-[vector server engine] [handle server node] pending leaf node number: %v, discard leaf node number: %v, process success leaf node number: %v", pendingLeafNodeNumber, discardLeafNodeNumber, processSuccessLeafNodeNumber)

		toUpdateServerNodes := make([]*definition.MTreePairActionNodes, 0)
		// 更改可同步服务端非叶子节点状态
		for _, node := range relPathNodeMap {
			if node.Node.New == nil {
				// 删除节点，不需要特殊处理
				continue
			}

			if node.NodeStatus == definition.ActionNodeStatusPending && node.Node.New != nil {
				pendingNodeNum := 0
				discardNodeFilePath := make([]string, 0)
				partialSyncNodeNum := 0
				for _, childrenNode := range node.Node.New.Children {
					childrenNodeStatus := ""
					if childrenNode.Type == merkletree.TypeLeaf {
						for _, leafNode := range leafNodeMap[childrenNode.Hash] {
							if leafNode.RelativePath == childrenNode.RelativePath {
								childrenNodeStatus = leafNode.NodeStatus
								break
							}
						}
					} else {
						if tmpNode, ok := relPathNodeMap[childrenNode.RelativePath]; ok {
							childrenNodeStatus = tmpNode.NodeStatus
						}
					}

					if childrenNodeStatus == "" {
						// 子节点没找到，说明已经同步
						continue
					}

					if childrenNodeStatus == definition.ActionNodeStatusPending {
						pendingNodeNum += 1
					} else if childrenNodeStatus == definition.ActionLeafNodeStatusProcessSuccess {
						// 存在一个子节点为处理成功时，代表该文件在服务端已处理成功，该文件可以写入向量数据库
						// 看其他子节点完成状态
					} else if childrenNodeStatus == definition.ActionNodeStatusNodePartialSynced {
						// 代表子节点是个目录节点，同时已经完成同步了
						// 但由于存在同步失败的文件，因此为部分同步，需要更新父节点hash
						partialSyncNodeNum += 1
					} else if childrenNodeStatus == definition.ActionNodeStatusNodeSynced {
						// 存在一个子节点为已同步时，不需要处理
						// 看其他子节点完成状态
					} else if childrenNodeStatus == definition.ActionNodeStatusDiscard {
						// 节点同步失败，记录下来
						discardNodeFilePath = append(discardNodeFilePath, childrenNode.RelativePath)
					} else {
						// 异常情况，直接该节点放弃
						discardNodeFilePath = append(discardNodeFilePath, childrenNode.RelativePath)
					}
				}

				if pendingNodeNum == 0 {
					// 已无等待同步节点，可以进行同步
					if partialSyncNodeNum > 0 || len(discardNodeFilePath) > 0 {
						node.Node.New = definition.GetNewToSyncNode(node.Node.New, discardNodeFilePath)
						node.Node.IsPartial = true
					}

					// 子节点均完成同步，同步父节点
					toUpdateServerNodes = append(toUpdateServerNodes, node)
				}
			}
		}

		executeUpdateServerNodeNum := len(toUpdateServerNodes)
		if len(toUpdateServerNodes) > 0 {
			// 有需要同步的节点
			// 分批来做，因为可能同步的节点很多，单次做可能超时
			log.Debugf("[codebase]-[vector server engine] [handle server node] to sync total %v server node", len(toUpdateServerNodes))
			for len(toUpdateServerNodes) > 0 {
				updateNodeBatchSize := definition.DefaultPerRequestMaxUpdateNotLeafNodeNum
				if len(toUpdateServerNodes) < updateNodeBatchSize {
					updateNodeBatchSize = len(toUpdateServerNodes)
				}
				batchUpdate := toUpdateServerNodes[:updateNodeBatchSize]
				toUpdateServerNodes = toUpdateServerNodes[updateNodeBatchSize:]
				batchUpdateServerNodes := make([]*definition.MTreePairNodes, 0)
				for _, node := range batchUpdate {
					batchUpdateServerNodes = append(batchUpdateServerNodes, node.Node)
					verboseLogf("[codebase]-[vector server engine] [handle server node] sync server node: %v", node.Node.New.RelativePath)
				}
				log.Debugf("[codebase]-[vector server engine] [handle server node] start to sync %v server node", len(batchUpdateServerNodes))
				handle := engine.getServerHandle()
				if handle == nil {
					log.Errorf("[codebase]-[vector server engine] [handle server node] server handle is nil")
					continue
				}
				err := handle.UpdateServerMerkelNodes(batchUpdateServerNodes)
				if err != nil {
					// 节点更新失败
					log.Errorf("[codebase]-[vector server engine] [handle server sync] sync server node err: %v", err)
					for _, node := range batchUpdate {
						node.NodeStatus = definition.ActionNodeStatusDiscard
					}
				} else {
					// 节点更新成功
					log.Debugf("[codebase]-[vector server engine] [handle server sync] sync server node success")
					for _, node := range batchUpdate {
						if node.Node.IsPartial {
							node.NodeStatus = definition.ActionNodeStatusNodePartialSynced
						} else {
							node.NodeStatus = definition.ActionNodeStatusNodeSynced
						}
					}
				}
			}

			if executeCheckStatusFileNum*3 >= definition.DefaultPerRequestMaxCheckFileStatusNum &&
				float32(executeUpdateServerNodeNum)/float32(executeCheckStatusFileNum) <= definition.DefaultSyncServerNodeTooFewNumThreshold {
				// 单次同步节点的数量太过于少，而检查的叶子节点数量又太多时，让同步协程休眠一会
				// 目标是为了减少资源的浪费，避免频繁的请求服务端
				sleepTime := 10 * time.Second
				log.Debugf("[codebase]-[vector server engine] [handle server node] too few server node to sync, sleep %.2fs", sleepTime.Seconds())
				time.Sleep(sleepTime)
			}

			continuousQueryNoUpdateNodeTimes = 0
		} else {
			continuousQueryNoUpdateNodeTimes += 1
			if continuousQueryNoUpdateNodeTimes >= definition.DefaultSyncServerNodeContinuousNoUpdateLimit {
				continuousQueryNoUpdateNodeTimes = definition.DefaultSyncServerNodeContinuousNoUpdateLimit
			}
			sleepTime := time.Second * time.Duration(continuousQueryNoUpdateNodeTimes<<1)
			log.Debugf("[codebase]-[vector server engine] [handle server node] no ready server node to sync, sleep %.2fs", sleepTime.Seconds())
			time.Sleep(sleepTime)
		}

		pendingNotLeafNodeNumber := 0
		discardNotLeafNodeNumber := 0
		syncedNotLeafNodeNumber := 0
		pendingNodes := make([]*definition.MTreePairActionNodes, 0)
		filePaths := make([]string, 0, len(pendingNodes))
		for _, node := range relPathNodeMap {
			if node.NodeStatus == definition.ActionNodeStatusPending {
				pendingNotLeafNodeNumber++
				pendingNodes = append(pendingNodes, node)
				if node.Node.New != nil {
					filePaths = append(filePaths, node.Node.New.RelativePath)
				}
			} else if node.NodeStatus == definition.ActionNodeStatusDiscard {
				discardNotLeafNodeNumber++
			} else if node.NodeStatus == definition.ActionNodeStatusNodeSynced {
				syncedNotLeafNodeNumber++
			}

		}

		if len(filePaths) <= 20 && len(filePaths) > 0 {
			log.Debugf("[codebase]-[vector server engine] [handle server node] pending node cnt: %v, details: %v", len(filePaths), strings.Join(filePaths, " | "))
		} else {
			log.Debugf("[codebase]-[vector server engine] [handle server node] pending node cnt: %v", len(filePaths))
		}

		leafNodeSumNum := pendingLeafNodeNumber + discardLeafNodeNumber + processSuccessLeafNodeNumber
		notLeafNodeSumNum := pendingNotLeafNodeNumber + discardNotLeafNodeNumber + syncedNotLeafNodeNumber
		displayData.FinishedNum = discardLeafNodeNumber + processSuccessLeafNodeNumber + discardNotLeafNodeNumber + syncedNotLeafNodeNumber
		displayData.TotalNum = leafNodeSumNum + notLeafNodeSumNum
		// 输出进度
		if source == definition.VectorFullIndexSource {
			// 全量索引输出进度条和完成banner
			displayer.Display(displayData)

		} else {
			// 增量索引只输出完成banner
			if displayData.FinishedNum >= displayData.TotalNum {
				displayer.Display(displayData)
			}
		}

		if pendingNotLeafNodeNumber == 0 && pendingLeafNodeNumber == 0 {
			// 已经没有在等待的节点了
			break
		}

		if time.Since(startTime) >= definition.DefaultMaxServerIndexTime {
			log.Warnf("[codebase]-[vector server engine] [handle server node] exceed max server index time")
			break
		}
	}
	return false
}

// HandleUploadFilesV2
// 适配新的校验文件状态V2接口
func (engine *ServerVecRetrieveEngine) HandleUploadFilesV2(leafNodes []*definition.LeafActionNodes) bool {
	if !engine.indexEnable() {
		log.Infof("[codebase]-[server vector engine] [upload files v2] workspacePath: %s, upload files is stop", engine.WorkspacePath)
		return true
	}
	// 先批量获取当前新节点的服务端文件状态，以确定需要上传哪些文件
	log.Debugf("[codebase]-[server vector engine] [upload file v2] workspacePath: %s, to upload file cnt: %v", engine.WorkspacePath, len(leafNodes))

	// 叶子节点map
	leafNodeMap := make(map[string]*definition.LeafActionNodes)
	// 待检查文件节点
	toCheckFileNodes := make([]*definition.LeafActionNodes, 0)
	for _, node := range leafNodes {
		leafNodeMap[definition.GetLeafNodeKey(node.RelativePath, node.Hash)] = node
		toCheckFileNodes = append(toCheckFileNodes, node)
	}

	// 待上传文件节点
	toUploadFileNodes := make([]*definition.LeafActionNodes, 0)
	for len(toCheckFileNodes) > 0 {
		if !engine.indexEnable() {
			log.Infof("[codebase]-[server vector engine] [upload file v2] workspacePath: %s, check file status index is stop", engine.WorkspacePath)
			return true
		}

		// 批量检查文件状态
		batchSize := definition.DefaultPerRequestMaxCheckFileStatusNum
		if len(toCheckFileNodes) < batchSize {
			batchSize = len(toCheckFileNodes)
		}

		batchCheckStatusFileNodes := toCheckFileNodes[:batchSize]
		toCheckFileNodes = toCheckFileNodes[batchSize:]

		// 不为空时，检查一次文件状态
		if len(batchCheckStatusFileNodes) >= 0 {
			fileStatuses := make([]definition.FileStatusV2, 0)
			for _, node := range batchCheckStatusFileNodes {
				fileStatuses = append(fileStatuses, definition.FileStatusV2{
					Digest:       node.Hash,
					RelativePath: node.RelativePath,
				})
			}

			verboseLogf("[codebase]-[server vector engine] [upload files v2] check file status cnt: %v", len(fileStatuses))
			// 执行check文件状态
			response, err := components.CheckServerFileStatusV2(fileStatuses)
			if err != nil || response == nil {
				// 失败时，丢弃这些文件
				for _, node := range batchCheckStatusFileNodes {
					node.NodeStatus = definition.ActionNodeStatusDiscard
				}
				log.Errorf("[codebase]-[vector server engine] [upload files v2] workspacePath: %s, check file status failed: %v", engine.WorkspacePath, err)
			} else {
				checkFileNodeMap := make(map[string]*definition.LeafActionNodes)
				for _, node := range batchCheckStatusFileNodes {
					checkFileNodeMap[definition.GetLeafNodeKey(node.RelativePath, node.Hash)] = node
				}

				for _, fileStatus := range response.FileStatuses {
					fileNodeKey := definition.GetLeafNodeKey(fileStatus.RelativePath, fileStatus.Digest)
					fileNode, ok := checkFileNodeMap[fileNodeKey]
					if !ok {
						continue
					}

					// 取一个节点删除一个节点，防止服务端漏返回
					delete(checkFileNodeMap, fileNodeKey)
					if fileStatus.Status == definition.ServerFileStatusNotSynced {
						// 说明此时文件未同步，需要上传文件
						toUploadFileNodes = append(toUploadFileNodes, fileNode)
					} else if fileStatus.Status == definition.ServerFileStatusPending {
						// 说明此时文件已上传，服务端正在处理，不需要重复上传文件
						engine.StorageFileNumber.Add(1)
					} else if fileStatus.Status == definition.ServerFileStatusSynced {
						// 说明此时文件已处理完毕，直接将节点状态置为文件处理成功
						fileNode.NodeStatus = definition.ActionLeafNodeStatusProcessSuccess
						engine.StorageFileNumber.Add(1)
					} else {
						// 兜底逻辑，丢弃该文件
						fileNode.NodeStatus = definition.ActionNodeStatusDiscard
					}
				}

				// 对于仍在map中的节点，直接置为丢弃
				for _, node := range checkFileNodeMap {
					node.NodeStatus = definition.ActionNodeStatusDiscard
				}
			}
		}
	}

	log.Debugf("[codebase]-[server vector engine] [upload files v2] to upload file cnt: %v", len(toUploadFileNodes))

	batchUploadFileNodes := make([]*definition.LeafActionNodes, 0)
	// 开始上传文件
	uploadFileSizeSum := 0
	reachedMaxUploadFileSize := false
	totalFileCnt := len(toUploadFileNodes)
	for len(toUploadFileNodes) > 0 {
		if !engine.indexEnable() {
			log.Infof("[codebase]-[server vector engine] [upload files] workspacePath: %s, upload file is stop", engine.WorkspacePath)
			return true
		}
		if len(toUploadFileNodes) > 0 {
			// 查看当前上传队列 uploadBatchFileIds 是否仍然能够添加文件
			fileNode := toUploadFileNodes[0]
			filePath := filepath.Join(engine.WorkspacePath, fileNode.RelativePath)
			stat, err := os.Stat(filePath)
			if err != nil {
				// 文件出错
				toUploadFileNodes = toUploadFileNodes[1:]
				fileNode.NodeStatus = definition.ActionNodeStatusDiscard
				continue
			}

			fileSize := int(stat.Size())
			if fileSize > definition.DefaultMaxMTreeFileSize || fileSize <= definition.DefaultMinMTreeFileSize {
				// 单文件太大，直接跳过
				// 单文件太小，没有价值，直接跳过
				toUploadFileNodes = toUploadFileNodes[1:]
				fileNode.NodeStatus = definition.ActionNodeStatusDiscard
				continue
			}

			if fileSize+uploadFileSizeSum < definition.DefaultPerRequestMaxUploadFileSize {
				// 添加文件后，依然小于最大上传文件限制，则取出文件添加到上传队列中
				batchUploadFileNodes = append(batchUploadFileNodes, fileNode)
				uploadFileSizeSum += fileSize
				toUploadFileNodes = toUploadFileNodes[1:]
			} else {
				reachedMaxUploadFileSize = true
			}
		}

		if int(engine.StorageFileNumber.Load()) >= global.GetMaxServerStorageFileNum() {
			log.Infof("[codebase]-[server vector engine] [upload files] workspacePath: %s, reached max storage file num, break", engine.WorkspacePath)
			// 达到服务端存储上限,剩余文件不再上传
			break
		}

		if reachedMaxUploadFileSize ||
			len(batchUploadFileNodes) >= definition.DefaultPerRequestMaxUploadFileNum ||
			len(toUploadFileNodes) == 0 {
			// reachedMaxUploadFileSize 代表到达了这一批上传文件的大小上限
			// len(toUploadFileIds) == 0 代表此时已经是最后一批上传的文件了
			// len(uploadBatchFileIds) >= definition.DefaultPerRequestMaxUploadFileNum 代表这一批上传文件的数量已达到上限
			var toUploadFilePaths []string
			for _, fileNode := range batchUploadFileNodes {
				filePath := filepath.Join(engine.WorkspacePath, fileNode.RelativePath)
				toUploadFilePaths = append(toUploadFilePaths, filePath)
			}
			batchUploadFileNodes = make([]*definition.LeafActionNodes, 0)
			uploadFileSizeSum = 0
			reachedMaxUploadFileSize = false
			log.Debugf("[codebase]-[vector server engine] [upload files v2] workspacePath: %s, upload cnt: %v", engine.WorkspacePath, len(toUploadFilePaths))
			handle := engine.getServerHandle()
			if handle == nil {
				log.Errorf("[codebase]-[server vector engine] [upload files v2] workspacePath: %s, server handle is nil", engine.WorkspacePath)
				continue
			}
			codebaseId, err := handle.GetCodebaseId()
			if err != nil {
				log.Errorf("[codebase]-[server vector engine] [upload files v2] workspacePath: %s, get codebase id failed: %v", engine.WorkspacePath, err)
				continue
			}
			response, err := components.UploadFileToEmbedding(engine.WorkspacePath, toUploadFilePaths, totalFileCnt, codebaseId)
			if err != nil || response == nil {
				// 失败则丢弃这一批文件
				log.Errorf("[codebase]-[vector server engine] [upload files v2] workspacePath: %s, error: %v", engine.WorkspacePath, err)
			} else {
				uploadFileNodeMap := make(map[string]*definition.LeafActionNodes)
				for _, node := range batchUploadFileNodes {
					uploadFileNodeMap[definition.GetLeafNodeKey(node.RelativePath, node.Hash)] = node
				}

				for _, result := range response.Results {
					if result.Success {
						// 文件上传成功
						engine.StorageFileNumber.Add(int32(1))
					} else {
						// 文件上传失败
						// 或者未从response找到该文件
						// 直接丢弃，等待下次索引
						fileNode, ok := uploadFileNodeMap[definition.GetLeafNodeKey(result.FilePath, result.FileId)]
						if ok {
							fileNode.NodeStatus = definition.ActionNodeStatusDiscard
						}

						filePath := filepath.Join(engine.WorkspacePath, result.FilePath)
						log.Errorf("[codebase]-[vector server engine] [upload files v2] workspacePath: %s, filePath: %s, error: %v", engine.WorkspacePath, filePath, result.Error)
					}
				}
			}
		}
	}

	return false
}

func (engine *ServerVecRetrieveEngine) HandleSyncServerNodesV2(relPathNodeMap map[string]*definition.MTreePairActionNodes, leafNodes []*definition.LeafActionNodes, source string) bool {
	if !engine.indexEnable() {
		log.Infof("[codebase]-[server vector engine] [handle server nodes v2] workspacePath: %s, handle sync node index is stop", engine.WorkspacePath)
		return true
	}
	// 叶子节点map
	leafNodeMap := make(map[string]*definition.LeafActionNodes, len(leafNodes))
	// 待检查文件节点
	toCheckFileNodes := make([]*definition.LeafActionNodes, 0)
	for _, node := range leafNodes {
		leafNodeMap[definition.GetLeafNodeKey(node.RelativePath, node.Hash)] = node
		toCheckFileNodes = append(toCheckFileNodes, node)
	}

	for _, node := range toCheckFileNodes {
		verboseLogf("[codebase]-[vector server engine] [handle server node v2] to sync leaf: %v", node.RelativePath)
	}

	toSyncLeafNodeCnt := len(leafNodes)
	log.Debugf("[codebase]-[vector server engine] [handle server node v2] workspacePath: %s, %v leaf node waiting to sync", engine.WorkspacePath, toSyncLeafNodeCnt)

	startTime := time.Now()
	continuousQueryNoUpdateNodeTimes := 0
	// 检查服务端文件状态，目的是拿到需要上传的文件
	displayer := NewIndexDisplayer()
	displayData := DisplayData{
		WorkspacePath: engine.WorkspacePath,
		FinishedNum:   0,
		TotalNum:      0,
		OutputMode:    DisplayOutputModeLog,
	}

	if source == definition.VectorFullIndexSource {
		displayData.IndexType = DisplayIndexTypeServerVectorFull
	} else {
		displayData.IndexType = DisplayIndexTypeServerVectorIncremental
	}

	for {
		if !engine.indexEnable() {
			log.Infof("[codebase]-[server vector engine] [handle new nodes v2] workspacePath: %s, handle sync node index is stop", engine.WorkspacePath)
			return true
		}

		batchSize := definition.DefaultPerRequestMaxCheckFileStatusNum
		if len(toCheckFileNodes) < batchSize {
			batchSize = len(toCheckFileNodes)
		}

		batchCheckStatusFileNodes := toCheckFileNodes[:batchSize]
		toCheckFileNodes = toCheckFileNodes[batchSize:]
		executeCheckStatusFileNum := len(batchCheckStatusFileNodes)
		// 不为空时，检查一次文件状态
		if len(batchCheckStatusFileNodes) > 0 {
			if !engine.indexEnable() {
				log.Infof("[codebase]-[server vector engine] [handle new nodes v2] workspacePath: %s, handle sync node index is stop", engine.WorkspacePath)
				return true
			}

			for _, node := range batchCheckStatusFileNodes {
				verboseLogf("[codebase]-[vector server engine] [handle server node v2] to check filepath: %v", node.RelativePath)
			}

			fileStatuses := make([]definition.FileStatusV2, 0)
			for _, node := range batchCheckStatusFileNodes {
				fileStatuses = append(fileStatuses, definition.FileStatusV2{
					Digest:       node.Hash,
					RelativePath: node.RelativePath,
				})
			}
			// 执行check文件状态
			log.Debugf("[codebase]-[vector server engine] [handle server node v2] workspacePath: %s, check file status cnt: %v", engine.WorkspacePath, len(batchCheckStatusFileNodes))
			response, err := components.CheckServerFileStatusV2(fileStatuses)
			if err != nil || response == nil {
				// 失败代表这些节点全部discard
				for _, node := range batchCheckStatusFileNodes {
					node.NodeStatus = definition.ActionNodeStatusDiscard
				}
				log.Errorf("[codebase]-[vector server engine] [handle server node v2] workspacePath: %s, check file status failed: %v", engine.WorkspacePath, err)
			} else {
				checkFileNodeMap := make(map[string]*definition.LeafActionNodes)
				for _, node := range batchCheckStatusFileNodes {
					checkFileNodeMap[definition.GetLeafNodeKey(node.RelativePath, node.Hash)] = node
				}

				for _, fileStatus := range response.FileStatuses {
					fileNodeKey := definition.GetLeafNodeKey(fileStatus.RelativePath, fileStatus.Digest)
					fileNode, ok := checkFileNodeMap[fileNodeKey]
					if !ok {
						continue
					}

					verboseLogf("[codebase]-[vector server engine] [handle server node v2] check fileId: %v, filePaths: %v, file status: %v", fileNode.Hash, fileNode.RelativePath, fileStatus.Status)
					// 取一个节点删除一个节点，防止服务端漏返回
					delete(checkFileNodeMap, fileNodeKey)
					if fileStatus.Status == definition.ServerFileStatusNotSynced {
						// 此前文件已经全部上传完毕
						// 说明此时文件未上传，直接将该节点设置为丢弃
						fileNode.NodeStatus = definition.ActionNodeStatusDiscard
					} else if fileStatus.Status == definition.ServerFileStatusPending {
						// 说明此时文件正在处理，等待即可
						// 仍然需要将该文件放回，下次继续check
						toCheckFileNodes = append(toCheckFileNodes, fileNode)
					} else if fileStatus.Status == definition.ServerFileStatusSynced {
						// 说明此时文件已处理完毕，直接将节点状态置为文件处理成功
						fileNode.NodeStatus = definition.ActionLeafNodeStatusProcessSuccess
					} else {
						// 兜底逻辑，丢弃该文件
						fileNode.NodeStatus = definition.ActionNodeStatusDiscard
					}
				}

				// 对于仍在map中的节点，直接置为丢弃
				for _, node := range checkFileNodeMap {
					node.NodeStatus = definition.ActionNodeStatusDiscard
				}
			}
		} else {
			log.Debugf("[codebase]-[vector server engine] [handle server node v2] workspacePath: %s, no file to check", engine.WorkspacePath)
		}

		// 获取pending leaf节点状态
		pendingLeafNodeNumber := 0
		discardLeafNodeNumber := 0
		processSuccessLeafNodeNumber := 0
		for _, node := range leafNodeMap {
			if node.NodeStatus == definition.ActionNodeStatusPending {
				pendingLeafNodeNumber++
			} else if node.NodeStatus == definition.ActionLeafNodeStatusProcessSuccess {
				processSuccessLeafNodeNumber++
			} else if node.NodeStatus == definition.ActionNodeStatusDiscard {
				discardLeafNodeNumber++
			}
		}
		log.Debugf("[codebase]-[vector server engine] [handle server node v2] workspacePath: %s, pending leaf node number: %v, discard leaf node number: %v, process success leaf node number: %v", engine.WorkspacePath, pendingLeafNodeNumber, discardLeafNodeNumber, processSuccessLeafNodeNumber)

		// 待更新服务端节点
		toUpdateServerNodes := make([]*definition.MTreePairActionNodes, 0)
		// 更改可同步服务端非叶子节点状态
		for _, node := range relPathNodeMap {
			if node.Node.New == nil {
				// 删除节点，不需要特殊处理
				continue
			}

			if node.NodeStatus == definition.ActionNodeStatusPending && node.Node.New != nil {
				pendingNodeNum := 0
				discardNodeFilePath := make([]string, 0)
				partialSyncNodeNum := 0
				for _, childrenNode := range node.Node.New.Children {
					childrenNodeStatus := ""
					if childrenNode.Type == merkletree.TypeLeaf {
						leafNode, ok := leafNodeMap[definition.GetLeafNodeKey(childrenNode.RelativePath, childrenNode.Hash)]
						if ok {
							childrenNodeStatus = leafNode.NodeStatus
						}
					} else {
						if tmpNode, ok := relPathNodeMap[childrenNode.RelativePath]; ok {
							childrenNodeStatus = tmpNode.NodeStatus
						}
					}

					if childrenNodeStatus == "" {
						// 子节点没找到，说明已经同步
						continue
					}

					if childrenNodeStatus == definition.ActionNodeStatusPending {
						pendingNodeNum += 1
					} else if childrenNodeStatus == definition.ActionLeafNodeStatusProcessSuccess {
						// 存在一个子节点为处理成功时，代表该文件在服务端已处理成功，该文件可以写入向量数据库
						// 看其他子节点完成状态
					} else if childrenNodeStatus == definition.ActionNodeStatusNodePartialSynced {
						// 代表子节点是个目录节点，同时已经完成同步了
						// 但由于存在同步失败的文件，因此为部分同步，需要更新父节点hash
						partialSyncNodeNum += 1
					} else if childrenNodeStatus == definition.ActionNodeStatusNodeSynced {
						// 存在一个子节点为已同步时，不需要处理
						// 看其他子节点完成状态
					} else if childrenNodeStatus == definition.ActionNodeStatusDiscard {
						// 节点同步失败，记录下来
						discardNodeFilePath = append(discardNodeFilePath, childrenNode.RelativePath)
					} else {
						// 异常情况，直接该节点放弃
						discardNodeFilePath = append(discardNodeFilePath, childrenNode.RelativePath)
					}
				}

				if pendingNodeNum == 0 {
					// 已无等待同步节点，可以进行同步
					if partialSyncNodeNum > 0 || len(discardNodeFilePath) > 0 {
						node.Node.New = definition.GetNewToSyncNode(node.Node.New, discardNodeFilePath)
						node.Node.IsPartial = true
					}

					// 子节点均完成同步，同步父节点
					toUpdateServerNodes = append(toUpdateServerNodes, node)
				}
			}
		}

		executeUpdateServerNodeNum := len(toUpdateServerNodes)
		if len(toUpdateServerNodes) > 0 {
			// 有需要同步的节点
			// 分批来做，因为可能同步的节点很多，单次做可能超时
			log.Debugf("[codebase]-[vector server engine] [handle server node v2] workspacePath: %s, to sync total %v server node", engine.WorkspacePath, len(toUpdateServerNodes))
			for len(toUpdateServerNodes) > 0 {
				if !engine.indexEnable() {
					log.Infof("[codebase]-[server vector engine] [handle new nodes v2] workspacePath: %s, handle sync node index is stop", engine.WorkspacePath)
					return true
				}

				var batchServerNodes []*definition.MTreePairActionNodes
				notLeafNodeBatchSize := definition.DefaultPerRequestMaxUpdateNotLeafNodeNum
				leafNodeBatchSize := definition.DefaultPerRequestMaxUpdateLeafNodeNum
				chunkBatchSize := definition.DefaultPerRequestMaxUpdateChunkNum
				totalLeafNodeNum := 0
				chunkNum := 0
				for len(batchServerNodes) < notLeafNodeBatchSize &&
					totalLeafNodeNum < leafNodeBatchSize &&
					chunkNum < chunkBatchSize &&
					len(toUpdateServerNodes) > 0 {
					toUpdateNode := toUpdateServerNodes[0]
					totalLeafNodeNum = GetSyncLeafNodeNumber(totalLeafNodeNum, toUpdateNode)
					chunkNum = GetSyncLeafNodeChunkNumber(engine.WorkspacePath, chunkNum, toUpdateNode)
					batchServerNodes = append(batchServerNodes, toUpdateNode)
					toUpdateServerNodes = toUpdateServerNodes[1:]
					verboseLogf("[codebase]-[vector server engine] [handle server node v2] sync server node: %v", toUpdateNode.Node.New.RelativePath)
				}
				log.Debugf("[codebase]-[vector server engine] [handle server node v2] workspacePath: %s, start to sync %v server node", engine.WorkspacePath, len(batchServerNodes))
				handle := engine.getServerHandle()
				if handle == nil {
					log.Errorf("[codebase]-[vector server engine] [handle server node v2] workspacePath: %s, server handle is nil", engine.WorkspacePath)
					// handle获取为空，丢弃这一批节点
					for _, node := range batchServerNodes {
						node.NodeStatus = definition.ActionNodeStatusDiscard
					}
					continue
				}
				toUpdateNodes := make([]*definition.MTreePairNodes, 0)
				for _, node := range batchServerNodes {
					toUpdateNodes = append(toUpdateNodes, node.Node)
				}
				err := handle.UpdateServerMerkelNodes(toUpdateNodes)
				if err != nil {
					// 节点更新失败
					log.Errorf("[codebase]-[vector server engine] [handle server node v2] workspacePath: %s, sync server node err: %v", engine.WorkspacePath, err)
					for _, node := range batchServerNodes {
						node.NodeStatus = definition.ActionNodeStatusDiscard
					}
				} else {
					// 节点更新成功
					log.Debugf("[codebase]-[vector server engine] [handle server node v2] workspacePath: %s, sync server node success", engine.WorkspacePath)
					for _, node := range batchServerNodes {
						if node.Node.IsPartial {
							node.NodeStatus = definition.ActionNodeStatusNodePartialSynced
						} else {
							node.NodeStatus = definition.ActionNodeStatusNodeSynced
						}
					}
				}
			}

			if executeCheckStatusFileNum*3 >= definition.DefaultPerRequestMaxCheckFileStatusNum &&
				float32(executeUpdateServerNodeNum)/float32(executeCheckStatusFileNum) <= definition.DefaultSyncServerNodeTooFewNumThreshold {
				// 单次同步节点的数量太过于少，而检查的叶子节点数量又太多时，让同步协程休眠一会
				// 目标是为了减少资源的浪费，避免频繁的请求服务端
				sleepTime := 10 * time.Second
				log.Debugf("[codebase]-[vector server engine] [handle server node v2] workspacePath: %s, too few server node to sync, sleep %.2fs", engine.WorkspacePath, sleepTime.Seconds())
				time.Sleep(sleepTime)
			}

			continuousQueryNoUpdateNodeTimes = 0
		} else {
			continuousQueryNoUpdateNodeTimes += 1
			if continuousQueryNoUpdateNodeTimes >= definition.DefaultSyncServerNodeContinuousNoUpdateLimit {
				continuousQueryNoUpdateNodeTimes = definition.DefaultSyncServerNodeContinuousNoUpdateLimit
			}
			sleepTime := time.Second * time.Duration(continuousQueryNoUpdateNodeTimes<<1)
			log.Debugf("[codebase]-[vector server engine] [handle server node v2] workspacePath: %s, no ready server node to sync, sleep %.2fs", engine.WorkspacePath, sleepTime.Seconds())
			time.Sleep(sleepTime)
		}

		pendingNotLeafNodeNumber := 0
		discardNotLeafNodeNumber := 0
		syncedNotLeafNodeNumber := 0
		pendingNodes := make([]*definition.MTreePairActionNodes, 0)
		filePaths := make([]string, 0, len(pendingNodes))
		for _, node := range relPathNodeMap {
			if node.NodeStatus == definition.ActionNodeStatusPending {
				pendingNotLeafNodeNumber++
				pendingNodes = append(pendingNodes, node)
				if node.Node.New != nil {
					filePaths = append(filePaths, node.Node.New.RelativePath)
				}
			} else if node.NodeStatus == definition.ActionNodeStatusDiscard {
				discardNotLeafNodeNumber++
			} else if node.NodeStatus == definition.ActionNodeStatusNodeSynced {
				syncedNotLeafNodeNumber++
			}
		}

		if len(filePaths) <= 20 && len(filePaths) > 0 {
			log.Debugf("[codebase]-[vector server engine] [handle server node v2] workspacePath: %s, pending node cnt: %v, details: %v", engine.WorkspacePath, len(filePaths), strings.Join(filePaths, " | "))
		} else {
			log.Debugf("[codebase]-[vector server engine] [handle server node v2] workspacePath: %s, pending node cnt: %v", engine.WorkspacePath, len(filePaths))
		}

		leafNodeSumNum := pendingLeafNodeNumber + discardLeafNodeNumber + processSuccessLeafNodeNumber
		notLeafNodeSumNum := pendingNotLeafNodeNumber + discardNotLeafNodeNumber + syncedNotLeafNodeNumber
		displayData.FinishedNum = discardLeafNodeNumber + processSuccessLeafNodeNumber + discardNotLeafNodeNumber + syncedNotLeafNodeNumber
		displayData.TotalNum = leafNodeSumNum + notLeafNodeSumNum
		// 输出进度
		if source == definition.VectorFullIndexSource {
			// 全量索引输出进度条和完成banner
			displayer.Display(displayData)
		} else {
			// 增量索引只输出完成banner
			if displayData.FinishedNum >= displayData.TotalNum {
				displayer.Display(displayData)
			}
		}

		if pendingNotLeafNodeNumber == 0 && pendingLeafNodeNumber == 0 {
			// 已经没有在等待的节点了
			break
		}

		if time.Since(startTime) >= definition.DefaultMaxServerIndexTime {
			log.Warnf("[codebase]-[vector server engine] [handle server node v2] workspacePath: %s, exceed max server index time", engine.WorkspacePath)
			break
		}
	}
	return false
}

func GetSyncLeafNodeNumber(leafNodeNumber int, node *definition.MTreePairActionNodes) int {
	if node == nil || node.Node == nil || node.Node.New == nil {
		return leafNodeNumber
	}

	for _, child := range node.Node.New.Children {
		if child.Type == merkletree.TypeLeaf {
			leafNodeNumber++
		}
	}

	return leafNodeNumber
}

func GetSyncLeafNodeChunkNumber(workspacePath string, chunkNumber int, node *definition.MTreePairActionNodes) int {
	if node == nil || node.Node == nil || node.Node.New == nil {
		return chunkNumber
	}

	for _, child := range node.Node.New.Children {
		if child.Type == merkletree.TypeLeaf {
			filePath := filepath.Join(workspacePath, child.RelativePath)
			stat, err := os.Stat(filePath)
			if err != nil {
				continue
			}
			fileSize := int(stat.Size())
			chunkNumber += tokenizer.GetChunkCountWithFileSize(fileSize)
		}
	}

	return chunkNumber
}

// GetActionNodes
// 使用批量查询来返回服务端与客户端的节点diff集合
// 返回需要进行操作的节点集合，服务端已存储文件数量
func (engine *ServerVecRetrieveEngine) GetActionNodes(virtualFiles []definition.VirtualFile, source string) (*definition.DiffNodeActions, error) {
	clientOldTree := tree.NewWorkspaceMerkleTree(engine.WorkspacePath)
	if clientOldTree == nil {
		return nil, errors.New("get client old tree is nil")
	}
	clientMTree := clientOldTree.Clone()

	handle := engine.getServerHandle()
	if handle.IsNew {
		defer func() {
			handle.IsNew = false
		}()

		actionNodes := &definition.DiffNodeActions{
			AddNodes:      make([]*definition.MTreePairNodes, 0),
			UpdateNodes:   make([]*definition.MTreePairNodes, 0),
			DeleteNodes:   make([]*definition.MTreePairNodes, 0),
			LeafNodes:     make([]*definition.MTreeActionNodeMeta, 0),
			ServerFileNum: 0,
		}
		// 0. 全新库，直接将所有节点都加入add
		err := clientMTree.Tree.Iter(func(node *merkletree.MerkleNode) error {
			if node.Type != merkletree.TypeLeaf {
				childrenNodeMetas := make([]definition.MTreeActionNodeMeta, 0)
				for _, child := range node.Children {
					childrenNodeMetas = append(childrenNodeMetas, definition.MTreeActionNodeMeta{
						Hash:         child.Hash,
						RelativePath: child.RelativePath,
						Type:         child.Type,
					})
				}

				actionNodes.AddNodes = append(actionNodes.AddNodes, &definition.MTreePairNodes{
					New: &definition.MTreeActionNode{
						MTreeActionNodeMeta: definition.MTreeActionNodeMeta{
							Hash:         node.Hash,
							RelativePath: node.RelativePath,
							Type:         node.Type,
						},
						Children: childrenNodeMetas,
					},
					Old:       nil,
					IsPartial: false,
				})
			} else {
				actionNodes.LeafNodes = append(actionNodes.LeafNodes,
					&definition.MTreeActionNodeMeta{
						Hash:         node.Hash,
						RelativePath: node.RelativePath,
						Type:         node.Type,
					},
				)
			}

			return nil
		})

		if err != nil {
			log.Errorf("[codebase]-[vector server engine] [get action nodes] workspacePath: %s, new codebase iter client tree error: %v", engine.WorkspacePath, err)
			return nil, err
		}

		return actionNodes, nil
	}

	toQueryNodes := collection.NewQueue[*merkletree.MerkleNode]()
	if source == definition.VectorFullIndexSource {
		// 全量索引
		// 1.将所有客户端树非叶子节点入队
		err := clientMTree.Tree.Iter(func(node *merkletree.MerkleNode) error {
			if node.Type != merkletree.TypeLeaf {
				toQueryNodes.Enqueue(node)
			}
			return nil
		})
		log.Debugf("[codebase]-[vector server engine] [full handle server node] workspacePath: %s, to query nodes: %v", engine.WorkspacePath, toQueryNodes.Size())
		if err != nil {
			log.Errorf("[codebase]-[vector server engine] [full handle server node] workspacePath: %s, iter client tree error: %v", engine.WorkspacePath, err)
			return nil, err
		}
	} else {
		// 增量索引
		nodeMap := make(map[string]*merkletree.MerkleNode)
		// 1.将所有传入的文件节点的父节点入队
		// 2.将所有传入的非文件节点入队
		for _, virtualFile := range virtualFiles {
			lastRelFilePath, err := filepath.Rel(engine.WorkspacePath, virtualFile.GetFilePath())
			if err != nil {
				log.Errorf("[codebase]-[vector server engine] [increment handle server node] filepath: %s, workspacePath: %s, get relative path error: %v", virtualFile.GetFilePath(), engine.WorkspacePath, err)
			} else {
				// 递归向上查询该节点的父节点
				for {
					relFilePath := filepath.Dir(lastRelFilePath)
					if relFilePath == "" ||
						lastRelFilePath == relFilePath {
						break
					}

					if _, ok := nodeMap[relFilePath]; !ok {
						parentNode := clientMTree.GetNode(relFilePath)
						if parentNode == nil {
							nodeType := merkletree.TypeNode
							if relFilePath == "." {
								nodeType = merkletree.TypeRoot
							}
							parentNode = &merkletree.MerkleNode{
								RelativePath: relFilePath,
								Hash:         "",
								Type:         nodeType,
								Children:     nil,
							}
						}

						toQueryNodes.Enqueue(parentNode)
						nodeMap[relFilePath] = parentNode
					}

					if relFilePath == "." {
						break
					}

					lastRelFilePath = relFilePath
				}
			}

			stat, err := os.Stat(virtualFile.GetFilePath())
			if err != nil {
				if os.IsNotExist(err) {
					// 文件不存在时，代表其为删除操作，且已一定为目录节点
					// 直接添加节点检查即可
					filePath := virtualFile.GetFilePath()
					var relativePath string
					if filePath == engine.WorkspacePath {
						relativePath = "."
					} else {
						trimPath := engine.WorkspacePath
						if !strings.HasSuffix(trimPath, string(os.PathSeparator)) {
							trimPath += string(os.PathSeparator)
						}
						relativePath = strings.TrimPrefix(filePath, trimPath)
					}
					if _, ok := nodeMap[relativePath]; !ok {
						newNode := &merkletree.MerkleNode{
							RelativePath: relativePath,
							Hash:         "",
							Type:         merkletree.TypeNode,
							Children:     nil,
						}
						toQueryNodes.Enqueue(newNode)
						nodeMap[relativePath] = newNode
					}
				}
			} else {
				var dirFilePath string
				if stat.IsDir() {
					dirFilePath = virtualFile.GetFilePath()
				} else {
					dirFilePath = filepath.Dir(virtualFile.GetFilePath())
				}

				// 拿到该节点对应的所有子目录节点，并入队
				parentNode := clientMTree.GetNode(dirFilePath)
				if parentNode != nil {
					parentNodeQueue := collection.NewQueue[*merkletree.MerkleNode]()
					parentNodeQueue.Enqueue(parentNode)
					for !parentNodeQueue.IsEmpty() {
						node, ok := parentNodeQueue.Dequeue()
						if !ok || node == nil {
							continue
						}

						if node.Type != merkletree.TypeLeaf {
							// 非叶子节点，入队
							if _, ok := nodeMap[node.RelativePath]; !ok {
								toQueryNodes.Enqueue(node)
								nodeMap[node.RelativePath] = node
							}

							for _, childrenNode := range node.Children {
								if childrenNode == nil {
									continue
								}

								if childrenNode.Type != merkletree.TypeLeaf {
									parentNodeQueue.Enqueue(childrenNode)
								}
							}
						}
					}
				}
			}

		}

		log.Debugf("[codebase]-[vector server engine] [increment handle server node] workspacePath: %s, to query nodes: %v", engine.WorkspacePath, toQueryNodes.Size())
	}

	return engine.GetServerAllDiffNodes(clientMTree, toQueryNodes)
}

// GetServerAllDiffNodes
// 服务端全量索引使用
// 处理add update delete操作
func (engine *ServerVecRetrieveEngine) GetServerAllDiffNodes(clientTree *tree.MerkleTree, toQueryNodes *collection.Queue[*merkletree.MerkleNode]) (*definition.DiffNodeActions, error) {
	actionNodes := &definition.DiffNodeActions{
		AddNodes:      make([]*definition.MTreePairNodes, 0),
		UpdateNodes:   make([]*definition.MTreePairNodes, 0),
		DeleteNodes:   make([]*definition.MTreePairNodes, 0),
		LeafNodes:     make([]*definition.MTreeActionNodeMeta, 0),
		ServerFileNum: 0,
	}

	// 索引时要用到这个，查询到相同节点后，不再向下查询
	hashEqualRelMap := make(map[string]struct{})
	judgeSkipChildNode := func(relFilePath string) bool {
		for {
			if _, ok := hashEqualRelMap[relFilePath]; ok {
				return true
			}

			relFilePath = path.Dir(relFilePath)
			if relFilePath == "." {
				break
			}
		}

		if _, ok := hashEqualRelMap[relFilePath]; ok {
			return true
		}

		return false
	}

	// 2. 批量出队进行查询
	for {
		if !engine.indexEnable() {
			return nil, nil
		}

		queryNodes := make([]*merkletree.MerkleNode, 0)
		for len(queryNodes) < definition.DefaultPerRequestMaxQueryNodeNum &&
			!toQueryNodes.IsEmpty() {
			node, ok := toQueryNodes.Dequeue()
			if ok && node != nil {
				if judgeSkipChildNode(node.RelativePath) {
					continue
				}
				queryNodes = append(queryNodes, node)
			}
		}

		if len(queryNodes) == 0 && toQueryNodes.IsEmpty() {
			break
		}

		nodePaths := make([]string, 0)
		for _, node := range queryNodes {
			verboseLogf("[codebase]-[server vector engine] [batch get tree] query node: %s", node.RelativePath)
			nodePaths = append(nodePaths, node.RelativePath)
		}

		handle := engine.getServerHandle()
		if handle == nil {
			log.Errorf("[codebase]-[server vector engine] [batch get tree] workspacePath: %s, server handle is nil", engine.WorkspacePath)
			return nil, errors.New("server handle is nil")
		}

		serverNodes, err := handle.BatchGetServerMerkelNodes(nodePaths)
		if err != nil {
			log.Errorf("[codebase]-[server vector engine] [batch get tree] workspacePath: %s, error: %v", engine.WorkspacePath, err)
			return nil, err
		}

		serverNodeMap := make(map[string]*merkletree.MerkleNode)
		for _, serverNode := range serverNodes.ServerNodes {
			serverNodeMap[serverNode.RelativePath] = serverNode
		}

		// 服务端节点的多种情况
		// 0.服务端不存在，客户端也不存在
		// 1.服务端不存在，客户端存在
		// 2.服务端存在，客户端不存在
		// 3.服务端存在，客户端存在，hash不一致
		// 4.服务端存在，客户端存在，hash一致
		for _, relFilePath := range nodePaths {
			// 获取到客户端节点
			clientNode := clientTree.Tree.FindNode(relFilePath)
			if serverNode, ok := serverNodeMap[relFilePath]; !ok || serverNode == nil {
				// 服务端不存在
				if clientNode == nil {
					// 0.服务端不存在，客户端也不存在
					// 说明出了大问题，查到了了不得的节点
					log.Errorf("[codebase]-[server vector engine] [batch get tree] workspacePath: %s, client node is nil, server node is nil, relFilePath: %v", engine.WorkspacePath, relFilePath)
				} else {
					// 1.服务端不存在，客户端存在
					childrenNodeMetas := make([]definition.MTreeActionNodeMeta, 0)
					for _, child := range clientNode.Children {
						childrenNodeMetas = append(childrenNodeMetas, definition.MTreeActionNodeMeta{
							Hash:         child.Hash,
							RelativePath: child.RelativePath,
							Type:         child.Type,
						})
					}

					// 针对当前查询节点，执行add操作
					actionNodes.AddNodes = append(actionNodes.AddNodes, &definition.MTreePairNodes{
						New: &definition.MTreeActionNode{
							MTreeActionNodeMeta: definition.MTreeActionNodeMeta{
								Hash:         clientNode.Hash,
								RelativePath: clientNode.RelativePath,
								Type:         clientNode.Type,
							},
							Children: childrenNodeMetas,
						},
						Old:       nil,
						IsPartial: false,
					})

					// 对该节点的直接子节点中的叶子结点，执行add操作
					// 继续向下处理客户端节点
					for _, childrenNode := range clientNode.Children {
						if childrenNode == nil {
							continue
						}
						// 只需要递归一层客户端节点
						if childrenNode.Type == merkletree.TypeLeaf {
							// 叶子节点，应当check
							// 服务端不存储叶子结点
							actionNodes.LeafNodes = append(actionNodes.LeafNodes,
								&definition.MTreeActionNodeMeta{
									Hash:         childrenNode.Hash,
									RelativePath: childrenNode.RelativePath,
									Type:         childrenNode.Type,
								},
							)
						} else {
							// 非叶子节点，递归查询服务端
							// 队列中已经存在，因此不需要操作
						}
					}
				}
			} else {
				// 服务端存在
				if clientNode == nil {
					// 2.服务端存在，客户端不存在
					// 客户端不存在的节点，服务端需要执行delete操作
					childrenNodeMetas := make([]definition.MTreeActionNodeMeta, 0)
					for _, child := range serverNode.Children {
						childrenNodeMetas = append(childrenNodeMetas, definition.MTreeActionNodeMeta{
							Hash:         child.Hash,
							RelativePath: child.RelativePath,
							Type:         child.Type,
						})
					}

					actionNodes.DeleteNodes = append(actionNodes.DeleteNodes, &definition.MTreePairNodes{
						New: nil,
						Old: &definition.MTreeActionNode{
							MTreeActionNodeMeta: definition.MTreeActionNodeMeta{
								Hash:         serverNode.Hash,
								RelativePath: serverNode.RelativePath,
								Type:         serverNode.Type,
							},
							Children: childrenNodeMetas,
						},
						IsPartial: false,
					})

					// 待删除节点的子节点，如果是目录节点，则需要递归查询
					for _, child := range serverNode.Children {
						if child.Type != merkletree.TypeLeaf {
							// 待删除子节点中如果有非叶子结点，需要递归查询
							toQueryNodes.Enqueue(child)
						} else {
							// 待删除的叶子结点加入ActionNodes，服务端会删除向量
						}
					}
				} else {
					// 服务端存在，客户端存在
					if clientNode.Hash != serverNode.Hash {
						// 3.服务端存在，客户端存在，hash不一致
						// 需要执行update操作
						// 同时需要继续向下遍历
						if clientNode.Type != merkletree.TypeLeaf {
							// case1: 服务端节点和客户端节点均为目录节点
							// case2: 服务端节点为叶子结点，客户端节点为目录节点
							// 执行此操作
							clientChildrenNodeMetas := make([]definition.MTreeActionNodeMeta, 0)
							for _, child := range clientNode.Children {
								clientChildrenNodeMetas = append(clientChildrenNodeMetas, definition.MTreeActionNodeMeta{
									Hash:         child.Hash,
									RelativePath: child.RelativePath,
									Type:         child.Type,
								})
							}

							serverChildrenNodeMetas := make([]definition.MTreeActionNodeMeta, 0)
							for _, child := range serverNode.Children {
								serverChildrenNodeMetas = append(serverChildrenNodeMetas, definition.MTreeActionNodeMeta{
									Hash:         child.Hash,
									RelativePath: child.RelativePath,
									Type:         child.Type,
								})
							}

							actionNodes.UpdateNodes = append(actionNodes.UpdateNodes, &definition.MTreePairNodes{
								New: &definition.MTreeActionNode{
									MTreeActionNodeMeta: definition.MTreeActionNodeMeta{
										Hash:         clientNode.Hash,
										RelativePath: clientNode.RelativePath,
										Type:         clientNode.Type,
									},
									Children: clientChildrenNodeMetas,
								},
								Old: &definition.MTreeActionNode{
									MTreeActionNodeMeta: definition.MTreeActionNodeMeta{
										Hash:         serverNode.Hash,
										RelativePath: serverNode.RelativePath,
										Type:         serverNode.Type,
									},
									Children: serverChildrenNodeMetas,
								},
								IsPartial: false,
							})

							clientChildNodeMap := make(map[string]*merkletree.MerkleNode)
							for _, clientChild := range clientNode.Children {
								clientChildNodeMap[clientChild.RelativePath] = clientChild
							}
							serverChildNodeMap := make(map[string]*merkletree.MerkleNode)
							for _, serverChild := range serverNode.Children {
								serverChildNodeMap[serverChild.RelativePath] = serverChild
							}

							// 以客户端子节点为主循环，确定子节点中的add update节点
							for relPath, clientChild := range clientChildNodeMap {
								if serverChild, ok := serverChildNodeMap[relPath]; ok {
									// 客户端子节点存在，服务端子节点存在
									if clientChild.Hash == serverChild.Hash {
										// hash一致
										// 将查到的节点放入已同步节点中，跳过该孩子节点的剩余子节点，不再查询
										if clientChild.Type != merkletree.TypeLeaf {
											if !judgeSkipChildNode(clientChild.RelativePath) {
												actionNodes.ServerFileNum += clientChild.GetLeafNodeNumber()
											}
											hashEqualRelMap[clientChild.RelativePath] = struct{}{}
										}
									} else {
										// hash不一致
										if clientChild.Type == merkletree.TypeLeaf {
											// 这个文件要update，因为hash不一致
											actionNodes.LeafNodes = append(actionNodes.LeafNodes,
												&definition.MTreeActionNodeMeta{
													Hash:         clientChild.Hash,
													RelativePath: clientChild.RelativePath,
													Type:         clientChild.Type,
												},
											)
										}
									}

									if clientChild.Type != serverChild.Type &&
										serverChild.Type != merkletree.TypeLeaf {
										// 客户端节点为叶子结点，但服务端节点为非叶子节点
										// 则需要将服务端节点入队，下一轮进行query查看
										toQueryNodes.Enqueue(serverChild)
									}
								} else {
									// 客户端子节点存在，服务端子节点不存在
									// 子节点要上传，因此要查询状态
									if clientChild.Type == merkletree.TypeLeaf {
										actionNodes.LeafNodes = append(actionNodes.LeafNodes,
											&definition.MTreeActionNodeMeta{
												Hash:         clientChild.Hash,
												RelativePath: clientChild.RelativePath,
												Type:         clientChild.Type,
											},
										)
									}
								}
							}

							// 以服务端子节点为主循环，确定delete节点
							for relPath, serverChild := range serverChildNodeMap {
								if _, ok := clientChildNodeMap[relPath]; !ok {
									// 服务端子节点存在，客户端子节点不存在
									if serverChild.Type != merkletree.TypeLeaf {
										// 非叶子节点，直接入队，等待下一次查询
										toQueryNodes.Enqueue(serverChild)
									} else {
										// 叶子结点，已经执行删除操作
									}
								}
							}
						}

					} else {
						// 4.服务端存在，客户端存在，hash一致
						if clientNode.Type != merkletree.TypeLeaf {
							// 非叶子节点
							// hash一致，同时要比较children是否完全一致
							serverChildrenNodeMap := make(map[string]*merkletree.MerkleNode)
							for _, child := range serverNode.Children {
								serverChildrenNodeMap[child.RelativePath] = child
							}
							clientChildrenNodeMap := make(map[string]*merkletree.MerkleNode)
							for _, child := range clientNode.Children {
								clientChildrenNodeMap[child.RelativePath] = child
							}

							needSync := false
							if len(clientChildrenNodeMap) != len(serverChildrenNodeMap) {
								// children数量不一致，代表节点不相同，需要进行同步
								needSync = true
							} else {
								for relPath, clientChild := range clientChildrenNodeMap {
									if serverChild, ok := serverChildrenNodeMap[relPath]; ok {
										if clientChild.Hash != serverChild.Hash {
											// hash不一致
											needSync = true
											break
										}
									} else {
										// 服务端子节点不存在
										needSync = true
										break
									}
								}
							}

							if needSync {
								clientChildrenNodeMetas := make([]definition.MTreeActionNodeMeta, 0)
								for _, child := range clientNode.Children {
									clientChildrenNodeMetas = append(clientChildrenNodeMetas, definition.MTreeActionNodeMeta{
										Hash:         child.Hash,
										RelativePath: child.RelativePath,
										Type:         child.Type,
									})
								}

								serverChildrenNodeMetas := make([]definition.MTreeActionNodeMeta, 0)
								for _, child := range serverNode.Children {
									serverChildrenNodeMetas = append(serverChildrenNodeMetas, definition.MTreeActionNodeMeta{
										Hash:         child.Hash,
										RelativePath: child.RelativePath,
										Type:         child.Type,
									})
								}

								actionNodes.UpdateNodes = append(actionNodes.UpdateNodes, &definition.MTreePairNodes{
									New: &definition.MTreeActionNode{
										MTreeActionNodeMeta: definition.MTreeActionNodeMeta{
											Hash:         clientNode.Hash,
											RelativePath: clientNode.RelativePath,
											Type:         clientNode.Type,
										},
										Children: clientChildrenNodeMetas,
									},
									Old: &definition.MTreeActionNode{
										MTreeActionNodeMeta: definition.MTreeActionNodeMeta{
											Hash:         serverNode.Hash,
											RelativePath: serverNode.RelativePath,
											Type:         serverNode.Type,
										},
										Children: serverChildrenNodeMetas,
									},
									IsPartial: false,
								})
							} else {
								// 将节点加入equal map
								if !judgeSkipChildNode(clientNode.RelativePath) {
									// 首次验证skip时，加入计数
									actionNodes.ServerFileNum += clientNode.GetLeafNodeNumber()
								}
								hashEqualRelMap[clientNode.RelativePath] = struct{}{}
							}
						}
					}
				}
			}
		}
	}

	return actionNodes, nil
}

// verboseLogf 根据 LINGMA_CLIENT_SERVER_VECTOR_LOG_VERBOSE 环境变量控制debug日志输出
// 如果开启了 LINGMA_CLIENT_SERVER_VECTOR_LOG_VERBOSE 环境变量，则展示更多debug信息
func verboseLogf(format string, args ...interface{}) {
	if showVerboseLog {
		log.Debugf("[SERVER_VECTOR_VERBOSE] "+format, args...)
	}
}

// verboseLog 根据 LINGMA_CLIENT_SERVER_VECTOR_LOG_VERBOSE 环境变量控制debug日志输出（无格式化参数）
func verboseLog(msg string) {
	if showVerboseLog {
		log.Debugf("[SERVER_VECTOR_VERBOSE] %s", msg)
	}
}

func init() {
	showVerboseLog = os.Getenv("LINGMA_CLIENT_SERVER_VECTOR_LOG_VERBOSE") != ""
}
