package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/config"
	"github.com/go-git/go-git/v5/plumbing"
	"github.com/go-git/go-git/v5/plumbing/object"
	"github.com/go-git/go-git/v5/plumbing/transport/http"
	"github.com/go-git/go-git/v5/plumbing/transport/ssh"
)

// GitCommitPushExample 演示如何使用 go-git 包提交代码并推送到远程分支
type GitCommitPushExample struct {
	repoPath string
	repo     *git.Repository
}

// NewGitCommitPushExample 创建新的 Git 操作实例
func NewGitCommitPushExample(repoPath string) (*GitCommitPushExample, error) {
	repo, err := git.PlainOpen(repoPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open repository: %w", err)
	}

	return &GitCommitPushExample{
		repoPath: repoPath,
		repo:     repo,
	}, nil
}

// AddAndCommit 添加文件到暂存区并提交
func (g *GitCommitPushExample) AddAndCommit(commitMessage string, authorName, authorEmail string) error {
	// 获取工作树
	worktree, err := g.repo.Worktree()
	if err != nil {
		return fmt.Errorf("failed to get worktree: %w", err)
	}

	// 添加所有修改的文件到暂存区
	_, err = worktree.Add(".")
	if err != nil {
		return fmt.Errorf("failed to add files: %w", err)
	}

	// 创建提交
	commit, err := worktree.Commit(commitMessage, &git.CommitOptions{
		Author: &object.Signature{
			Name:  authorName,
			Email: authorEmail,
			When:  time.Now(),
		},
	})
	if err != nil {
		return fmt.Errorf("failed to commit: %w", err)
	}

	fmt.Printf("Commit created: %s\n", commit.String())
	return nil
}

// AddSpecificFilesAndCommit 添加指定文件到暂存区并提交
func (g *GitCommitPushExample) AddSpecificFilesAndCommit(files []string, commitMessage string, authorName, authorEmail string) error {
	worktree, err := g.repo.Worktree()
	if err != nil {
		return fmt.Errorf("failed to get worktree: %w", err)
	}

	// 添加指定文件
	for _, file := range files {
		_, err = worktree.Add(file)
		if err != nil {
			return fmt.Errorf("failed to add file %s: %w", file, err)
		}
		fmt.Printf("Added file: %s\n", file)
	}

	// 提交
	commit, err := worktree.Commit(commitMessage, &git.CommitOptions{
		Author: &object.Signature{
			Name:  authorName,
			Email: authorEmail,
			When:  time.Now(),
		},
	})
	if err != nil {
		return fmt.Errorf("failed to commit: %w", err)
	}

	fmt.Printf("Commit created: %s\n", commit.String())
	return nil
}

// PushWithHTTPS 使用 HTTPS 认证推送到远程分支
func (g *GitCommitPushExample) PushWithHTTPS(remoteName, branchName, username, password string) error {
	// 配置认证
	auth := &http.BasicAuth{
		Username: username,
		Password: password, // 对于 GitHub，这里应该是 Personal Access Token
	}

	// 推送到远程分支
	err := g.repo.Push(&git.PushOptions{
		RemoteName: remoteName,
		RefSpecs: []config.RefSpec{
			config.RefSpec(fmt.Sprintf("refs/heads/%s:refs/heads/%s", branchName, branchName)),
		},
		Auth: auth,
	})

	if err != nil {
		return fmt.Errorf("failed to push: %w", err)
	}

	fmt.Printf("Successfully pushed to %s/%s\n", remoteName, branchName)
	return nil
}

// PushWithSSH 使用 SSH 密钥推送到远程分支
func (g *GitCommitPushExample) PushWithSSH(remoteName, branchName, sshKeyPath, sshKeyPassword string) error {
	// 读取 SSH 私钥
	sshKey, err := os.ReadFile(sshKeyPath)
	if err != nil {
		return fmt.Errorf("failed to read SSH key: %w", err)
	}

	// 配置 SSH 认证
	auth, err := ssh.NewPublicKeys("git", sshKey, sshKeyPassword)
	if err != nil {
		return fmt.Errorf("failed to create SSH auth: %w", err)
	}

	// 推送到远程分支
	err = g.repo.Push(&git.PushOptions{
		RemoteName: remoteName,
		RefSpecs: []config.RefSpec{
			config.RefSpec(fmt.Sprintf("refs/heads/%s:refs/heads/%s", branchName, branchName)),
		},
		Auth: auth,
	})

	if err != nil {
		return fmt.Errorf("failed to push: %w", err)
	}

	fmt.Printf("Successfully pushed to %s/%s\n", remoteName, branchName)
	return nil
}

// CreateAndSwitchBranch 创建并切换到新分支
func (g *GitCommitPushExample) CreateAndSwitchBranch(branchName string) error {
	worktree, err := g.repo.Worktree()
	if err != nil {
		return fmt.Errorf("failed to get worktree: %w", err)
	}

	// 获取当前 HEAD 引用
	headRef, err := g.repo.Head()
	if err != nil {
		return fmt.Errorf("failed to get HEAD: %w", err)
	}

	// 创建新分支引用
	branchRef := plumbing.NewBranchReferenceName(branchName)
	ref := plumbing.NewHashReference(branchRef, headRef.Hash())

	// 创建分支
	err = g.repo.Storer.SetReference(ref)
	if err != nil {
		return fmt.Errorf("failed to create branch: %w", err)
	}

	// 切换到新分支
	err = worktree.Checkout(&git.CheckoutOptions{
		Branch: branchRef,
	})
	if err != nil {
		return fmt.Errorf("failed to checkout branch: %w", err)
	}

	fmt.Printf("Created and switched to branch: %s\n", branchName)
	return nil
}

// GetStatus 获取仓库状态
func (g *GitCommitPushExample) GetStatus() error {
	worktree, err := g.repo.Worktree()
	if err != nil {
		return fmt.Errorf("failed to get worktree: %w", err)
	}

	status, err := worktree.Status()
	if err != nil {
		return fmt.Errorf("failed to get status: %w", err)
	}

	fmt.Println("Repository Status:")
	for file, stat := range status {
		fmt.Printf("  %s: %s\n", file, stat.Staging.String()+stat.Worktree.String())
	}

	return nil
}

// 示例用法
func main() {
	// 初始化 Git 操作实例
	gitOps, err := NewGitCommitPushExample(".")
	if err != nil {
		log.Fatalf("Failed to initialize git operations: %v", err)
	}

	// 1. 查看仓库状态
	fmt.Println("=== Repository Status ===")
	err = gitOps.GetStatus()
	if err != nil {
		log.Printf("Failed to get status: %v", err)
	}

	// 2. 创建并切换到新分支（可选）
	// err = gitOps.CreateAndSwitchBranch("feature/new-feature")
	// if err != nil {
	//     log.Printf("Failed to create branch: %v", err)
	// }

	// 3. 添加指定文件并提交
	files := []string{"examples/git_commit_push_example.go"}
	err = gitOps.AddSpecificFilesAndCommit(
		files,
		"feat: add git commit and push example",
		"Your Name",
		"<EMAIL>",
	)
	if err != nil {
		log.Printf("Failed to commit: %v", err)
		return
	}

	// 4. 推送到远程分支
	// 使用 HTTPS 认证（推荐使用 Personal Access Token）
	err = gitOps.PushWithHTTPS("origin", "main", "your-username", "your-token")
	if err != nil {
		log.Printf("Failed to push with HTTPS: %v", err)
	}

	// 或者使用 SSH 认证
	// err = gitOps.PushWithSSH("origin", "main", "/path/to/ssh/key", "ssh-key-password")
	// if err != nil {
	//     log.Printf("Failed to push with SSH: %v", err)
	// }

	fmt.Println("Git operations completed successfully!")
}
