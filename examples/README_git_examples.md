# Go-Git 使用示例

本目录包含了使用 go-git 包进行 Git 操作的完整示例，包括提交代码和推送到远程分支。

## 文件说明

- `git_commit_push_example.go` - 完整的 Git 操作示例，包含多种认证方式
- `simple_git_example.go` - 简化版本，展示最常用的操作

## 快速开始

### 1. 基本提交和推送

```go
package main

import (
    "fmt"
    "time"
    "github.com/go-git/go-git/v5"
    "github.com/go-git/go-git/v5/config"
    "github.com/go-git/go-git/v5/plumbing/object"
    "github.com/go-git/go-git/v5/plumbing/transport/http"
)

func main() {
    // 打开仓库
    repo, err := git.PlainOpen(".")
    if err != nil {
        panic(err)
    }

    // 获取工作树
    worktree, err := repo.Worktree()
    if err != nil {
        panic(err)
    }

    // 添加所有文件
    _, err = worktree.Add(".")
    if err != nil {
        panic(err)
    }

    // 提交
    commit, err := worktree.Commit("feat: add new feature", &git.CommitOptions{
        Author: &object.Signature{
            Name:  "Your Name",
            Email: "<EMAIL>",
            When:  time.Now(),
        },
    })
    if err != nil {
        panic(err)
    }

    fmt.Printf("Commit: %s\n", commit.String())

    // 推送
    err = repo.Push(&git.PushOptions{
        RemoteName: "origin",
        RefSpecs: []config.RefSpec{"refs/heads/main:refs/heads/main"},
        Auth: &http.BasicAuth{
            Username: "your-username",
            Password: "your-token", // GitHub Personal Access Token
        },
    })
    if err != nil {
        panic(err)
    }

    fmt.Println("Pushed successfully!")
}
```

### 2. 认证方式

#### HTTPS 认证（推荐）

```go
auth := &http.BasicAuth{
    Username: "your-username",
    Password: "your-personal-access-token", // 不是密码，是 PAT
}
```

#### SSH 认证

```go
import "github.com/go-git/go-git/v5/plumbing/transport/ssh"

sshKey, err := os.ReadFile("/path/to/ssh/private/key")
if err != nil {
    panic(err)
}

auth, err := ssh.NewPublicKeys("git", sshKey, "ssh-key-passphrase")
if err != nil {
    panic(err)
}
```

### 3. 常用操作

#### 添加指定文件

```go
// 添加单个文件
_, err = worktree.Add("path/to/file.go")

// 添加多个文件
files := []string{"file1.go", "file2.go", "dir/"}
for _, file := range files {
    _, err = worktree.Add(file)
    if err != nil {
        panic(err)
    }
}
```

#### 创建新分支

```go
import "github.com/go-git/go-git/v5/plumbing"

// 获取当前 HEAD
headRef, err := repo.Head()
if err != nil {
    panic(err)
}

// 创建新分支引用
branchRef := plumbing.NewBranchReferenceName("feature/new-branch")
ref := plumbing.NewHashReference(branchRef, headRef.Hash())

// 创建分支
err = repo.Storer.SetReference(ref)
if err != nil {
    panic(err)
}

// 切换到新分支
err = worktree.Checkout(&git.CheckoutOptions{
    Branch: branchRef,
})
if err != nil {
    panic(err)
}
```

#### 推送到指定分支

```go
err = repo.Push(&git.PushOptions{
    RemoteName: "origin",
    RefSpecs: []config.RefSpec{
        config.RefSpec("refs/heads/feature-branch:refs/heads/feature-branch"),
    },
    Auth: auth,
})
```

### 4. 错误处理

```go
import "github.com/go-git/go-git/v5/plumbing/transport"

err = repo.Push(&git.PushOptions{...})
if err != nil {
    if err == git.NoErrAlreadyUpToDate {
        fmt.Println("Repository is already up to date")
    } else if err == transport.ErrAuthenticationRequired {
        fmt.Println("Authentication failed")
    } else {
        fmt.Printf("Push failed: %v\n", err)
    }
}
```

## 运行示例

```bash
# 运行完整示例
go run examples/git_commit_push_example.go

# 运行简化示例
go run examples/simple_git_example.go
```

## 注意事项

1. **认证信息安全**：
   - 不要在代码中硬编码认证信息
   - 使用环境变量或配置文件存储敏感信息
   - GitHub 推荐使用 Personal Access Token 而不是密码

2. **错误处理**：
   - 始终检查错误返回值
   - 处理常见的 Git 错误情况

3. **分支操作**：
   - 推送前确保本地分支存在
   - 注意远程分支的引用格式

4. **文件操作**：
   - 使用相对路径添加文件
   - 注意 `.gitignore` 文件的影响

## 环境变量示例

```bash
export GIT_USERNAME="your-username"
export GIT_TOKEN="your-personal-access-token"
export GIT_AUTHOR_NAME="Your Name"
export GIT_AUTHOR_EMAIL="<EMAIL>"
```

然后在代码中使用：

```go
import "os"

username := os.Getenv("GIT_USERNAME")
token := os.Getenv("GIT_TOKEN")
authorName := os.Getenv("GIT_AUTHOR_NAME")
authorEmail := os.Getenv("GIT_AUTHOR_EMAIL")
```
