package main

import (
	"fmt"
	"log"
	"time"

	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/config"
	"github.com/go-git/go-git/v5/plumbing/object"
	"github.com/go-git/go-git/v5/plumbing/transport/http"
)

// SimpleGitCommitAndPush 简单的提交和推送示例
func SimpleGitCommitAndPush() error {
	// 1. 打开现有仓库
	repo, err := git.PlainOpen(".")
	if err != nil {
		return fmt.Errorf("failed to open repository: %w", err)
	}

	// 2. 获取工作树
	worktree, err := repo.Worktree()
	if err != nil {
		return fmt.Errorf("failed to get worktree: %w", err)
	}

	// 3. 添加所有修改的文件到暂存区
	_, err = worktree.Add(".")
	if err != nil {
		return fmt.Errorf("failed to add files: %w", err)
	}

	// 4. 提交更改
	commit, err := worktree.Commit("feat: add new feature", &git.CommitOptions{
		Author: &object.Signature{
			Name:  "Your Name",
			Email: "<EMAIL>",
			When:  time.Now(),
		},
	})
	if err != nil {
		return fmt.Errorf("failed to commit: %w", err)
	}

	fmt.Printf("Commit created: %s\n", commit.String())

	// 5. 推送到远程仓库
	err = repo.Push(&git.PushOptions{
		RemoteName: "origin",
		RefSpecs: []config.RefSpec{
			"refs/heads/main:refs/heads/main", // 推送 main 分支
		},
		Auth: &http.BasicAuth{
			Username: "your-username",
			Password: "your-personal-access-token", // GitHub Personal Access Token
		},
	})
	if err != nil {
		return fmt.Errorf("failed to push: %w", err)
	}

	fmt.Println("Successfully pushed to remote repository!")
	return nil
}

// CommitSpecificFiles 提交指定文件
func CommitSpecificFiles(files []string, message string) error {
	repo, err := git.PlainOpen(".")
	if err != nil {
		return fmt.Errorf("failed to open repository: %w", err)
	}

	worktree, err := repo.Worktree()
	if err != nil {
		return fmt.Errorf("failed to get worktree: %w", err)
	}

	// 添加指定文件
	for _, file := range files {
		_, err = worktree.Add(file)
		if err != nil {
			return fmt.Errorf("failed to add file %s: %w", file, err)
		}
		fmt.Printf("Added: %s\n", file)
	}

	// 提交
	commit, err := worktree.Commit(message, &git.CommitOptions{
		Author: &object.Signature{
			Name:  "Your Name",
			Email: "<EMAIL>",
			When:  time.Now(),
		},
	})
	if err != nil {
		return fmt.Errorf("failed to commit: %w", err)
	}

	fmt.Printf("Commit created: %s\n", commit.String())
	return nil
}

// PushToSpecificBranch 推送到指定分支
func PushToSpecificBranch(branchName, username, token string) error {
	repo, err := git.PlainOpen(".")
	if err != nil {
		return fmt.Errorf("failed to open repository: %w", err)
	}

	err = repo.Push(&git.PushOptions{
		RemoteName: "origin",
		RefSpecs: []config.RefSpec{
			config.RefSpec(fmt.Sprintf("refs/heads/%s:refs/heads/%s", branchName, branchName)),
		},
		Auth: &http.BasicAuth{
			Username: username,
			Password: token,
		},
	})
	if err != nil {
		return fmt.Errorf("failed to push to branch %s: %w", branchName, err)
	}

	fmt.Printf("Successfully pushed to branch: %s\n", branchName)
	return nil
}

func main() {
	// 示例 1: 简单的提交和推送
	fmt.Println("=== Simple Commit and Push ===")
	err := SimpleGitCommitAndPush()
	if err != nil {
		log.Printf("Error: %v", err)
	}

	// 示例 2: 提交指定文件
	fmt.Println("\n=== Commit Specific Files ===")
	files := []string{"examples/simple_git_example.go", "README.md"}
	err = CommitSpecificFiles(files, "docs: update examples and README")
	if err != nil {
		log.Printf("Error: %v", err)
	}

	// 示例 3: 推送到指定分支
	fmt.Println("\n=== Push to Specific Branch ===")
	err = PushToSpecificBranch("main", "your-username", "your-token")
	if err != nil {
		log.Printf("Error: %v", err)
	}
}
