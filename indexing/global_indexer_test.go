package indexing

import (
	"cosy/definition"
	"cosy/storage/factory"
	"github.com/stretchr/testify/assert"
	"os"
	"path/filepath"
	"testing"
)

func TestGlobalFileIndex_GetOrAddIndexer(t *testing.T) {
	currentPath, err := filepath.Abs("./")
	assert.Nil(t, err)
	currentPath = filepath.Join(currentPath, "tmp")
	defer os.RemoveAll(currentPath)

	db, err := factory.NewKvStoreWithPath(currentPath, factory.BBlotStore)
	globalIndex := NewGlobalFileIndex(db)
	tmpDir, err := os.MkdirTemp("", "test_global_indexer")
	assert.Nil(t, err)
	defer os.RemoveAll(tmpDir)

	workspaceInfo := definition.NewWorkspaceInfo(tmpDir)

	indexer := globalIndex.GetOrAddIndexer(workspaceInfo)

	assert.NotNil(t, indexer)
}

func TestGlobalFileIndex_IndexWorkspace(t *testing.T) {
	currentPath, err := filepath.Abs("./")
	assert.Nil(t, err)
	currentPath = filepath.Join(currentPath, "tmp")
	defer os.RemoveAll(currentPath)

	db, err := factory.NewKvStoreWithPath(currentPath, factory.BBlotStore)
	globalIndex := NewGlobalFileIndex(db)
	tmpDir, err := os.MkdirTemp("", "test_global_indexer")
	assert.Nil(t, err)
	defer os.RemoveAll(tmpDir)

	workspaceInfo := definition.NewWorkspaceInfo(tmpDir)

	indexer := globalIndex.GetOrAddIndexer(workspaceInfo)
	assert.NotNil(t, indexer)
}
