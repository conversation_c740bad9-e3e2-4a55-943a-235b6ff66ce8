package indexing

import (
	"testing"
)

// 测试 IncrementIndexCount 方法
func TestIncrementIndexCount(t *testing.T) {
	pis := NewProjectIndexStat()
	pis.IncrementIndexCount("type1", 10)
	pis.IncrementIndexCount("type1", 5)
	pis.IncrementIndexCount("type2", 3)

	if pis.IndexCountStats["type1"].Load() != 15 { // 修改: 使用 Load() 方法获取值
		t.<PERSON><PERSON><PERSON>("Expected type1 count to be 15, got %d", pis.IndexCountStats["type1"].Load())
	}
	if pis.IndexCountStats["type2"].Load() != 3 { // 修改: 使用 Load() 方法获取值
		t.Errorf("Expected type2 count to be 3, got %d", pis.IndexCountStats["type2"].Load())
	}
}

// 测试 GetIndexCount 方法
func TestGetIndexCount(t *testing.T) {
	pis := NewProjectIndexStat()
	pis.IncrementIndexCount("type1", 10)
	pis.IncrementIndexCount("type2", 3)

	if pis.GetIndexCount("type1") != 10 {
		t.Errorf("Expected type1 count to be 10, got %d", pis.GetIndexCount("type1"))
	}
	if pis.GetIndexCount("type2") != 3 {
		t.Errorf("Expected type2 count to be 3, got %d", pis.GetIndexCount("type2"))
	}
	if pis.GetIndexCount("type3") != 0 {
		t.Errorf("Expected type3 count to be 0, got %d", pis.GetIndexCount("type3"))
	}
}

// 测试 String 方法
func TestString(t *testing.T) {
	pis := NewProjectIndexStat()
	pis.IncrementIndexCount("type1", 10)
	pis.IncrementIndexCount("type2", 3)

	expected := "type1:10 type2:3 "
	result := pis.String()

	if result != expected {
		t.Errorf("Expected string to be %q, got %q", expected, result)
	}
}
