package indexing

import (
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/indexing/common"
	"cosy/indexing/manager"
	"cosy/lang/indexer/rag"
	"cosy/storage/factory"
	"cosy/tokenizer"
	"cosy/util"
	"fmt"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"testing"
)

// MockFileIndexer is a mock implementation of common.FileIndexer
type MockFileIndexer struct {
	mock.Mock
}

func (m *MockFileIndexer) Init(env *common.IndexEnvironment) error {
	args := m.Called(env)
	return args.Error(0)
}

func (m *MockFileIndexer) PrepareIndexing(env *common.IndexEnvironment) {
	m.Called(env)
}

func (m *MockFileIndexer) IndexFiles(env *common.IndexEnvironment, files []definition.VirtualFile) {
	m.Called(env, files)
}

func (m *MockFileIndexer) CompleteIndexing(env *common.IndexEnvironment) {
	m.Called(env)
}

func (m *MockFileIndexer) OnFileDelete(env *common.IndexEnvironment, files []definition.VirtualFile) {
	m.Called(env, files)
}

func (m *MockFileIndexer) OnFileModify(env *common.IndexEnvironment, files []definition.VirtualFile) {
	m.Called(env, files)
}

func (m *MockFileIndexer) OnFileSave(env *common.IndexEnvironment, files []definition.VirtualFile) {
	m.Called(env, files)
}

func (m *MockFileIndexer) Close() {

}

func (m *MockFileIndexer) IndexChatWorkspace(env *common.IndexEnvironment) {
	m.Called(env)

}

func (m *MockFileIndexer) RegisterProjectIgnore(ignore *common.ProjectIgnore) {
	m.Called(ignore)
}

// createBasicStructure 创建基本目录结构
func createBasicStructure(testDir string) error {
	// 创建基本目录
	dirs := []string{
		testDir,
		filepath.Join(testDir, "a_dir"),
		filepath.Join(testDir, "c_dir"),
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建目录失败 %s: %v", dir, err)
		}
	}

	// 创建基本文件
	files := map[string][]byte{
		filepath.Join(testDir, "a_dir", "a.go"): []byte("AAA"),
		filepath.Join(testDir, "c_dir", "c.go"): []byte("CCC"),
	}

	for path, content := range files {
		if err := os.WriteFile(path, content, 0644); err != nil {
			return fmt.Errorf("写入文件失败 %s: %v", path, err)
		}
	}

	return nil
}

// createNoAccessDir 创建无权限目录
func createNoAccessDir(testDir string) error {
	noAccessDir := filepath.Join(testDir, "b_dir_no_access")
	if err := os.MkdirAll(noAccessDir, 0755); err != nil {
		return err
	}
	subTestFilePath := filepath.Join(noAccessDir, "b.go")
	if err := os.WriteFile(subTestFilePath, []byte("This is a test file in no-access directory"), 0644); err != nil {
		return err
	}
	rootTestFilePath := filepath.Join(testDir, "b_file_no_access.go")
	if err := os.WriteFile(rootTestFilePath, []byte("This is a no-access test file"), 0000); err != nil {
		return err
	}
	return os.Chmod(noAccessDir, 0000)
}

// buildWalkDirectoryTestEnv 初始化测试环境并创建用于测试的目录结构
// test_fixtures/
// ├── a_dir/
// │   └── a.go                # 内容为 "AAA"
// ├── b_dir_no_access/        # 该目录权限为 0000（无任何权限）
// │   └── b.go                # 内容为 "This is a test file in no-access directory"
// ├── b_file_no_access.go     # 文件权限为 0000（无法读取）
// └── c_dir/
//
//	└── c.go                # 内容为 "CCC"
func buildWalkDirectoryTestEnv(t *testing.T) string {
	// 获取当前文件所在目录
	_, file, _, _ := runtime.Caller(0)
	currentDir := filepath.Dir(file)

	// 构造测试目录路径
	testDir := filepath.Join(currentDir, "test_fixtures")

	// 清理旧目录
	_ = os.RemoveAll(testDir)
	assert.Nil(t, os.MkdirAll(testDir, 0755), "创建测试目录失败")

	// 添加清理函数
	t.Cleanup(func() {
		noAccessDir := filepath.Join(testDir, "b_dir_no_access")
		_ = os.Chmod(noAccessDir, 0755)
		noAccessFile := filepath.Join(testDir, "b_file_no_access.go")
		_ = os.Chmod(noAccessFile, 0755)
		err := os.RemoveAll(testDir)
		if err != nil {
			t.Logf("删除测试目录失败: %v", err)
		}
	})

	// 创建基本目录结构
	if err := createBasicStructure(testDir); err != nil {
		t.Fatalf("创建基本目录结构失败: %v", err)
	}

	// 创建无权限目录结构
	if err := createNoAccessDir(testDir); err != nil {
		t.Fatalf("创建无权限目录失败: %v", err)
	}

	// 返回测试目录的绝对路径
	absTestDir, err := filepath.Abs(testDir)
	assert.Nil(t, err, "获取绝对路径失败")
	return absTestDir
}

func TestProjectFileIndex_IndexWorkspace(t *testing.T) {
	currentPath, err := filepath.Abs("./")
	assert.Nil(t, err)
	currentPath = filepath.Join(currentPath, "tmp")
	defer os.RemoveAll(currentPath)

	db, err := factory.NewKvStoreWithPath(currentPath, factory.BBlotStore)
	workspacePath := "../lang/java/parser/testdata/proj1"
	assert.Nil(t, err)

	workspaceInfo := definition.NewWorkspaceInfo(workspacePath)
	projectIndex := NewProjectFileIndex(db, workspaceInfo)

	mockIndexer := new(MockFileIndexer)
	mockIndexer.On("Init", mock.Anything).Return(nil)
	mockIndexer.On("PrepareIndexing", mock.Anything).Return()
	mockIndexer.On("IndexFiles", mock.Anything, mock.Anything).Return()
	mockIndexer.On("CompleteIndexing", mock.Anything).Return()

	var mockFileIndexers = common.NewDefaultWorkspaceFileIndexer[*MockFileIndexer](func(env *common.IndexEnvironment) *MockFileIndexer {
		return mockIndexer
	})
	projectIndex.workspaceFileIndexers = map[string]*WorkspaceFileIndexers{
		"mock_indexer": {
			workspaceFileIndexers: mockFileIndexers,
			maxIndexCount:         MaxIndexFileCount,
		},
	}

	err = projectIndex.IndexWorkspace(ProjectIndexParam{
		EnableFileIndexers: map[string]bool{
			"mock_indexer": true,
		},
	})
	assert.NoError(t, err)
	mockIndexer.AssertExpectations(t)
}

func TestProjectFileIndex_IndexWorkspace_VectorIndexChunkCompleteness(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()
	//testRepo := "https://github.com/macrozheng/mall.git"
	//testRepo := "https://github.com/qiwsir/algorithm.git"
	//testRepo := "https://github.com/kubernetes/kubernetes.git"
	testRepo := "https://github.com/vuejs/vue.git"
	//testRepo := "https://github.com/numpy/numpy.git"
	//testRepo := "https://github.com/ccxt/ccxt.git"
	localRepoDir, err := util.GetTestRepoLocalPath(testRepo)
	fmt.Println(localRepoDir)
	assert.Nil(t, err)
	workspaceInfo := definition.NewWorkspaceInfo(localRepoDir)
	indexer := NewProjectFileIndex(nil, workspaceInfo)

	requestId := uuid.NewString()

	param := NewStartupChatProjectIndexParam()
	indexer.foreachFileIndexers(param.EnableFileIndexers, func(indexerName string, fileIndexer common.FileIndexer) {
		fileIndexer.PrepareIndexing(indexer.Environment)
	})

	// 全量建立索引
	indexer.IndexWorkspace(param)
	// 等待向量索引构建完毕
	manager.GlobalIndexBuilderManager.WaitForWorkspaceFinish(requestId, manager.VectorIndexTopic)

	vectorIndexer, ok := indexer.GetChatRetrieveFileVectorIndexer()
	assert.Equal(t, true, ok)
	assert.NotNil(t, vectorIndexer)

	engine, err := vectorIndexer.GetClientVectorRetrieveEngine()
	assert.Nil(t, err)
	assert.NotNil(t, engine)
	vectorEngine := engine.(*rag.SqliteVecRetrieveEngine)

	_, _ = indexer.WalkDirectory(localRepoDir, func(path string) error {
		storageChunks, err := vectorEngine.GetStorageFileChunks(path)
		assert.Nil(t, err)
		storageMap := make(map[string]bool)
		for _, chunk := range storageChunks {
			//chunkEmbeddingSum := float32(0.0)
			//for idx, embedding := range chunk.Embedding {
			//	chunkEmbeddingSum += float32(idx) * embedding
			//}
			chunkIdentifier := fmt.Sprintf("%s%d%d%s", chunk.FilePath, chunk.StartLine, chunk.EndLine, chunk.FileName)
			storageMap[chunkIdentifier] = true
		}
		if len(storageMap) == 0 {
			return nil
		}

		task := definition.NewTask(definition.NewVirtualFile(path), requestId, localRepoDir, 0, 0)
		splitWrapper, err := vectorEngine.SplitFile(task, false)
		if err != nil && strings.Contains(err.Error(), "file line feed too large") {
			return nil
		}
		assert.Nil(t, err)

		splitMap := make(map[string]bool)
		for _, chunk := range splitWrapper.Chunks {
			chunkIdentifier := fmt.Sprintf("%s%d%d%s", chunk.FilePath, chunk.StartLine, chunk.EndLine, chunk.FileName)
			splitMap[chunkIdentifier] = true
		}

		successCnt := 0
		assert.Equal(t, len(storageMap), len(splitMap))
		for chunkIdentifier, _ := range storageMap {
			_, ok := splitMap[chunkIdentifier]
			if ok {
				successCnt += 1
			}
			assert.Equal(t, true, ok)
		}
		if len(storageMap) == len(splitMap) && successCnt == len(storageMap) {
			fmt.Println(path, "验证成功")
		} else {
			fmt.Println(path, "验证失败！！！")
		}
		return nil
	})

}

func TestProjectFileIndex_Calculate_ChunkNum_PerFile(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	repoLists := []string{
		//"https://github.com/vuejs/vue.git",
		//"https://github.com/macrozheng/mall.git",
		//"https://github.com/apache/flink.git",
		//"https://github.com/apache/hadoop.git",
		//"https://github.com/facebook/react.git",
		//"https://github.com/kubernetes/kubernetes.git",
		"https://github.com/microsoft/vscode.git",
		//"https://github.com/tensorflow/tensorflow.git",

		//"https://github.com/qiwsir/algorithm.git",
	}

	uniqueChunk := make(map[string]int)
	// 用来计算chunkToken和对应的chunk数量的
	chunkMap := make(map[int]int)
	// 用来计算每个文件切块数量的
	// key为单文件的切块数量，value为对应切块数量的文件数量
	chunkFileMap := make(map[int]int)

	for _, testRepo := range repoLists {
		localRepoDir, err := util.GetTestRepoLocalPath(testRepo)
		assert.Nil(t, err)

		workspaceInfo := definition.NewWorkspaceInfo(localRepoDir)
		indexer := NewProjectFileIndex(nil, workspaceInfo)
		fmt.Println(localRepoDir)

		param := NewStartupChatProjectIndexParam()
		indexer.foreachFileIndexers(param.EnableFileIndexers, func(indexerName string, fileIndexer common.FileIndexer) {
			fileIndexer.PrepareIndexing(indexer.Environment)
		})

		vectorIndexer, ok := indexer.GetChatRetrieveFileVectorIndexer()
		assert.Equal(t, true, ok)
		assert.NotNil(t, vectorIndexer)

		engine, err := vectorIndexer.GetClientVectorRetrieveEngine()
		assert.Nil(t, err)
		assert.NotNil(t, engine)
		vectorEngine := engine.(*rag.SqliteVecRetrieveEngine)

		_, _ = indexer.WalkDirectory(localRepoDir, func(path string) error {
			task := definition.NewTask(definition.NewVirtualFile(path), "", localRepoDir, 0, 0)
			splitWrapper, err := vectorEngine.SplitFile(task, false)
			if err != nil {
				fmt.Println(path, "切块失败！！！", err)
				return nil
			}
			if splitWrapper == nil {
				return nil
			}

			for _, chunk := range splitWrapper.Chunks {
				token := tokenizer.GetQwenTokenSize(chunk.Content)
				chunkMap[token] += 1
				uniqueChunk[chunk.ChunkId] += 1
			}
			chunkFileMap[len(splitWrapper.Chunks)] += 1
			return nil
		})
	}

	averageTokenPerChunk := 0.0
	totalChunkCnt := 0
	totalToken := 0
	for chunkToken, chunkCount := range chunkMap {
		//fmt.Printf("token为:%d 的文件数量为: %d \n", chunkToken, chunkCount)
		totalChunkCnt += chunkCount
		totalToken += chunkToken * chunkCount
	}
	averageTokenPerChunk = float64(totalToken) / float64(totalChunkCnt)

	repeatChunkCnt := 0
	for _, cnt := range uniqueChunk {
		if cnt == 1 {
			continue
		}
		repeatChunkCnt += cnt - 1
		//fmt.Printf("chunkId为:%s 的文件数量为: %d \n", chunkId, uniqueChunk[chunkId])
	}
	fmt.Println("=============================")
	fmt.Printf("总共的chunk数量：%d,其中重复的chunk数量为：%d", len(uniqueChunk), repeatChunkCnt)
	fmt.Println("=============================")

	fmt.Println("切块的平均token为：", averageTokenPerChunk)
	fmt.Println("=============================")

	averageChunkPerFile := 0.0
	totalFileCnt := 0
	totalChunkCnt = 0
	for chunkCount, fileCount := range chunkFileMap {
		//fmt.Printf("切块数量为:%d 的文件数量为: %d \n", chunkCount, fileCount)
		totalFileCnt += fileCount
		totalChunkCnt += chunkCount * fileCount
	}
	averageChunkPerFile = float64(totalChunkCnt) / float64(totalFileCnt)
	fmt.Println("每个文件的平均切块数量为：", averageChunkPerFile)
}

// TestWalkNoPermissionDir 测试 WalkDirectory 是否能正确处理无权限目录。
func TestWalkNoPermissionDir(t *testing.T) {
	// Step1: 构建测试环境（含无权限目录）
	testDir := buildWalkDirectoryTestEnv(t)

	// Step2: 初始化ProjectFileIndex
	workspaceInfo := definition.NewWorkspaceInfo(testDir)
	indexer := NewProjectFileIndex(nil, workspaceInfo)

	visitedFiles := make([]string, 0)

	// Step3: 执行WalkDirectory遍历
	_, _ = indexer.WalkDirectory(testDir, func(path string) error {
		// 记录访问到的路径
		visitedFiles = append(visitedFiles, path)
		return nil
	})
	t.Log(visitedFiles)

	// Step4: 断言错误及文件
	assert.Equal(t, len(visitedFiles), 3)
	assert.Contains(t, visitedFiles[0], "a_dir/a.go") // 接下来不会遍历到 b_dir_no_access/b.go
	assert.Contains(t, visitedFiles[1], "b_file_no_access.go")
	assert.Contains(t, visitedFiles[2], "c_dir/c.go")
}

// TestWalkNoPermissionReadFileThrowError 测试在 WalkDirectory 遍历时尝试读取每个文件内容时的行为
func TestWalkNoPermissionReadFileThrowError(t *testing.T) {
	// Step1: 构建测试环境（含无权限目录）
	testDir := buildWalkDirectoryTestEnv(t)

	// Step2: 初始化ProjectFileIndex
	workspaceInfo := definition.NewWorkspaceInfo(testDir)
	indexer := NewProjectFileIndex(nil, workspaceInfo)

	visitedFiles := make([]string, 0)

	// Step3: 执行WalkDirectory遍历
	_, _ = indexer.WalkDirectory(testDir, func(path string) error {
		// 记录访问到的路径
		_, err := os.ReadFile(path)
		if err != nil {
			return err
		}
		visitedFiles = append(visitedFiles, path)
		return nil
	})
	t.Log(visitedFiles)

	// Step4: 断言错误及文件
	assert.Equal(t, len(visitedFiles), 1)
	assert.Contains(t, visitedFiles[0], "a_dir/a.go")
}

// TestWalkNoPermissionReadFileHandleError 测试在 WalkDirectory 遍历中如何处理文件读取错误
func TestWalkNoPermissionReadFileHandleError(t *testing.T) {
	// Step1: 构建测试环境（含无权限目录）
	testDir := buildWalkDirectoryTestEnv(t)

	// Step2: 初始化ProjectFileIndex
	workspaceInfo := definition.NewWorkspaceInfo(testDir)
	indexer := NewProjectFileIndex(nil, workspaceInfo)

	visitedFiles := make([]string, 0)

	// Step3: 执行WalkDirectory遍历
	_, _ = indexer.WalkDirectory(testDir, func(path string) error {
		// 记录访问到的路径
		_, err := os.ReadFile(path)
		if err != nil {
			if os.IsPermission(err) {
				t.Logf("Skipping no permission file=%s, err=%v", path, err)
				//return filepath.SkipDir 这样会直接终止遍历，而不是跳过无权限文件
				return nil
			}
			return err
		}
		visitedFiles = append(visitedFiles, path)
		return nil
	})
	t.Log(visitedFiles)

	// Step4: 断言错误及文件
	assert.Equal(t, len(visitedFiles), 2)
	assert.Contains(t, visitedFiles[0], "a_dir/a.go")
	assert.Contains(t, visitedFiles[1], "c_dir/c.go")
}
