package index_setting

import (
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/storage/bblotdb"
	"cosy/tree"
	"cosy/user"
	"cosy/util"
	"fmt"
	"math/rand"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"time"
)

const (
	IndexProgressNone    = "none"
	IndexProgressRunning = "running"
	IndexProgressFinish  = "finish"

	IndexModeVector = "vector"
	IndexModeGraph  = "graph"

	SettingTableName = "codebase_settings"

	SettingsVersion = "v1"
)

type CodebaseSettings struct {
	Mtx            sync.RWMutex
	OpenWorkspaces map[string]struct{}
	StartEvents    map[string]struct{}
	Settings       *bblotdb.BboltStore
}

var codebaseSettings *CodebaseSettings
var defaultSetting *definition.CodebaseIndexSetting

func init() {
	codebaseSettings = &CodebaseSettings{
		Mtx:            sync.RWMutex{},
		OpenWorkspaces: make(map[string]struct{}),
		StartEvents:    make(map[string]struct{}),
		Settings:       nil,
	}

	defaultSetting = &definition.CodebaseIndexSetting{
		AutoIndex:      false,
		Progress:       IndexProgressNone,
		FileNumber:     0,
		IsCanceled:     false,
		IsDeleted:      false,
		UserTrigger:    false,
		GraphProgress:  IndexProgressNone,
		VectorProgress: IndexProgressNone,
		IsFinished:     false,
	}
}

func InitCodebaseSettings() {
	defer func() {
		if r := recover(); r != nil {
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Errorf("Panic recovered in InitCodebaseSettings: %v, %s", r, string(stack))
		}
	}()

	codebaseSettings.Mtx.Lock()
	defer codebaseSettings.Mtx.Unlock()

	codebaseSettingsStore, err := bblotdb.NewBboltStore(filepath.Join(util.GetCosyHomePath(), "index", ".settings", SettingsVersion))
	if err != nil {
		log.Errorf("[codebase]-[setting] create index setting store failed: %v", err)
		return
	}
	codebaseSettings.Settings = codebaseSettingsStore
	log.Infof("[codebase]-[setting] init codebase settings success")
}

// codebase 压测时使用
func InitTestCodebaseSettings() {
	codebaseSettings.Mtx.Lock()
	defer codebaseSettings.Mtx.Unlock()

	log.Errorf("[codebase]-[setting] init test codebase settings")
	codebaseSettingsStore, err := bblotdb.NewBboltStore(filepath.Join(util.GetCosyHomePath(), "index", ".settings-test", SettingsVersion))
	if err != nil {
		log.Errorf("[codebase]-[setting]-[test] create index setting store failed: %v", err)
		return
	}
	codebaseSettings.Settings = codebaseSettingsStore
}

func getSettingsTableName() (string, error) {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		log.Debugf("[codebase]-[setting] user info is nil, please login first")
		return "", fmt.Errorf("user info is nil")
	}

	tableName := fmt.Sprintf("%s_%s", SettingTableName, userInfo.Uid)
	codebaseSettings.Settings.AddTable(tableName)
	return tableName, nil
}

func RegisterWorkspace(workspacePath string) {
	if util.IsIllegalWorkspace(workspacePath) {
		return
	}
	codebaseSettings.Mtx.Lock()
	defer codebaseSettings.Mtx.Unlock()
	log.Debugf("[codebase]-[setting] register workspace, workspacePath: %s", workspacePath)
	codebaseSettings.OpenWorkspaces[workspacePath] = struct{}{}
}

func UnregisterWorkspace(workspacePath string) {
	if util.IsIllegalWorkspace(workspacePath) {
		return
	}
	codebaseSettings.Mtx.Lock()
	defer codebaseSettings.Mtx.Unlock()
	log.Debugf("[codebase]-[setting] unregister workspace, workspacePath: %s", workspacePath)
	delete(codebaseSettings.OpenWorkspaces, workspacePath)
}

func TriggerIndexWorkspace() {
	codebaseSettings.Mtx.RLock()
	defer codebaseSettings.Mtx.RUnlock()
	for openWorkspace := range codebaseSettings.OpenWorkspaces {
		tableName, err := getSettingsTableName()
		if err != nil {
			continue
		}
		setting := getCodebaseIndexSetting(openWorkspace, tableName)
		if setting.AutoIndex {
			go func(workspacePath string) {
				randomTime := rand.Intn(5) + 5
				time.Sleep(time.Second * time.Duration(randomTime))
				StartBuildIndex(workspacePath, false)
			}(openWorkspace)
			log.Debugf("[codebase]-[setting] user login success, trigger index workspace, workspacePath: %s", openWorkspace)
		}
	}
}

func GetCodebaseIndexSetting(workspacePath string) definition.CodebaseIndexSetting {
	if util.IsIllegalWorkspace(workspacePath) {
		return *defaultSetting
	}

	codebaseSettings.Mtx.RLock()
	defer codebaseSettings.Mtx.RUnlock()

	tableName, err := getSettingsTableName()
	if err != nil {
		return *defaultSetting
	}

	setting := getCodebaseIndexSetting(workspacePath, tableName)
	newSetting := *setting
	if newSetting.IsCanceled {
		if newSetting.IsFinished {
			newSetting.Progress = IndexProgressFinish
		} else {
			newSetting.Progress = IndexProgressNone
		}
	}
	return newSetting
}

func getCodebaseIndexSetting(workspacePath string, tableName string) *definition.CodebaseIndexSetting {
	if util.IsIllegalExistingWorkspace(workspacePath) {
		tmpSetting := *defaultSetting
		return &tmpSetting
	}

	workspaceTree := tree.NewWorkspaceMerkleTree(workspacePath)
	if codebaseSettings.Settings == nil {
		log.Errorf("[codebase]-[setting] codebase settings storage table is nil")
		go func() {
			InitCodebaseSettings()
		}()
		tmpSetting := *defaultSetting
		return &tmpSetting
	}

	setting := &definition.CodebaseIndexSetting{}
	err := codebaseSettings.Settings.Get(workspacePath, tableName, setting)
	if err == nil {
		setting.FileNumber = workspaceTree.GetLeafNodeCount()
		return setting
	}

	// 如果设置不存在，则创建默认设置
	setting = &definition.CodebaseIndexSetting{
		AutoIndex:      true,
		Progress:       IndexProgressRunning,
		FileNumber:     workspaceTree.GetLeafNodeCount(),
		IsCanceled:     false,
		IsDeleted:      false,
		UserTrigger:    false,
		GraphProgress:  IndexProgressRunning,
		VectorProgress: IndexProgressRunning,
		IsFinished:     false,
	}
	if setting.FileNumber >= global.GetMaxAutoIndexFileNum() {
		setting.AutoIndex = false
		setting.Progress = IndexProgressNone
		// 超过自动文件限制，向量默认是不做的
		setting.VectorProgress = IndexProgressNone
		// 图谱默认是执行的
		setting.GraphProgress = IndexProgressRunning
	}

	bigRepoAutoIndex := os.Getenv(definition.MockBigRepoAutoIndexEnvKey)
	if bigRepoAutoIndex == "true" {
		// 开启了大库默认index的环境变量
		setting.AutoIndex = true
		setting.Progress = IndexProgressRunning
		setting.GraphProgress = IndexProgressRunning
		setting.VectorProgress = IndexProgressRunning
	}

	err = codebaseSettings.Settings.Set(workspacePath, tableName, setting)
	if err != nil {
		log.Debugf("[codebase]-[setting] create index setting failed: %v", err)
		return setting
	}
	log.Debugf("[codebase]-[setting] create index setting, workspacePath: %s, setting: %v", workspacePath, setting)
	return setting
}

func SetAutoIndexSetting(workspacePath string, autoIndex bool) definition.CodebaseIndexSetting {
	if util.IsIllegalWorkspace(workspacePath) {
		return definition.CodebaseIndexSetting{
			AutoIndex:   false,
			Progress:    IndexProgressNone,
			FileNumber:  0,
			IsCanceled:  false,
			IsDeleted:   false,
			UserTrigger: false,
		}
	}

	codebaseSettings.Mtx.Lock()
	defer codebaseSettings.Mtx.Unlock()

	tableName, err := getSettingsTableName()
	if err != nil {
		return definition.CodebaseIndexSetting{
			AutoIndex:   false,
			Progress:    IndexProgressNone,
			FileNumber:  0,
			IsCanceled:  true,
			IsDeleted:   false,
			UserTrigger: false,
		}
	}

	setting := getCodebaseIndexSetting(workspacePath, tableName)
	setting.AutoIndex = autoIndex
	err = codebaseSettings.Settings.Set(workspacePath, tableName, setting)
	if err != nil {
		log.Debugf("[codebase]-[setting] set auto index setting failed: %v", err)
		return *setting
	}
	log.Debugf("[codebase]-[setting] set auto index setting, workspacePath: %s, autoIndex: %v", workspacePath, autoIndex)
	return *setting
}

func StartBuildIndex(workspacePath string, userTrigger bool) definition.CodebaseIndexSetting {
	if util.IsIllegalWorkspace(workspacePath) {
		return *defaultSetting
	}

	codebaseSettings.Mtx.Lock()
	defer codebaseSettings.Mtx.Unlock()

	tableName, err := getSettingsTableName()
	if err != nil {
		return *defaultSetting
	}

	setting := getCodebaseIndexSetting(workspacePath, tableName)
	setting.IsCanceled = false
	setting.IsDeleted = false
	setting.GraphProgress = IndexProgressRunning
	setting.VectorProgress = IndexProgressRunning
	setting.Progress = IndexProgressRunning
	setting.UserTrigger = userTrigger
	err = codebaseSettings.Settings.Set(workspacePath, tableName, setting)
	if err != nil {
		log.Errorf("[codebase]-[setting] set index setting failed: %v", err)
	}
	codebaseSettings.StartEvents[workspacePath] = struct{}{}
	log.Debugf("[codebase]-[setting] start build index, workspacePath: %s, userTrigger: %v", workspacePath, userTrigger)
	return *setting
}

// CheckAndStartIndex
// 这个接口看起来很蠢，实际上是因为解决循环引用的问题
// websocket接口无法获取fileIndexer，因此只能通过事件机制绕开
func CheckAndStartIndex(workspacePath string) bool {
	if util.IsIllegalWorkspace(workspacePath) {
		return false
	}

	codebaseSettings.Mtx.Lock()
	defer codebaseSettings.Mtx.Unlock()
	_, ok := codebaseSettings.StartEvents[workspacePath]
	if ok {
		delete(codebaseSettings.StartEvents, workspacePath)
		log.Debugf("[codebase]-[setting] receive start index event, start index, workspacePath: %s", workspacePath)
		return true
	}
	return false
}

func FinishBuildIndex(workspacePath string, mode string) {
	if util.IsIllegalWorkspace(workspacePath) {
		return
	}

	codebaseSettings.Mtx.Lock()
	defer codebaseSettings.Mtx.Unlock()

	tableName, err := getSettingsTableName()
	if err != nil {
		return
	}

	setting := getCodebaseIndexSetting(workspacePath, tableName)
	if mode == IndexModeGraph {
		setting.GraphProgress = IndexProgressFinish
	} else if mode == IndexModeVector {
		setting.VectorProgress = IndexProgressFinish
	}
	updateProgress(workspacePath, setting)
	err = codebaseSettings.Settings.Set(workspacePath, tableName, setting)
	if err != nil {
		log.Errorf("[codebase]-[setting] set index setting failed: %v", err)
	}
	log.Debugf("[codebase]-[setting] finish build index, workspacePath: %s, mode: %s", workspacePath, mode)
}

func updateProgress(workspacePath string, setting *definition.CodebaseIndexSetting) {
	if setting.VectorProgress == IndexProgressNone {
		setting.Progress = IndexProgressNone
	} else if setting.VectorProgress == IndexProgressRunning {
		setting.Progress = IndexProgressRunning
	} else if setting.VectorProgress == IndexProgressFinish {
		// 消费本次用户主动点击建立索引的事件
		setting.UserTrigger = false
		setting.IsFinished = true
		setting.Progress = IndexProgressFinish
	} else {
		setting.Progress = IndexProgressNone
	}
	// 老的逻辑，未去掉图谱索引的进度控制
	// if setting.GraphProgress == IndexProgressNone || setting.VectorProgress == IndexProgressNone {
	// 	setting.Progress = IndexProgressNone
	// } else if setting.GraphProgress == IndexProgressRunning || setting.VectorProgress == IndexProgressRunning {
	// 	setting.Progress = IndexProgressRunning
	// } else if setting.GraphProgress == IndexProgressFinish && setting.VectorProgress == IndexProgressFinish {
	// 	// 消费本次用户主动点击建立索引的事件
	// 	setting.UserTrigger = false
	// 	setting.IsFinished = true
	// 	setting.Progress = IndexProgressFinish
	// } else {
	// 	setting.Progress = IndexProgressNone
	// }
}

func CancelBuildIndex(workspacePath string) definition.CodebaseIndexSetting {
	if util.IsIllegalWorkspace(workspacePath) {
		return *defaultSetting
	}

	codebaseSettings.Mtx.Lock()
	defer codebaseSettings.Mtx.Unlock()

	tableName, err := getSettingsTableName()
	if err != nil {
		return *defaultSetting
	}

	setting := getCodebaseIndexSetting(workspacePath, tableName)
	setting.IsCanceled = true
	setting.AutoIndex = false
	setting.UserTrigger = false
	// setting.GraphProgress = IndexProgressNone
	setting.VectorProgress = IndexProgressNone
	updateProgress(workspacePath, setting)
	err = codebaseSettings.Settings.Set(workspacePath, tableName, setting)
	if err != nil {
		log.Errorf("[codebase]-[setting] cancel build index failed: %v", err)
		return *defaultSetting
	}
	log.Debugf("[codebase]-[setting] cancel build index, workspacePath: %s", workspacePath)
	return *setting
}

func IndexEnable(workspacePath string) bool {
	if util.IsIllegalWorkspace(workspacePath) {
		return false
	}

	setting := GetCodebaseIndexSetting(workspacePath)
	// 第一优先查看auto标志
	// auto时，所有索引均可以开始
	if setting.AutoIndex {
		return true
	}

	// 未打开Auto按钮情况下
	// 第二优先检查cancel标志，如果存在cancel标志，代表用户处于索引取消状态，不建立任何索引
	// 如果用户已取消索引，则不执行索引
	if setting.IsCanceled {
		log.Debugf("[codebase]-[setting] [index enable] workspacePath: %s, index is canceled, index can't be started", workspacePath)
		return false
	}

	// 未打开Auto按钮情况下
	// 第三优先查看是否为用户主动点击而建立索引
	// 如果用户手动触发索引，则可以执行
	if setting.UserTrigger {
		return true
	}

	// 否则不能执行
	return false
}

func GetAutoIndexSetting(workspacePath string) bool {
	if util.IsIllegalWorkspace(workspacePath) {
		return false
	}

	codebaseSettings.Mtx.RLock()
	defer codebaseSettings.Mtx.RUnlock()
	tableName, err := getSettingsTableName()
	if err != nil {
		return false
	}

	setting := getCodebaseIndexSetting(workspacePath, tableName)
	return setting.AutoIndex
}

func IndexIsCanceled(workspacePath string) bool {
	if util.IsIllegalWorkspace(workspacePath) {
		return true
	}

	codebaseSettings.Mtx.RLock()
	defer codebaseSettings.Mtx.RUnlock()
	tableName, err := getSettingsTableName()
	if err != nil {
		return true
	}

	setting := getCodebaseIndexSetting(workspacePath, tableName)
	return setting.IsCanceled
}

func IndexIsDeleted(workspacePath string) bool {
	if util.IsIllegalWorkspace(workspacePath) {
		return true
	}

	codebaseSettings.Mtx.RLock()
	defer codebaseSettings.Mtx.RUnlock()
	tableName, err := getSettingsTableName()
	if err != nil {
		return true
	}

	setting := getCodebaseIndexSetting(workspacePath, tableName)
	return setting.IsDeleted
}

func SetVectorProgressForTest(workspacePath string, progress string) error {
	if util.IsIllegalWorkspace(workspacePath) {
		return nil
	}

	codebaseSettings.Mtx.Lock()
	defer codebaseSettings.Mtx.Unlock()

	tableName, err := getSettingsTableName()
	if err != nil {
		log.Errorf("[codebase]-[setting] get settings table name failed: %v", err)
		return err
	}

	setting := getCodebaseIndexSetting(workspacePath, tableName)
	setting.VectorProgress = progress
	err = codebaseSettings.Settings.Set(workspacePath, tableName, setting)
	if err != nil {
		log.Errorf("[codebase]-[setting] set vector progress failed: %v", err)
		return err
	}
	log.Debugf("[codebase]-[setting] set vector progress, workspacePath: %s, progress: %s", workspacePath, progress)
	return nil
}
