package indexing

import (
	"fmt"
	"strings"
	"sync"
	"sync/atomic"
)

// ProjectIndexStat is used to record the indexing statistics of the project.
type ProjectIndexStat struct {
	IndexCountStats map[string]*atomic.Uint64
	statMutex       sync.Mutex
}

func NewProjectIndexStat() *ProjectIndexStat {
	return &ProjectIndexStat{
		IndexCountStats: make(map[string]*atomic.Uint64),
		statMutex:       sync.Mutex{},
	}
}

// IncrementIndexCount increments the index count for the given index type.
func (p *ProjectIndexStat) IncrementIndexCount(indexType string, count uint64) {
	p.statMutex.Lock()
	defer p.statMutex.Unlock()
	if stat, ok := p.IndexCountStats[indexType]; ok {
		stat.Add(count)
	} else {
		stat = &atomic.Uint64{}
		stat.Add(count)
		p.IndexCountStats[indexType] = stat
	}
}

// GetIndexCount returns the index count for the given index type.
func (p *ProjectIndexStat) GetIndexCount(indexType string) uint64 {
	p.statMutex.Lock()
	defer p.statMutex.Unlock()
	if stat, ok := p.IndexCountStats[indexType]; ok {
		return stat.Load()
	}
	return 0
}

// String returns a string representation of the ProjectIndexStat.
func (p *ProjectIndexStat) String() string {
	result := strings.Builder{}
	for indexType, stat := range p.IndexCountStats {
		result.WriteString(fmt.Sprintf("%s:%d", indexType, stat.Load()))
		result.WriteString(" ")
	}
	return result.String()
}
