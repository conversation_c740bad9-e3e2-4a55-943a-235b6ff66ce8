package indexing

import (
	"context"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/storage"
	"cosy/util"
	"runtime"
	"runtime/debug"
	"sync"
	"time"
)

const (
	ProjectStartupIndex = "startup"
)

var (
	GlobalFileIndexer *GlobalFileIndex
)

type IndexEvent struct {
	EventType string
	Data      interface{}
	Param     ProjectIndexParam
}

// GlobalFileIndex 管理所有工程索引
type GlobalFileIndex struct {
	indexerLock sync.Mutex
	indexers    *util.SerializableMap[*ProjectFileIndex]
	db          storage.KvStore
	indexQueue  chan *IndexEvent
}

func NewGlobalFileIndex(db storage.KvStore) *GlobalFileIndex {
	manager := &GlobalFileIndex{
		indexerLock: sync.Mutex{},
		indexers:    util.NewSerializableMap[*ProjectFileIndex](),
		db:          db,
		indexQueue:  make(chan *IndexEvent, 10),
	}
	go manager.watchWorkspaceIndex()
	GlobalFileIndexer = manager
	return manager
}

// GetIndexer 获取工程索引, 如果不存在则返回nil
func (b *GlobalFileIndex) GetIndexer(workspace string) (*ProjectFileIndex, bool) {
	b.indexerLock.Lock()
	defer b.indexerLock.Unlock()
	if indexer, ok := b.indexers.Get(workspace); ok {
		return indexer, true
	}
	return nil, false
}

// GetOrAddIndexer 获取或创建工程索引, 如果已存在则返回
func (b *GlobalFileIndex) GetOrAddIndexer(workspaceInfo definition.WorkspaceInfo) *ProjectFileIndex {
	return b.GetOrAddIndexerWithHandler(workspaceInfo, nil)
}

// GetOrAddIndexer 获取或创建工程索引, 如果已存在则返回
func (b *GlobalFileIndex) GetOrAddIndexerWithHandler(workspaceInfo definition.WorkspaceInfo, completeIndexHandler CompleteIndexHandler) *ProjectFileIndex {
	b.indexerLock.Lock()
	defer b.indexerLock.Unlock()
	log.Debug("start get or add indexer:", workspaceInfo.GetWorkspaceFolders())
	path, _ := workspaceInfo.GetWorkspaceFolder()
	if obj, ok := b.indexers.Get(path); ok {
		log.Infof("get exist index workspace %s", path)
		if completeIndexHandler != nil {
			log.Debug("call complete index handler")
			completeIndexHandler(obj)
		}
		return obj
	}
	indexer := NewProjectFileIndex(b.db, workspaceInfo)
	log.Infof("create new index workspace %s", path)
	param := NewAllProjectIndexParam()
	param.CompleteIndexHandler = completeIndexHandler
	b.indexers.Set(path, indexer)

	// 标记indexer已经可以使用
	global.SetIndexerFlag(path, true)

	log.Infof("enqueue index workspace %s", path)
	b.indexQueue <- &IndexEvent{
		EventType: ProjectStartupIndex,
		Data:      path,
		Param:     param,
	}
	log.Debugf("finish new index workspace %s: done", path)

	return indexer
}

// watchWorkspaceIndex 监听工作区索引
func (b *GlobalFileIndex) watchWorkspaceIndex() {
	for {
		event, ok := <-b.indexQueue // 阻塞读取直到获取到数据或队列关闭
		if !ok {                    // 如果通道已关闭并且没有数据，则跳出循环
			log.Warn("index consume ", event.EventType, " data:", event.Data, ": Channel closed, exiting...")
			break
		}
		log.Info("index consume ", event.EventType, " data:", event.Data, ": starting index...")
		if event.EventType == ProjectStartupIndex {
			workspacePath := event.Data.(string)
			b.IndexWorkspace(workspacePath, event.Param)
		}
	}
}

// IndexWorkspace 索引工作区
func (b *GlobalFileIndex) IndexWorkspace(workspace string, param ProjectIndexParam) {
	defer func() {
		startTime := time.Now()
		runtime.GC()
		log.Debugf("index workspace %s: done, gc cost %s", workspace, time.Since(startTime))
		if r := recover(); r != nil {
			log.Error("Error to index workspace:", workspace, " err:", r)
			if global.DebugMode {
				log.Error("crash stack:", string(debug.Stack()))
			}
		}
	}()
	if indexer, ok := b.indexers.Get(workspace); ok {
		err := indexer.IndexWorkspace(param, false)
		if err != nil {
			log.Error("Error to index workspace:", workspace, " err:", err)
		}
	}
}

// GetFileIndexerFromContextWithFallback 从context中获取fileIndexer，如果不存在则从GlobalFileIndexer中获取作为兜底
func GetFileIndexerFromContextWithFallback(ctx context.Context, workspace definition.WorkspaceInfo) (context.Context, *ProjectFileIndex, bool) {
	// 先尝试从context中获取
	if fileIndexer, ok := ctx.Value(definition.ContextKeyFileIndexer).(*ProjectFileIndex); ok && fileIndexer != nil {
		return ctx, fileIndexer, true
	}

	// 兜底逻辑：从GlobalFileIndexer中获取
	projectPath, ok := workspace.GetWorkspaceFolder()
	if !ok {
		log.Warnf("[GetFileIndexerFromContextWithFallback] Unable to get workspace folder")
		return ctx, nil, false
	}

	if GlobalFileIndexer != nil {
		if indexer, found := GlobalFileIndexer.GetIndexer(projectPath); found && indexer != nil {
			log.Debugf("[GetFileIndexerFromContextWithFallback] Successfully obtained fileIndexer from GlobalFileIndexer for workspace: %s", projectPath)
			// 将获取到的 fileIndexer 设置到 context 中
			ctx = context.WithValue(ctx, definition.ContextKeyFileIndexer, indexer)
			return ctx, indexer, true
		}
	}

	log.Warnf("[GetFileIndexerFromContextWithFallback] Failed to get fileIndexer from both context and GlobalFileIndexer for workspace: %s", projectPath)
	return ctx, nil, false
}
