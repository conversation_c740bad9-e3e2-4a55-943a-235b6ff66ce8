package indexing

import (
	"context"
	"cosy/definition"
	"cosy/indexing/chat_indexing"
	"cosy/indexing/chat_indexing/workspace_tree_indexing"
	"cosy/indexing/common"
	"cosy/indexing/completion_indexing"
	"cosy/indexing/index_setting"
	"cosy/lang/indexer"
	"cosy/lang/indexer/misc"
	"cosy/log"
	"cosy/memory/ltm"
	"cosy/similar/finder"
	"cosy/storage"
	"cosy/tree"
	"cosy/util"
	"cosy/util/collection"
	"cosy/util/ops"
	"errors"
	"os"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	merkletree "code.alibaba-inc.com/cosy/mtree"
)

const (
	// 单个工程最大索引的文件数
	MaxIndexFileCount = 6000
	// 单个工程最大统计的文件数
	MaxStatFileCount = 8000
	// 单个工程树形索引最大文件数
	MaxTreeFileCount = 100000

	MaxCompletionIndexFileCount = 10000

	// 单个工程向向量引擎索引的最大文件数
	MaxVectorIndexFileCount = 100000
	// 增量最大一批的索引文件数
	MaxIncrementIndexFileCount = 256
	// 单个工程最大图索引的文件数
	MaxGraphIndexFileCount = 100000

	OpenOperation   = "open"   // 文件打开
	CloseOperation  = "close"  // 文件关闭
	SaveOperation   = "save"   // 文件保存
	DeleteOperation = "delete" // 文件删除，目录删除由Mtree处理成为文件删除
	ChangeOperation = "change" // 代码变更，但未保存，这里调的比较少，jb可能未实现

	MaxFileCacheChanSize = 1000
)

type CompleteIndexHandler = func(fileIndexer *ProjectFileIndex)

var cpuCoresNumber = runtime.NumCPU()

// getIndexConcurrency 计算并返回索引操作的并发度。
// 该函数没有参数。
// 返回值 int：并发度，根据CPU核心数决定。
func getIndexConcurrency() int {
	if cpuCoresNumber > 4 {
		return 2
	}
	return 1
}

type ProjectIndexParam struct {
	EnableFileIndexers   map[string]bool
	CompleteIndexHandler CompleteIndexHandler
}

func NewCompletionProjectIndexParam() ProjectIndexParam {
	return ProjectIndexParam{
		EnableFileIndexers: map[string]bool{
			workspace_tree_indexing.WorkspaceTreeFileIndexerName: true,

			// 这部分是completion indexer的注册
			completion_indexing.MetaFileIndexerName:               true,
			completion_indexing.LangStatFileIndexerName:           true,
			completion_indexing.DependStatFileIndexerName:         true,
			completion_indexing.CompletionRetrieveFileIndexerName: true,
		},
	}
}

func NewChatProjectIndexParam() ProjectIndexParam {
	return ProjectIndexParam{
		EnableFileIndexers: map[string]bool{
			workspace_tree_indexing.WorkspaceTreeFileIndexerName: true,

			// 这部分是chat indexer的注册
			chat_indexing.ChatRetrieveFileTextIndexerName:   true,
			chat_indexing.ChatRetrieveFileVectorIndexerName: true,
			chat_indexing.GraphIndexerName:                  true,
		},
	}
}

func NewChatVectorIndexParam() ProjectIndexParam {
	return ProjectIndexParam{
		EnableFileIndexers: map[string]bool{
			chat_indexing.ChatRetrieveFileVectorIndexerName: true,
		},
	}
}

func newSyncInitProjectIndexParam() ProjectIndexParam {
	return ProjectIndexParam{
		EnableFileIndexers: map[string]bool{
			// 这部分是completion indexer的注册
			completion_indexing.MetaFileIndexerName:               true,
			completion_indexing.LangStatFileIndexerName:           true,
			completion_indexing.DependStatFileIndexerName:         true,
			completion_indexing.CompletionRetrieveFileIndexerName: true,

			// 这部分是chat indexer的注册
			//chat_indexing.ChatRetrieveFileTextIndexerName:   true,
			chat_indexing.ChatRetrieveFileVectorIndexerName: true,
			chat_indexing.GraphIndexerName:                  true,
		},
	}
}

func newAsyncInitProjectIndexParam() ProjectIndexParam {
	return ProjectIndexParam{
		EnableFileIndexers: map[string]bool{
			workspace_tree_indexing.WorkspaceTreeFileIndexerName: true,
		},
	}
}

func NewAllProjectIndexParam() ProjectIndexParam {
	return ProjectIndexParam{
		EnableFileIndexers: map[string]bool{
			workspace_tree_indexing.WorkspaceTreeFileIndexerName: true,

			// 这部分是completion indexer的注册
			completion_indexing.MetaFileIndexerName:               true,
			completion_indexing.LangStatFileIndexerName:           true,
			completion_indexing.DependStatFileIndexerName:         true,
			completion_indexing.CompletionRetrieveFileIndexerName: true,

			// 这部分是chat indexer的注册
			//chat_indexing.ChatRetrieveFileTextIndexerName:   true,
			chat_indexing.ChatRetrieveFileVectorIndexerName: true,
			chat_indexing.GraphIndexerName:                  true,
		},
	}
}

type WorkspaceFileIndexers struct {
	workspaceFileIndexers common.WorkspaceFileIndexer
	maxIndexCount         int
}

var globalWorkspaceFileIndexers = map[string]*WorkspaceFileIndexers{
	completion_indexing.MetaFileIndexerName: {
		workspaceFileIndexers: completion_indexing.MetaFileIndexers,
		maxIndexCount:         MaxIndexFileCount,
	},
	completion_indexing.LangStatFileIndexerName: {
		workspaceFileIndexers: completion_indexing.LangStatFileIndexers,
		maxIndexCount:         MaxStatFileCount,
	},
	completion_indexing.DependStatFileIndexerName: {
		workspaceFileIndexers: completion_indexing.DependStatFileIndexers,
		maxIndexCount:         MaxStatFileCount,
	},
	completion_indexing.CompletionRetrieveFileIndexerName: {
		workspaceFileIndexers: completion_indexing.CompletionRetrieveFileIndexers,
		maxIndexCount:         MaxIndexFileCount,
	},
	workspace_tree_indexing.WorkspaceTreeFileIndexerName: {
		workspaceFileIndexers: workspace_tree_indexing.WorkspaceTreeFileIndexers,
		maxIndexCount:         MaxTreeFileCount,
	},
	chat_indexing.ChatRetrieveFileTextIndexerName: {
		workspaceFileIndexers: chat_indexing.ChatRetrieveFileTextIndexers,
		maxIndexCount:         MaxStatFileCount,
	},
	chat_indexing.ChatRetrieveFileVectorIndexerName: {
		workspaceFileIndexers: chat_indexing.ChatRetrieveFileVectorIndexers,
		maxIndexCount:         MaxVectorIndexFileCount, // 真实建立索引的最大限制不在这里，这里尽可能的扫描文件
	},
	chat_indexing.MemoryRetrieveFileVectorIndexerName: {
		workspaceFileIndexers: chat_indexing.MemoryRetrieveFileVectorIndexers,
		maxIndexCount:         MaxVectorIndexFileCount,
	},
	chat_indexing.MemoryRetrieveFileTextIndexerName: {
		workspaceFileIndexers: chat_indexing.MemoryRetrieveFileTextIndexers,
		maxIndexCount:         MaxStatFileCount,
	},
	chat_indexing.GraphIndexerName: {
		workspaceFileIndexers: chat_indexing.GraphFileIndexers,
		maxIndexCount:         MaxGraphIndexFileCount,
	},
}

type ProjectFileIndex struct {
	db                    storage.KvStore          // 存储系统，用于存储键值对数据
	WorkspaceInfo         definition.WorkspaceInfo // 工作区信息，包含工作区的基本配置和元数据
	Environment           *common.IndexEnvironment
	fileCaches            []definition.FileCache // 文件缓存列表，存储文件内容的缓存实例
	indexMutex            sync.Mutex             // 用于索引操作的互斥锁，保证并发安全性
	indexDebouncer        *util.Debouncer        // 索引防抖函数，用于避免对同一文件的频繁索引
	IgnoreParser          *common.ProjectIgnore  // 项目忽略规则解析器，用于确定哪些文件或目录应被忽略
	workspaceFileIndexers map[string]*WorkspaceFileIndexers
	indexStat             *ProjectIndexStat
	workspaceTree         *tree.MerkleTree // 项目的实时文件树，文件数量小于某个固定值时建立，固定时间间隔重建一次
	isIllegal             bool             // 是否该workspace为非法
	indexInProgress       atomic.Bool      // 防呆机制，防止重复进入IndexWorkspace
}

func NewProjectFileIndex(db storage.KvStore, workspaceInfo definition.WorkspaceInfo) *ProjectFileIndex {
	workspacePath, _ := workspaceInfo.GetWorkspaceFolder()
	projectFileIndexer := &ProjectFileIndex{
		db:                    db,
		WorkspaceInfo:         workspaceInfo,
		Environment:           common.NewIndexEnvironment(context.Background(), db, workspaceInfo),
		workspaceTree:         nil,
		fileCaches:            []definition.FileCache{},
		indexMutex:            sync.Mutex{},
		indexDebouncer:        util.NewDebouncer(3 * time.Second),
		IgnoreParser:          common.NewProjectIgnore(workspacePath),
		workspaceFileIndexers: globalWorkspaceFileIndexers,
		indexStat:             NewProjectIndexStat(),
		isIllegal:             false,
		indexInProgress:       atomic.Bool{},
	}

	projectFileIndexer.indexInProgress.Store(false)
	// 空目录不合法，但要创建indexer，否则问答会报错
	projectFileIndexer.isIllegal = util.IsIllegalExistingWorkspace(workspacePath)
	if projectFileIndexer.isIllegal {
		// 非法workspace
		log.Debugf("[project indexer] workspace is illegal, skip indexing building and tree building, workspacePath: %s", workspacePath)
	} else {
		// 合法workspace
		{
			// indexer初始化
			syncInitParam := newSyncInitProjectIndexParam()
			// 对初始化时间短的indexer做同步初始化
			projectFileIndexer.foreachFileIndexers(syncInitParam.EnableFileIndexers, func(indexerName string, fileIndexer common.FileIndexer) {
				log.Debugf("[project indexer] workspace initialize indexer [%s] success, workspacePath: %s", indexerName, workspacePath)
			})

			go func() {
				// 针对初始化时间长的indexer做异步初始化，不阻塞project_indexer的创建
				// 这里是第一次初始化Mtree的地方
				asyncInitParam := newAsyncInitProjectIndexParam()
				projectFileIndexer.foreachFileIndexers(asyncInitParam.EnableFileIndexers, func(indexerName string, fileIndexer common.FileIndexer) {
					log.Debugf("[project indexer] workspace initialize indexer [%s] success, workspacePath: %s", indexerName, workspacePath)
				})
			}()
		}
		log.Debugf("[project indexer] init success, workspacePath: %s", workspacePath)

		// 开启project_indexer事件监听
		go func() {
			defer func() {
				if r := recover(); r != nil {
					log.Error("Error to index workspace err:", r, " crash stack:", string(debug.Stack()))
				}
			}()

			for {
				time.Sleep(1 * time.Second)
				// 监听Start事件触发
				if index_setting.CheckAndStartIndex(workspacePath) {
					log.Debugf("[project indexer] receive start index event, continue to start index, workspacePath: %s", workspacePath)
					go func() {
						// 问答相关索引，project indexer可重入，由业务方控制重入逻辑
						log.Debugf("[project indexer] start to build chat index, workspacePath: %s", workspacePath)
						projectFileIndexer.IndexWorkspace(NewChatProjectIndexParam(), true)
					}()
					go func() {
						// 补全相关索引，project indexer不可重入，由project indexer控制重入逻辑
						log.Debugf("[project indexer] start to build completion index, workspacePath: %s", workspacePath)
						projectFileIndexer.IndexWorkspace(NewCompletionProjectIndexParam(), false)
					}()
				}
			}
		}()
	}

	return projectFileIndexer
}

func (b *ProjectFileIndex) IndexWorkspace(param ProjectIndexParam, reEntryAllowed bool) error {
	var err error
	workspacePath, ok := b.WorkspaceInfo.GetWorkspaceFolder()
	if !ok {
		return err
	}

	if b.isIllegal {
		log.Errorf("workspace %s is illegal, skip indexing", workspacePath)
		return nil
	}

	if !reEntryAllowed && !b.indexInProgress.CompareAndSwap(false, true) {
		indexerNames := ""
		for indexerName := range param.EnableFileIndexers {
			indexerNames += indexerName + ","
		}
		log.Debugf("[project indexer] workspace is already in indexing, skip indexing, workspacePath: %s, indexerNames: %s", workspacePath, indexerNames)
		return nil
	}

	defer func() {
		if r := recover(); r != nil {
			log.Error("Error to index workspace err:", r, " crash stack:", string(debug.Stack()))
		}

		if !reEntryAllowed {
			b.indexInProgress.Store(false)
		}
	}()

	indexerNames := ""
	for indexerName := range param.EnableFileIndexers {
		indexerNames += indexerName + ","
	}
	log.Infof("start to Index workspace, workspacePath: %s, cores: %d, indexerNames: %s", workspacePath, cpuCoresNumber, indexerNames)
	newEnv := *b.Environment
	b.foreachFileIndexers(param.EnableFileIndexers, func(indexerName string, fileIndexer common.FileIndexer) {
		fileIndexer.PrepareIndexing(&newEnv)
	})

	startTime := time.Now().UnixMilli()
	indexPool := util.NewGoRoutinePool(getIndexConcurrency())
	caches := map[string][]string{}
	scanFileCount := 0
	ops.GlobalProfileStat.AppendAction(ops.IndexBuildingAction)
	defer ops.GlobalProfileStat.RemoveAction(ops.IndexBuildingAction)
	stopFlag := atomic.Bool{}
	stopFlag.Store(false)
	workspaceTree := b.GetWorkspaceTree()
	indexFilePaths := workspaceTree.GetAllLeafNodeFilePath()
	for _, filePath := range indexFilePaths {
		language := util.GetLanguageByFilePath(filePath)
		if _, cacheOk := caches[language]; !cacheOk {
			caches[language] = []string{}
		}
		caches[language] = append(caches[language], filePath)
		if len(caches[language]) >= 100 {
			tempCaches := caches[language]
			indexPool.Run(func() {
				if !b.handleBatchFiles(param, tempCaches) {
					stopFlag.Store(true)
				}
			})
			caches[language] = []string{}
		}
		scanFileCount++
		if stopFlag.Load() {
			log.Debugf("indexer reach limit count")
			return errors.New("all indexer reach limit count")
		}
		if scanFileCount%500 == 0 {
			time.Sleep(10 * time.Millisecond) // 适当休眠
		}
	}

	for _, paths := range caches {
		if len(paths) > 0 {
			virtualFiles := definition.BuildBatchVirtualFile(paths)
			b.foreachFileIndexers(param.EnableFileIndexers, func(indexerName string, fileIndexer common.FileIndexer) {
				fileIndexer.IndexFiles(&newEnv, virtualFiles)
				b.indexStat.IncrementIndexCount(indexerName, uint64(len(virtualFiles)))
			})
		}
	}

	indexPool.PoolWait()

	cost := time.Now().UnixMilli() - startTime
	log.Info("indexer walk files count:", scanFileCount, " walk cost:", cost)
	log.Debugf("index files stat: %s", b.indexStat.String())

	b.foreachFileIndexers(param.EnableFileIndexers, func(indexName string, fileIndexer common.FileIndexer) {
		fileIndexer.CompleteIndexing(&newEnv)
	})

	if param.CompleteIndexHandler != nil {
		param.CompleteIndexHandler(b)
	}

	if param.EnableFileIndexers[workspace_tree_indexing.WorkspaceTreeFileIndexerName] {
		ltm.AsyncRefreshProjectSummaryMemory(newEnv.WorkspaceInfo)
	}
	return nil
}

// handleBatchFiles 处理一批文件，并调用相应的索引器进行索引。
// 参数：
//
//	batchFiles []string：一批文件路径列表。
//
// 返回值：
//
//	bool：如果至少有一个索引器被调用，则返回true，否则返回false。
func (b *ProjectFileIndex) handleBatchFiles(param ProjectIndexParam, batchFiles []string) bool {
	validIndexer := false
	virtualFiles := definition.BuildBatchVirtualFile(batchFiles)
	b.foreachFileIndexers(param.EnableFileIndexers, func(indexerName string, fileIndexer common.FileIndexer) {
		if b.indexStat.GetIndexCount(indexerName) >= uint64(b.workspaceFileIndexers[indexerName].maxIndexCount) {
			// 索引数量达到最大值，不进行索引
			return
		}
		validIndexer = true
		fileIndexer.IndexFiles(b.Environment, virtualFiles)
		b.indexStat.IncrementIndexCount(indexerName, uint64(len(virtualFiles)))
	})
	return validIndexer
}

func (b *ProjectFileIndex) validateFileCache(fileCache definition.FileCache) []definition.FileCache {
	workspacePath, ok := b.WorkspaceInfo.GetWorkspaceFolder()
	if !ok {
		return nil
	}

	/*
		1.过滤非当前工程目录的文件，和ignore的文件
		2.根据operation来进行分发
		2.1 打开操作，直接建立索引
		2.2 关闭操作，不做任何操作
		2.3 修改操作，检查该文件和树中的文件记录是否一致，不一致则建立索引
		2.4 删除操作，获取所有树中叶子结点，合并目录FileCache，进行删除索引
		2.5 保存操作，检查该文件和树中文件记录是否一致，不一致则建立索引
	*/

	fileCaches := make([]definition.FileCache, 0)
	workspaceTree := b.GetWorkspaceTree()
	virtualFile := fileCache.VirtualFile
	filePath := virtualFile.GetFilePath()
	if fileCache.Operation != DeleteOperation {
		// 删除操作不判定ignore
		if filepath.Ext(filePath) != ".sql" && workspaceTree.IsIgnored(filePath) {
			// 1.过滤非当前工程目录的文件，和ignore的文件
			return nil
		}
	}

	if fileCache.Operation == OpenOperation {
		// 2.1 打开操作，直接建立索引
		fileCaches = append(fileCaches, fileCache)
	} else if fileCache.Operation == CloseOperation {
		// 2.2 关闭操作，不做任何操作
	} else if fileCache.Operation == ChangeOperation {
		// 2.3 修改操作，检查该文件和树中的文件记录是否一致，不一致则建立索引
		exist, changed := workspaceTree.FileChanged(filePath)
		if !exist || (exist && changed) {
			fileCaches = append(fileCaches, fileCache)
		}
	} else if fileCache.Operation == DeleteOperation {
		// 2.4 删除操作，获取所有树中叶子结点，合并目录FileCache，进行删除索引
		node := workspaceTree.GetNode(filePath)
		queue := collection.Queue[*merkletree.MerkleNode]{}
		queue.Enqueue(node)
		for !queue.IsEmpty() {
			toDeleteNode, ok := queue.Dequeue()
			if toDeleteNode == nil || !ok {
				continue
			}

			absFilePath := filepath.Join(workspacePath, toDeleteNode.RelativePath)
			isDir := false
			if toDeleteNode.Type != merkletree.TypeLeaf {
				isDir = true
				for _, child := range toDeleteNode.Children {
					queue.Enqueue(child)
				}
			}
			fileCaches = append(fileCaches, definition.NewFileCache(absFilePath, DeleteOperation, isDir))
		}
	} else if fileCache.Operation == SaveOperation {
		// 2.5 保存操作，先查看保存的是文件还是目录
		// 检查该文件和树中文件记录是否一致，不一致则建立索引
		if fileCache.IsDir {
			// 目录
			// 需要将当前目录下的所有文件遍历出来，然后建立索引
			queue := collection.Queue[string]{}
			queue.Enqueue(filePath)
			for !queue.IsEmpty() {
				filePath, ok := queue.Dequeue()
				if !ok {
					continue
				}
				// 判断是目录还是文件，如果是目录，则将目录下的所有文件遍历出来，如果是文件，则建立索引
				// 使用操作系统递归遍历目录
				stat, err := os.Stat(filePath)
				if err != nil {
					log.Errorf("stat file %s failed, err: %v", filePath, err)
					continue
				}
				if stat.IsDir() {
					files, err := os.ReadDir(filePath)
					if err != nil {
						log.Errorf("read dir %s failed, err: %v", filePath, err)
						continue
					}
					for _, file := range files {
						if file == nil {
							continue
						}
						info, err := file.Info()
						if err != nil || info == nil {
							log.Errorf("get file info %s failed, err: %v", filepath.Join(filePath, file.Name()), err)
							continue
						}
						absFilePath := filepath.Join(filePath, file.Name())
						if info.IsDir() {
							queue.Enqueue(absFilePath)
						} else {
							fileCaches = append(fileCaches, definition.NewFileCache(absFilePath, SaveOperation, false))
						}
					}
				} else {
					fileCaches = append(fileCaches, definition.NewFileCache(filePath, SaveOperation, false))
				}
			}
		} else {
			// 文件
			// 直接加入fileCaches
			fileCaches = append(fileCaches, fileCache)
		}

	}

	return fileCaches
}

func (b *ProjectFileIndex) IndexFile(fileCache definition.FileCache) {
	if b.isIllegal {
		return
	}

	validateFileCache := b.validateFileCache(fileCache)

	if len(validateFileCache) <= 0 {
		return
	}

	b.indexMutex.Lock()
	b.fileCaches = append(b.fileCaches, validateFileCache...)
	b.indexMutex.Unlock()
	log.Debugf("index file cache count:%d, operation: %s", len(validateFileCache), fileCache.Operation)
	b.indexDebouncer.Debounce(func() {
		b.indexCacheFiles()
	})
}

// indexCacheFiles
// 具体执行增量索引
func (b *ProjectFileIndex) indexCacheFiles() {
	defer func() {
		if r := recover(); r != nil {
			log.Error("Error to increment index file err:", r, " crash stack:", string(debug.Stack()))
		}
	}()
	b.indexMutex.Lock()
	tmpFileCaches := b.fileCaches
	b.fileCaches = []definition.FileCache{}
	b.indexMutex.Unlock()

	if len(tmpFileCaches) == 0 {
		return
	}

	if _, ok := b.WorkspaceInfo.GetWorkspaceFolder(); !ok {
		return
	}
	log.Info("try index cache file:", len(tmpFileCaches))
	ops.GlobalProfileStat.AppendAction(ops.IndexBuildingAction)
	defer ops.GlobalProfileStat.RemoveAction(ops.IndexBuildingAction)

	// 启动监控协程，读取文件操作队列
	// 为保证事件以正确顺序执行，因此这次使用同步操作，而不是起一个协程操作
	workspacePath, ok := b.WorkspaceInfo.GetWorkspaceFolder()
	if !ok {
		log.Error("Error to start up index workspace:", workspacePath)
	}

	fileCacheMap := make(map[string]definition.FileCache)

	for _, fileCache := range tmpFileCaches {
		// 后来的IDE事件需要覆盖之前来的IDE事件
		fileCacheMap[fileCache.GetFilePath()] = fileCache
	}

	treeIndexFilePaths := make([]string, 0)
	treeDeleteFilePaths := make([]string, 0)
	treeDeleteDirPaths := make([]string, 0)
	for _, fileCache := range fileCacheMap {
		if fileCache.Operation == OpenOperation {
			treeIndexFilePaths = append(treeIndexFilePaths, fileCache.GetFilePath())
		} else if fileCache.Operation == SaveOperation {
			treeIndexFilePaths = append(treeIndexFilePaths, fileCache.GetFilePath())
		} else if fileCache.Operation == ChangeOperation {
			treeIndexFilePaths = append(treeIndexFilePaths, fileCache.GetFilePath())
		} else if fileCache.Operation == DeleteOperation {
			if fileCache.IsDir {
				treeDeleteDirPaths = append(treeDeleteDirPaths, fileCache.GetFilePath())
			} else {
				treeDeleteFilePaths = append(treeDeleteFilePaths, fileCache.GetFilePath())
			}
		}
	}

	workspaceTree := b.GetWorkspaceTree()
	// 先将树中节点进行处理
	// 因为服务端更新操作要求能拿到客户端树的最新节点
	if len(treeIndexFilePaths) > 0 {
		err := workspaceTree.IndexFiles(treeIndexFilePaths)
		if err != nil {
			log.Errorf("[indexer]-[dispatch] [%s] workspacePath: %s, filePaths: %s, err: %v", SaveOperation, workspacePath, treeIndexFilePaths, err)
		} else {
			log.Debugf("[indexer]-[dispatch] [%s] workspacePath: %s, filePaths: %s", SaveOperation, workspacePath, treeIndexFilePaths)
		}
	}

	if len(treeDeleteFilePaths) > 0 {
		err := workspaceTree.DeleteFiles(treeDeleteFilePaths, false)
		if err != nil {
			log.Errorf("[indexer]-[dispatch] [%s files] workspacePath: %s, filePaths: %s, err: %v", DeleteOperation, workspacePath, treeDeleteFilePaths, err)
		} else {
			log.Debugf("[indexer]-[dispatch] [%s files] workspacePath: %s, filePaths: %s", DeleteOperation, workspacePath, treeDeleteFilePaths)
		}
	}

	if len(treeDeleteDirPaths) > 0 {
		err := workspaceTree.DeleteFiles(treeDeleteDirPaths, true)
		if err != nil {
			log.Errorf("[indexer]-[dispatch] [%s dirs] workspacePath: %s, filePaths: %s, err: %v", DeleteOperation, workspacePath, treeDeleteDirPaths, err)
		} else {
			log.Debugf("[indexer]-[dispatch] [%s dirs] workspacePath: %s, filePaths: %s", DeleteOperation, workspacePath, treeDeleteDirPaths)
		}
	}

}

func (b *ProjectFileIndex) GetLangStatFileIndexer() (*completion_indexing.LangStatFileIndexer, bool) {
	if fileIndexer, ok := b.GetFileIndexer(completion_indexing.LangStatFileIndexerName); ok {
		return fileIndexer.(*completion_indexing.LangStatFileIndexer), true
	}
	return nil, false
}

func (b *ProjectFileIndex) GetDependStatFileIndexer() (*completion_indexing.DependStatFileIndexer, bool) {
	if fileIndexer, ok := b.GetFileIndexer(completion_indexing.DependStatFileIndexerName); ok {
		return fileIndexer.(*completion_indexing.DependStatFileIndexer), true
	}
	return nil, false
}

func (b *ProjectFileIndex) GetMetaFileIndexer() (*completion_indexing.MetaFileIndexer, bool) {
	if fileIndexer, ok := b.GetFileIndexer(completion_indexing.MetaFileIndexerName); ok {
		return fileIndexer.(*completion_indexing.MetaFileIndexer), true
	}
	return nil, false
}

func (b *ProjectFileIndex) GetWorkspaceTreeFileIndexer() (*workspace_tree_indexing.WorkspaceTreeFileIndexer, bool) {
	if fileIndexer, ok := b.GetFileIndexer(workspace_tree_indexing.WorkspaceTreeFileIndexerName); ok {
		return fileIndexer.(*workspace_tree_indexing.WorkspaceTreeFileIndexer), true
	}
	return nil, false
}

func (b *ProjectFileIndex) GetChatRetrieveFileTextIndexer() (*chat_indexing.ChatRetrieveFileTextIndexer, bool) {
	if fileIndexer, ok := b.workspaceFileIndexers[chat_indexing.ChatRetrieveFileTextIndexerName].workspaceFileIndexers.GetOrAddIndexer(b.Environment); ok {
		return fileIndexer.(*chat_indexing.ChatRetrieveFileTextIndexer), true
	}
	return nil, false
}

func (b *ProjectFileIndex) GetChatRetrieveFileVectorIndexer() (*chat_indexing.ChatRetrieveFileVectorIndexer, bool) {
	if fileIndexer, ok := b.workspaceFileIndexers[chat_indexing.ChatRetrieveFileVectorIndexerName].workspaceFileIndexers.GetOrAddIndexer(b.Environment); ok {
		return fileIndexer.(*chat_indexing.ChatRetrieveFileVectorIndexer), true
	}
	return nil, false
}

func (b *ProjectFileIndex) GetMemoryRetrieveFileVectorIndexer() (*chat_indexing.MemoryRetrieveFileVectorIndexer, bool) {
	if fileIndexer, ok := b.workspaceFileIndexers[chat_indexing.MemoryRetrieveFileVectorIndexerName].workspaceFileIndexers.GetOrAddIndexer(b.Environment); ok {
		return fileIndexer.(*chat_indexing.MemoryRetrieveFileVectorIndexer), true
	}
	return nil, false
}

func (b *ProjectFileIndex) GetMemoryRetrieveFileTextIndexer() (*chat_indexing.MemoryRetrieveFileTextIndexer, bool) {
	if fileIndexer, ok := b.workspaceFileIndexers[chat_indexing.MemoryRetrieveFileTextIndexerName].workspaceFileIndexers.GetOrAddIndexer(b.Environment); ok {
		return fileIndexer.(*chat_indexing.MemoryRetrieveFileTextIndexer), true
	}
	return nil, false
}

func (b *ProjectFileIndex) GetCompletionRetrieveFileIndexer() (*completion_indexing.CompletionRetrieveFileIndexer, bool) {
	if fileIndexer, ok := b.GetFileIndexer(completion_indexing.CompletionRetrieveFileIndexerName); ok {
		return fileIndexer.(*completion_indexing.CompletionRetrieveFileIndexer), true
	}
	return nil, false
}

func (b *ProjectFileIndex) GetGraphFileIndexer() (*chat_indexing.GraphFileIndexer, bool) {
	if fileIndexer, ok := b.GetFileIndexer(chat_indexing.GraphIndexerName); ok {
		return fileIndexer.(*chat_indexing.GraphFileIndexer), true
	}
	return nil, false
}

func (b *ProjectFileIndex) GetFileIndexer(indexerName string) (common.FileIndexer, bool) {
	workspace, ok := b.Environment.WorkspaceInfo.GetWorkspaceFolder()
	if !ok {
		return nil, false
	}
	wpIndexers := b.workspaceFileIndexers[indexerName]
	if wpIndexers == nil {
		return nil, false
	}
	if fileIndexer, ok := wpIndexers.workspaceFileIndexers.GetIndexer(workspace); ok {
		return fileIndexer, true
	}
	return nil, false
}

func (b *ProjectFileIndex) foreachFileIndexers(enableFileIndexers map[string]bool, handler func(indexerName string, fileIndexer common.FileIndexer)) {
	for indexerName, wpFileIndexer := range b.workspaceFileIndexers {
		if enableFileIndexers[indexerName] {
			if fileIndexer, ok := wpFileIndexer.workspaceFileIndexers.GetOrAddIndexer(b.Environment); ok {
				handler(indexerName, fileIndexer)
			}
		}
	}
}

func (b *ProjectFileIndex) foreachValidFileIndexers(handler func(fileIndexer common.FileIndexer)) {
	workspace, _ := b.Environment.WorkspaceInfo.GetWorkspaceFolder()
	for _, wpFileIndexer := range b.workspaceFileIndexers {
		if fileIndexer, ok := wpFileIndexer.workspaceFileIndexers.GetIndexer(workspace); ok {
			handler(fileIndexer)
		}
	}
}

// WalkDirectory 扫描目录
func (b *ProjectFileIndex) WalkDirectory(targetDirectory string, fileHandler func(path string) error) (int, error) {
	ignoreFileCount := 0

	// 这里把GlobalIgnoreEnable 置为true
	// 考虑是索引应该过滤如svg、png等文件
	dirIgnoreRule := &common.IgnoreRule{
		GlobalIgnoreEnable: true,
		IsDir:              true,
	}
	fileIgnoreRule := &common.IgnoreRule{
		GlobalIgnoreEnable: true,
		IsDir:              false,
	}
	err := filepath.Walk(targetDirectory, func(path string, info os.FileInfo, err error) error {
		if info == nil {
			return nil
		}
		if info.IsDir() {
			if b.IgnoreParser.IsIgnored(targetDirectory, path, dirIgnoreRule) {
				//log.Debug("ignore dir: " + path)
				return filepath.SkipDir
			}
			return nil // 如果是目录或者被忽略，不做处理
		}
		if b.IgnoreParser.IsIgnored(targetDirectory, path, fileIgnoreRule) {
			ignoreFileCount++
			return nil
		}
		err = fileHandler(path)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		log.Error("walk workspace error: ", err)
	}
	return ignoreFileCount, err
}

func (b *ProjectFileIndex) RemoveFileQueue(path string) {
	if util.IsGlobalIgnoreFile(path) {
		return
	}
	b.Environment.ActiveFileQueue.RemoveFileQueue(path)
}

func (b *ProjectFileIndex) AddFileQueue(path string, code string) {
	defer func() {
		if r := recover(); r != nil {
			log.Error("Error to add active file err:", r, " crash stack:", string(debug.Stack()))
		}
	}()

	workspacePath, ok := b.WorkspaceInfo.GetWorkspaceFolder()
	if !ok {
		return
	}
	if b.Environment == nil {
		log.Debugf("%s project indexer not ready. environment is nil", workspacePath)
		return
	}
	// 如果不是这个目录下的文件，则不添加到活跃文件队列
	if !strings.HasPrefix(path, workspacePath) {
		log.Debugf("ignore active file %s not in workspace: %s", path, workspacePath)
		return
	}

	// 这里把GlobalIgnoreEnable 置为true
	// 考虑是索引应该过滤如svg、png等文件
	fileIgnoreRule := &common.IgnoreRule{
		GlobalIgnoreEnable: true,
		IsDir:              false,
	}
	if util.IsLargeFileByPath(path) || (b.IgnoreParser != nil && b.IgnoreParser.IsIgnored(workspacePath, path, fileIgnoreRule)) {
		log.Debug("ignore active file:" + path)
		return
	}
	language := util.GetLanguageByFilePath(path)
	var metas map[string]interface{}
	metaFileIndexer, ok := completion_indexing.MetaFileIndexers.GetFileIndexer(workspacePath)
	if ok {
		if _, ok := metaFileIndexer.GetLangIndexer(language); ok {
			//metas, err = langIndexer.IndexFile(context.Background(), workspacePath, definition.NewVirtualFile(path))
			metas = make(map[string]interface{})
			// 添加新的元素
			metas[path] = misc.MiscLangMeta{
				Code: code,
				MetaFile: indexer.MetaFile{
					FileName:     util.GetFileName(path),
					FileFullPath: path,
				},
			}

		}
	}

	b.Environment.ActiveFileQueue.AddFileQueue(language, common.ActiveFile{
		FilePath:  path,
		Metas:     metas,
		Timestamp: time.Now().UnixMilli(),
	})

	finder.PreheatMatchChunks(context.Background(), path, b.WorkspaceInfo)
}

func (b *ProjectFileIndex) GetWorkspaceTree() *tree.MerkleTree {
	if b.isIllegal {
		workspacePath, _ := b.WorkspaceInfo.GetWorkspaceFolder()
		return tree.NewTmpMerkleTree(workspacePath)
	}

	if b.workspaceTree != nil {
		return b.workspaceTree
	}

	b.indexMutex.Lock()
	defer b.indexMutex.Unlock()

	if b.workspaceTree != nil {
		return b.workspaceTree
	}

	workspacePath, ok := b.WorkspaceInfo.GetWorkspaceFolder()
	if !ok {
		return nil
	}

	b.workspaceTree = tree.NewWorkspaceMerkleTree(workspacePath)
	if b.workspaceTree == nil {
		log.Errorf("[workspaceTree] new workspace tree failed, workspacePath: %s, skip indexing and tree building", workspacePath)
		return tree.NewTmpMerkleTree(workspacePath)
	}

	param := NewAllProjectIndexParam()
	b.foreachFileIndexers(param.EnableFileIndexers, func(indexerName string, fileIndexer common.FileIndexer) {
		b.workspaceTree.RegisterFileIndexer(indexerName, fileIndexer)
	})

	return b.workspaceTree
}

func (b *ProjectFileIndex) GetActiveFileDirs() []string {
	activeDirs := []string{}
	workspaceUri, _ := b.WorkspaceInfo.GetWorkspaceFolder()
	activeFiles := b.Environment.ActiveFileQueue.GetAllActiveFiles()
	for _, file := range activeFiles {
		absDir := filepath.Base(file.FilePath)
		relDir, _ := filepath.Rel(workspaceUri, absDir)
		activeDirs = append(activeDirs, relDir)
	}
	return activeDirs
}

// WalkDirectoryForTest
// 为了codebase测试用的，业务方请不要调用
func (b *ProjectFileIndex) WalkDirectoryForTest(targetDirectory string, fileHandler func(path string) error) int {
	ignoreFileCount := 0

	// 这里把GlobalIgnoreEnable 置为true
	// 考虑是索引应该过滤如svg、png等文件
	dirIgnoreRule := &common.IgnoreRule{
		GlobalIgnoreEnable: true,
		IsDir:              true,
	}
	fileIgnoreRule := &common.IgnoreRule{
		GlobalIgnoreEnable: true,
		IsDir:              false,
	}

	err := filepath.Walk(targetDirectory, func(path string, info os.FileInfo, err error) error {
		if info == nil {
			return nil
		}
		if info.IsDir() {
			if b.IgnoreParser.IsIgnored(targetDirectory, path, dirIgnoreRule) {
				//log.Debug("ignore dir: " + path)
				return filepath.SkipDir
			}
			return nil // 如果是目录或者被忽略，不做处理
		}
		if b.IgnoreParser.IsIgnored(targetDirectory, path, fileIgnoreRule) {
			ignoreFileCount++
			return nil
		}
		err = fileHandler(path)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		log.Error("walk workspace error: ", err)
	}
	return ignoreFileCount
}

// GetIgnoreParserForTest
// 为了ignore测试用的，业务方请不要调用
func (b *ProjectFileIndex) GetIgnoreParserForTest() *common.ProjectIgnore {
	return b.IgnoreParser
}
