package regionha

import (
	"cosy/client"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/util"
	"fmt"
	"net/http"
	"strings"
	"time"
)

const (
	repoRegionPrefix = "repo"

	remoteAgentRegionPrefix = "qts"
)

// 判断是否是合法的数据region片段
// 参考：repo2
func isValidRegion(region string, regionType string) bool {
	if regionType == definition.DataRegionTypeCodebase {
		return strings.HasPrefix(region, repoRegionPrefix)
	} else if regionType == definition.DataRegionTypeRemoteAgent {
		return strings.HasPrefix(region, remoteAgentRegionPrefix)
	}
	return false
}

//	 根据region选择任一匹配的域名，忽略test/daily前缀，支持正式/日常/测试域名
//		入参：
//		region 			区域字符串，参考 repo2
//		endpoints		一组候选的Endpoint
//
// - 域名格式参考：
// - https://repo2.qoder.sh
// - https://daily-repo2.qoder.sh
// - https://test-repo2.qoder.sh
func selectMatchedEndpoint(region string, endpoints []string) string {
	if len(endpoints) == 0 {
		return ""
	}

	if region == "" {
		return endpoints[0]
	}

	var matchedEndpoints []string
	var officialEndpoint string

	for _, endpoint := range endpoints {
		// 提取域名部分，去掉协议前缀
		domain := strings.TrimPrefix(endpoint, "https://")
		domain = strings.TrimPrefix(domain, "http://")

		// 去掉路径部分
		if idx := strings.Index(domain, "/"); idx != -1 {
			domain = domain[:idx]
		}

		// 提取域名的第一段
		parts := strings.Split(domain, ".")
		if len(parts) == 0 {
			continue
		}

		firstPart := parts[0]

		// 去掉 daily- 或 test- 前缀
		cleanFirstPart := firstPart
		if strings.HasPrefix(firstPart, "daily-") {
			cleanFirstPart = strings.TrimPrefix(firstPart, "daily-")
		} else if strings.HasPrefix(firstPart, "test-") {
			cleanFirstPart = strings.TrimPrefix(firstPart, "test-")
		}

		// 精确匹配第一段
		if cleanFirstPart == region {
			matchedEndpoints = append(matchedEndpoints, endpoint)

			// 检查是否是正式域名（没有 daily- 或 test- 前缀）
			if firstPart == cleanFirstPart {
				if officialEndpoint == "" {
					officialEndpoint = endpoint
				}
			}
		}
	}

	// 优先返回正式域名
	if officialEndpoint != "" {
		return officialEndpoint
	}

	// 如果有匹配的域名，返回第一个
	if len(matchedEndpoints) > 0 {
		return matchedEndpoints[0]
	}

	// 如果都不匹配，返回第一个域名作为默认值
	return ""
}

// selectAndUpdateBestDataRegion 选择延迟最低的数据节点并更新到远端
func selectAndUpdateBestDataRegion(existDataEndpoint *definition.UserDataRegion, endpointConfig *remote.RemoteRegionConfig) *definition.UserDataRegion {
	if existDataEndpoint == nil {
		existDataEndpoint = &definition.UserDataRegion{}
	}

	// 遍历每个数据节点类型，选择最佳端点
	for nodeType, endpoints := range endpointConfig.DataNodeMap {
		if len(endpoints) == 0 {
			continue
		}

		if isDataRegionConfigured(existDataEndpoint, nodeType) {
			continue
		}

		bestEndpoint, minLatency := selectBestEndpoint(nodeType, endpoints)
		if bestEndpoint == "" {
			log.Warnf("No available endpoint found for node type: %s", nodeType)
			continue
		}

		log.Infof("Selected best endpoint for %s: %s (latency: %dms)", nodeType, bestEndpoint, minLatency)
		setEndpointByType(existDataEndpoint, nodeType, bestEndpoint)
	}

	// 如果找到了可用的端点，更新到远端
	if !existDataEndpoint.IsEmpty() {
		if err := pushUserDataRegionConfig(existDataEndpoint); err != nil {
			log.Errorf("Failed to update remote data node config: %v", err)
		}
	}

	return existDataEndpoint
}

func isDataRegionConfigured(dataEndpoint *definition.UserDataRegion, nodeType string) bool {
	if strings.EqualFold(nodeType, definition.DataRegionTypeCodebase) {
		return dataEndpoint.Repo != ""
	} else if strings.EqualFold(nodeType, definition.DataRegionTypeRemoteAgent) {
		return dataEndpoint.Ra != ""
	}
	return false
}

// setEndpointByType 根据节点类型设置端点
func setEndpointByType(dataEndpoint *definition.UserDataRegion, nodeType, endpoint string) {
	switch strings.ToLower(nodeType) {
	case strings.ToLower(definition.DataRegionTypeCodebase):
		region := util.ParseRegionFromEndpoint(endpoint)
		if region != "" && isValidRegion(region, definition.DataRegionTypeCodebase) {
			dataEndpoint.Repo = region
		}
	case strings.ToLower(definition.DataRegionTypeRemoteAgent):
		region := util.ParseRegionFromEndpoint(endpoint)
		if region != "" && isValidRegion(region, definition.DataRegionTypeRemoteAgent) {
			dataEndpoint.Ra = region
		}
	default:
		log.Warnf("Unknown node type: %s", nodeType)
	}
}

// selectBestEndpoint 选择延迟最低的数据节点端点
func selectBestEndpoint(nodeType string, endpoints []string) (string, int) {
	if len(endpoints) == 0 {
		return "", 0
	}

	// 如果只有一个端点，直接测试并返回
	if len(endpoints) == 1 {
		endpoint := endpoints[0]
		latency := measureEndpointLatency(nodeType, endpoint)
		return endpoint, latency
	}

	// 测试所有端点的延迟并选择最佳的
	latencies := testAllEndpoints(nodeType, endpoints)
	return findBestEndpoint(latencies)
}

// testAllEndpoints 测试一组数据节点端点的延迟
func testAllEndpoints(nodeType string, endpoints []string) []endpointLatency {
	if endpoints == nil {
		return nil
	}

	results := make([]endpointLatency, 0, len(endpoints))
	for _, endpoint := range endpoints {
		latency := measureEndpointLatency(nodeType, endpoint)
		results = append(results, endpointLatency{url: endpoint, latency: latency})
		// 避免所有请求集中到一个端点
		time.Sleep(sleepBetweenRequests)
	}

	return results
}

// findBestEndpoint 从延迟测试结果中找到最佳端点
func findBestEndpoint(latencies []endpointLatency) (string, int) {
	if len(latencies) == 0 {
		return "", 0
	}

	bestEndpoint := ""
	minLatency := invalidLatency

	for _, result := range latencies {
		if result.latency < minLatency {
			minLatency = result.latency
			bestEndpoint = result.url
		}
	}

	return bestEndpoint, minLatency
}

// measureEndpointLatency 测试单个端点的延迟
func measureEndpointLatency(nodeType string, endpoint string) int {
	pingPath := definition.UrlPathPing
	url := remote.BuildAlgoForRequestUrl(endpoint, pingPath)

	// 收集所有成功的延迟值
	latencies := make([]int, 0, latencyTestCount)

	for i := 0; i < latencyTestCount; i++ {
		latency, err := pingEndpoint(url)
		if err != nil {
			log.Debugf("Failed to ping endpoint %s (attempt %d): %v", endpoint, i+1, err)
			continue
		}

		latencies = append(latencies, latency)
		time.Sleep(sleepBetweenRequests)
	}

	if len(latencies) == 0 {
		log.Warnf("All ping attempts failed for endpoint: %s", endpoint)
		return invalidLatency
	}

	// 如果样本数量少于3个，直接计算平均值
	if len(latencies) < 3 {
		total := 0
		for _, latency := range latencies {
			total += latency
		}
		averageLatency := total / len(latencies)
		log.Debugf("Endpoint %s average latency over %d tests: %dms (no trimming)", endpoint, len(latencies), averageLatency)
		return averageLatency
	}

	// 找到最大值和最小值的索引
	minIdx, maxIdx := 0, 0
	for i := 1; i < len(latencies); i++ {
		if latencies[i] < latencies[minIdx] {
			minIdx = i
		}
		if latencies[i] > latencies[maxIdx] {
			maxIdx = i
		}
	}

	// 计算去除最高和最低值后的平均延迟
	total := 0
	count := 0
	for i, latency := range latencies {
		// 跳过最大值和最小值
		if i != minIdx && i != maxIdx {
			total += latency
			count++
		}
	}

	averageLatency := total / count
	log.Debugf("Endpoint %s trimmed average latency over %d tests (removed min: %dms, max: %dms): %dms",
		endpoint, count, latencies[minIdx], latencies[maxIdx], averageLatency)

	return averageLatency
}

func buildMeasureLatencyUrl(nodeType string, endpoint string) {

}

// pingEndpoint 向指定端点发送ping请求并返回延迟
func pingEndpoint(url string) (int, error) {
	start := time.Now()

	req, err := http.NewRequest(http.MethodPost, url, nil)
	if err != nil {
		return 0, fmt.Errorf("failed to create ping request: %w", err)
	}

	httpClient := client.GetDefaultClient()
	httpClient.Timeout = requestTimeout

	resp, err := httpClient.Do(req)
	if err != nil {
		return 0, fmt.Errorf("failed to send ping request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("ping returned status code %d", resp.StatusCode)
	}

	latency := int(time.Since(start) / time.Millisecond)
	return latency, nil
}

// 合并本地region HA配置与远程region配置
func getMergedEndpointConfig() *remote.RemoteRegionConfig {
	// 初始化结果配置
	mergedConfig := &remote.RemoteRegionConfig{
		CenterNodes: make([]string, 0),
		DataNodeMap: make(map[string][]string),
		InferNodes:  make([]string, 0),
	}

	// 获取本地HA配置
	localHAConfig := remote.CheckLoadBundledConfig()
	if localHAConfig != nil {
		// 复制本地配置到合并结果
		mergedConfig.CenterNodes = append(mergedConfig.CenterNodes, localHAConfig.CenterNodes...)
		mergedConfig.InferNodes = append(mergedConfig.InferNodes, localHAConfig.InferNodes...)

		// 复制本地数据节点配置
		for nodeType, endpoints := range localHAConfig.DataNodes {
			mergedConfig.DataNodeMap[nodeType] = append(mergedConfig.DataNodeMap[nodeType], endpoints...)
		}
	}

	// 获取远程配置并优先使用远程配置
	remoteEndpointConfig := remote.FetchRemoteEndpointConfig()
	if remoteEndpointConfig == nil || remoteEndpointConfig.IsEmpty() {
		log.Warnf("No remote endpoint config available, using local HA config")
		return mergedConfig
	}
	// 远程配置优先：用远程配置覆盖本地配置
	if len(remoteEndpointConfig.CenterNodes) > 0 {
		mergedConfig.CenterNodes = remoteEndpointConfig.CenterNodes
	}

	if len(remoteEndpointConfig.InferNodes) > 0 {
		mergedConfig.InferNodes = remoteEndpointConfig.InferNodes
	}

	// 合并数据节点配置，远程优先
	for nodeType, endpoints := range remoteEndpointConfig.DataNodeMap {
		if len(endpoints) > 0 {
			mergedConfig.DataNodeMap[nodeType] = endpoints
		}
	}

	log.Infof("Merged endpoint config: center nodes: %d, infer nodes: %d, data node types: %d",
		len(mergedConfig.CenterNodes), len(mergedConfig.InferNodes), len(mergedConfig.DataNodeMap))

	return mergedConfig
}
