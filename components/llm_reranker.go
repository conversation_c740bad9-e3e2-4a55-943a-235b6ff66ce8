package components

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/prompt"
	"cosy/remote"
	"cosy/tokenizer"
	"cosy/user"
	"errors"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
)

const (
	// DefaultTokenThreshold 默认的token阈值
	DefaultTokenThreshold = 100000
	// MaxTruncateIterations 最大截断迭代次数，防止无限循环
	MaxTruncateIterations = 10
)

// LLMReranker 是基于LLM的重排序器
type LLMReranker struct {
	ReturnDocuments bool
	TokenThreshold  int // token阈值，用于智能截断
}

// NewLLMReranker 创建一个基于LLM的重排序器
func NewLLMReranker() *LLMReranker {
	return &LLMReranker{
		ReturnDocuments: defaultReturnDocuments,
		TokenThreshold:  DefaultTokenThreshold,
	}
}

// getSystemPrompt 获取系统提示词
func (reranker *LLMReranker) getSystemPrompt() string {
	systemPrompt, err := prompt.Engine.RenderLLMRerankSystemPrompt(prompt.LLMRerankPromptInput{
		BaseInput: prompt.BaseInput{},
	})
	if err != nil {
		// Not logging the error to prevent prompt content leakage
		log.Debugf("[codebase] Failed to render LLM rerank system prompt: %v", err)
		return "You are a code retrieval assistant. I will provide you with a user query and some retrieved code documents. Please rank the code documents based on the user's query."
	}
	return systemPrompt
}

// calculateTotalTokens 计算所有文档的总token数量
func (reranker *LLMReranker) calculateTotalTokens(documents []string) (int, error) {
	totalTokens := 0
	for _, doc := range documents {
		tokens := tokenizer.GetTokenCountWithSimpleAsciiTokenizer(doc)
		totalTokens += tokens
	}
	return totalTokens, nil
}

// smartTruncateDocuments 智能截断文档，确保总token数不超过阈值
func (reranker *LLMReranker) smartTruncateDocuments(documents []string) ([]string, error) {
	if len(documents) == 0 {
		return documents, nil
	}

	currentDocs := documents

	// 迭代截断直到满足token限制
	for iteration := 0; iteration < MaxTruncateIterations; iteration++ {
		// 计算当前文档的总token数
		totalTokens, err := reranker.calculateTotalTokens(currentDocs)
		if err != nil {
			return nil, fmt.Errorf("failed to calculate total tokens in iteration %d: %w", iteration, err)
		}

		// 如果总token数未超过阈值，截断完成
		if totalTokens <= reranker.TokenThreshold {
			break
		}

		// 如果已经达到最大迭代次数，抛出错误
		if iteration == MaxTruncateIterations-1 {
			return nil, fmt.Errorf("failed to truncate documents within %d iterations: current documents=%d, tokens=%d, threshold=%d",
				MaxTruncateIterations, len(currentDocs), totalTokens, reranker.TokenThreshold)
		}

		// 计算需要保留的文档比例：阈值/当前总token数
		retainRatio := float64(reranker.TokenThreshold) / float64(totalTokens)

		// 计算保留的文档数量（向下取整确保不超过阈值）
		retainCount := int(math.Floor(float64(len(currentDocs)) * retainRatio))

		// 确保至少保留1个文档
		if retainCount < 1 {
			retainCount = 1
		}

		// 如果计算出的保留数量没有减少，强制减少以避免无限循环
		if retainCount >= len(currentDocs) {
			retainCount = len(currentDocs) - 1
			if retainCount < 1 {
				retainCount = 1
			}
		}

		// 按比例截断文档（保留前retainCount个）
		currentDocs = currentDocs[:retainCount]

		// 如果只剩1个文档，检查是否仍然超过阈值
		if len(currentDocs) <= 1 {
			// 计算单个文档的token数
			singleDocTokens, _ := reranker.calculateTotalTokens(currentDocs)
			if singleDocTokens > reranker.TokenThreshold {
				return nil, fmt.Errorf("single document (%d tokens) exceeds threshold (%d), cannot truncate further",
					singleDocTokens, reranker.TokenThreshold)
			}
			break
		}
	}

	// 最终检查：如果循环结束后仍然超过阈值，返回错误
	finalTokens, _ := reranker.calculateTotalTokens(currentDocs)
	if finalTokens > reranker.TokenThreshold {
		return nil, fmt.Errorf("failed to truncate documents within %d iterations: final documents=%d, tokens=%d, threshold=%d",
			MaxTruncateIterations, len(currentDocs), finalTokens, reranker.TokenThreshold)
	}

	return currentDocs, nil
}

func (reranker *LLMReranker) RerankDocuments(ctx context.Context, query string, documents []string, limit int) (*RerankResponse, error) {
	if len(documents) == 0 {
		return &RerankResponse{
			StatusCode: http.StatusOK,
			Output:     RerankResponseOutput{Results: []RerankResponseResult{}},
		}, nil
	}

	// 首先尝试LLM重排序
	llmRes, llmErr := reranker.llmRerank(ctx, query, documents, limit)
	if llmErr == nil && llmRes != nil && llmRes.Output.Results != nil {
		return llmRes, nil
	}

	// LLM重排序失败，降级到LingmaReranker
	log.Debugf("[codebase] LLM reranker failed (%v), falling back to base reranker", llmErr)
	baseReranker := NewLingmaReranker()
	lingmaRes, lingmaErr := baseReranker.RerankDocuments(ctx, query, documents, limit)
	if lingmaErr == nil && lingmaRes != nil {
		return lingmaRes, nil
	}

	// 两种重排序都失败了
	return nil, fmt.Errorf("[codebase] both LLM reranker (%v) and base reranker (%v) failed", llmErr, lingmaErr)
}

// llmRerank 执行纯LLM重排序逻辑
func (reranker *LLMReranker) llmRerank(ctx context.Context, query string, documents []string, limit int) (*RerankResponse, error) {
	// 使用智能截断机制替代简单的数量限制
	truncatedDocuments, err := reranker.smartTruncateDocuments(documents)
	if err != nil {
		return nil, fmt.Errorf("failed to truncate documents: %w", err)
	}

	// 更新文档列表为截断后的结果
	documents = truncatedDocuments

	// 创建原始索引数组（0, 1, 2, ...）
	originalIndexes := make([]int, len(documents))
	for i := range originalIndexes {
		originalIndexes[i] = i
	}

	// 直接对所有文档进行排序
	rerankResults, err := reranker.rankDocuments(ctx, query, documents, originalIndexes, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to rank documents: %w", err)
	}

	return &RerankResponse{
		StatusCode: http.StatusOK,
		Output:     RerankResponseOutput{Results: rerankResults},
		Usage:      RerankResponseUsage{TotalTokens: 0},
	}, nil
}

func (reranker *LLMReranker) rankDocuments(ctx context.Context, query string, filteredDocuments []string, originalIndexes []int, limit int) ([]RerankResponseResult, error) {
	// 构建排序提示词内容
	promptContent := reranker.buildRankPromptContent(query, filteredDocuments)

	// 构建消息
	messages := buildSystemUserMessage(reranker.getSystemPrompt(), promptContent)

	outputResp, err := InvokeMemoryModelWithTask(ctx, messages, definition.AgentTaskMemoryRerank, 5*time.Second)
	if err != nil {
		return nil, fmt.Errorf("failed to invoke model for ranking: %w", err)
	}

	// 解析排序响应
	return reranker.parseRankResponse(outputResp.Text, filteredDocuments, originalIndexes, limit)
}

func (reranker *LLMReranker) buildRankPromptContent(query string, documents []string) string {
	// 构建候选文档列表
	var candidatesBuilder strings.Builder
	for i, doc := range documents {
		candidatesBuilder.WriteString(fmt.Sprintf("Code Item Index: %d\n", i+1))
		candidatesBuilder.WriteString(fmt.Sprintf("Code Item Path: document_%d\n", i+1))
		candidatesBuilder.WriteString(fmt.Sprintf("Code Item Content: %s\n\n", doc))
	}

	// 使用模板系统渲染用户提示词
	input := prompt.LLMRerankPromptInput{
		BaseInput: prompt.BaseInput{},
		Query:     query,
		Documents: documents,
	}

	userPrompt, err := prompt.Engine.RenderLLMRerankUserPrompt(input)
	if err != nil {
		log.Errorf("[codebase] Failed to render LLM rerank user prompt: %v", err)
		// 回退到简单的字符串替换
		fallbackTemplate := `Query: {{ query }}

Candidates:
{% for item_index, item_path, item_content in codebase_recall_result %}
Code Item Index: {{ item_index }}
Code Item Path: {{ item_path }}
Code Item Content: {{ item_content }}
{% endfor %}

Based on the user's query and the retrieved code documents, rerank the code documents based on the relevance to the query in descending order, with the most relevant code document at the top. Return the indexes of the code documents, separated by the | character. Do not provide any additional explanation.
DO NOT DISCARD ANY CODE ITEM.`

		content := strings.Replace(fallbackTemplate, "{{ query }}", query, -1)
		content = strings.Replace(content, "{% for item_index, item_path, item_content in codebase_recall_result %}\nCode Item Index: {{ item_index }}\nCode Item Path: {{ item_path }}\nCode Item Content: {{ item_content }}\n{% endfor %}", candidatesBuilder.String(), 1)
		return content
	}

	return userPrompt
}

// InvokeMemoryModelWithTask 调用记忆相关的模型
func InvokeMemoryModelWithTask(ctx context.Context, messages []*agentDefinition.Message, task string, timeout time.Duration) (remote.CommonAgentResponse, error) {

	requestId := getStringFromContext(ctx, common.KeyRequestId)
	sessionId := getStringFromContext(ctx, common.KeySessionId)
	remoteAsk := definition.RemoteChatAsk{
		RequestId: requestId,
		// RequestSetId: requestSetId,
		ChatRecordId: requestId,
		SessionId:    sessionId,
		Stream:       true,
		Version:      "3",
		Parameters: map[string]any{
			"temperature": 0.1,
		},
		UserType: user.GetUserType(),
		AgentId:  definition.AgentCommonAgentId,
		TaskId:   task,
		Messages: messages,
	}

	requestParams := remote.CommonAgentRequestParams{
		ServiceName:         definition.AgentCommonAgentService,
		FetchKey:            "llm_model_result",
		ModelRequest:        remoteAsk,
		Timeout:             timeout,
		RequestId:           requestId,
		AgentId:             definition.AgentCommonAgentId,
		OutputFormatVersion: "2",
	}
	return remote.ExecuteStreamedAgentRequestWithModelConfig(ctx, requestParams, nil)
}
func getStringFromContext(ctx context.Context, key string) string {
	if value := ctx.Value(key); value != nil {
		if valueStr, ok := value.(string); ok {
			if valueStr != "" {
				return valueStr
			} else {
				return uuid.NewString()
			}
		} else {
			return uuid.NewString()
		}
	} else {
		return uuid.NewString()
	}
}
func buildSystemUserMessage(systemPrompt, userPrompt string) []*agentDefinition.Message {
	agentMessages := make([]*agentDefinition.Message, 0, 2)

	agentMessages = append(agentMessages, &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeSystem,
		Content: systemPrompt,
	})

	agentMessages = append(agentMessages, &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeUser,
		Content: userPrompt,
	})
	return agentMessages
}
func (reranker *LLMReranker) parseRankResponse(responseText string, filteredDocuments []string, originalIndexes []int, limit int) ([]RerankResponseResult, error) {
	responseText = strings.TrimSpace(responseText)

	// 校验：检查响应是否为空
	if responseText == "" {
		return []RerankResponseResult{}, errors.New("empty response")
	}

	// 解析索引，使用容错机制
	indexesStr := strings.Split(responseText, "|")
	var validIndexes []int
	var invalidItems []string
	var validRanks []int // 记录有效索引在原始响应中的位置

	// 添加去重map
	seenIndexes := make(map[int]bool)

	// 第一遍：分离有效和无效项目，同时去重
	for rank, indexStr := range indexesStr {
		indexStr = strings.TrimSpace(indexStr)
		if indexStr == "" {
			continue
		}

		index, err := strconv.Atoi(indexStr)
		if err != nil {
			// 记录无效项目
			invalidItems = append(invalidItems, indexStr)
			continue
		}

		// 转换为0-based索引并检查范围
		index = index - 1
		if index >= 0 && index < len(filteredDocuments) {
			// 去重检查：如果已经见过这个索引，跳过
			if seenIndexes[index] {
				continue
			}

			seenIndexes[index] = true
			validIndexes = append(validIndexes, index)
			validRanks = append(validRanks, rank) // 记录原始位置
		} else {
			invalidItems = append(invalidItems, fmt.Sprintf("%d(out-of-range)", index+1))
		}
	}

	// 容错检查：计算无效项目比例
	totalItems := len(validIndexes) + len(invalidItems)
	if totalItems == 0 {
		return []RerankResponseResult{}, errors.New("no items found in response")
	}

	invalidRatio := float64(len(invalidItems)) / float64(totalItems)

	// 如果无效项目超过30%，拒绝整个响应
	if invalidRatio > 0.3 {
		log.Debugf("[codebase] Too many invalid items in rank response: %d/%d (%.1f%% > 30%%), invalid items: %v",
			len(invalidItems), totalItems, invalidRatio*100, invalidItems)
		return []RerankResponseResult{}, fmt.Errorf("invalid items ratio %.1f%% exceeds 30%% threshold", invalidRatio*100)
	}

	// 如果有无效项目但在容忍范围内，记录警告
	if len(invalidItems) > 0 {
		log.Debugf("[codebase] Found %d invalid items in rank response (%.1f%% <= 30%%), extracting valid results: invalid items: %v",
			len(invalidItems), invalidRatio*100, invalidItems)
	}

	// 第二遍：构建有效结果，保持原始排序
	var results []RerankResponseResult
	for _, index := range validIndexes {
		// 获取原始文档索引
		originalIndex := originalIndexes[index]

		// 所有结果的相关性分数都设为1.0
		relevanceScore := 1.0

		result := RerankResponseResult{
			Document: RerankResponseDocument{
				Text: filteredDocuments[index],
			},
			Index:          originalIndex,
			RelevanceScore: relevanceScore,
		}
		results = append(results, result)
	}

	if len(results) == 0 {
		return []RerankResponseResult{}, errors.New("no valid results after tolerance filtering")
	}

	// 限制返回结果数量
	if limit > 0 && len(results) > limit {
		results = results[:limit]
	}

	return results, nil
}
