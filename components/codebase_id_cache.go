package components

import (
	"context"
	"sync"
	"time"

	"github.com/patrickmn/go-cache"
)

var (
	codebaseIdCacheRwLock = sync.RWMutex{}
	codebaseIdCache       = cache.New(time.Hour*2, time.Hour)
)

func GetCodebaseId(ctx context.Context, workspacePath string) (string, error) {
	codebaseId, ok := codebaseIdCache.Get(workspacePath)
	if ok {
		return codebaseId.(string), nil
	}

	codebaseIdCacheRwLock.Lock()
	defer codebaseIdCacheRwLock.Unlock()
	if codebaseId, ok := codebaseIdCache.Get(workspacePath); ok {
		return codebaseId.(string), nil
	}

	handle, err := NewServerHandle(workspacePath)
	if err != nil {
		return "", err
	}

	codebaseId, err = handle.GetCodebaseId()
	if err != nil {
		return "", err
	}

	codebaseIdCache.Set(workspacePath, codebaseId, time.Hour*2)
	return codebaseId.(string), nil
}
