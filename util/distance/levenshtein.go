package distance

import "fmt"

func CalculateWordLevenshtein(str1, str2 []string) (dist, prefixLen, suffixLen int) {
	insCost := 1
	subCost := 1
	delCost := 1
	maxCost := 0
	l1, l2 := len(str1), len(str2)
	// trim common prefix, if any, as it doesn't affect the distance
	for ; prefixLen < l1 && prefixLen < l2; prefixLen++ {
		if str1[prefixLen] != str2[prefixLen] {
			break
		}
	}
	str1, str2 = str1[prefixLen:], str2[prefixLen:]
	l1 -= prefixLen
	l2 -= prefixLen
	// trim common suffix, if any, as it doesn't affect the distance
	for 0 < l1 && 0 < l2 {
		if str1[l1-1] != str2[l2-1] {
			str1, str2 = str1[:l1], str2[:l2]
			break
		}
		l1--
		l2--
		suffixLen++
	}
	// if the first string is empty, the distance is the length of the second string times the cost of insertion
	if l1 == 0 {
		dist = l2 * insCost
		return
	}
	// if the second string is empty, the distance is the length of the first string times the cost of deletion
	if l2 == 0 {
		dist = l1 * delCost
		return
	}

	// variables used in inner "for" loops
	var y, dy, c, l int

	// if maxCost is greater than or equal to the maximum possible distance, it's equivalent to 'unlimited'
	if maxCost > 0 {
		if subCost < delCost+insCost {
			if maxCost >= l1*subCost+(l2-l1)*insCost {
				maxCost = 0
			}
		} else {
			if maxCost >= l1*delCost+l2*insCost {
				maxCost = 0
			}
		}
	}

	if maxCost > 0 {
		// prefer the longer string first, to minimize time;
		// a swap also transposes the meanings of insertion and deletion.
		if l1 < l2 {
			str1, str2, l1, l2, insCost, delCost = str2, str1, l2, l1, delCost, insCost
		}

		// the length differential times cost of deletion is a lower bound for the cost;
		// if it is higher than the maxCost, there is no point going into the main calculation.
		if dist = (l1 - l2) * delCost; dist > maxCost {
			return
		}

		d := make([]int, l1+1)

		// offset and length of d in the current row
		doff, dlen := 0, 1
		for y, dy = 1, delCost; y <= l1 && dy <= maxCost; dlen++ {
			d[y] = dy
			y++
			dy = y * delCost
		}
		// fmt.Printf("%q -> %q: init doff=%d dlen=%d d[%d:%d]=%v\n", str1, str2, doff, dlen, doff, doff+dlen, d[doff:doff+dlen])

		for x := 0; x < l2; x++ {
			dy, d[doff] = d[doff], d[doff]+insCost
			for doff < l1 && d[doff] > maxCost && dlen > 0 {
				if str1[doff] != str2[x] {
					dy += subCost
				}
				doff++
				dlen--
				if c = d[doff] + insCost; c < dy {
					dy = c
				}
				dy, d[doff] = d[doff], dy
			}
			for y, l = doff, doff+dlen-1; y < l; dy, d[y] = d[y], dy {
				if str1[y] != str2[x] {
					dy += subCost
				}
				if c = d[y] + delCost; c < dy {
					dy = c
				}
				y++
				if c = d[y] + insCost; c < dy {
					dy = c
				}
			}
			if y < l1 {
				if str1[y] != str2[x] {
					dy += subCost
				}
				if c = d[y] + delCost; c < dy {
					dy = c
				}
				for ; dy <= maxCost && y < l1; dy, d[y] = dy+delCost, dy {
					y++
					dlen++
				}
			}
			// fmt.Printf("%q -> %q: x=%d doff=%d dlen=%d d[%d:%d]=%v\n", str1, str2, x, doff, dlen, doff, doff+dlen, d[doff:doff+dlen])
			if dlen == 0 {
				dist = maxCost + 1
				return
			}
		}
		if doff+dlen-1 < l1 {
			dist = maxCost + 1
			return
		}
		dist = d[l1]
	} else {
		// ToDo: This is O(l1*l2) time and O(min(l1,l2)) space; investigate if it is
		// worth to implement diagonal approach - O(l1*(1+dist)) time, up to O(l1*l2) space
		// http://www.csse.monash.edu.au/~lloyd/tildeStrings/Alignment/92.IPL.html

		// prefer the shorter string first, to minimize space; time is O(l1*l2) anyway;
		// a swap also transposes the meanings of insertion and deletion.
		if l1 > l2 {
			str1, str2, l1, l2, insCost, delCost = str2, str1, l2, l1, delCost, insCost
		}
		d := make([]int, l1+1)

		for y = 1; y <= l1; y++ {
			d[y] = y * delCost
		}
		for x := 0; x < l2; x++ {
			dy, d[0] = d[0], d[0]+insCost
			for y = 0; y < l1; dy, d[y] = d[y], dy {
				if str1[y] != str2[x] {
					dy += subCost
				}
				if c = d[y] + delCost; c < dy {
					dy = c
				}
				y++
				if c = d[y] + insCost; c < dy {
					dy = c
				}
			}
		}
		dist = d[l1]
	}

	return
}

// Operation 表示编辑操作
type Operation struct {
	Type     string // "替换", "删除", "插入"
	Position int
	OldChar  rune
	NewChar  rune
}

// Difference 表示差异项
type Difference struct {
	SourcePart    string
	TargetPart    string
	StartPosition int
}

// EditDistanceCalculator 编辑距离计算器
type EditDistanceCalculator struct{}

// NewEditDistanceCalculator 创建新的编辑距离计算器实例
func NewEditDistanceCalculator() *EditDistanceCalculator {
	return &EditDistanceCalculator{}
}

// isChineseChar 判断字符是否为中文字符
func (e *EditDistanceCalculator) isChineseChar(r rune) bool {
	return r >= '\u4e00' && r <= '\u9fff'
}

// isChineseText 判断文本是否包含中文字符
func (e *EditDistanceCalculator) isChineseText(text string) bool {
	for _, r := range text {
		if e.isChineseChar(r) {
			return true
		}
	}
	return false
}

// isChineseText 判断文本是否全是中文字符
func (e *EditDistanceCalculator) IsAllChineseText(text string) bool {
	for _, r := range text {
		if !e.isChineseChar(r) {
			return false
		}
	}
	return true
}

// CalculateEditDistanceWithOperations 计算两个字符串的编辑距离并返回具体的修改操作
func (e *EditDistanceCalculator) CalculateEditDistanceWithOperations(str1, str2 string) (int, []Operation) {
	runes1 := []rune(str1)
	runes2 := []rune(str2)
	m, n := len(runes1), len(runes2)

	// 创建DP表格
	dp := make([][]int, m+1)
	for i := range dp {
		dp[i] = make([]int, n+1)
	}

	// 初始化边界条件
	for i := 0; i <= m; i++ {
		dp[i][0] = i // 删除所有字符
	}
	for j := 0; j <= n; j++ {
		dp[0][j] = j // 插入所有字符
	}

	// 填充DP表格
	for i := 1; i <= m; i++ {
		for j := 1; j <= n; j++ {
			if runes1[i-1] == runes2[j-1] {
				dp[i][j] = dp[i-1][j-1] // 字符相同，不需要操作
			} else {
				dp[i][j] = 1 + min(
					dp[i-1][j],   // 删除
					dp[i][j-1],   // 插入
					dp[i-1][j-1], // 替换
				)
			}
		}
	}

	// 回溯获取操作序列
	operations := e.backtrackOperations(runes1, runes2, dp)

	return dp[m][n], operations
}

// min 返回三个整数的最小值
func min(a, b, c int) int {
	if a <= b && a <= c {
		return a
	} else if b <= c {
		return b
	}
	return c
}

// backtrackOperations 回溯DP表格获取具体的操作序列
func (e *EditDistanceCalculator) backtrackOperations(runes1, runes2 []rune, dp [][]int) []Operation {
	var operations []Operation
	i, j := len(runes1), len(runes2)

	for i > 0 || j > 0 {
		if i > 0 && j > 0 && runes1[i-1] == runes2[j-1] {
			// 字符相同，不需要操作
			i--
			j--
		} else if i > 0 && j > 0 && dp[i][j] == dp[i-1][j-1]+1 {
			// 替换操作
			operations = append(operations, Operation{
				Type:     "替换",
				Position: i - 1,
				OldChar:  runes1[i-1],
				NewChar:  runes2[j-1],
			})
			i--
			j--
		} else if i > 0 && dp[i][j] == dp[i-1][j]+1 {
			// 删除操作
			operations = append(operations, Operation{
				Type:     "删除",
				Position: i - 1,
				OldChar:  runes1[i-1],
			})
			i--
		} else if j > 0 && dp[i][j] == dp[i][j-1]+1 {
			// 插入操作
			operations = append(operations, Operation{
				Type:     "插入",
				Position: i,
				NewChar:  runes2[j-1],
			})
			j--
		}
	}

	// 反转操作序列，因为是从后向前回溯的
	for i, j := 0, len(operations)-1; i < j; i, j = i+1, j-1 {
		operations[i], operations[j] = operations[j], operations[i]
	}

	return operations
}

// FormatOperations 格式化操作序列为可读的字符串
func (e *EditDistanceCalculator) FormatOperations(operations []Operation) []string {
	var formatted []string
	for _, op := range operations {
		switch op.Type {
		case "替换":
			formatted = append(formatted, fmt.Sprintf("位置%d: 将 '%c' 替换为 '%c'", op.Position, op.OldChar, op.NewChar))
		case "删除":
			formatted = append(formatted, fmt.Sprintf("位置%d: 删除 '%c'", op.Position, op.OldChar))
		case "插入":
			formatted = append(formatted, fmt.Sprintf("位置%d: 插入 '%c'", op.Position, op.NewChar))
		}
	}
	return formatted
}

// FindDifferences 找到两个字符串的差异项（连续的不同部分）
func (e *EditDistanceCalculator) FindDifferences(str1, str2 string) []Difference {
	_, operations := e.CalculateEditDistanceWithOperations(str1, str2)

	if len(operations) == 0 {
		return []Difference{}
	}

	var differences []Difference
	currentDiff := struct {
		start    int
		str1Part string
		str2Part string
	}{start: -1}

	for _, op := range operations {
		// 检查是否需要开始新的差异项
		if currentDiff.start > -2 && op.Position != currentDiff.start+1 {

			differences = append(differences, Difference{
				SourcePart:    currentDiff.str1Part,
				TargetPart:    currentDiff.str2Part,
				StartPosition: currentDiff.start,
			})
			currentDiff = struct {
				start    int
				str1Part string
				str2Part string
			}{start: -2}
		}

		switch op.Type {
		case "替换":
			currentDiff.start = op.Position
			currentDiff.str1Part += string(op.OldChar)
			currentDiff.str2Part += string(op.NewChar)
		case "删除":
			currentDiff.start = op.Position
			currentDiff.str1Part += string(op.OldChar)
		case "插入":
			currentDiff.start = op.Position - 1
			currentDiff.str2Part += string(op.NewChar)
		}
	}

	// 处理最后一个差异项
	if currentDiff.start != -2 {
		differences = append(differences, Difference{
			SourcePart:    currentDiff.str1Part,
			TargetPart:    currentDiff.str2Part,
			StartPosition: currentDiff.start,
		})
	}

	return differences
}
