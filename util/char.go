package util

import (
	"fmt"
	"strings"
	"unicode"
)

// FullwidthHalfwidthComparator 全角半角字符比较器
type FullwidthHalfwidthComparator struct {
	// 全角到半角的映射表
	fullwidthToHalfwidth map[rune]rune
	// 半角到全角的映射表
	halfwidthToFullwidth map[rune]rune
}

// NewFullwidthHalfwidthComparator 创建新的全角半角比较器
func NewFullwidthHalfwidthComparator() *FullwidthHalfwidthComparator {
	comparator := &FullwidthHalfwidthComparator{
		fullwidthToHalfwidth: make(map[rune]rune),
		halfwidthToFullwidth: make(map[rune]rune),
	}

	// 初始化映射表
	comparator.initMappings()
	return comparator
}

// initMappings 初始化全角半角字符映射表
func (c *FullwidthHalfwidthComparator) initMappings() {
	// 数字映射 (０-９ -> 0-9)
	for i := 0; i <= 9; i++ {
		fullwidth := rune('０' + i)
		halfwidth := rune('0' + i)
		c.fullwidthToHalfwidth[fullwidth] = halfwidth
		c.halfwidthToFullwidth[halfwidth] = fullwidth
	}

	// 大写字母映射 (Ａ-Ｚ -> A-Z)
	for i := 0; i < 26; i++ {
		fullwidth := rune('Ａ' + i)
		halfwidth := rune('A' + i)
		c.fullwidthToHalfwidth[fullwidth] = halfwidth
		c.halfwidthToFullwidth[halfwidth] = fullwidth
	}

	// 小写字母映射 (ａ-ｚ -> a-z)
	for i := 0; i < 26; i++ {
		fullwidth := rune('ａ' + i)
		halfwidth := rune('a' + i)
		c.fullwidthToHalfwidth[fullwidth] = halfwidth
		c.halfwidthToFullwidth[halfwidth] = fullwidth
	}

	// 常用标点符号映射
	punctuationMappings := map[rune]rune{
		'　': ' ',  // 全角空格 -> 半角空格
		'！': '!',  // 感叹号
		'？': '?',  // 问号
		'，': ',',  // 逗号
		'。': '.',  // 句号
		'；': ';',  // 分号
		'：': ':',  // 冒号
		'（': '(',  // 左括号
		'）': ')',  // 右括号
		'［': '[',  // 左方括号
		'］': ']',  // 右方括号
		'｛': '{',  // 左大括号
		'｝': '}',  // 右大括号
		'＠': '@',  // @符号
		'＃': '#',  // #号
		'＄': '$',  // 美元符
		'％': '%',  // 百分号
		'＾': '^',  // 脱字符
		'＆': '&',  // 与号
		'＊': '*',  // 星号
		'＋': '+',  // 加号
		'－': '-',  // 减号
		'＝': '=',  // 等号
		'＿': '_',  // 下划线
		'～': '~',  // 波浪号
		'｀': '`',  // 反引号
		'｜': '|',  // 竖线
		'＼': '\\', // 反斜杠
		'／': '/',  // 斜杠
		'“': '"',  // 左双引号
		'”': '"',  // 右双引号
		'‘': '\'', // 左单引号
		'’': '\'', // 右单引号
	}

	// 添加标点符号映射
	for fullwidth, halfwidth := range punctuationMappings {
		c.fullwidthToHalfwidth[fullwidth] = halfwidth
		c.halfwidthToFullwidth[halfwidth] = fullwidth
	}
}

// ToHalfwidth 将字符转换为半角
func (c *FullwidthHalfwidthComparator) ToHalfwidth(r rune) rune {
	if halfwidth, exists := c.fullwidthToHalfwidth[r]; exists {
		return halfwidth
	}
	return r
}

// ToFullwidth 将字符转换为全角
func (c *FullwidthHalfwidthComparator) ToFullwidth(r rune) rune {
	if fullwidth, exists := c.halfwidthToFullwidth[r]; exists {
		return fullwidth
	}
	return r
}

// IsFullwidthHalfwidthEqual 判断两个字符是否全半角相等
func (c *FullwidthHalfwidthComparator) IsFullwidthHalfwidthEqual(r1, r2 rune) bool {
	// 如果字符完全相同，直接返回true
	if r1 == r2 {
		return true
	}

	// 将两个字符都转换为半角后比较
	halfwidth1 := c.ToHalfwidth(r1)
	halfwidth2 := c.ToHalfwidth(r2)

	return halfwidth1 == halfwidth2
}

// IsStringFullwidthHalfwidthEqual 判断两个字符串是否全半角相等
func (c *FullwidthHalfwidthComparator) IsStringFullwidthHalfwidthEqual(s1, s2 string) bool {
	runes1 := []rune(s1)
	runes2 := []rune(s2)

	// 长度不同则不相等
	if len(runes1) != len(runes2) {
		return false
	}

	// 逐字符比较
	for i := 0; i < len(runes1); i++ {
		if !c.IsFullwidthHalfwidthEqual(runes1[i], runes2[i]) {
			return false
		}
	}

	return true
}

// NormalizeToHalfwidth 将字符串中的全角字符转换为半角
func (c *FullwidthHalfwidthComparator) NormalizeToHalfwidth(s string) string {
	var result strings.Builder
	for _, r := range s {
		result.WriteRune(c.ToHalfwidth(r))
	}
	return result.String()
}

// NormalizeToFullwidth 将字符串中的半角字符转换为全角
func (c *FullwidthHalfwidthComparator) NormalizeToFullwidth(s string) string {
	var result strings.Builder
	for _, r := range s {
		result.WriteRune(c.ToFullwidth(r))
	}
	return result.String()
}

// IsFullwidth 判断字符是否为全角字符
func (c *FullwidthHalfwidthComparator) IsFullwidth(r rune) bool {
	// 检查是否在全角字符范围内
	if r >= 0xFF01 && r <= 0xFF5E { // 全角ASCII字符范围
		return true
	}

	// 检查是否在映射表中
	_, exists := c.fullwidthToHalfwidth[r]
	return exists
}

// IsHalfwidth 判断字符是否为半角字符
func (c *FullwidthHalfwidthComparator) IsHalfwidth(r rune) bool {
	// ASCII字符范围
	if r >= 0x20 && r <= 0x7E {
		return true
	}

	// 检查是否在映射表中
	_, exists := c.halfwidthToFullwidth[r]
	return exists
}

// GetCharacterInfo 获取字符的详细信息
func (c *FullwidthHalfwidthComparator) GetCharacterInfo(r rune) map[string]interface{} {
	info := make(map[string]interface{})

	info["character"] = string(r)
	info["unicode_code"] = fmt.Sprintf("U+%04X", r)
	info["is_fullwidth"] = c.IsFullwidth(r)
	info["is_halfwidth"] = c.IsHalfwidth(r)

	if c.IsFullwidth(r) {
		info["halfwidth_equivalent"] = string(c.ToHalfwidth(r))
	}

	if c.IsHalfwidth(r) {
		info["fullwidth_equivalent"] = string(c.ToFullwidth(r))
	}

	// 字符类型判断
	if unicode.IsLetter(r) {
		info["type"] = "letter"
	} else if unicode.IsDigit(r) {
		info["type"] = "digit"
	} else if unicode.IsPunct(r) {
		info["type"] = "punctuation"
	} else if unicode.IsSpace(r) {
		info["type"] = "space"
	} else {
		info["type"] = "other"
	}

	return info
}

// FindFullwidthHalfwidthDifferences 找出两个字符串中全角半角不匹配的位置
func (c *FullwidthHalfwidthComparator) FindFullwidthHalfwidthDifferences(s1, s2 string) []map[string]interface{} {
	var differences []map[string]interface{}

	runes1 := []rune(s1)
	runes2 := []rune(s2)
	maxLen := len(runes1)
	if len(runes2) > maxLen {
		maxLen = len(runes2)
	}

	for i := 0; i < maxLen; i++ {
		var r1, r2 rune
		var char1, char2 string

		if i < len(runes1) {
			r1 = runes1[i]
			char1 = string(r1)
		} else {
			char1 = ""
		}

		if i < len(runes2) {
			r2 = runes2[i]
			char2 = string(r2)
		} else {
			char2 = ""
		}

		// 如果位置超出某一字符串长度，或字符不相等（包括全半角）
		if char1 == "" || char2 == "" || !c.IsFullwidthHalfwidthEqual(r1, r2) {
			diff := map[string]interface{}{
				"position": i,
				"char1":    char1,
				"char2":    char2,
			}

			if char1 != "" {
				diff["char1_info"] = c.GetCharacterInfo(r1)
			}
			if char2 != "" {
				diff["char2_info"] = c.GetCharacterInfo(r2)
			}

			differences = append(differences, diff)
		}
	}

	return differences
}
