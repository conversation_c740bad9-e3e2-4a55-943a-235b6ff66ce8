package git

import (
	"fmt"
	"github.com/go-git/go-git/v5"
	"runtime"
)

// GetGitCommand returns the appropriate git command for the current OS
func GetGitCommand() string {
	if runtime.GOOS == "windows" {
		return "git.exe"
	}
	return "git"
}

// GetRemoteURLs returns a map of remote names and their URLs for the given repository path
func GetRemoteURLs(repoPath string) (map[string][]string, error) {
	// 存储结果的 map
	result := make(map[string][]string)

	// 打开仓库
	r, err := git.PlainOpen(repoPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open repository: %w", err)
	}

	// 获取所有远程仓库
	remotes, err := r.Remotes()
	if err != nil {
		return nil, fmt.Errorf("failed to get remotes: %w", err)
	}

	// 遍历远程仓库
	for _, remote := range remotes {
		config := remote.Config()
		result[config.Name] = config.URLs
	}

	return result, nil
}
