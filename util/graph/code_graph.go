package graph

import (
	cosy_definition "cosy/definition"
	"cosy/experiment"
	"cosy/util"
	"path/filepath"
	"strings"
	"sync"
)

func GetGraphStoreDir() string {
	return filepath.Join(util.GetCosyHomePath(), "index", "graph", "v1")
}

func GetGraphStoreDbName() string {
	return "graph.db"
}

var GraphWorkspaceWorkerLock = &sync.Map{}

func GetGlobalGraphSwitch() bool {
	return experiment.ConfigService.GetBoolConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeyGlobalSwitch,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeyGlobalSwitch,
	)
}

func GetAcceptParseNodeLanguageExt() map[string]bool {
	acceptLanguageExt := experiment.ConfigService.GetStringConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeySupportParseNodeLanguageList,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeySupportParseNodeLanguageList,
	)
	split := strings.Split(acceptLanguageExt, ",")

	res := map[string]bool{}
	for _, ext := range split {
		res[ext] = true
	}
	return res
}

func GetAcceptParseRelationLanguageExt() map[string]bool {
	acceptLanguageExt := experiment.ConfigService.GetStringConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeySupportParseRelationLanguageList,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeySupportParseRelationLanguageList,
	)
	split := strings.Split(acceptLanguageExt, ",")

	res := map[string]bool{}
	for _, ext := range split {
		res[ext] = true
	}
	return res
}

func GetAcceptIdeSearchNodeLanguageExt() map[string]bool {
	acceptLanguageExt := experiment.ConfigService.GetStringConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeySupportIdeSearchNodeLanguageList,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeySupportIdeSearchNodeLanguageList,
	)
	split := strings.Split(acceptLanguageExt, ",")

	res := map[string]bool{}
	for _, ext := range split {
		res[ext] = true
	}
	return res
}

func GetAcceptIdeSearchRelationLanguageExt() map[string]bool {
	acceptLanguageExt := experiment.ConfigService.GetStringConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeySupportIdeSearchRelationLanguageList,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeySupportIdeSearchRelationLanguageList,
	)
	split := strings.Split(acceptLanguageExt, ",")

	res := map[string]bool{}
	for _, ext := range split {
		res[ext] = true
	}
	return res
}

func GetAcceptSearchNodeLanguageExt() map[string]bool {
	acceptLanguageExt := experiment.ConfigService.GetStringConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeySupportParseNodeLanguageList,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeySupportSearchNodeLanguageList,
	)
	split := strings.Split(acceptLanguageExt, ",")

	res := map[string]bool{}
	for _, ext := range split {
		res[ext] = true
	}
	return res
}

func GetAcceptSearchRelationLanguageExt() map[string]bool {
	acceptLanguageExt := experiment.ConfigService.GetStringConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeySupportSearchRelationLanguageList,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeySupportSearchRelationLanguageList,
	)
	split := strings.Split(acceptLanguageExt, ",")

	res := map[string]bool{}
	for _, ext := range split {
		res[ext] = true
	}
	return res
}

// GetExpendGraphTimeout 获取代码图谱扩展图超时时间，返回毫秒值
func GetExpendGraphTimeout() int {
	return experiment.ConfigService.GetIntConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeyExpendGraphTimeout,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeyExpendGraphTimeout,
	)
}

func GetNextStageMinThreshold() float64 {
	value := experiment.ConfigService.GetDoubleConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeyStageMinThreshold,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeyStageMinThreshold,
	)

	if value <= 0 || value > 1 {
		value = cosy_definition.DefaultCodebaseGraphKeyStageMinThreshold
	}
	return value
}

func GetMaxFileCount() int64 {
	value := experiment.ConfigService.GetIntConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeyMaxFileCount,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeyMaxFileCount,
	)

	if value <= 0 {
		value = cosy_definition.DefaultCodebaseGraphKeyMaxFileCount
	}
	return int64(value)
}

func GetManagerScanInterval() int {
	value := experiment.ConfigService.GetIntConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeyManagerScanInterval,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeyManagerScanInterval,
	)

	if value <= 0 {
		value = cosy_definition.DefaultCodebaseGraphKeyManagerScanInterval
	}
	return value
}

func GetManagerCompensateRound() int {
	value := experiment.ConfigService.GetIntConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeyManagerCompensateRound,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeyManagerCompensateRound,
	)

	if value <= 0 {
		value = cosy_definition.DefaultCodebaseGraphKeyManagerCompensateRound
	}
	return value
}

func GetWorkerScanInterval() int {
	value := experiment.ConfigService.GetIntConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeyWorkerScanInterval,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeyWorkerScanInterval,
	)

	if value <= 0 {
		value = cosy_definition.DefaultCodebaseGraphKeyWorkerScanInterval
	}
	return value
}

func GetWorkerSingleFileTimeout() int {
	value := experiment.ConfigService.GetIntConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeyWorkerSingleFileTimeout,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeyWorkerSingleFileTimeout,
	)

	if value <= 0 {
		value = cosy_definition.DefaultCodebaseGraphKeyWorkerSingleFileTimeout
	}
	return value
}

func GetWorkerScanBatchSize() int {
	value := experiment.ConfigService.GetIntConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeyWorkerScanBatchSize,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeyWorkerScanBatchSize,
	)

	if value <= 0 {
		value = cosy_definition.DefaultCodebaseGraphKeyWorkerScanBatchSize
	}
	return value
}

func GetWorkerIdleTime() int {
	value := experiment.ConfigService.GetIntConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeyWorkerIdleTime,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeyWorkerIdleTime,
	)

	if value <= 0 {
		value = cosy_definition.DefaultCodebaseGraphKeyWorkerIdleTime
	}
	return value
}

func GetSearchByIdeSwitch() bool {
	return experiment.ConfigService.GetBoolConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeySearchByIdeSwitch,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeySearchByIdeSwitch,
	)
}

func GetRemoteImportSwitch() bool {
	return experiment.ConfigService.GetBoolConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeyRemoteImportSwitch,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeyRemoteImportSwitch,
	)
}

func GetIgnoreGraphParseError() map[string]bool {
	ignoreError := experiment.ConfigService.GetStringConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeyIgnoreParseErrorList,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeyIgnoreParseErrorList,
	)
	split := strings.Split(ignoreError, "|--|")

	res := map[string]bool{}
	for _, ext := range split {
		res[ext] = true
	}
	return res
}

func GetMaxFailedFileCount() int {
	value := experiment.ConfigService.GetIntConfigValue(
		cosy_definition.ExperimentCodebaseGraphKeyWorkerSingleFileMaxFailedCount,
		experiment.ConfigScopeClient,
		cosy_definition.DefaultCodebaseGraphKeyWorkerSingleFileMaxFailedCount,
	)

	if value <= 0 {
		value = cosy_definition.DefaultCodebaseGraphKeyWorkerSingleFileMaxFailedCount
	}
	return value
}

func JudgeCanUseIdeGraph(fileAbsPath string) bool {
	if !GetSearchByIdeSwitch() {
		return false
	}

	canUse := GetAcceptIdeSearchRelationLanguageExt()
	ext := strings.ToLower(filepath.Ext(fileAbsPath))
	if _, ok := canUse[ext]; ok {
		return true
	}
	return false
}

func JudgeCanUseIdeNode(fileAbsPath string) bool {
	if !GetSearchByIdeSwitch() {
		return false
	}

	canUse := GetAcceptIdeSearchNodeLanguageExt()
	ext := strings.ToLower(filepath.Ext(fileAbsPath))
	if _, ok := canUse[ext]; ok {
		return true
	}
	return false
}
