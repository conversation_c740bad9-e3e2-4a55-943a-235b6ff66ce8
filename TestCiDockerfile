FROM zeus-acr-cn-beijing-1-registry.cn-beijing.cr.aliyuncs.com/codeup/lingma_api_base:v1.0 AS builder
COPY . /root/cosy_code
WORKDIR /root/cosy_code
ENV CGO_ENABLED=1
ENV GOPROXY='http://goproxy.alibaba-inc.com,direct'
ENV CC=x86_64-linux-musl-gcc
ENV CXX=x86_64-linux-musl-g++
ENV GOARCH=amd64
ENV GOOS=linux
ENV PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/x86_64-linux-musl/output/bin:/usr/local/bin/go/bin:/usr/local/bin/zig-linux-x86_64-0.13.0"
RUN go mod vendor && go build -buildvcs=false -tags "ne,prod" -trimpath -ldflags "-linkmode external -extldflags -static -s -w -X cosy/global.CosyVersion=1.3.8" -o out/x86_64_linux/Lingma

FROM zeus-acr-cn-beijing-1-registry.cn-beijing.cr.aliyuncs.com/codeup/lingma_api_test_env:v1.0

# 将构建后的二进制文件复制到新的镜像中
COPY --from=builder /root/cosy_code/out/ /
# 设置容器启动时运行的命令
CMD ["sh","-c","cd /x86_64_linux && ./Lingma start"]