package extension

type ExtensionError struct {
	Msg string
}

// 实现 error 接口
func (e ExtensionError) Error() string {
	return e.Msg
}

// ErrConnectionLoss 通信异常
var ErrConnectionLoss = ExtensionError{Msg: "connection loss error"}

// ErrInternalSystem 系统内部错误
var ErrInternalSystem = ExtensionError{Msg: "internal system error"}

// ErrConnectionTimeout 通信超时
var ErrConnectionTimeout = ExtensionError{Msg: "communicate timeout error"}

var ErrNodeNotFound = ExtensionError{Msg: "node binary not found error"}
