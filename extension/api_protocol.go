package extension

import (
	"context"
	"cosy/definition"
)

type ExtensionApiBaseResult struct {
	IsSuccess    bool                   `json:"isSuccess"`
	RequestId    string                 `json:"requestId"` //请求id
	ErrorCode    string                 `json:"errorCode"`
	ErrorMessage string                 `json:"errorMessage"`
	ExtraInfo    map[string]interface{} `json:"extraInfo,omitempty"`
}

type ExtensionConfigDefinition struct {
	Commands            []CommandProviderWrapper `json:"commands"`
	ContextProviders    []ContextProviderWrapper `json:"contextProviders"`
	ContentHandlers     []ContentHandlerWrapper  `json:"contentHandlers"`
	PostContentHandlers []ContentHandlerWrapper  `json:"postContentHandlers"`
}

type ExtensionConfigRefreshApiResult struct {
	ExtensionApiBaseResult
	Data ExtensionConfigDefinition `json:"data"`
}

type ExtensionConfigRefreshApiRequest struct {
	Config ExtensionConfig `json:"config"`
}

type GetContextResponseApiResult struct {
	ExtensionApiBaseResult
	Result definition.GetContextResponse `json:"data"`
}

type GetContextResponseApiRequest struct {
	Identifier             string                       `json:"identifier"`
	ContextProviderRequest definition.GetContextRequest `json:"contextProviderRequest"`
	Sdk                    definition.SDKTool           `json:"sdkTool"`
	Payload                definition.ContextPayload    `json:"payload"`
	ctx                    context.Context
}

type CommandExecutionApiRequest struct {
	Identifier string                      `json:"identifier"`
	Options    definition.ExecutionOptions `json:"options"`
	Sdk        definition.SDKTool          `json:"sdkTool"`
}

type ContentHandlerApiRequest struct {
	Identifier     string                    `json:"identifier"`
	ContentRequest definition.ContentRequest `json:"contentRequest"`
	Sdk            definition.SDKTool        `json:"sdkTool"`
}

type PostContentHandlerApiRequest struct {
	Identifier     string                    `json:"identifier"`
	ContentRequest definition.ContentRequest `json:"contentRequest"`
	AIResponse     definition.AIResponse     `json:"aiResponse"`
	Sdk            definition.SDKTool        `json:"sdkTool"`
}

type CommandExecutionApiResult struct {
	ExtensionApiBaseResult
	Result definition.CommandOutputResult `json:"data"`
}

type ContentHandlerApiResult struct {
	ExtensionApiBaseResult
	Result definition.ContentResponse `json:"data"`
}

type PostContentHandlerApiResult struct {
	ExtensionApiBaseResult
	Result definition.PostContentResponse `json:"data"`
}

type GetComboBoxItemsResponseApiResult struct {
	ExtensionApiBaseResult
	Result definition.GetComboBoxItemsResponse `json:"data"`
}

type LoadComboboxItemsApiRequest struct {
	Identifier              string                             `json:"identifier"`
	GetComboBoxItemsRequest definition.GetComboBoxItemsRequest `json:"getComboBoxItemsRequest"`
	Sdk                     definition.SDKTool                 `json:"sdkTool"`
}

type File struct {
	Name string `json:"name"`
	Path string `json:"path"`
}
