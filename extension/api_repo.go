package extension

import (
	"context"
	"cosy/definition"
	"cosy/experiment"
	"cosy/log"
	"cosy/sls"
	"cosy/websocket"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
)

const (
	// TimeoutForChatFilter 问答内容过滤超时，单位毫秒
	TimeoutForChatFilter = 5000

	// TimeoutForCompletionFilter 补全内容过滤超时，，单位毫秒
	TimeoutForCompletionFilter = 200
)

// ApiExecutor 基于node的扩展执行器
var ApiExecutor *ExtensionApiExecutor

// 缓存sdk与wsClient的映射关系
var _ideWsClients sync.Map

type CommandProviderWrapper struct {
	Identifier  string `json:"identifier"`  //唯一标识符
	Name        string `json:"name"`        //指令名称
	DisplayName string `json:"displayName"` //展示名称
	Description string `json:"description"` //描述信息
}

// ContentHandlerWrapper 同RequestPreHandler
type ContentHandlerWrapper struct {
	Identifier string `json:"identifier"`
	Name       string `json:"name"`
	BizType    string `json:"bizType"`  // "completion", "chat";
	ExecType   string `json:"execType"` // filter/block/no_op
	Stage      string `json:"stage"`    //pre,post
}

type ContextProviderWrapper struct {
	Identifier    string `json:"identifier"` //唯一标识符
	Name          string `json:"name"`
	DisplayName   string `json:"displayName"` //展示名称
	ComponentType string `json:"componentType"`
	// 来源类型(system:系统来源， UserDefinedType:用户来源)
	SourceType     string `json:"sourceType"`
	RequiredPrompt string `json:"requiredPrompt"`
}

func (c *ContextProviderWrapper) loadComboBoxItems(ctx context.Context, extensionService *ExtensionService, request definition.GetComboBoxItemsRequest, ideSdk definition.SDKTool) (definition.GetComboBoxItemsResponse, error) {
	setSdkHash(ctx, &ideSdk)

	defer unsetSdkClient(&ideSdk)

	apiResult := GetComboBoxItemsResponseApiResult{}

	err := extensionService.CallWithTimeout(5000*time.Millisecond, "contextProvider/loadComboBoxItems",
		LoadComboboxItemsApiRequest{Identifier: c.Identifier, GetComboBoxItemsRequest: request, Sdk: ideSdk}, &apiResult)
	if err != nil {
		log.Errorf("Error execute load comboBoxItems: %s", err.Error())
		return definition.GetComboBoxItemsResponse{}, err
	}
	if !apiResult.IsSuccess {
		return definition.GetComboBoxItemsResponse{}, fmt.Errorf("loading comboBox items failure")
	}
	return apiResult.Result, nil
}

func (c *ContextProviderWrapper) invoke(ctx context.Context, extensionService *ExtensionService, request definition.GetContextRequest, ideSdk definition.SDKTool) (definition.GetContextResponse, error) {
	setSdkHash(ctx, &ideSdk)

	defer unsetSdkClient(&ideSdk)

	apiResult := GetContextResponseApiResult{}

	err := extensionService.CallWithTimeout(5000*time.Millisecond, "contextProvider/run",
		GetContextResponseApiRequest{Identifier: c.Identifier, ContextProviderRequest: request, Sdk: ideSdk}, &apiResult)
	if err != nil {
		log.Errorf("Error executing context provider: %s", err.Error())
		return definition.GetContextResponse{}, err
	}
	if !apiResult.IsSuccess {
		return definition.GetContextResponse{}, fmt.Errorf("context provider execute failure")
	}
	return apiResult.Result, nil
}

func (e *ExtensionApiExecutor) InvokeContextProvider(ctx context.Context, contextProviderIdentifier string, request definition.GetContextRequest, ideSdk definition.SDKTool) (definition.GetContextResponse, error) {
	setSdkHash(ctx, &ideSdk)

	defer unsetSdkClient(&ideSdk)

	contextProvider, ok := e.apiRepo.ContextProviders[contextProviderIdentifier]
	if !ok {
		return definition.GetContextResponse{}, errors.New("context provider not found")
	}
	comboBoxItemsResponse, err := contextProvider.invoke(ctx, e.ExtensionCoreService, request, ideSdk)
	if err != nil {
		log.Errorf("Error executing context provider: %s", err.Error())
		return definition.GetContextResponse{}, errors.New("error executing context provider")
	}
	return comboBoxItemsResponse, nil
}

func (e *ExtensionApiExecutor) LoadComboBoxItems(ctx context.Context, contextProviderIdentifier string, request definition.GetComboBoxItemsRequest, ideSdk definition.SDKTool) (definition.GetComboBoxItemsResponse, error) {
	setSdkHash(ctx, &ideSdk)

	defer unsetSdkClient(&ideSdk)

	contextProvider, ok := e.apiRepo.ContextProviders[contextProviderIdentifier]
	if !ok {
		return definition.GetComboBoxItemsResponse{}, errors.New("context provider not found")
	}
	comboBoxItemsResponse, err := contextProvider.loadComboBoxItems(ctx, e.ExtensionCoreService, request, ideSdk)
	if err != nil {
		log.Errorf("Error executing context provider: %s", err.Error())
		return definition.GetComboBoxItemsResponse{}, errors.New("error executing context provider")
	}
	return comboBoxItemsResponse, nil
}

func (c *CommandProviderWrapper) execute(extensionService *ExtensionService, executionOptions definition.ExecutionOptions, ideSdk definition.SDKTool) (definition.CommandOutputResult, error) {
	commandApiResult := CommandExecutionApiResult{}

	err := extensionService.CallWithTimeout(5000*time.Millisecond, "command/run",
		CommandExecutionApiRequest{Identifier: c.Identifier, Options: executionOptions, Sdk: ideSdk}, &commandApiResult)
	if err != nil {
		log.Errorf("Error executing command: %s", err.Error())
		return definition.CommandOutputResult{}, err
	}
	if !commandApiResult.IsSuccess {
		return definition.CommandOutputResult{}, fmt.Errorf("command api execution failure")
	}
	return commandApiResult.Result, nil
}

func (c *ContentHandlerWrapper) execute(extensionService *ExtensionService, requestId string, action string, payload definition.ContentPayload, sdkTool definition.SDKTool, callTimeout int64) (definition.ContentResponse, error) {
	contentHandlerApiResult := ContentHandlerApiResult{}

	err := extensionService.CallWithTimeout(time.Duration(callTimeout)*time.Millisecond, "context_filter/run",
		ContentHandlerApiRequest{
			Identifier: c.Identifier,
			ContentRequest: definition.ContentRequest{
				RequestId: requestId,
				Action:    action,
				Payload:   payload,
			},
			Sdk: sdkTool,
		}, &contentHandlerApiResult)
	if err != nil {
		log.Errorf("Error executing context filter: %s", err.Error())
		if errors.Is(err, ErrConnectionTimeout) {
			// 上报超时埋点
			statisticsData := make(map[string]string)
			statisticsData["action"] = action
			go sls.Report(definition.CallLingmaExtensionTimeout, requestId, statisticsData)
		}
		return definition.ContentResponse{}, err
	}
	if !contentHandlerApiResult.IsSuccess {
		return definition.ContentResponse{}, fmt.Errorf("command api execution failure")
	}
	return contentHandlerApiResult.Result, nil
}

// postStageExecute 执行请求后置过滤
func (c *ContentHandlerWrapper) postStageExecute(extensionService *ExtensionService, requestId string, action string, payload definition.ContentPayload, aiResponse definition.AIResponse, sdkTool definition.SDKTool, callTimeout int64) (definition.PostContentResponse, error) {
	contentHandlerApiResult := PostContentHandlerApiResult{}

	err := extensionService.CallWithTimeout(time.Duration(callTimeout)*time.Millisecond, "post_context_filter/run",
		PostContentHandlerApiRequest{
			Identifier: c.Identifier,
			ContentRequest: definition.ContentRequest{
				RequestId: requestId,
				Action:    action,
				Payload:   payload,
			},
			AIResponse: aiResponse,
			Sdk:        sdkTool,
		}, &contentHandlerApiResult)
	if err != nil {
		log.Errorf("Error executing context filter: %s", err.Error())
		if errors.Is(err, ErrConnectionTimeout) {
			// 上报超时埋点
			statisticsData := make(map[string]string)
			statisticsData["action"] = action
			go sls.Report(definition.CallLingmaExtensionTimeout, requestId, statisticsData)
		}
		return definition.PostContentResponse{}, err
	}
	if !contentHandlerApiResult.IsSuccess {
		return definition.PostContentResponse{}, fmt.Errorf("postStageFilter api execution failure")
	}
	return contentHandlerApiResult.Result, nil
}

type ExtensionApiRepo struct {
	//key：command identifier
	CommandProviders map[string]CommandProviderWrapper
	//key：过滤场景
	ContentHandlers map[string][]ContentHandlerWrapper
	// key：context Provider identifier
	ContextProviders map[string]ContextProviderWrapper
	//key:后置过滤场景
	PostContentHandlers map[string][]ContentHandlerWrapper
}

type ExtensionApiExecutor struct {
	apiRepo              *ExtensionApiRepo
	ExtensionCoreService *ExtensionService
	ConfigDefinition     *ExtensionConfigDefinition
}

func GetRegisteredContextProvider(identifier string, includeSystem bool) (ContextProviderWrapper, error) {
	if includeSystem {
		if provider, ok := SystemContextProviderMap[identifier]; ok {
			return ContextProviderWrapper{
				Name:           provider.Name,
				Identifier:     provider.Identifier,
				DisplayName:    provider.DisplayName,
				ComponentType:  provider.ComponentType,
				RequiredPrompt: provider.RequiredPrompt,
			}, nil
		}
	}

	if ApiExecutor == nil || ApiExecutor.apiRepo == nil || ApiExecutor.apiRepo.ContextProviders == nil {
		return ContextProviderWrapper{}, errors.New("extension api repo is nil")
	}
	if provider, ok := ApiExecutor.apiRepo.ContextProviders[identifier]; ok {
		return ContextProviderWrapper{
			Name:           provider.Name,
			Identifier:     provider.Identifier,
			DisplayName:    provider.DisplayName,
			ComponentType:  provider.ComponentType,
			RequiredPrompt: provider.RequiredPrompt,
		}, nil
	}
	return ContextProviderWrapper{}, errors.New("context provider not found")
}

func NewExtensionApiExecutor(service *ExtensionService) *ExtensionApiExecutor {
	executor := &ExtensionApiExecutor{
		apiRepo:              &ExtensionApiRepo{},
		ExtensionCoreService: service,
	}
	return executor
}

func (e *ExtensionApiExecutor) RefreshApiRegistration(config ExtensionConfig) error {
	if e.ExtensionCoreService == nil || !e.ExtensionCoreService.IsReady() {
		log.Warnf("ExtensionApiExecutor is not ready yet")
		return errors.New("ExtensionApiExecutor is not ready yet")
	}
	apiResult := ExtensionConfigRefreshApiResult{}
	err := e.ExtensionCoreService.CallWithTimeout(10000*time.Millisecond, "extension/refresh",
		ExtensionConfigRefreshApiRequest{
			Config: config,
		}, &apiResult)
	if err != nil {
		log.Errorf("Error executing extension refresh: %s", err.Error())
		return err
	}
	if !apiResult.IsSuccess {
		log.Warnf("Command api refresh failure. errorCode: %s, errorMessage: %s", apiResult.ErrorCode, apiResult.ErrorMessage)
		return errors.New("command api refresh failure")
	}
	log.Infof("Command api refresh success. data: %+v", apiResult.Data)

	e.refreshExtensionConfig(apiResult.Data)

	return nil
}

func (e *ExtensionApiExecutor) InvokeCommand(ctx context.Context, cmdIdentifier string, inputs definition.ExecutionOptions, ideSdk definition.SDKTool) (string, error) {
	setSdkHash(ctx, &ideSdk)

	defer unsetSdkClient(&ideSdk)

	commandProvider, ok := e.apiRepo.CommandProviders[cmdIdentifier]
	if !ok {
		return "", errors.New("command provider not found")
	}
	commandResult, err := commandProvider.execute(e.ExtensionCoreService, inputs, ideSdk)
	if err != nil {
		log.Errorf("Error executing command: %s", err.Error())
		return "", errors.New("error executing command")
	}
	return commandResult.Prompt, nil
}

func (e *ExtensionApiExecutor) CheckPostContentHandlerExists(filterBizType string) bool {
	if e.apiRepo == nil || e.apiRepo.PostContentHandlers == nil {
		return false
	}
	_, ok := e.apiRepo.PostContentHandlers[filterBizType]
	return ok
}

func (e *ExtensionApiExecutor) InvokePostContentHandler(filterBizType string, requestId string, action string, payload definition.ContentPayload, aiResponse definition.AIResponse, sdkTool definition.SDKTool) (*definition.PostContentResponse, error) {
	if filterBizType != BizTypeCompletion && filterBizType != BizTypeChatAsk {
		log.Errorf("Invalid filterBizType: %s", filterBizType)
		return nil, errors.New("invalid post filter biz type")
	}
	contentHandlers, _ := e.apiRepo.PostContentHandlers[filterBizType]
	if len(contentHandlers) == 0 {
		if log.IsDebugEnabled() {
			log.Debugf("post content filter not found for %s", filterBizType)
		}
		return nil, nil
	}
	startFilterTime := time.Now()

	callTimeoutInMilliseconds := int64(0)
	if filterBizType == BizTypeChatAsk {
		callTimeoutInMilliseconds = TimeoutForChatFilter
	} else if filterBizType == BizTypeCompletion {
		callTimeoutInMilliseconds = TimeoutForCompletionFilter
	}

	var mergedFilterResult *definition.PostContentResponse
	for _, contentHandler := range contentHandlers {
		filterResult, err := contentHandler.postStageExecute(e.ExtensionCoreService, requestId, action, payload, aiResponse, sdkTool, callTimeoutInMilliseconds)
		if err != nil {
			log.Errorf("Error executing post stage context filter: %s", err.Error())
			continue
		}
		if filterResult.HandlePolicy == definition.HandlePolicyBlock {
			//阻断类型
			return &filterResult, nil
		} else if filterResult.HandlePolicy == definition.HandlePolicyNoOps {
			//无操作类型，do nothing
			if mergedFilterResult == nil {
				mergedFilterResult = &filterResult
			} else {
				mergedFilterResult.HitRules = append(mergedFilterResult.HitRules, filterResult.HitRules...)
			}
		} else if filterResult.HandlePolicy == definition.HandlePolicyFilter {
			if mergedFilterResult == nil {
				mergedFilterResult = &filterResult
			} else {
				//合并filter result
				copyPostFilterProcessedResult(&filterResult.ProcessedResult, &mergedFilterResult.ProcessedResult)
			}
		}
	}
	endFilterTime := time.Now()
	if log.IsDebugEnabled() {
		log.Debugf("finish post content filter for %s, time consumed(Milliseconds): %d", filterBizType, (endFilterTime.Sub(startFilterTime)).Milliseconds())
	}
	return mergedFilterResult, nil
}

func (e *ExtensionApiExecutor) CheckContentHandlerExists(filterBizType string) bool {
	if e.apiRepo == nil || e.apiRepo.ContentHandlers == nil {
		return false
	}
	_, ok := e.apiRepo.ContentHandlers[filterBizType]
	return ok
}

// InvokeContentHandler 执行内容过滤场景
func (e *ExtensionApiExecutor) InvokeContentHandler(filterBizType string, requestId string, action string, payload definition.ContentPayload, sdkTool definition.SDKTool) (*definition.ContentResponse, error) {
	if filterBizType != BizTypeCompletion && filterBizType != BizTypeChatAsk {
		log.Errorf("Invalid filterBizType: %s", filterBizType)
		return nil, errors.New("invalid filter biz type")
	}
	//检测全局内容过滤开关
	if filterBizType == BizTypeChatAsk && experiment.ConfigService.GetBoolConfigValue(definition.ExperimentKeyDisableChatFilter, experiment.ConfigScopeClient, false) {
		log.Debugf("skip chat content filter, disabled")
		return nil, nil
	}
	if filterBizType == BizTypeCompletion && experiment.ConfigService.GetBoolConfigValue(definition.ExperimentKeyDisableCompletionFilter, experiment.ConfigScopeClient, false) {
		log.Debugf("skip completion content filter, disabled")
		return nil, nil
	}

	contentHandlers, _ := e.apiRepo.ContentHandlers[filterBizType]
	if len(contentHandlers) == 0 {
		if log.IsDebugEnabled() {
			log.Debugf("content filter not found for %s", filterBizType)
		}
		return nil, nil
	}
	startFilterTime := time.Now()

	callTimeoutInMilliseconds := int64(0)
	if filterBizType == BizTypeChatAsk {
		callTimeoutInMilliseconds = TimeoutForChatFilter
	} else if filterBizType == BizTypeCompletion {
		callTimeoutInMilliseconds = TimeoutForCompletionFilter
	}

	var mergedFilterResult *definition.ContentResponse

	for _, contentHandler := range contentHandlers {
		filterResult, err := contentHandler.execute(e.ExtensionCoreService, requestId, action, payload, sdkTool, callTimeoutInMilliseconds)
		if err != nil {
			log.Errorf("Error executing context filter: %s", err.Error())
			continue
		}
		if filterResult.HandlePolicy == definition.HandlePolicyBlock {
			//阻断类型
			return &filterResult, nil
		} else if filterResult.HandlePolicy == definition.HandlePolicyNoOps {
			//无操作类型，do nothing
		} else if filterResult.HandlePolicy == definition.HandlePolicyFilter {
			if mergedFilterResult == nil {
				mergedFilterResult = &filterResult
			} else {
				//合并filter result
				copyPayload(&filterResult.Payload, &mergedFilterResult.Payload)
			}
		}
	}
	endFilterTime := time.Now()
	if log.IsDebugEnabled() {
		log.Debugf("finish content filter for %s, time consumed(Milliseconds): %d", filterBizType, (endFilterTime.Sub(startFilterTime)).Milliseconds())
	}
	return mergedFilterResult, nil
}

func (e *ExtensionApiExecutor) refreshExtensionConfig(configDefinition ExtensionConfigDefinition) {
	e.ConfigDefinition = &configDefinition

	commands := configDefinition.Commands
	e.apiRepo.CommandProviders = map[string]CommandProviderWrapper{}

	if len(commands) > 0 {
		for _, command := range commands {
			e.apiRepo.CommandProviders[command.Identifier] = CommandProviderWrapper{
				Identifier:  command.Identifier,
				Name:        command.Name,
				DisplayName: command.DisplayName,
			}
		}
	}

	contentHandlers := configDefinition.ContentHandlers
	e.apiRepo.ContentHandlers = map[string][]ContentHandlerWrapper{}
	if len(contentHandlers) > 0 {
		for _, contentHandler := range contentHandlers {
			bizType := contentHandler.BizType
			if bizType != definition.ContextFilterBizTypeChatAsk && bizType != definition.ContextFilterBizTypeCompletion {
				continue
			}
			if e.apiRepo.ContentHandlers[bizType] == nil {
				e.apiRepo.ContentHandlers[bizType] = make([]ContentHandlerWrapper, 0)
			}
			e.apiRepo.ContentHandlers[bizType] = append(e.apiRepo.ContentHandlers[bizType], ContentHandlerWrapper{
				Identifier: contentHandler.Identifier,
				Name:       contentHandler.Name,
				BizType:    contentHandler.BizType,
			})
		}
	}

	postContentHandlers := configDefinition.PostContentHandlers
	e.apiRepo.PostContentHandlers = map[string][]ContentHandlerWrapper{}
	if len(postContentHandlers) > 0 {
		for _, contentHandler := range postContentHandlers {
			bizType := contentHandler.BizType
			if bizType != definition.ContextFilterBizTypeChatAsk && bizType != definition.ContextFilterBizTypeCompletion {
				continue
			}
			if e.apiRepo.PostContentHandlers[bizType] == nil {
				e.apiRepo.PostContentHandlers[bizType] = make([]ContentHandlerWrapper, 0)
			}
			e.apiRepo.PostContentHandlers[bizType] = append(e.apiRepo.PostContentHandlers[bizType], ContentHandlerWrapper{
				Identifier: contentHandler.Identifier,
				Name:       contentHandler.Name,
				BizType:    contentHandler.BizType,
			})
		}
	}

	contextProviders := configDefinition.ContextProviders
	e.apiRepo.ContextProviders = map[string]ContextProviderWrapper{}
	if len(contextProviders) > 0 {
		for _, contextProvider := range contextProviders {
			e.apiRepo.ContextProviders[contextProvider.Identifier] = ContextProviderWrapper{
				Identifier:    contextProvider.Identifier,
				Name:          contextProvider.Name,
				DisplayName:   contextProvider.DisplayName,
				ComponentType: contextProvider.ComponentType,
				SourceType:    UserDefinedType,
			}
		}
	}

}

func GetExtensionApiConfig() (definition.ExtensionApiConfig, error) {
	var nodeExtensionConfig *ExtensionConfigDefinition

	if ApiExecutor == nil {
		log.Warn("ExtensionApi executor not inited.")
	} else if ApiExecutor.ConfigDefinition == nil {
		log.Warn("ExtensionApi definition not inited.")
	} else {
		nodeExtensionConfig = ApiExecutor.ConfigDefinition
	}
	return BuildExtensionApiConfig(nodeExtensionConfig, SystemContextProviders), nil
}

// ClearExtensionApiConfig 清理ExtensionApiConfig
func ClearExtensionApiConfig() {
	emptyExtensionConfig := definition.ExtensionApiConfig{}

	websocket.SendBroadcastWithTimeout(context.Background(), "extension/register", emptyExtensionConfig, nil)
}

func setSdkHash(ctx context.Context, sdkTool *definition.SDKTool) {
	var sdkHash string
	client, ok := ctx.Value(websocket.ClientCtxKey).(*websocket.Client)
	if ok {
		sdkHash = uuid.NewString()
		_ideWsClients.Store(sdkHash, client)
	}
	sdkTool.Hash = sdkHash
}

func unsetSdkClient(sdkTool *definition.SDKTool) {
	if sdkTool.Hash != "" {
		_ideWsClients.Delete(sdkTool.Hash)
	}
}
