package extension

import (
	"cosy/log"
	"errors"
)

type Command struct {
	BasicComponent

	// 系统提示词
	SystemPrompt string `json:"systemPrompt"`

	// prompt
	Prompt string `json:"prompt"`

	// 描述
	Description string `json:"description"`

	// 指令展示名称
	DisplayName string `json:"displayName"`

	// 是否包含历史会话
	IncludeHistory bool `json:"includeHistory"`

	// 依赖的上下文 deprecated
	RequiredContextItems []ContextItem `json:"requiredContextItems"`

	// 依赖的上下文Provider
	RequiredContextProviders []ContextProvider `json:"requiredContextProviders"`
}

// 比对两次的Command列表，返回新增、修改、删除的列表
func CompareSlashCommandsDiff(newSlashCommands, originSlashCommands []Command) (added, modified, deleted []Command) {
	newSlashCommandMap := make(map[string]Command)
	for _, item := range newSlashCommands {
		newSlashCommandMap[item.Name] = item
	}

	originSlashCommandMap := make(map[string]Command)
	for _, item := range originSlashCommands {
		originSlashCommandMap[item.Name] = item
	}

	// 检查是否有新增与修改的元素
	for name, item := range newSlashCommandMap {
		if _, exists := originSlashCommandMap[name]; !exists {
			added = append(added, item)
		} else if originSlashCommandMap[name].Version != item.Version {
			modified = append(modified, item)
		} else if originSlashCommandMap[name].Version == item.Version && originSlashCommandMap[name].Identifier == item.Identifier && originSlashCommandMap[name].State != item.State {
			// state状态变更也需要去更新组件类型
			modified = append(modified, item)
		}
	}

	// 检查是否有删除的元素
	for name, _ := range originSlashCommandMap {
		if _, exists := newSlashCommandMap[name]; !exists {
			deleted = append(deleted, originSlashCommandMap[name])
		}
	}

	return
}

// 获取需要脚本的组件列表
func GetNeedScriptSlashCommands(slashCommands []Command) (needDownloadSlashCommands []Command) {
	for _, slashCommand := range slashCommands {
		if slashCommand.ComponentType == ScriptType && slashCommand.State == EnableState {
			needDownloadSlashCommands = append(needDownloadSlashCommands, slashCommand)
		}
	}
	return
}

func MergeSlashCommands(serverCommands, localCommands []Command) (merged []Command) {
	serverSlashCommandMap := make(map[string]Command)
	for _, item := range serverCommands {
		serverSlashCommandMap[item.Name] = item
	}

	merged = append(merged, serverCommands...)
	for _, item := range localCommands {
		if _, exists := serverSlashCommandMap[item.Name]; !exists {
			merged = append(merged, item)
		} else {
			log.Warnf("local contextFilterScript%v has conflict with sever config", item.Name)
		}
	}
	return
}

// 校验slashCommand信息是否合法
func checkSlashCommand(slashCommand *Command) error {
	if slashCommand.Name == "" {
		return errors.New("command name is blank")
	}
	if slashCommand.DisplayName == "" {
		return errors.New("command displayName is blank")
	}
	if slashCommand.ComponentType != ScriptType && slashCommand.ComponentType != PromptType {
		return errors.New("componentType is invalid")
	}
	if slashCommand.ComponentType == PromptType && slashCommand.SystemPrompt == "" {
		return errors.New("systemPrompt config is invalid")
	}
	return nil
}
