package extension

import (
	"cosy/log"
	"errors"
)

const (
	GeneralType  = "general"
	ComboBoxType = "comboBox"
)

const (
	SystemType      = "system"
	UserDefinedType = "userDefined"
)

type ContextProvider struct {
	BasicComponent

	// 组件展示名称
	DisplayName string `json:"displayName"`

	// 组件类型
	ComponentType string `json:"componentType"`

	// 来源类型(system:系统来源， UserDefinedType:用户来源)
	SourceType string `json:"sourceType"`

	// 可选，部分自定义上下文需要的任务prompt
	RequiredPrompt string `json:"requiredPrompt"`

	// 自定义指令组合场景使用(是否必须)
	Required bool `json:"required"`
}

func CompareContextProviderDiff(newContextProviders, originContextProviders []ContextProvider) (added, modified, deleted []ContextProvider) {
	newContextProviderMap := make(map[string]ContextProvider)
	for _, item := range newContextProviders {
		newContextProviderMap[item.Name] = item
	}

	originContextProviderMap := make(map[string]ContextProvider)
	for _, item := range originContextProviders {
		originContextProviderMap[item.Name] = item
	}

	// 检查是否有新增与修改的元素
	for name, item := range newContextProviderMap {
		if _, exists := originContextProviderMap[name]; !exists {
			added = append(added, item)
		} else if originContextProviderMap[name].Version != item.Version {
			modified = append(modified, item)
		} else if originContextProviderMap[name].Version == item.Version && originContextProviderMap[name].Identifier == item.Identifier && originContextProviderMap[name].State != item.State {
			// state状态变更也需要去更新组件类型
			modified = append(modified, item)
		}
	}

	// 检查是否有删除的元素
	for name, _ := range originContextProviderMap {
		if _, exists := newContextProviderMap[name]; !exists {
			deleted = append(deleted, originContextProviderMap[name])
		}
	}
	return
}

func MergeContextProviders(serverContextProviders, localContextProviders []ContextProvider) (merged []ContextProvider) {
	serverContextProviderMap := make(map[string]ContextProvider)
	for _, item := range serverContextProviders {
		serverContextProviderMap[item.Name] = item
	}

	for _, item := range localContextProviders {
		if _, exists := serverContextProviderMap[item.Name]; !exists {
			serverContextProviderMap[item.Name] = item
		} else {
			log.Warnf("local contextProvider%v has conflict with sever config", item.Name)
		}
	}

	// 系统上下文优先级高于用户自定义上下文，出现冲突时使用系统上下文
	for _, item := range SystemContextProviders {
		if _, exists := serverContextProviderMap[item.Name]; exists {
			serverContextProviderMap[item.Name] = item
			log.Warnf("system contextProvider%v has conflict with sever config", item.Name)
		}
	}

	for _, item := range serverContextProviderMap {
		merged = append(merged, item)
	}
	return
}

func checkContextProvider(contextProvider *ContextProvider) error {
	if contextProvider.Name == "" {
		return errors.New("contextProvider name is blank")
	}
	if contextProvider.DisplayName == "" {
		return errors.New("contextProvider display name is blank")
	}
	if contextProvider.ComponentType != GeneralType && contextProvider.ComponentType != ComboBoxType {
		return errors.New("contextProvider ComponentType is illegal:" + contextProvider.ComponentType)
	}
	return nil
}
