package mcpconfig

import (
	"context"
	"cosy/log"
	"encoding/json"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/stretchr/testify/assert"
)

func Test_Start_Mcp_Sever_Call_Tool(t *testing.T) {
	severConfigStr := "{\n  \"mcpServers\": {\n    \"everything\": {\n      \"command\": \"npx\",\n      \"args\": [\n        \"-y\",\n        \"@modelcontextprotocol/server-everything\"\n      ]\n    }\n  }\n}"
	var config MCPSeversConfig
	err := json.Unmarshal([]byte(severConfigStr), &config)
	if err != nil {
		t.Error(err)
	}

	updateGlobalMCPSeversConfig(&config)
	for _, serverConfig := range GlobalMCPSeversConfig.MCPServers {
		mcpHost := MCPHost{
			SeverConfig: serverConfig,

			HealthCheckQuitChan: make(chan int),
		}
		err := mcpHost.startMcpSever()
		if err != nil {
			t.Error(err)
		}

		err = mcpHost.Client.Ping(context.Background())
		if err != nil {
			t.Error(err)
		}

		MCPHostMap[serverConfig.Name] = &mcpHost

		tools, err := mcpHost.listTools()
		if err != nil {
			t.Error(err)
		}
		GlobalMCPToolsMap[serverConfig.Name] = tools

		request := mcp.CallToolRequest{}
		request.Params.Name = "echo"
		request.Params.Arguments = map[string]interface{}{
			"message": "test123",
		}
		callToolResult, err := CallTool(request, "everything", 0)
		if err != nil {
			t.Error(err)
		}
		assert.Equal(t, "Echo: test123", callToolResult.Content[0].(mcp.TextContent).Text)
	}
	for _, mcpHost := range MCPHostMap {
		mcpHost.close()
	}
}

// 需要自己启动sse mcpSever，不然run不过
func Test_Start_Sse_Mcp_Sever_Call_Tool(t *testing.T) {
	severConfigStr := "{\n  \"mcpServers\": {\n    \"weather-remote\": {\n      \"url\": \"http://localhost:8080/sse\"\n    }\n  }\n}\n"
	var config MCPSeversConfig
	err := json.Unmarshal([]byte(severConfigStr), &config)
	if err != nil {
		t.Error(err)
	}

	updateGlobalMCPSeversConfig(&config)
	for _, serverConfig := range GlobalMCPSeversConfig.MCPServers {
		mcpHost := MCPHost{
			SeverConfig: serverConfig,

			HealthCheckQuitChan: make(chan int),
		}
		err := mcpHost.startMcpSever()
		if err != nil {
			t.Error(err)
		}
		err = mcpHost.Client.Ping(context.Background())
		if err != nil {
			t.Error(err)
		}
		MCPHostMap[serverConfig.Name] = &mcpHost

		tools, err := mcpHost.listTools()
		if err != nil {
			t.Error(err)
		}
		GlobalMCPToolsMap[serverConfig.Name] = tools

		request := mcp.CallToolRequest{}
		request.Params.Name = "make_authenticated_request"
		request.Params.Arguments = map[string]interface{}{
			"message": "test123",
		}
		callToolResult, err := CallTool(request, "weather-remote", 0)
		if err != nil {
			log.Errorf("error:%v", err)
			t.Error(err)
		}
		assert.True(t, strings.Contains(callToolResult.Content[0].(mcp.TextContent).Text, "Args"))
	}

	for _, mcpHost := range MCPHostMap {
		mcpHost.close()
	}
}

func TestLoadUserEnvPath(t *testing.T) {
	// Save original SHELL environment variable
	originalShell := os.Getenv("SHELL")
	defer os.Setenv("SHELL", originalShell)

	tests := []struct {
		name          string
		shell         string
		expectedError bool
		expectedPath  string
	}{
		{
			name:          "Success with zsh",
			shell:         "/bin/zsh",
			expectedError: false,
		},
		{
			name:          "Success with bash",
			shell:         "/bin/bash",
			expectedError: false,
		},
		{
			name:          "Error with invalid shell",
			shell:         "/invalid/shell",
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set the SHELL environment variable for the test
			os.Setenv("SHELL", tt.shell)

			path, err := LoadUserEnvPath()

			if tt.expectedError {
				if err == nil {
					t.Errorf("Expected error but got nil")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if path == "" {
					t.Errorf("Expected non-empty PATH but got empty string")
				}
			}
		})
	}
}

func TestHelperProcess(t *testing.T) {
	if os.Getenv("GO_WANT_HELPER_PROCESS") != "1" {
		return
	}
	// Simulate shell output
	os.Stdout.WriteString("/usr/local/bin:/usr/bin:/bin")
	os.Exit(0)
}

func TestLoadUserEnvPathLocal(t *testing.T) {
	path1, err := LoadUserEnvPath()
	if err != nil {
		t.Error(err)
	}
	path2, err := LoadUserEnvPath()
	if err != nil {
		t.Error(err)
	}
	path3, err := LoadUserEnvPath()
	if err != nil {
		t.Error(err)
	}
	assert.Equal(t, path1, path2)
	assert.Equal(t, path1, path3)
}

func TestModelScopeSSE(t *testing.T) {
	sseClient, err := client.NewSSEMCPClient("https://mcp-7432f689-d345-42b6.api-inference.modelscope.net/sse")
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer sseClient.Close()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Start the client
	if err := sseClient.Start(ctx); err != nil {
		t.Fatalf("Failed to start client: %v", err)
	}

	// Initialize
	initRequest := mcp.InitializeRequest{}
	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	initRequest.Params.ClientInfo = mcp.Implementation{
		Name:    "test-client",
		Version: "1.0.0",
	}

	result, err := sseClient.Initialize(ctx, initRequest)
	if err != nil {
		t.Fatalf("Failed to initialize: %v", err)
	}
	if result.ServerInfo.Name != "mcp-fetch" {
		t.Errorf(
			"Expected server name 'mcp-fetch', got '%s'",
			result.ServerInfo.Name,
		)
	}

	// Test Ping
	if err := sseClient.Ping(ctx); err != nil {
		t.Errorf("Ping failed: %v", err)
	}

	// Test ListTools
	toolsRequest := mcp.ListToolsRequest{}
	toolListResult, err := sseClient.ListTools(ctx, toolsRequest)
	if err != nil {
		t.Errorf("ListTools failed: %v", err)
	}
	if toolListResult == nil || len((*toolListResult).Tools) == 0 {
		t.Errorf("Expected one tool")
	}
}

func TestValidateMCPCommandSecurityAtStartup(t *testing.T) {
	tests := []struct {
		name        string
		command     string
		args        []string
		expectError bool
		description string
	}{
		{
			name:        "blocked rm command",
			command:     "rm",
			args:        []string{"file.txt"},
			expectError: true,
			description: "Should block rm command",
		},
		{
			name:        "blocked del command",
			command:     "del",
			args:        []string{"file.txt"},
			expectError: true,
			description: "Should block del command",
		},
		{
			name:        "blocked format command",
			command:     "format",
			args:        []string{"C:"},
			expectError: true,
			description: "Should block format command",
		},
		{
			name:        "blocked shutdown command",
			command:     "shutdown",
			args:        []string{"-h", "now"},
			expectError: true,
			description: "Should block shutdown command",
		},
		{
			name:        "blocked halt command",
			command:     "halt",
			args:        []string{},
			expectError: true,
			description: "Should block halt command",
		},
		{
			name:        "blocked reboot command",
			command:     "reboot",
			args:        []string{},
			expectError: true,
			description: "Should block reboot command",
		},
		{
			name:        "blocked rm with path",
			command:     "/bin/rm",
			args:        []string{"file.txt"},
			expectError: true,
			description: "Should block rm command with path",
		},
		{
			name:        "blocked shutdown with path",
			command:     "/sbin/shutdown",
			args:        []string{"-h", "now"},
			expectError: true,
			description: "Should block shutdown command with path",
		},
		{
			name:        "allowed curl command",
			command:     "curl",
			args:        []string{"-o", "file.txt", "https://example.com"},
			expectError: false,
			description: "Should allow curl command",
		},
		{
			name:        "allowed wget command",
			command:     "wget",
			args:        []string{"https://example.com"},
			expectError: false,
			description: "Should allow wget command",
		},
		{
			name:        "allowed npm command",
			command:     "npm",
			args:        []string{"install", "package"},
			expectError: false,
			description: "Should allow npm command",
		},
		{
			name:        "allowed python command",
			command:     "python",
			args:        []string{"-c", "print('hello')"},
			expectError: false,
			description: "Should allow python command without pipes",
		},
		{
			name:        "blocked pipe symbol |",
			command:     "cat",
			args:        []string{"file.txt", "|", "grep", "pattern"},
			expectError: true,
			description: "Should block pipe symbol in args",
		},
		{
			name:        "blocked pipe symbol &&",
			command:     "bash",
			args:        []string{"-c", "echo hello && echo world"},
			expectError: true,
			description: "Should block && symbol in args",
		},
		{
			name:        "blocked pipe symbol ||",
			command:     "bash",
			args:        []string{"-c", "echo hello || echo world"},
			expectError: true,
			description: "Should block || symbol in args",
		},
		{
			name:        "blocked semicolon",
			command:     "python",
			args:        []string{"-c", "print('hello'); print('world')"},
			expectError: true,
			description: "Should block semicolon in args",
		},
		{
			name:        "original malicious example",
			command:     "bash",
			args:        []string{"-c", "echo 'malicious payload 666' > /Users/<USER>/Develop/qoder/malicious.txt && exec npx -y @modelcontextprotocol/server-everything"},
			expectError: true,
			description: "Should block original malicious example due to && symbol",
		},
		{
			name:        "safe bash command",
			command:     "bash",
			args:        []string{"-c", "echo 'hello world' > output.txt"},
			expectError: false,
			description: "Should allow safe bash command without pipes",
		},
		{
			name:        "safe npx command",
			command:     "npx",
			args:        []string{"-y", "@modelcontextprotocol/server-filesystem"},
			expectError: false,
			description: "Should allow safe npx command",
		},
		{
			name:        "command with malicious keywords (should be allowed)",
			command:     "echo",
			args:        []string{"malicious payload hack exploit"},
			expectError: false,
			description: "Should allow commands with malicious keywords since keyword detection is disabled",
		},
		{
			name:        "empty command",
			command:     "",
			args:        []string{"arg1", "arg2"},
			expectError: false,
			description: "Should allow empty command",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateMCPCommandSecurityAtStartup(tt.command, tt.args)

			if tt.expectError {
				assert.Error(t, err, "Expected error for %s: %s", tt.name, tt.description)
				t.Logf("Correctly blocked: %s - Error: %s", tt.name, err.Error())
			} else {
				assert.NoError(t, err, "Unexpected error for %s: %s", tt.name, tt.description)
				t.Logf("Correctly allowed: %s", tt.name)
			}
		})
	}
}

func TestGetBlockedCommands(t *testing.T) {
	expectedCommands := []string{"rm", "del", "format", "shutdown", "halt", "reboot"}
	actualCommands := getBlockedCommands()

	assert.Equal(t, expectedCommands, actualCommands, "getBlockedCommands should return expected command list")
	assert.Len(t, actualCommands, 6, "Should have exactly 6 blocked commands")

	// Test that all expected commands are present
	for _, expected := range expectedCommands {
		assert.Contains(t, actualCommands, expected, "Blocked commands should contain %s", expected)
	}
}
