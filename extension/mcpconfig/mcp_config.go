package mcpconfig

import (
	cosyDefinition "cosy/definition"
	"cosy/experiment"
	"fmt"
	"regexp"
	"strings"
	"sync"
	"time"

	"cosy/util"
	"cosy/util/collection"

	"github.com/google/uuid"
	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
)

// mcpSever状态
const (
	Disconnected = "disconnected"

	Connecting = "connecting"

	Connected = "connected"

	ErrorStatus = "error"
)

const (
	SourceUser = "user" // 用户添加
)

const (
	MarketTypeModelScope = "modelscope" // 魔搭
)

var (
	SSEIgnoredHeaders = []string{
		"Accept",
		"Cache-Control",
		"Connection",
	}
	StreamableHTTPIgnoredHeaders = []string{
		"Content-Type",
		"Accept",
	}
)

const maxDescLength = 200
const defaultMCPSecurityCheck = true

var mcpServeNameCheck = regexp.MustCompile(`^[a-zA-Z0-9_-]{1,16}$`)

type MCPHost struct {
	// McpSever配置
	SeverConfig MCPSeverConfig

	// McpClient
	Client *client.Client

	// 健康检查扫描器
	HealthTicker *time.Ticker

	HealthCheckQuitChan chan int

	HealthCheckIsStop bool

	// 重连定时器
	ReconnectTicker *time.Ticker

	// 重连退出通道
	ReconnectQuitChan chan int

	// 重连是否停止标志
	ReconnectIsStop bool

	// 重连开始时间
	ReconnectStartTime time.Time

	// 重连协程是否运行
	ReconnectGoroutineRunning bool

	// 操作锁，防止健康检查和重连冲突
	operationMutex sync.Mutex

	// 与McpSever的连接状态
	Status string

	// 连接异常的errorMsg，Status为ErrorStatus时有值
	ErrorMsg string
}

type MCPSeverConfig struct {
	// severConfig唯一标识，非空
	Identifier string `json:"identifier"`
	// mcpSever名称
	Name string `json:"-"`
	// map指令配置
	Command string `json:"command"`
	// 命令行参数(stdio场景非空)
	Args []string `json:"args"`
	// 环境参数配置，可空
	Env map[string]string `json:"env,omitempty"`
	// mcp sever地址(sse场景非空)
	Url string `json:"url"`
	// headers
	Headers map[string]string `json:"headers,omitempty"`

	// 是否禁用(false:启用 true:禁用)
	Disabled bool `json:"disabled"`
	// 白名单
	AutoApprove []string `json:"autoApprove"`
	// 配置来源
	Source string `json:"source"`
	// 描述
	Description string `json:"description"`
	// version
	Version int64 `json:"version"`
	// create
	CreateAt int64 `json:"createAt"`

	// 来源 - MODELSCOPE
	From string `json:"from"`
	// 来源id - 魔搭serverId
	FromId string `json:"fromId"`
}

type MCPSeversConfig struct {
	MCPServers    map[string]MCPSeverConfig `json:"mcpServers"`
	UserConfigMD5 string                    `json:"userConfigMD5"` // 用户mcp config文件md5
}

var GlobalMCPSeversConfig = MCPSeversConfig{
	MCPServers:    make(map[string]MCPSeverConfig),
	UserConfigMD5: "",
}

var GlobalMCPSeverConfigIdMap = make(map[string]MCPSeverConfig)
var GlobalMCPToolsMap = make(map[string][]mcp.Tool)
var MCPHostMap = make(map[string]*MCPHost)
var GlobalUserConfigError bool = false
var GlobalUserConfigErrorMsg string = ""
var mcpHostMapModifiedLock sync.Mutex
var mcpToolsMapModifiedLock sync.Mutex

func updateGlobalMCPSeversConfig(mcpSeversConfig *MCPSeversConfig) {
	for key, sever := range mcpSeversConfig.MCPServers {
		sever.Name = key
		//err := checkMCPSeverConfig(&sever)
		//if err != nil {
		//	log.Errorf("mcp sever %s is illegal %v", key, err)
		//	continue
		//}
		if sever.Version == 0 {
			sever.Version = time.Now().UnixMilli()
		}
		if sever.Identifier == "" {
			// 补齐Identifier
			sever.Identifier = uuid.NewString()
		}
		GlobalMCPSeversConfig.MCPServers[key] = sever
		GlobalMCPSeverConfigIdMap[sever.Identifier] = sever
	}
	GlobalMCPSeversConfig.UserConfigMD5 = mcpSeversConfig.UserConfigMD5
	// 清理空key
	delete(GlobalMCPSeversConfig.MCPServers, "")
}

func getEnableMcpServerCount() int {
	total := 0
	for _, server := range GlobalMCPSeversConfig.MCPServers {
		if !server.Disabled {
			total++
		}
	}
	return total
}

// enableMCPSecurityCheck returns true if MCP security check is enabled
func enableMCPSecurityCheck() bool {
	defaultCheck := defaultMCPSecurityCheck
	key := cosyDefinition.ExperimentKeyMCPSecurityCheck
	enable := experiment.ConfigService.GetBoolConfigValue(key, experiment.ConfigScopeClient, defaultCheck)
	return enable
}

// getBlockedCommands returns the list of commands that are not allowed
func getBlockedCommands() []string {
	return []string{
		"rm",
		"del",
		"format",
		"shutdown",
		"halt",
		"reboot",
	}
}

// validateMCPCommandSecurityAtStartup validates MCP command security at startup
func validateMCPCommandSecurityAtStartup(command string, args []string) error {
	if !enableMCPSecurityCheck() {
		return nil
	}

	if command == "" {
		return nil // empty command needs no validation
	}

	// Extract base command name from path
	baseCommand := command
	if strings.Contains(command, "/") {
		parts := strings.Split(command, "/")
		baseCommand = parts[len(parts)-1]
	}

	// Check if command is in blocked list
	blockedCommands := getBlockedCommands()
	for _, blocked := range blockedCommands {
		if baseCommand == blocked {
			return fmt.Errorf("command '%s' is not allowed", blocked)
		}
	}

	// Check for pipe symbols in arguments
	pipeSymbols := []string{"|", "||", "&&", ";"}
	for _, arg := range args {
		for _, pipe := range pipeSymbols {
			if strings.Contains(arg, pipe) {
				return fmt.Errorf("pipe symbol '%s' is not allowed in arguments", pipe)
			}
		}
	}

	return nil
}

func checkMCPSeverConfig(mcpSeverConfig *MCPSeverConfig) error {
	// command与url不可都为空
	if mcpSeverConfig.Command == "" && mcpSeverConfig.Url == "" {
		return fmt.Errorf("command or url is required")
	}
	// name校验
	//if mcpSeverConfig.Name == "" || !mcpServeNameCheck.MatchString(mcpSeverConfig.Name) {
	//	return fmt.Errorf("name is required")
	//}
	if mcpSeverConfig.Name == "" {
		return fmt.Errorf("name is required")
	}
	// 描述长度校验
	if len([]rune(mcpSeverConfig.Description)) > maxDescLength {
		return fmt.Errorf("description is too long")
	}

	// url非空场景校验url合法性
	if mcpSeverConfig.Url != "" && !util.CheckUrlValidate(mcpSeverConfig.Url) {
		return fmt.Errorf("url is illegal")
	}

	return nil
}

type MCPOfficialConfig struct {
	MCPServers collection.LinkedHashMap[string, MCPOfficialConfigItem] `json:"mcpServers"`
}

type MCPOfficialConfigItem struct {
	// map指令配置
	Command string `json:"command,omitempty"`
	// 命令行参数(stdio场景非空)
	Args []string `json:"args,omitempty"`
	// 环境参数配置，可空
	Env map[string]string `json:"env,omitempty"`
	// mcp sever地址(sse场景非空)
	Url string `json:"url,omitempty"`
	// HTTP headers for the server
	Headers map[string]string `json:"headers,omitempty"`
}

type MergeUserConfigResult struct {
	Removed map[string]MCPSeverConfig
	Updated map[string]MCPSeverConfig
	Added   map[string]MCPSeverConfig
}

// ServerMCPServerItem 服务的返回的mcp服务信息
type ServerMCPServerItem struct {
	ID            string   `json:"id"`
	Publisher     string   `json:"publisher"`
	ChineseName   string   `json:"chineseName"`
	Description   string   `json:"description"`
	LogoUrl       string   `json:"logoUrl"`
	Author        string   `json:"author"`
	Owner         string   `json:"owner"`
	SourceUrl     string   `json:"sourceUrl"`
	ServerConfig  string   `json:"serverConfig"`
	IsHosted      bool     `json:"isHosted"`
	IsVerified    bool     `json:"isVerified"`
	VewCount      int64    `json:"viewCount"`
	Tags          []string `json:"tags"`
	Url           string   `json:"url"`
	Recommend     bool     `json:"recommend"`
	NameEn        string   `json:"nameEn"`
	DescriptionEn string   `json:"descriptionEn"`
}

// ServerMCPServerListRequest 服务端mcp列表查询参数
type ServerMCPServerListRequest struct {
	PageSize   int    `json:"pageSize"`
	PageNumber int    `json:"pageNumber"`
	Query      string `json:"query"`
	Category   string `json:"category"`
}

// ServerMCPServerListFilter 服务端mcp列表查询过滤参数
type ServerMCPServerListFilter struct {
	Category string `json:"category"`
}

// ServerMCPServerListResult 服务端返回的mcp服务列表
type ServerMCPServerListResult struct {
	TotalCount    int                   `json:"totalCount"`
	MCPServerList []ServerMCPServerItem `json:"mcpserverList"`
}

func PutMcpHostMap(serverName string, mcpHost *MCPHost) {
	mcpHostMapModifiedLock.Lock()
	defer mcpHostMapModifiedLock.Unlock()

	MCPHostMap[serverName] = mcpHost
}

func DeleteMcpHostMap(serverName string) {
	mcpHostMapModifiedLock.Lock()
	defer mcpHostMapModifiedLock.Unlock()

	delete(MCPHostMap, serverName)
}

func PutMcpToolMap(serverName string, tools []mcp.Tool) {
	mcpToolsMapModifiedLock.Lock()
	defer mcpToolsMapModifiedLock.Unlock()

	GlobalMCPToolsMap[serverName] = tools
}

func DeleteMCPToolMap(serverName string) {
	mcpToolsMapModifiedLock.Lock()
	defer mcpToolsMapModifiedLock.Unlock()

	delete(GlobalMCPToolsMap, serverName)
}
