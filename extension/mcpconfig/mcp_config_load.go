package mcpconfig

import (
	"path/filepath"
	"time"

	"cosy/global"
	"cosy/log"
	"cosy/sls"
	"cosy/util"
	"cosy/util/collection"

	"golang.org/x/exp/maps"
	"golang.org/x/exp/slices"

	"github.com/google/uuid"
)

// mcp配置文件位置，merge了用户数据
var localMCPConfigFilePath string

// 用户mcp文件位置
var UserMCPConfigFilePath string

// Initialize
// 初始化参数配置
func Initialize() {
	cosyHomePath := util.GetCosyHomePath()
	localMCPConfigFilePath = filepath.Join(cosyHomePath, "extension", "local", "mcp.json")
	useFileName := "lingma_mcp.json"
	if global.IsQoderProduct() {
		useFileName = "mcp.json"
	}
	UserMCPConfigFilePath = filepath.Join(cosyHomePath, useFileName)
}

const MAX_ENABLED_MCP_SEVER_COUNT = 10

const MAX_MCP_SEVER_CONFIG_COUNT = 100

const UserConfigRefreshDelay = 1 * time.Second
const UserConfigMergeDelay = 2 * time.Second

func StartMcpSevers(watchFunc func()) {
	log.Info("do start mcp severs")

	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Panic recovered in StartMcpSevers: %v", r)
		}
	}()

	// 读取mcpServer配置
	loadLocalMCPConfig()
	if watchFunc != nil {
		// 监听用户配置文件变化
		go watchFunc()
	}

	// 启动mcpServer
	enableSeverCount := 0
	for _, serverConfig := range GlobalMCPSeversConfig.MCPServers {
		if serverConfig.Disabled {
			log.Infof("MCP server disabled for server %s, not inited", serverConfig.Name)
			continue
		}
		enableSeverCount++
		if enableSeverCount > MAX_ENABLED_MCP_SEVER_COUNT {
			log.Infof("MCP sever more than %d not start remain sever", MAX_ENABLED_MCP_SEVER_COUNT)
			break
		}
		mcpHost := MCPHost{
			SeverConfig:         serverConfig,
			HealthCheckQuitChan: make(chan int, 1),
			ReconnectQuitChan:   make(chan int, 1),
		}
		PutMcpHostMap(serverConfig.Name, &mcpHost)

		err := mcpHost.startMcpSever()
		if err != nil {
			log.Errorf("start mcp sever err:%v", err)
			mcpHost.Status = ErrorStatus
			mcpHost.ErrorMsg = err.Error()
			continue
		}

		// 获取tools列表
		tools, err := mcpHost.listTools()
		if err != nil {
			log.Errorf("list tools err:%v", err)
			continue
		}
		PutMcpToolMap(serverConfig.Name, tools)
	}
}

// 从本地读取mcpConfig配置
func loadLocalMCPConfig() {
	fileExists := CheckMCPConfigFileExist(localMCPConfigFilePath)
	if !fileExists {
		// 初始化空配置文件
		log.Info("local mcpConfig file not exists then create empty local mcpConfig")
		writeMCPConfigToDisk(localMCPConfigFilePath, &GlobalMCPSeversConfig)
		return
	}
	// 从本地配置文件中读取mcpSever配置
	mcpSeversConfig := loadMCPConfigFromDisk(localMCPConfigFilePath)

	// 更新全局配置
	updateGlobalMCPSeversConfig(&mcpSeversConfig)

	// 加载userConfig
	userConfigExists := CheckMCPConfigFileExist(UserMCPConfigFilePath)
	if !userConfigExists {
		// 使用localConfig中user配置的mcp写入userConfig
		md5, err := initUserConfigByLocalConfig(&mcpSeversConfig)
		if err == nil {
			// 更新md5到localConfig中
			mcpSeversConfig.UserConfigMD5 = md5
			writeMCPConfigToDisk(localMCPConfigFilePath, &mcpSeversConfig)
			// 更新全局配置
			GlobalMCPSeversConfig.UserConfigMD5 = md5
		}
	} else {
		// 比较userConfig的md5，如果有更新需要更新localConfig
		userConfig, md5, err := LoadUserConfigFromDisk(UserMCPConfigFilePath)
		log.Debugf("userMCPConfig md5=%s, localMCPConfig md5=%s", md5, mcpSeversConfig.UserConfigMD5)
		if err == nil && md5 != mcpSeversConfig.UserConfigMD5 {
			log.Debugf("userMCPConfig md5 changed, update localConfig")
			MergeUserConfig(userConfig, md5)
		} else if err != nil {
			GlobalUserConfigError = true
			GlobalUserConfigErrorMsg = err.Error()
		}
	}
}

// initUserConfigByLocalConfig 使用extension/local/mcp.json来初始化lingma_mcp.json
func initUserConfigByLocalConfig(mcpSeversConfig *MCPSeversConfig) (string, error) {
	configItems := *collection.NewLinkedHashMap[string, MCPOfficialConfigItem]()
	for serverName, mcpSeverConfig := range mcpSeversConfig.MCPServers {
		if !isConfigByUser(mcpSeverConfig.Source) {
			continue
		}
		configItem := MCPOfficialConfigItem{
			Url:     mcpSeverConfig.Url,
			Command: mcpSeverConfig.Command,
			Args:    mcpSeverConfig.Args,
			Env:     mcpSeverConfig.Env,
			Headers: mcpSeverConfig.Headers,
		}
		configItems.Put(serverName, configItem)
	}
	config := MCPOfficialConfig{
		MCPServers: configItems,
	}
	return WriteUserMCPConfigToDisk(UserMCPConfigFilePath, &config)
}

// isConfigByUser 判断是否用户配置的mcp
func isConfigByUser(source string) bool {
	if source == "" || source == SourceUser {
		return true
	}
	return false
}

// hasConfigChanged 判断配置是否有变更
func hasConfigChanged(userConfigItem *MCPOfficialConfigItem, localConfigItem *MCPSeverConfig) bool {
	// 比较URL
	if userConfigItem.Url != localConfigItem.Url {
		return true
	}

	// 比较Command
	if userConfigItem.Command != localConfigItem.Command {
		return true
	}

	// 比较Args
	if !slices.Equal(userConfigItem.Args, localConfigItem.Args) {
		return true
	}

	// 比较Env
	if !maps.Equal(userConfigItem.Env, localConfigItem.Env) {
		return true
	}

	// 比较Headers
	if !maps.Equal(userConfigItem.Headers, localConfigItem.Headers) {
		return true
	}

	return false
}

// addToUserConfig 添加配置到用户配置中
func addToUserConfig(serverName string, mcpSeverConfig *MCPSeverConfig) (string, error) {
	config, _, err := LoadUserConfigFromDisk(UserMCPConfigFilePath)
	if err != nil {
		log.Errorf("update userConfig error, reason=LoadUserConfigFromDisk error:%v", err)
		return "", err
	}
	configItem := MCPOfficialConfigItem{
		Url:     mcpSeverConfig.Url,
		Command: mcpSeverConfig.Command,
		Args:    mcpSeverConfig.Args,
		Env:     mcpSeverConfig.Env,
		Headers: mcpSeverConfig.Headers,
	}
	config.MCPServers.Put(serverName, configItem)
	return WriteUserMCPConfigToDisk(UserMCPConfigFilePath, config)
}

// updateToUserConfig 更新用户配置
func updateToUserConfig(serverName string, mcpSeverConfig *MCPSeverConfig, changeSeverName bool, originalSeverName string) (string, error) {
	config, _, err := LoadUserConfigFromDisk(UserMCPConfigFilePath)
	if err != nil {
		log.Errorf("update userConfig error, reason=LoadUserConfigFromDisk error:%v", err)
		return "", err
	}
	if changeSeverName {
		config.MCPServers.Remove(originalSeverName)
		configItem := MCPOfficialConfigItem{
			Url:     mcpSeverConfig.Url,
			Command: mcpSeverConfig.Command,
			Args:    mcpSeverConfig.Args,
			Env:     mcpSeverConfig.Env,
			Headers: mcpSeverConfig.Headers,
		}
		config.MCPServers.Put(serverName, configItem)
	} else {
		configItem, ok := config.MCPServers.Get(serverName)
		if ok {
			configItem.Url = mcpSeverConfig.Url
			configItem.Command = mcpSeverConfig.Command
			configItem.Args = mcpSeverConfig.Args
			configItem.Env = mcpSeverConfig.Env
			configItem.Headers = mcpSeverConfig.Headers
			config.MCPServers.Put(serverName, configItem)
		}
	}
	return WriteUserMCPConfigToDisk(UserMCPConfigFilePath, config)
}

// removeFromUserConfig 从用户配置中移除
func removeFromUserConfig(serverName string, mcpSeverConfig *MCPSeverConfig) (string, error) {
	config, _, err := LoadUserConfigFromDisk(UserMCPConfigFilePath)
	if err != nil {
		log.Errorf("remove userConfig error, reason=LoadUserConfigFromDisk error:%v", err)
		return "", err
	}
	config.MCPServers.Remove(serverName)
	return WriteUserMCPConfigToDisk(UserMCPConfigFilePath, config)
}

// slsAddChannel 埋点添加mcp渠道
func slsAddChannel(channel string, serverName string, serverId string) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Error occurred in slsMCP: %v", r)
		}
	}()
	if channel == "" {
		return
	}
	requestId := uuid.NewString()
	eventData := map[string]string{
		"request_id":  requestId,
		"channel":     channel,
		"server_name": serverName,
		"server_id":   serverId,
	}
	go sls.Report(sls.EventTypeAgentMCPServerAdd, requestId, eventData)
}

// slsStartError 埋点mcp启动失败的埋点
func slsStartError(mcpHost *MCPHost) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Error occurred in slsStartError: %v", r)
		}
	}()
	requestId := uuid.NewString()
	eventData := map[string]string{
		"request_id":      requestId,
		"server_name":     mcpHost.SeverConfig.Name,
		"server_id":       mcpHost.SeverConfig.FromId,
		"start_error_msg": mcpHost.ErrorMsg,
	}
	go sls.Report(sls.EventTypeAgentMCPServerStartError, requestId, eventData)
}
