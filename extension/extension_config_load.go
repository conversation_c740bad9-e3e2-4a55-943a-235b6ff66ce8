package extension

import (
	"context"
	"cosy/client"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/remote"
	"cosy/user"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"path/filepath"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
)

var nodeVm *NodeVm
var localConfigFilePath string
var serverConfigFilePath string
var localScriptsPath string
var serverScriptsPath string
var defaultConfig = ExtensionConfig{}
var refreshExtensionConfigLock sync.Mutex

// Initialize
// 初始化参数配置
func Initialize() {
	cosyHomePath := util.GetCosyHomePath()
	localConfigFilePath = filepath.Join(cosyHomePath, "extension", "local", "config.json")
	serverConfigFilePath = filepath.Join(cosyHomePath, "extension", "server", "config.json")
	localScriptsPath = filepath.Join(cosyHomePath, "extension", "local", "script")
	serverScriptsPath = filepath.Join(cosyHomePath, "extension", "server", "script")
}

// StartLoadLocalConfig 开启加载本地配置，启动时在对应路径创建本地config.json文件，并开启针对文件变更的实时监听
func StartLoadLocalConfig() {
	// 判断本地是否存在local的config.json文件，不存在则手工创建
	if !checkConfigFileExist(localConfigFilePath) {
		writeConfigToDisk(localConfigFilePath, &defaultConfig)
	}
	if log.IsDebugEnabled() {
		log.Debugf("Load Local Extension Config File: %s", localConfigFilePath)
	}

	// 开启文件监听
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		log.Errorf("start init local config watcher error:%v", err)
		return
	}
	defer func(watcher *fsnotify.Watcher) {
		if r := recover(); r != nil {
			log.Errorf("Panic recovered in StartLoadLocalConfig: %v", r)
		}
		err := watcher.Close()
		if err != nil {
			log.Errorf("close watcher error:%v", err)
		}
	}(watcher)

	openWatcher := func() {
		if !checkConfigFileExist(localConfigFilePath) {
			writeConfigToDisk(localConfigFilePath, &defaultConfig)
		}
		err = watcher.Add(localConfigFilePath)
		if err != nil {
			log.Errorf("start init local config watcher error:%v", err)
		}
	}

	done := make(chan bool)
	go func() {
		for {
			select {
			case event, ok := <-watcher.Events:
				if !ok {
					return
				}
				if event.Op&fsnotify.Write == fsnotify.Write { //文件内容被修改
					log.Infof("local config.json has modiefied, do refresh")
					refreshConfigForLocalConfigChange()
				} else if event.Op&fsnotify.Create == fsnotify.Create { // 文件被创建
					log.Infof("local config.json has created, do refresh")
					refreshConfigForLocalConfigChange()
				} else if event.Op&fsnotify.Remove == fsnotify.Remove { // 文件被删除
					log.Infof("local config.json has remove, do remove")
					refreshConfigForLocalConfigChange()

					// 重新开启监听
					openWatcher()
				} else if event.Op&fsnotify.Rename == fsnotify.Rename { // 文件被rename
					log.Infof("config.json has renamed, do refresh")
					refreshConfigForLocalConfigChange()

					// 重新开启监听
					openWatcher()
				} else {
					log.Infof("config.json event:%v, do nothing", event)
				}
			case err, ok := <-watcher.Errors:
				if !ok {
					return
				}
				log.Errorf("watcher has error:%v", err)
			}
		}
	}()

	openWatcher()
	<-done
}

// StartLoadServerConfig 定时拉取服务端的config.json配置,启动时默认执行一次拉取，之后每间隔5分钟拉取一次
func StartLoadServerConfig() {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Panic recovered in StartLoadServerConfig: %v", r)
		}
	}()

	if log.IsDebugEnabled() {
		log.Debugf("Load Server Extension Config File: %s", serverConfigFilePath)
	}

	// 创建一个 ticker，设置间隔时间为5分钟
	ticker := time.NewTicker(time.Duration(global.ExtensionConfigPullInterval) * time.Minute)
	for _ = range ticker.C {
		log.Infof("do pull config.json from server on schedule with period:%v", global.ExtensionConfigPullInterval)
		executeLoadServerConfigIncrement()
	}
}

// 全量拉取服务端配置
func ExecuteLoadFullServerConfig() {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Panic recovered in ExecuteLoadFullServerConfig: %v", r)
		}
	}()

	if !enablePullServerConfig() {
		log.Infof("current not public enterprise user,not open pullServerExtension config, clear server config")
		parseServerConfigAndWriteToDisk(&defaultConfig)
		return
	}
	latestConfig := pullConfigFromServer()

	parseServerConfigAndWriteToDisk(&latestConfig)
}

func parseServerConfigAndWriteToDisk(latestConfig *ExtensionConfig) {
	if parseServerConfig(latestConfig) {
		// 将最新的服务端配置更新至本地文件系统
		writeConfigToDisk(serverConfigFilePath, latestConfig)
	}
}

// 增量执行服务端配置拉取
func executeLoadServerConfigIncrement() {
	// 加锁
	refreshExtensionConfigLock.Lock()
	defer refreshExtensionConfigLock.Unlock()

	if !enablePullServerConfig() {
		log.Infof("current not public enterprise user,not open pullServerExtension config")
		parseServerConfigAndWriteToDisk(&defaultConfig)
		return
	}
	// 从服务端拉取最新的config.json配置
	latestConfig := pullConfigFromServer()

	// 读取本地已存在的服务端config.json配置
	localConfigFileExist := checkConfigFileExist(serverConfigFilePath)
	var refreshConfigSuccess bool
	if localConfigFileExist {
		// 若本地已存在服务端拉取的配置，则进行diff后执行增量更新
		currentConfig := loadConfigFromDisk(serverConfigFilePath)

		// 比较latestConfig与currentConfig，对比出差异的元素
		addedSlashCommands, modifiedSlashCommands, deletedSlashCommands := CompareSlashCommandsDiff(latestConfig.Commands, currentConfig.Commands)
		addedContextFilterRules, modifiedContextFilterRules, deletedContextFilterRules := CompareContextFilterRulesDiff(latestConfig.ContentHandlerRules, currentConfig.ContentHandlerRules)
		addedContextFilterScripts, modifiedContextFilterScripts, deletedContextFilterScripts := CompareContextFilterScriptDiff(latestConfig.ContentHandlerScripts, currentConfig.ContentHandlerScripts)
		addedContextProviders, modifiedContextProviders, deletedContextProviders := CompareContextProviderDiff(latestConfig.ContextProviders, currentConfig.ContextProviders)

		// 移除待删除的组件对应的脚本文件
		for _, deletedSlashCommand := range deletedSlashCommands {
			if deletedSlashCommand.ComponentType == ScriptType {
				// 删除脚本文件
				scriptFilePath := filepath.Join(serverScriptsPath, deletedSlashCommand.Identifier+".js")

				deleteScriptFile(scriptFilePath)
			}
		}
		for _, deletedContextFilterScript := range deletedContextFilterScripts {
			if deletedContextFilterScript.ComponentType == ScriptType {
				// 删除脚本文件
				scriptFilePath := filepath.Join(serverScriptsPath, deletedContextFilterScript.Identifier+".js")
				deleteScriptFile(scriptFilePath)
			}
		}
		for _, deletedContextProvider := range deletedContextProviders {
			if deletedContextProvider.ComponentType == ScriptType {
				// 删除脚本文件
				scriptFilePath := filepath.Join(serverScriptsPath, deletedContextProvider.Identifier+".js")
				deleteScriptFile(scriptFilePath)
			}
		}

		// 从待添加的组件里解析出需要下载脚本的组件执行下载
		needDownloadSlashCommands := make([]Command, 0)
		needDownloadSlashCommands = append(needDownloadSlashCommands, GetNeedScriptSlashCommands(addedSlashCommands)...)
		needDownloadSlashCommands = append(needDownloadSlashCommands, GetNeedScriptSlashCommands(modifiedSlashCommands)...)

		needDownloadContextFilterScript := make([]ContentHandlerScript, 0)
		needDownloadContextFilterScript = append(needDownloadContextFilterScript, GetEnabledContextFilterScripts(addedContextFilterScripts)...)
		needDownloadContextFilterScript = append(needDownloadContextFilterScript, GetEnabledContextFilterScripts(modifiedContextFilterScripts)...)

		// 调用下载脚本方法完成脚本下载
		DownloadCommandScript(needDownloadSlashCommands)
		DownloadFilterScript(needDownloadContextFilterScript)

		// 判断是否需要更新脚本
		needReloadSlashCommands := len(addedSlashCommands) > 0 || len(modifiedSlashCommands) > 0 || len(deletedSlashCommands) > 0
		needReloadContextFilterRules := len(addedContextFilterRules) > 0 || len(modifiedContextFilterRules) > 0 || len(deletedContextFilterRules) > 0
		needReloadContextFilterScripts := len(addedContextFilterScripts) > 0 || len(modifiedContextFilterScripts) > 0 || len(deletedContextFilterScripts) > 0
		needReloadContextProviders := len(addedContextProviders) > 0 || len(modifiedContextProviders) > 0 || len(deletedContextProviders) > 0

		if needReloadSlashCommands || needReloadContextFilterRules || needReloadContextFilterScripts || needReloadContextProviders {
			localConfig := executeLoadLocalConfig()
			mergeConfig := mergeServerAndLocalConfig(&latestConfig, &localConfig)
			refreshConfigSuccess = notifyNodeVmToRefresh(&mergeConfig)
		}
	} else {
		// 补偿逻辑，每次增量拉取时若发现本地无配置，则执行全量初始化
		refreshConfigSuccess = parseServerConfig(&latestConfig)
	}

	// 将最新的服务端配置更新至本地文件系统
	if refreshConfigSuccess {
		writeConfigToDisk(serverConfigFilePath, &latestConfig)
	}
}

func DownloadNodeVm() bool {
	if !enableDownloadNodeVm() {
		return false
	}
	if nodeVm == nil {
		nodeVm = NewNodeVm(util.GetPlatform(), global.CosyVersion)
	}
	return nodeVm.Download()
}

// 解析服务端配置
func parseServerConfig(latestConfig *ExtensionConfig) bool {
	// 从待添加组件中解析出需要有自定义脚本的组件
	needLoadScriptSlashCommands := GetNeedScriptSlashCommands(latestConfig.Commands)
	//  执行脚本下载
	if len(needLoadScriptSlashCommands) > 0 {
		DownloadCommandScript(needLoadScriptSlashCommands)
	}
	if len(latestConfig.ContentHandlerScripts) > 0 {
		DownloadFilterScript(latestConfig.ContentHandlerScripts)
	}

	// 合并本地配置与服务端配置
	localConfig := executeLoadLocalConfig()
	mergeConfig := mergeServerAndLocalConfig(latestConfig, &localConfig)

	// 通知node vm执行refresh
	return notifyNodeVmToRefresh(&mergeConfig)
}

// executeLoadLocalConfig 执行本地config.json配置加载
func executeLoadLocalConfig() ExtensionConfig {
	if !checkConfigFileExist(localConfigFilePath) {
		return defaultConfig
	}
	currentConfig := loadConfigFromDisk(localConfigFilePath)

	// 从待添加组件中解析出需要有自定义脚本的组件
	realNeedLoadSlashCommands := make([]Command, 0)
	// 遍历检查脚本文件是否已存在(若不存在则说明组件非法，不去load组件)
	for _, command := range currentConfig.Commands {
		if command.ComponentType == ScriptType {
			// 检查脚本文件是否存在
			scriptFilePath := filepath.Join(localScriptsPath, command.Identifier+".js")
			if !checkConfigFileExist(scriptFilePath) {
				log.Errorf("script file not exist, command:%v", command)
				continue
			}
			command.ScriptPath = scriptFilePath
		}
		if err := checkSlashCommand(&command); err != nil {
			log.Errorf("checkCommand %v, error:%v", command, err)
			continue
		}
		realNeedLoadSlashCommands = append(realNeedLoadSlashCommands, command)
	}

	realNeedLoadContextFilterScripts := make([]ContentHandlerScript, 0)
	for _, contextFilterScript := range currentConfig.ContentHandlerScripts {
		// 检查脚本文件是否存在
		scriptFilePath := filepath.Join(localScriptsPath, contextFilterScript.Identifier+".js")
		if !checkConfigFileExist(scriptFilePath) {
			log.Errorf("script file not exist, contextFilterScript:%v", contextFilterScript)
			continue
		}
		if err := checkContextFilterScript(&contextFilterScript); err != nil {
			log.Errorf("checkContextFilterScript %v, error:%v", contextFilterScript, err)
			continue
		}
		contextFilterScript.ScriptPath = scriptFilePath
		realNeedLoadContextFilterScripts = append(realNeedLoadContextFilterScripts, contextFilterScript)
	}

	realNeedLoadContextFilterRules := make([]ContentHandlerRule, 0)
	for _, contextFilterRule := range currentConfig.ContentHandlerRules {
		if err := checkContextFilterRule(&contextFilterRule); err != nil {
			log.Errorf("checkContextFilterRule %v, error:%v", contextFilterRule, err)
			continue
		}
		realNeedLoadContextFilterRules = append(realNeedLoadContextFilterRules, contextFilterRule)
	}

	realNeedLoadContextProviders := make([]ContextProvider, 0)
	for _, contextProvider := range currentConfig.ContextProviders {
		if err := checkContextProvider(&contextProvider); err != nil {
			log.Errorf("checkContextProvider %v, error:%v", contextProvider, err)
			continue
		}
		realNeedLoadContextProviders = append(realNeedLoadContextProviders, contextProvider)
	}
	return ExtensionConfig{Commands: realNeedLoadSlashCommands, ContentHandlerScripts: realNeedLoadContextFilterScripts, ContentHandlerRules: realNeedLoadContextFilterRules, ContextProviders: realNeedLoadContextProviders}
}

// pullConfigFromServer 从服务端拉取配置信息
func pullConfigFromServer() (serverExtensionConfig ExtensionConfig) {
	log.Infof("start pullConfigFromServer")
	userInfo := user.GetCachedUserInfo()
	userId := userInfo.YxUid
	orgId := userInfo.OrgId

	pullConfigUrl := fmt.Sprintf("%s?user_id=%s&org_id=%s", definition.UrlPathPullServerConfigUrl, userId, orgId)
	pullConfigRequest, err := remote.BuildBigModelAuthRequest(http.MethodGet, pullConfigUrl, nil)
	if err != nil {
		log.Error("Failed to build pullConfig request:%w", err)
		return defaultConfig
	}
	response, err := client.GetExtensionDownloadClient().Do(pullConfigRequest)
	if err != nil {
		log.Error("Failed to pullServerConfig: ", err)
		return defaultConfig
	}
	defer response.Body.Close()
	if response.StatusCode != http.StatusOK {
		log.Errorf("Failed to pullServerConfig, statusCode=%d", response.StatusCode)
		return defaultConfig
	}
	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		log.Errorf("Failed to read response:%v", err)
		return defaultConfig
	}
	if string(responseBody) == "" {
		log.Warnf("Failed to pullServerConfig, response is empty.")
		return defaultConfig
	}
	if err := json.Unmarshal(responseBody, &serverExtensionConfig); err != nil {
		log.Errorf("Failed to unmarshal response: %s, error: %v", string(responseBody), err)
		return defaultConfig
	}
	// fetch requiredContextProviders
	serverExtensionConfig.fetchRequiredContextProviders()
	return
}

func notifyNodeVmToRefresh(config *ExtensionConfig) bool {
	log.Debugf("start notify node vm to refresh config")
	if log.IsDebugEnabled() {
		log.Debugf("notify node vm config detail:%+v", config)
	}
	// 只有已开启状态的组件才会被加载
	enableContextFilterScripts := make([]ContentHandlerScript, 0)
	enablePostContextFilterScripts := make([]ContentHandlerScript, 0)
	for _, item := range config.ContentHandlerScripts {
		if item.State == EnableState {
			if item.Stage == PostStage {
				enablePostContextFilterScripts = append(enablePostContextFilterScripts, item)
			} else {
				enableContextFilterScripts = append(enableContextFilterScripts, item)
			}
		}
	}

	enableContextFilterRules := make([]ContentHandlerRule, 0)
	enablePostContextFilterRules := make([]ContentHandlerRule, 0)
	for _, item := range config.ContentHandlerRules {
		if item.State == EnableState {
			if item.Stage == PostStage {
				enablePostContextFilterRules = append(enablePostContextFilterRules, item)
			} else {
				enableContextFilterRules = append(enableContextFilterRules, item)
			}
		}
	}

	enableCommands := make([]Command, 0)
	for _, item := range config.Commands {
		if item.State == EnableState {
			enableCommands = append(enableCommands, item)
		}
	}

	enableContextProviders := make([]ContextProvider, 0)
	for _, item := range config.ContextProviders {
		if item.State == EnableState {
			enableContextProviders = append(enableContextProviders, item)
		}
	}

	if ApiExecutor == nil {
		if log.IsDebugEnabled() {
			log.Debugf("ApiExecutor is nil,not inited")
		}
		extensionApiConfig, _ := GetExtensionApiConfig()
		websocket.SendBroadcastWithTimeout(context.Background(), "extension/register", extensionApiConfig, nil)

		return false
	}

	err := ApiExecutor.RefreshApiRegistration(ExtensionConfig{
		Commands:                  enableCommands,
		ContentHandlerRules:       enableContextFilterRules,
		ContentHandlerScripts:     enableContextFilterScripts,
		ContextProviders:          enableContextProviders,
		PostContentHandlerRules:   enablePostContextFilterRules,
		PostContentHandlerScripts: enablePostContextFilterScripts,
	})
	if err != nil {
		log.Errorf("Failed to notify node vm to refresh: %v", err)
		return false
	} else {
		extensionApiConfig, _ := GetExtensionApiConfig()
		websocket.SendBroadcastWithTimeout(context.Background(), "extension/register", extensionApiConfig, nil)
	}

	// 数据格式转换
	webViewCommandJsObjects := make([]definition.WebViewCommandJsObject, 0)
	for _, item := range enableCommands {
		webViewCommandJsObjects = append(webViewCommandJsObjects, definition.WebViewCommandJsObject{
			Identifier: item.Identifier,
			NameLeft:   item.Name,
			NameRight:  item.DisplayName,
		})
	}
	commandOrder := definition.WebViewCommandOrder{
		Commands:           webViewCommandJsObjects,
		OfficialCommandEnd: GlobalProfileData.CommandOrder.OfficialCommandEnd,
	}

	// 更新全局指令顺序
	err = UpdateProfileData(definition.WebViewShowData{
		CommandOrder: commandOrder,
	})
	if err != nil {
		log.Errorf("config.json updated, but updating global profile data fail")
	} else {
		log.Infof("config.json updated, and updating global profile data succeed")
	}
	return true
}

// refreshConfigForLocalConfigChange 本地配置变更执行配置更新
func refreshConfigForLocalConfigChange() {
	refreshExtensionConfigLock.Lock()
	defer refreshExtensionConfigLock.Unlock()

	localConfig := executeLoadLocalConfig()
	serverConfig := loadConfigFromDisk(serverConfigFilePath)
	mergedConfig := mergeServerAndLocalConfig(&serverConfig, &localConfig)
	notifyNodeVmToRefresh(&mergedConfig)
}

// 合并本地与与server的配置
func mergeServerAndLocalConfig(serverConfig, localConfig *ExtensionConfig) ExtensionConfig {
	realNeedMergeContextFilterScripts := make([]ContentHandlerScript, 0)
	for _, contextFilterScript := range serverConfig.ContentHandlerScripts {
		// 检查脚本文件是否存在
		scriptFilePath := filepath.Join(serverScriptsPath, contextFilterScript.Identifier+".js")
		if !checkConfigFileExist(scriptFilePath) {
			log.Errorf("server script file not exist, contextFilterScript:%v", contextFilterScript)
			continue
		}
		contextFilterScript.ScriptPath = scriptFilePath
		realNeedMergeContextFilterScripts = append(realNeedMergeContextFilterScripts, contextFilterScript)
	}
	mergeContextFilterScripts := MergeContextFilterScripts(realNeedMergeContextFilterScripts, localConfig.ContentHandlerScripts)

	realNeedMergeCommands := make([]Command, 0)
	for _, command := range serverConfig.Commands {
		if command.ComponentType == ScriptType {
			// 检查脚本文件是否存在
			scriptFilePath := filepath.Join(serverScriptsPath, command.Identifier+".js")
			if !checkConfigFileExist(scriptFilePath) {
				log.Errorf("server script file not exist, command:%v", command)
				continue
			}
			command.ScriptPath = scriptFilePath
			realNeedMergeCommands = append(realNeedMergeCommands, command)
		} else {
			realNeedMergeCommands = append(realNeedMergeCommands, command)
		}
	}
	mergeSlashCommands := MergeSlashCommands(serverConfig.Commands, localConfig.Commands)

	mergeContextRules := MergeContextRules(serverConfig.ContentHandlerRules, localConfig.ContentHandlerRules)

	mergeContextProviders := MergeContextProviders(serverConfig.ContextProviders, localConfig.ContextProviders)
	extensionConfig := ExtensionConfig{
		Commands:              mergeSlashCommands,
		ContentHandlerRules:   mergeContextRules,
		ContentHandlerScripts: mergeContextFilterScripts,
		ContextProviders:      mergeContextProviders,
	}
	SetGlobalExtensionConfig(&extensionConfig)
	return extensionConfig
}

// 判断是否需要从服务端拉取配置(当前仅有公有云企业用户需要开启次功能)
func enablePullServerConfig() bool {
	if isEnterPriseUser() {
		return true
	}
	log.Debugf("current login user not open pullServerConfig")
	return false
}

// 判断是否启用下载nodeVm()
func enableDownloadNodeVm() bool {
	if isEnterPriseUser() {
		return true
	}
	log.Debugf("current login user not open downloadNodeVm")
	return false
}

// 判断是否是企业客户
func isEnterPriseUser() bool {
	cachedUserInfo := user.GetCachedUserInfo()
	isEnterpriseUser := cachedUserInfo != nil && (cachedUserInfo.UserType == definition.UserTypeEnterpriseDedicated || cachedUserInfo.UserType == definition.UserTypeEnterpriseStandard)
	return isEnterpriseUser
}
