package extension

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_CompareSlashCommandsDiff(t *testing.T) {
	wantAddCommandName := "MytestCommand5"
	wantModifiedCommandName := "MytestCommand1"
	wantDeletedCommandName := "MytestCommand3"
	wantModifiedCommandStateChange := "MytestCommand6"

	newSlashCommands := make([]Command, 0)
	newSlashCommands = append(newSlashCommands, Command{Description: "test123", Prompt: "ddddd",
		BasicComponent: BasicComponent{Name: wantModifiedCommandName, ComponentType: "prompt", Version: "222"}})
	newSlashCommands = append(newSlashCommands, Command{Description: "test123",
		BasicComponent: BasicComponent{Name: "MytestCommand2", ComponentType: "script", Version: "111"}})
	newSlashCommands = append(newSlashCommands, Command{Description: "test123", Prompt: "kkkkk",
		BasicComponent: BasicComponent{Name: wantAddCommandName, ComponentType: "prompt", Version: "111"}})
	newSlashCommands = append(newSlashCommands, Command{Description: "test123", Prompt: "kkkkk",
		BasicComponent: BasicComponent{Name: wantModifiedCommandStateChange, ComponentType: "prompt", Version: "111", State: EnableState, Identifier: wantModifiedCommandStateChange}})

	originSlashCommands := make([]Command, 0)
	originSlashCommands = append(originSlashCommands, Command{Description: "test123", Prompt: "ccccc",
		BasicComponent: BasicComponent{Name: "MytestCommand1", ComponentType: "prompt", Version: "111"}})
	originSlashCommands = append(originSlashCommands, Command{Description: "test123",
		BasicComponent: BasicComponent{Name: "MytestCommand2", ComponentType: "script", Version: "111"}})
	originSlashCommands = append(originSlashCommands, Command{Description: "test123", Prompt: "33333",
		BasicComponent: BasicComponent{Name: wantDeletedCommandName, ComponentType: "prompt", Version: "111"}})
	originSlashCommands = append(originSlashCommands, Command{Description: "test123", Prompt: "kkkkk",
		BasicComponent: BasicComponent{Name: wantModifiedCommandStateChange, ComponentType: "prompt", Version: "111", State: DisableState, Identifier: wantModifiedCommandStateChange}})

	added, modified, deleted := CompareSlashCommandsDiff(newSlashCommands, originSlashCommands)
	assert.Equal(t, 1, len(added), "one need added")
	assert.Equal(t, 2, len(modified), "one need modified")
	assert.Equal(t, 1, len(deleted), "one need deleted")
	assert.Equal(t, wantAddCommandName, added[0].Name, wantAddCommandName+" should be added")
	assert.Equal(t, wantModifiedCommandName, modified[0].Name, wantModifiedCommandName+" should be modified")
	assert.Equal(t, wantModifiedCommandStateChange, modified[1].Name, wantModifiedCommandStateChange+" should be modified")
	assert.Equal(t, wantDeletedCommandName, deleted[0].Name, wantDeletedCommandName+" should be deleted")
}

func Test_checkSlashCommand(t *testing.T) {
	command := Command{Description: "test123", Prompt: "ccccc",
		BasicComponent: BasicComponent{ComponentType: "prompt", Version: "111"}}
	err := checkSlashCommand(&command)
	assert.Equal(t, "command name is blank", err.Error(), "should check command name")

	command = Command{Description: "test123", Prompt: "ccccc",
		BasicComponent: BasicComponent{Name: "test123", ComponentType: "prompt", Version: "111"}}
	err = checkSlashCommand(&command)
	assert.Equal(t, "command displayName is blank", err.Error(), "should check command displayName")

	command = Command{DisplayName: "测试", Description: "test123", Prompt: "ccccc",
		BasicComponent: BasicComponent{Name: "testName", ComponentType: "abc", Version: "111"}}
	err = checkSlashCommand(&command)
	assert.Equal(t, "componentType is invalid", err.Error(), "should check componentType")

	command = Command{DisplayName: "测试", Description: "test123",
		BasicComponent: BasicComponent{Name: "testName", ComponentType: "prompt", Version: "111"}}
	err = checkSlashCommand(&command)
	assert.Equal(t, "systemPrompt config is invalid", err.Error(), "should check prompt")
}
