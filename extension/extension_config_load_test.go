package extension

import (
	"cosy/extension/mcpconfig"
	"cosy/util"
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"testing"
)

func Test_ParseServerConfig(t *testing.T) {
	var config ExtensionConfig
	data := []byte("{\n  \"contextFilterScripts\":[\n    {\n      \"identifier\":\"10d1f5cc28204a95b4dc2bb3\",\n      \"name\":\"MyPoliticsWordFilter\",\n      \"strategy\":\"block\",\n      \"bizType\":\"chat_ask\",\n      \"version\":\"20240819155231\"\n    },\n    {\n      \"identifier\":\"f058f2f27f91498492e9164d\",\n      \"name\":\"MyPrivateCodeFilter\",\n      \"strategy\":\"block\",\n      \"bizType\":\"completion\",\n      \"version\":\"20240819165231\"\n    }\n  ],\n  \"contextFilterRules\":[\n    {\n      \"identifier\":\"e40a8c983800467e9294e8b5\",\n      \"name\":\"FilterRepoAccessKeyRule\",\n      \"strategy\":\"warning\",\n      \"regExp\":\".*\\\\baccessKey\\\\b.*\",\n      \"replaceWord\":\"test_access_key\",\n      \"webhookList\":[\n        \"https://codeup.aliyun.com\"\n      ],\n      \"bizType\":\"chat_ask\",\n      \"version\":\"20240818125231\"\n    },\n    {\n      \"identifier\":\"dce4df2991194a50a1e39ae2\",\n      \"name\":\"FilterRepoAccessSecretRule\",\n      \"regExp\":\".*\\\\baccessSecret\\\\b.*\",\n      \"strategy\":\"warning\",\n      \"bizType\":\"chat_ask\",\n      \"webhookList\":[\n        \"https://flow.aliyun.com\"\n      ],\n      \"version\":\"20240815125231\"\n    },\n    {\n      \"identifier\":\"524307f661ee4c19ae8fbb8a\",\n      \"name\":\"ReportFilterRule\",\n      \"regExp\":\"abc\",\n      \"strategy\":\"no_ops\",\n      \"bizType\":\"chat_ask\",\n      \"webhookList\":[\n        \"https://package.aliyun.com\"\n      ],\n      \"version\":\"20240813125231\"\n    },\n    {\n      \"identifier\":\"356fb90626d943ffa2aed30b\",\n      \"name\":\"FilterRepoAccessKeyRule\",\n      \"strategy\":\"warning\",\n      \"regExp\":\".*\\\\baccessKey\\\\b.*\",\n      \"replaceWord\":\"test_access_key\",\n      \"webhookList\":[\n        \"https://codeup.aliyun.com\"\n      ],\n      \"bizType\":\"completion\",\n      \"version\":\"20240818125231\"\n    },\n    {\n      \"identifier\":\"7bd83edd35294614b97106dc\",\n      \"name\":\"FilterRepoAccessSecretRule\",\n      \"regExp\":\".*\\\\baccessSecret\\\\b.*\",\n      \"strategy\":\"warning\",\n      \"bizType\":\"completion\",\n      \"webhookList\":[\n        \"https://flow.aliyun.com\"\n      ],\n      \"version\":\"20240815125231\"\n    },\n    {\n      \"identifier\":\"0ba9007a17e94037804a79ee\",\n      \"name\":\"ReportFilterRule\",\n      \"regExp\":\"abc\",\n      \"strategy\":\"no_ops\",\n      \"bizType\":\"completion\",\n      \"webhookList\":[\n        \"https://package.aliyun.com\"\n      ],\n      \"version\":\"20240813125231\"\n    }\n  ],\n  \"commands\":[\n    {\n      \"identifier\":\"cdff08f1b1064fdeaca74042\",\n      \"nameEn\":\"shellCmd\",\n      \"nameCn\":\"生成shell命令\",\n      \"description\":\"针对描述生成shell命令\",\n      \"systemPrompt\":\"请使用shell生成\",\n      \"prompt\":\"请为下面的描述生成shell命令\",\n      \"type\":\"prompt\",\n      \"version\":\"20240813125231\",\n      \"includeHistory\":false,\n      \"requiredContextItems\":[\n        {\n          \"contextKey\":\"userInput\",\n          \"required\":true\n        }\n      ]\n    },\n    {\n      \"identifier\":\"214d5962ccf8489cbe2e08d1\",\n      \"nameCn\":\"专用生成单测\",\n      \"nameEn\":\"MyUnitTestCmd\",\n      \"description\":\"公司专用的单测生产命令\",\n      \"systemPrompt\":\"请使用shell生成\",\n      \"prompt\":\"$selectCode 请使用junit5为上述代码生成单元测试\",\n      \"type\":\"prompt\",\n      \"version\":\"20240819155231\",\n      \"includeHistory\":true,\n      \"requiredContextItems\":[\n        {\n          \"contextKey\":\"userInput\",\n          \"required\":true\n        },\n        {\n          \"contextKey\":\"selectCode\",\n          \"required\":true\n        }\n      ]\n    }\n  ]\n}")
	err := json.Unmarshal(data, &config)
	if err != nil {
		panic(err)
	}
	config.fetchRequiredContextProviders()
	assert.Equal(t, 1, len(config.Commands[0].RequiredContextItems), "should Parse RequiredContextProviders")
	assert.Equal(t, "userInput", config.Commands[0].RequiredContextItems[0].ContextKey, "should Has ContextKey")
}

func Test_ParseMCPOfficialConfig(t *testing.T) {
	var mcpConfig mcpconfig.MCPOfficialConfig
	str := []byte("{\"mcpServers\":{\"elasticsearch-mcp-server\":{\"command\":\"npx\",\"args\":[\"-y\",\"@elastic/mcp-server-elasticsearch\"],\"env\":{\"ES_API_KEY\":\"your-api-key\",\"ES_URL\":\"your-elasticsearch-url\"}},\"fetch\":{\"command\":\"uvx\",\"args\":[\"mcp-server-fetch\"]},\"tavily-mcp\":{\"command\":\"npx\",\"args\":[\"-y\",\"tavily-mcp@0.1.4\"],\"env\":{\"TAVILY_API_KEY\":\"your-api-key-here\"}},\"LeetCode\":{\"command\":\"npx\",\"args\":[\"-y\",\"@jinzcdev/leetcode-mcp-server\",\"--site\",\"cn\"]}}}")
	err := json.Unmarshal(str, &mcpConfig)
	if err != nil {
		panic(err)
	}
	jsonStr := util.ToJsonStr(mcpConfig)
	assert.Equal(t, string(str), jsonStr)
}
