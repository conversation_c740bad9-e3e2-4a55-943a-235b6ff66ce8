package extension

import (
	"cosy/definition"
)

// contextProvider内置执行接口
type IContextProvider interface {

	// 获取上下文内容
	GetContext(getContextRequest GetContextResponseApiRequest) (definition.GetContextResponse, error)

	// 加载下拉框列表
	LoadComboBoxItems(loadComboboxItemsRequest LoadComboboxItemsApiRequest) (definition.GetComboBoxItemsResponse, error)
}

func GetSystemContextProviderExecutor(identifier string) IContextProvider {
	if _, exists := SystemContextProviderMap[identifier]; !exists {
		return nil
	}
	return SystemContextProviderEngineMap[identifier]
}
