package extension

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func Test_CompareContextProviderDiff(t *testing.T) {
	wantAddedContextName := "contextProvider-added"
	wantModifiedContextName := "contextProvider-modified"
	wantDeletedContextName := "contextProvider-deleted"
	wantModifiedContextWithStateChange := "contextProvider-state-changed"

	newContextProviderScripts := make([]ContextProvider, 0)
	newContextProviderScripts = append(newContextProviderScripts, ContextProvider{
		BasicComponent: BasicComponent{
			Identifier:    wantAddedContextName + "chat_ask",
			ComponentType: "script",
			Name:          wantAddedContextName,
			Version:       "111",
		},
		DisplayName:   wantAddedContextName,
		ComponentType: ComboBoxType,
	})
	newContextProviderScripts = append(newContextProviderScripts, ContextProvider{
		BasicComponent: BasicComponent{
			Identifier:    wantModifiedContextName + "completion",
			ComponentType: "script",
			Name:          wantModifiedContextName,
			Version:       "222",
		},
		DisplayName:   wantModifiedContextName,
		ComponentType: GeneralType,
	})
	newContextProviderScripts = append(newContextProviderScripts, ContextProvider{
		BasicComponent: BasicComponent{
			Identifier:    wantModifiedContextWithStateChange,
			ComponentType: "script",
			Name:          wantModifiedContextWithStateChange,
			Version:       "222",
			State:         EnableState,
		},
		DisplayName:   wantModifiedContextWithStateChange,
		ComponentType: GeneralType,
	})

	originContextProviderScripts := make([]ContextProvider, 0)
	originContextProviderScripts = append(originContextProviderScripts, ContextProvider{
		BasicComponent: BasicComponent{
			Identifier:    wantModifiedContextName + "completion",
			ComponentType: "script",
			Name:          wantModifiedContextName,
			Version:       "111",
		},
		DisplayName:   wantModifiedContextName,
		ComponentType: GeneralType,
	})
	originContextProviderScripts = append(originContextProviderScripts, ContextProvider{
		BasicComponent: BasicComponent{
			Identifier:    wantDeletedContextName + "chat_ask",
			ComponentType: "script",
			Name:          wantDeletedContextName,
			Version:       "222",
		},
		DisplayName:   wantDeletedContextName,
		ComponentType: GeneralType,
	})
	originContextProviderScripts = append(originContextProviderScripts, ContextProvider{
		BasicComponent: BasicComponent{
			Identifier:    wantModifiedContextWithStateChange,
			ComponentType: "script",
			Name:          wantModifiedContextWithStateChange,
			Version:       "111",
			State:         DisableState,
		},
		DisplayName:   wantModifiedContextWithStateChange,
		ComponentType: GeneralType,
	})

	added, modified, deleted := CompareContextProviderDiff(newContextProviderScripts, originContextProviderScripts)
	assert.Equal(t, 1, len(added), "one need added")
	assert.Equal(t, 2, len(modified), "one need modified")
	assert.Equal(t, 1, len(deleted), "one need deleted")
	assert.Equal(t, wantAddedContextName, added[0].Name, wantAddedContextName+" should be added")
	assert.Equal(t, wantModifiedContextName, modified[0].Name, wantModifiedContextName+" should be modified")
	assert.Equal(t, wantModifiedContextWithStateChange, modified[1].Name, wantModifiedContextWithStateChange+" should be modified")
	assert.Equal(t, wantDeletedContextName, deleted[0].Name, wantDeletedContextName+" should be deleted")
}

func Test_checkContextProvider(t *testing.T) {
	contextProvider := ContextProvider{
		BasicComponent: BasicComponent{
			ComponentType: "prompt",
			Version:       "111",
		},
	}
	assert.Equal(t, "contextProvider name is blank", checkContextProvider(&contextProvider).Error(), "should check contextProvider name")

	contextProvider = ContextProvider{
		BasicComponent: BasicComponent{
			Name:          "file",
			ComponentType: "prompt",
			Version:       "111",
		},
	}
	assert.Equal(t, "contextProvider display name is blank", checkContextProvider(&contextProvider).Error(), "should check contextProvider display name")

	contextProvider = ContextProvider{
		BasicComponent: BasicComponent{
			Name:          "file",
			ComponentType: "prompt",
			Version:       "111",
		},
		DisplayName:   "display name",
		ComponentType: "illegal name",
	}
	assert.Equal(t, "contextProvider ComponentType is illegal:illegal name", checkContextProvider(&contextProvider).Error(), "should check contextProvider illegal name")

}

func Test_MergeContextProvider(t *testing.T) {
	InitSystemContextProvider()

	serverContextProviderScripts := make([]ContextProvider, 0)
	serverContextProviderScripts = append(serverContextProviderScripts, ContextProvider{
		BasicComponent: BasicComponent{
			Identifier:    "serverContext",
			ComponentType: "script",
			Name:          "file",
			Version:       "111",
		},
		DisplayName:   "file",
		ComponentType: ComboBoxType,
	})

	serverContextProviderScripts = append(serverContextProviderScripts, ContextProvider{
		BasicComponent: BasicComponent{
			Identifier:    "serverContext1",
			ComponentType: "script",
			Name:          "file1",
			Version:       "111",
		},
		DisplayName:   "file",
		ComponentType: ComboBoxType,
	})

	localContextProviderScripts := make([]ContextProvider, 0)
	localContextProviderScripts = append(localContextProviderScripts, ContextProvider{
		BasicComponent: BasicComponent{
			Identifier:    "localContext1",
			ComponentType: "script",
			Name:          "file1",
			Version:       "111",
		},
		DisplayName:   "file1",
		ComponentType: ComboBoxType,
	})
	localContextProviderScripts = append(localContextProviderScripts, ContextProvider{
		BasicComponent: BasicComponent{
			Identifier:    "localContext2",
			ComponentType: "script",
			Name:          "file",
			Version:       "111",
		},
		DisplayName:   "file",
		ComponentType: ComboBoxType,
	})

	merged := MergeContextProviders(serverContextProviderScripts, localContextProviderScripts)
	assert.Equal(t, 2, len(merged), "one need merged")
}
