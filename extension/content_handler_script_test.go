package extension

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_CompareContextFilterScriptDiff(t *testing.T) {
	wantAddedScriptName := "filterMyRepoFile-added"
	wantModifiedScriptName := "filterMyRepoFile-modified"
	wantDeletedScriptName := "filterMyRepoFile-deleted"
	wantModifiedScriptWithStateChange := "filterMyRepoFile-state-changed"

	newContextFilterScripts := make([]ContentHandlerScript, 0)
	newContextFilterScripts = append(newContextFilterScripts, ContentHandlerScript{
		BasicComponent: BasicComponent{
			Identifier:    wantAddedScriptName + "chat_ask",
			ComponentType: "script",
			Name:          wantAddedScriptName,
			Version:       "111",
		},
		BizType:  "chat_ask",
		Strategy: "blocker",
	})
	newContextFilterScripts = append(newContextFilterScripts, ContentHandlerScript{
		BasicComponent: BasicComponent{
			Identifier:    wantModifiedScriptName + "completion",
			ComponentType: "script",
			Name:          wantModifiedScriptName,
			Version:       "222",
		},
		BizType:  "completion",
		Strategy: FilterType,
	})
	newContextFilterScripts = append(newContextFilterScripts, ContentHandlerScript{
		BasicComponent: BasicComponent{
			Identifier:    "filterMyRepoFile-added" + "completion",
			ComponentType: "script",
			Name:          "filterMyRepoFile-added",
			Version:       "111",
		},
		BizType:  "completion",
		Strategy: FilterType,
	})
	newContextFilterScripts = append(newContextFilterScripts, ContentHandlerScript{
		BasicComponent: BasicComponent{
			Identifier:    wantModifiedScriptWithStateChange,
			ComponentType: "script",
			Name:          wantModifiedScriptWithStateChange,
			Version:       "111",
			State:         EnableState,
		},
		BizType:  "completion",
		Strategy: FilterType,
	})

	originContextFilterScripts := make([]ContentHandlerScript, 0)
	originContextFilterScripts = append(originContextFilterScripts, ContentHandlerScript{
		BasicComponent: BasicComponent{
			Identifier:    wantModifiedScriptName + "completion",
			ComponentType: "script",
			Name:          wantModifiedScriptName,
			Version:       "111",
		},
		BizType:  "completion",
		Strategy: "blocker",
	})
	originContextFilterScripts = append(originContextFilterScripts, ContentHandlerScript{
		BasicComponent: BasicComponent{
			Identifier:    wantDeletedScriptName + "chat_ask",
			ComponentType: "script",
			Name:          wantDeletedScriptName,
			Version:       "222",
		},
		BizType:  "chat_ask",
		Strategy: "blocker",
	})
	originContextFilterScripts = append(originContextFilterScripts, ContentHandlerScript{
		BasicComponent: BasicComponent{
			Identifier:    "filterMyRepoFile-added" + "completion",
			ComponentType: "script",
			Name:          "filterMyRepoFile-added",
			Version:       "111",
		},
		BizType:  "completion",
		Strategy: "warning",
	})
	originContextFilterScripts = append(originContextFilterScripts, ContentHandlerScript{
		BasicComponent: BasicComponent{
			Identifier:    wantModifiedScriptWithStateChange,
			ComponentType: "script",
			Name:          wantModifiedScriptWithStateChange,
			Version:       "111",
			State:         DisableState,
		},
		BizType:  "completion",
		Strategy: FilterType,
	})

	added, modified, deleted := CompareContextFilterScriptDiff(newContextFilterScripts, originContextFilterScripts)
	assert.Equal(t, 1, len(added), "one need added")
	assert.Equal(t, 2, len(modified), "one need modified")
	assert.Equal(t, 1, len(deleted), "one need deleted")
	assert.Equal(t, wantAddedScriptName, added[0].Name, wantAddedScriptName+" should be added")
	assert.Equal(t, wantModifiedScriptName, modified[0].Name, wantModifiedScriptName+" should be modified")
	assert.Equal(t, wantModifiedScriptWithStateChange, modified[1].Name, wantModifiedScriptWithStateChange+" should be modified")
	assert.Equal(t, wantDeletedScriptName, deleted[0].Name, wantDeletedScriptName+" should be deleted")
}

func Test_checkContextFilterScript(t *testing.T) {
	contextFilterScript := ContentHandlerScript{
		BasicComponent: BasicComponent{
			ComponentType: "script",
			Version:       "111",
		},
		BizType:  "answer",
		Strategy: "blocker",
	}
	assert.Equal(t, "contextFilterScript name is blank", checkContextFilterScript(&contextFilterScript).Error(), "should check contextFilterScript name")

	contextFilterScript = ContentHandlerScript{
		BasicComponent: BasicComponent{
			ComponentType: "script",
			Version:       "111",
			Name:          "filterMyRepoFile",
		},
		BizType:  "answer",
		Strategy: "blocker",
	}
	assert.Equal(t, "bizType is invalid", checkContextFilterScript(&contextFilterScript).Error(), "should check BizType")

	contextFilterScript = ContentHandlerScript{
		BasicComponent: BasicComponent{
			ComponentType: "script",
			Version:       "111",
			Name:          "filterMyRepoFile",
		},
		BizType:  BizTypeChatAsk,
		Strategy: "blocker",
		Stage:    "abc",
	}
	assert.Equal(t, "stage is invalid", checkContextFilterScript(&contextFilterScript).Error(), "should check stage")
}
