package extension

import (
	"cosy/log"
	"errors"
)

type ContentHandlerRule struct {
	BasicComponent

	// 拦截策略
	Strategy string `json:"strategy"`

	// 正则表达式
	RegExp string `json:"regExp"`

	// 替换的词表
	ReplaceWord string `json:"replaceWord"`

	// 规则拦截上报的webhook列表
	WebHookList []string `json:"webHookList"`

	// 业务类型，补全 or 问答
	BizType string `json:"bizType"`

	// 处理阶段 pre:前置处理 post:后置处理
	Stage string `json:"stage"`
}

// 比对两次的ContextFilterRule列表，返回新增、修改、删除的列表
func CompareContextFilterRulesDiff(newContextFilterRules, originContextFilterRules []ContentHandlerRule) (added, modified, deleted []ContentHandlerRule) {
	newContextFilterRuleMap := make(map[string]ContentHandlerRule)
	for _, item := range newContextFilterRules {
		newContextFilterRuleMap[item.Identifier] = item
	}

	originContextFilterRuleMap := make(map[string]ContentHandlerRule)
	for _, item := range originContextFilterRules {
		originContextFilterRuleMap[item.Identifier] = item
	}

	// 检查是否有新增与修改的元素
	for name, item := range newContextFilterRuleMap {
		if _, exists := originContextFilterRuleMap[name]; !exists {
			added = append(added, item)
			continue
		}
		if originContextFilterRuleMap[name].Version != item.Version {
			modified = append(modified, item)
			continue
		}
		if originContextFilterRuleMap[name].Version == item.Version && originContextFilterRuleMap[name].Identifier == item.Identifier && originContextFilterRuleMap[name].State != item.State {
			// state状态变更也需要去更新组件类型
			modified = append(modified, item)
			continue
		}
		if originContextFilterRuleMap[name].Version == item.Version && originContextFilterRuleMap[name].Identifier == item.Identifier {
			// webhook配置变更页需要去更新组件
			if len(originContextFilterRuleMap[name].WebHookList) != len(item.WebHookList) {
				modified = append(modified, item)
				continue
			}
			if hasModifiedItemByWebhookChange(originContextFilterRuleMap[name], item) {
				modified = append(modified, item)
			}
		}
		// strategy的变化需要去更新组件类型
		if originContextFilterRuleMap[name].Version == item.Version && originContextFilterRuleMap[name].Identifier == item.Identifier && originContextFilterRuleMap[name].Strategy != item.Strategy {
			modified = append(modified, item)
		}
	}

	// 检查是否有删除的元素
	for name, _ := range originContextFilterRuleMap {
		if _, exists := newContextFilterRuleMap[name]; !exists {
			deleted = append(deleted, originContextFilterRuleMap[name])
		}
	}
	return
}

// MergeContextRules MergeConfig 合并规则，若存在唯一标识冲突则优先使用server端配置
func MergeContextRules(serverRules, localRules []ContentHandlerRule) (merged []ContentHandlerRule) {
	serverContextFilterRuleMap := make(map[string]ContentHandlerRule)
	for _, item := range serverRules {
		serverContextFilterRuleMap[item.Identifier] = item
	}

	merged = append(merged, serverRules...)
	for _, item := range localRules {
		if _, exists := serverContextFilterRuleMap[item.Identifier]; !exists {
			merged = append(merged, item)
		} else {
			log.Warnf("local contextFilterRule%v has conflict with sever config", item.Identifier)
		}
	}
	return
}

func checkContextFilterRule(contextFilterRule *ContentHandlerRule) error {
	if contextFilterRule.Name == "" {
		return errors.New("contextFilterRule name is blank")
	}
	if contextFilterRule.BizType != BizTypeCompletion && contextFilterRule.BizType != BizTypeChatAsk {
		return errors.New("bizType is invalid")
	}
	if contextFilterRule.Strategy != BlockerType && contextFilterRule.Strategy != FilterType && contextFilterRule.Strategy != NoOpsType {
		return errors.New("strategy is invalid")
	}
	if contextFilterRule.RegExp == "" {
		return errors.New("contextFilterRule RegExp is blank")
	}
	if contextFilterRule.Stage != "" && contextFilterRule.Stage != PreStage && contextFilterRule.Stage != PostStage {
		return errors.New("stage is invalid")
	}
	return nil
}

func hasModifiedItemByWebhookChange(originalRule, newRule ContentHandlerRule) bool {
	for _, webhook := range originalRule.WebHookList {
		for _, newWebHook := range newRule.WebHookList {
			if webhook != newWebHook {
				return true
			}
		}
	}
	return false
}
