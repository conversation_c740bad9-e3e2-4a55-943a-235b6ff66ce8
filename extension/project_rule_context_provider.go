package extension

import (
	"cosy/definition"
	cosyError "cosy/errors"
	"cosy/extension/rule"
	"cosy/log"
	"strings"
)

type ProjectRuleContextProvider struct {
}

func (r *ProjectRuleContextProvider) GetContext(getContextRequest GetContextResponseApiRequest) (definition.GetContextResponse, error) {
	ruleFilePath := getContextRequest.ContextProviderRequest.Query
	if ruleFilePath == "" {
		return definition.GetContextResponse{}, cosyError.ErrFileNotFound
	}
	ruleDir := rule.GetRuleFileDir(getContextRequest.Sdk.WorkspaceDir)
	projectRule, err := rule.ReadRuleFile(ruleDir, ruleFilePath)
	if err != nil {
		log.Error(err)
		return definition.GetContextResponse{}, err
	}
	// 组装返回结果
	fileContextItem := definition.ContextItem{
		Identifier: ruleFilePath,
		Key:        ruleFilePath,
		Value:      projectRule.Content,
	}
	contextItems := make([]definition.ContextItem, 0)
	contextItems = append(contextItems, fileContextItem)
	return definition.GetContextResponse{
		ContextItems: contextItems,
	}, nil
}

func (r *ProjectRuleContextProvider) LoadComboBoxItems(loadComboboxItemsRequest LoadComboboxItemsApiRequest) (definition.GetComboBoxItemsResponse, error) {
	workspaceDir := loadComboboxItemsRequest.Sdk.WorkspaceDir
	if workspaceDir == "" {
		return definition.GetComboBoxItemsResponse{}, cosyError.New(cosyError.FileNotFound, "no workspaceDir provided")
	}
	query := loadComboboxItemsRequest.GetComboBoxItemsRequest.Query
	manualRules, err := getAllRules(workspaceDir, query)
	if err != nil {
		return definition.GetComboBoxItemsResponse{}, err
	}

	comboBoxItems := make([]definition.ComboBoxItem, 0)
	for i := 0; i < len(manualRules); i++ {
		projectRule := manualRules[i]
		comboBoxItems = append(comboBoxItems, definition.ComboBoxItem{
			Identifier:  projectRule.FilePath,
			Name:        projectRule.Name,
			DisplayName: projectRule.FilePath,
		})
	}
	return definition.GetComboBoxItemsResponse{
		ComboBoxItems: comboBoxItems,
	}, nil
}

// getAllManualRules 获取所有手动规则
func getAllRules(rootPath string, searchString string) ([]*rule.ProjectRule, error) {
	// 查询所有手动规则
	allRules, err := rule.GetProjectRulesByTrigger(rootPath, []rule.ProjectRulesTrigger{rule.ManualRule, rule.GlobRule, rule.AlwaysOnRule, rule.ModelDecisionRule})
	if err != nil {
		return nil, err
	}

	var matchedRules []*rule.ProjectRule
	// 遍历规则，根据搜索字符串过滤
	for i := 0; i < len(allRules); i++ {
		projectRule := allRules[i]
		// 如果有搜索字符串，检查规则名称是否包含搜索字符串
		if searchString != "" {
			if !strings.Contains(strings.ToLower(projectRule.Name), strings.ToLower(searchString)) {
				continue
			}
		}
		matchedRules = append(matchedRules, projectRule)
	}
	return matchedRules, nil
}
