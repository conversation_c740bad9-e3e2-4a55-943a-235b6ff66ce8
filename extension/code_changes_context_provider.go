package extension

import (
	"bytes"
	"cosy/definition"
	"cosy/log"
	"fmt"
	"io"
	"os"
	"os/exec"
	"runtime"

	"github.com/go-git/go-git/v5"
)

type CodeChangesContextProvider struct {
}

const maxDiffSize = 50 * 1024

func (c *CodeChangesContextProvider) GetContext(getContextRequest GetContextResponseApiRequest) (definition.GetContextResponse, error) {
	// 打开代码库
	workspaceDir := getContextRequest.Sdk.WorkspaceDir

	_, err := git.PlainOpen(workspaceDir)
	if err != nil {
		log.Warnf("failed to open repository:%s", err.Error())
		return definition.GetContextResponse{
			ContextItems: make([]definition.ContextItem, 0),
		}, nil
	}

	// 获取git diff内容
	diffText, err := getGitDiffByCmd(workspaceDir)
	if err != nil {
		return definition.GetContextResponse{}, err
	}

	// 组装返回结果
	codeChangesContextItem := definition.ContextItem{
		Identifier: "codeChanges",
		Key:        "codeChanges",
		Value:      diffText,
	}
	contextItems := make([]definition.ContextItem, 0)
	contextItems = append(contextItems, codeChangesContextItem)
	return definition.GetContextResponse{
		ContextItems: contextItems,
	}, nil
}

func (c *CodeChangesContextProvider) LoadComboBoxItems(loadComboboxItemsRequest LoadComboboxItemsApiRequest) (definition.GetComboBoxItemsResponse, error) {
	return definition.GetComboBoxItemsResponse{}, nil
}

// 调用命令行获取git diff
func getGitDiffByCmd(workspaceDir string) (string, error) {
	diffText, err := runGitDiffCommand(workspaceDir, false)
	if err != nil {
		// 如果是gitBadVersionError场景再调用git diff --cached获取diff内容
		if !isGitHeadExist(workspaceDir) {
			log.Errorf("git diff origin error is:%v", err.Error())
			diffText, err = runGitDiffCommand(workspaceDir, true)
			if err != nil {
				return "", err
			}
			return diffText, nil
		}
		return "", err
	}
	return diffText, nil
}

func gitBinary() string {
	if runtime.GOOS == "windows" {
		return "git.exe"
	}
	return "git"
}

/**
 * 使用调用命令行操作gif diff命令获取diff内容
 */
func runGitDiffCommand(workspace string, cached bool) (string, error) {
	// Build git command arguments
	args := []string{"diff"}
	if cached {
		args = append(args, "--cached")
	} else {
		args = append(args, "HEAD", "--")
	}

	// Find git executable path, handling Windows case
	gitPath := gitBinary()

	cmd := exec.Command(gitPath, args...)
	cmd.Dir = workspace

	// Set LC_ALL=C environment
	env := os.Environ()
	env = append(env, "LC_ALL=C")
	cmd.Env = env

	// Get command stdout pipe
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return "", fmt.Errorf("failed to get stdout pipe: %v", err)
	}

	// Get command stderr pipe
	stderr, err := cmd.StderrPipe()
	if err != nil {
		return "", fmt.Errorf("failed to get stderr pipe: %v", err)
	}

	// Start the command
	if err := cmd.Start(); err != nil {
		return "", fmt.Errorf("failed to start git command: %v", err)
	}

	// Read stderr in a separate goroutine
	var errBuf bytes.Buffer
	go func() {
		io.Copy(&errBuf, stderr)
	}()

	// Read stdout with size limit
	var output bytes.Buffer
	buf := make([]byte, 4096) // 使用固定大小的缓冲区
	totalSize := 0

	cmdHasBeenKilled := false
	for {
		n, err := stdout.Read(buf)
		if n > 0 {
			// 计算可以写入的字节数
			remainingSpace := maxDiffSize - totalSize
			if remainingSpace <= 0 {
				if err = cmd.Process.Kill(); err != nil {
					log.Errorf("failed to kill git command: %v", err)
				}
				cmdHasBeenKilled = true
				break
			}

			// 确定这次要写入的字节数
			writeLen := n
			if writeLen > remainingSpace {
				writeLen = remainingSpace
			}

			output.Write(buf[:writeLen])
			totalSize += writeLen

			if totalSize >= maxDiffSize {
				if err = cmd.Process.Kill(); err != nil {
					log.Errorf("failed to kill git command: %v", err)
				}
				cmdHasBeenKilled = true
				break
			}
		}

		if err == io.EOF {
			break
		}
		if err != nil {
			return "", fmt.Errorf("error reading git output: %v", err)
		}
	}

	// Wait for the command to finish
	err = cmd.Wait()

	// If we encountered an error and it's not due to the process being killed
	if err != nil && !cmdHasBeenKilled {
		return "", fmt.Errorf("git diff error: %v", err)
	}

	// Check if there was any stderr output and exitCode != 0 and cmdNotHasBeenKilled
	exitCode := cmd.ProcessState.ExitCode()
	if errBuf.Len() > 0 && !cmdHasBeenKilled && exitCode != 0 {
		return "", fmt.Errorf("git diff error: %s", errBuf.String())
	}

	return output.String(), nil
}

// 探测HEAD是否存在
func isGitHeadExist(workspaceDir string) bool {
	// do git rev-parse HEAD -- cmd
	cmd := exec.Command("git", "rev-parse", "HEAD", "--")

	// set env LC_ALL=C
	cmd.Env = append(os.Environ(), "LC_ALL=C")

	// set cmd dir
	cmd.Dir = workspaceDir

	// do cmd
	if output, err := cmd.CombinedOutput(); err != nil {
		log.Warnf("do git rev-parse HEAD has error: %v, %s", err, output)
		return false
	}
	return true
}
