package extension

import (
	"context"
	"encoding/json"
	"errors"
	"io/fs"
	"os"
	"path/filepath"

	"cosy/config"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/util"
	"cosy/websocket"
)

const (
	ScriptType = "script"
	PromptType = "prompt"
	RuleType   = "rule"

	BizTypeCompletion = "completion"
	BizTypeChatAsk    = "chat"
)

const (
	BlockerType = "blocker"
	FilterType  = "filter"
	NoOpsType   = "no_ops"
)

const (
	PreStage  = "pre"
	PostStage = "post"
)

const (
	// ContextProviderTypeGeneral 通用类型
	ContextProviderTypeGeneral = "general"

	// ContextProviderTypeComboBox 下拉框类型
	ContextProviderTypeComboBox = "comboBox"

	// ContextProviderTypeQuery 查询类型
	ContextProviderTypeQuery = "query"
)

const (
	EnableState  = "enabled"
	DisableState = "disabled"
)

// BasicComponent 基础组件
type BasicComponent struct {
	// 组件唯一标识
	Identifier string `json:"identifier"`

	// 名称
	Name string `json:"name"`

	// 组件类型 script,prompt
	ComponentType string `json:"type"`

	// 参数
	Params map[string]interface{} `json:"params"`

	// 版本
	Version string `json:"version"`

	// 脚本存储路径
	ScriptPath string `json:"scriptPath"`

	// 组件状态(enable:开启,disable:关闭)
	State string `json:"state"`
}

// ExtensionConfig 从服务端拉取到的原始动态扩展配置
type ExtensionConfig struct {
	ContentHandlerRules []ContentHandlerRule `json:"contentHandlerRules"`

	ContentHandlerScripts []ContentHandlerScript `json:"contentHandlerScripts"`

	Commands []Command `json:"commands"`

	ContextProviders []ContextProvider `json:"contextProviders"`

	// 后置过滤规则
	PostContentHandlerRules []ContentHandlerRule `json:"postContentHandlerRules"`

	// 后置过滤脚本
	PostContentHandlerScripts []ContentHandlerScript `json:"postContentHandlerScripts"`
}

type ContextFilterSetting struct {

	// 业务类型
	BizType string

	// 状态
	State string

	// 用户是否做过配置
	IsConfigured bool

	// 组件类型
	ComponentType string

	// 策略
	Strategy string
}

func (e *ExtensionConfig) GetCommandConfig(identifier string) *Command {
	for _, cmd := range e.Commands {
		if cmd.Identifier == identifier {
			return &cmd
		}
	}
	return nil
}

func (e *ExtensionConfig) IsChatFilterEnable() bool {
	for _, rule := range e.ContentHandlerRules {
		if rule.BizType == BizTypeChatAsk && rule.State == EnableState {
			return true
		}
	}
	for _, script := range e.ContentHandlerScripts {
		if script.BizType == BizTypeChatAsk && script.State == EnableState {
			return true
		}
	}
	return false
}

func (e *ExtensionConfig) IsCompletionFilterEnable() bool {
	for _, rule := range e.ContentHandlerRules {
		if rule.BizType == BizTypeCompletion && rule.State == EnableState {
			return true
		}
	}
	for _, script := range e.ContentHandlerScripts {
		if script.BizType == BizTypeCompletion && script.State == EnableState {
			return true
		}
	}
	return false
}

var SystemCommands = make([]Command, 0)

var SystemContextProviders = make([]ContextProvider, 0)

var SystemContextProviderMap = make(map[string]ContextProvider)

var SystemContextProviderEngineMap = make(map[string]IContextProvider)

var GlobalExtensionConfig = ExtensionConfig{}

var NodeVersion = "v20.17.0"

// GlobalExensionConfig 设置全局扩展配置
func SetGlobalExtensionConfig(config *ExtensionConfig) {
	GlobalExtensionConfig = *config
	postContentHandlerRules := make([]ContentHandlerRule, 0)
	for _, rule := range config.ContentHandlerRules {
		if rule.Stage == PostStage {
			postContentHandlerRules = append(postContentHandlerRules, rule)
		}
	}
	GlobalExtensionConfig.PostContentHandlerRules = postContentHandlerRules

	postContentHandlerScripts := make([]ContentHandlerScript, 0)
	for _, script := range config.ContentHandlerScripts {
		if script.Stage == PostStage {
			postContentHandlerScripts = append(postContentHandlerScripts, script)
		}
	}
	GlobalExtensionConfig.PostContentHandlerScripts = postContentHandlerScripts
}

// 获取过滤规则的webhook上报地址
func GetFilterRuleWebHook(bizType string) (webHook string) {
	if len(GlobalExtensionConfig.ContentHandlerRules) > 0 {
		for _, item := range GlobalExtensionConfig.ContentHandlerRules {
			if len(item.WebHookList) > 0 && item.BizType == bizType && item.State == EnableState {
				return item.WebHookList[0]
			}
		}
	}
	return ""
}

// 获取后置过滤规则的webhook上报地址
func GetPostFilterRuleWebHook(bizType string) (webHook string) {
	if len(GlobalExtensionConfig.PostContentHandlerRules) > 0 {
		for _, item := range GlobalExtensionConfig.PostContentHandlerRules {
			if len(item.WebHookList) > 0 && item.BizType == bizType && item.State == EnableState {
				return item.WebHookList[0]
			}
		}
	}
	return ""
}

// 判断前置过滤启用情况
func GetContextFilterSettingByBizType(bizType string) (contextFilterSetting ContextFilterSetting) {
	filterSetting := ContextFilterSetting{}
	if bizType == "" {
		return filterSetting
	}

	for _, item := range GlobalExtensionConfig.ContentHandlerScripts {
		if item.BizType == bizType && item.Stage != PostStage {
			filterSetting.State = item.State
			filterSetting.ComponentType = ScriptType
			filterSetting.Strategy = item.Strategy
			filterSetting.IsConfigured = true
			filterSetting.BizType = bizType
			return filterSetting
		}
	}

	for _, item := range GlobalExtensionConfig.ContentHandlerRules {
		if item.BizType == bizType && item.Stage != PostStage {
			filterSetting.State = item.State
			filterSetting.ComponentType = RuleType
			filterSetting.Strategy = item.Strategy
			filterSetting.IsConfigured = true
			filterSetting.BizType = bizType
			return filterSetting
		}
	}
	filterSetting.IsConfigured = false
	return filterSetting
}

// 判断后置过滤器的启用情况
func GetPostContextFilterSettingByBizType(bizType string) (contextFilterSetting ContextFilterSetting) {
	filterSetting := ContextFilterSetting{}
	if bizType == "" {
		return filterSetting
	}

	for _, item := range GlobalExtensionConfig.PostContentHandlerScripts {
		if item.BizType == bizType && item.Stage == PostStage {
			filterSetting.State = item.State
			filterSetting.ComponentType = ScriptType
			filterSetting.Strategy = item.Strategy
			filterSetting.IsConfigured = true
			filterSetting.BizType = bizType
			return filterSetting
		}
	}

	for _, item := range GlobalExtensionConfig.PostContentHandlerRules {
		if item.BizType == bizType && item.Stage == PostStage {
			filterSetting.State = item.State
			filterSetting.ComponentType = RuleType
			filterSetting.Strategy = item.Strategy
			filterSetting.IsConfigured = true
			filterSetting.BizType = bizType
			return filterSetting
		}
	}
	filterSetting.IsConfigured = false
	return filterSetting
}

var GlobalProfileData = definition.WebViewShowData{
	CommandOrder: definition.WebViewCommandOrder{
		Commands:           nil,
		OfficialCommandEnd: false,
	},
}

// UpdateProfileData 供外部调用
// 作用是：更新全局指令顺序
func UpdateProfileData(targetProfileData definition.WebViewShowData) error {
	savedData := &definition.ProfileData{}

	// 1.获取到新的自定义指令的顺序
	var newCommandOrder []definition.WebViewCommandJsObject
	newCommandOrder = nil
	if targetProfileData.CommandOrder.Commands == nil || len(targetProfileData.CommandOrder.Commands) == 0 {
		newCommandOrder = make([]definition.WebViewCommandJsObject, 0)
	} else {
		for _, targetOrder := range targetProfileData.CommandOrder.Commands {
			for _, cmd := range GlobalExtensionConfig.Commands {
				if cmd.Identifier != "" && cmd.Identifier == targetOrder.Identifier {
					newCommandOrder = append(newCommandOrder, definition.WebViewCommandJsObject{
						Identifier: cmd.Identifier,
						NameLeft:   cmd.DisplayName,
						NameRight:  cmd.Name,
					})
					savedData.CommandShowOrder = append(savedData.CommandShowOrder, cmd.Identifier)
					break
				}
			}
		}
	}

	// TODO 2.如何将官方指令放入newCommandOrder

	// 3.更新全局配置
	if len(newCommandOrder) > 0 {
		GlobalProfileData.CommandOrder.Commands = newCommandOrder
	}
	GlobalProfileData.CommandOrder.OfficialCommandEnd = targetProfileData.CommandOrder.OfficialCommandEnd

	// 4.broadcast到各个端
	// TODO register方法在这里异步调用

	extensionApiConfig, _ := GetExtensionApiConfig()
	websocket.SendBroadcastWithTimeout(context.Background(), "extension/register", extensionApiConfig, nil)

	// 5.保存到文件中
	if err := WriteProfileData(savedData); err != nil {
		log.Infof("Failed to update local command order: %s", err)
		return err
	}

	return nil
}

// WriteProfileData 写入个人profile文件
func WriteProfileData(targetCommandOrder *definition.ProfileData) error {
	// 检测要写的数据的有效性
	if targetCommandOrder == nil {
		return nil
	}

	if savedProfileData, err := ReadProfileData(); err == nil {
		// 为了兜底
		// 没有传过来的数据部分，则使用之前保存的，确保只更新传过来的部分
		if targetCommandOrder.CommandShowOrder == nil {
			targetCommandOrder.CommandShowOrder = savedProfileData.CommandShowOrder
		}
	}

	profilePath := filepath.Join(util.GetCosyHomePath(), "cache", definition.ProfileFile)
	writeData, err := json.Marshal(targetCommandOrder)

	if err != nil {
		log.Infof("Failed to write profile file: %s", err)
		return errors.New("marshal profile data error")
	}

	if err = os.WriteFile(profilePath, writeData, 0644); err != nil {
		log.Infof("Failed to write profile file: %s", err)
		return errors.New("failed to write profile file")
	}
	return nil
}

// ReadProfileData 读取个人profile文件
func ReadProfileData() (*definition.ProfileData, error) {
	profilePath := filepath.Join(util.GetCosyHomePath(), "cache", definition.ProfileFile)
	readData, err := os.ReadFile(profilePath)
	if err != nil {
		// 检查错误是否为文件不存在
		if errors.Is(err, fs.ErrNotExist) {
			// 通常是因为用户未登录，没有生成user文件
			log.Infof("profile not exist, please login")
			return nil, errors.New("profile not exist, please login")
		}
		log.Infof("Failed to read profile file: %s", err)
		return nil, errors.New("failed to read profile file")
	}

	var savedProfileData = &definition.ProfileData{}
	if err = json.Unmarshal(readData, savedProfileData); err != nil {
		return nil, errors.New("unmarshal profile data error")
	}
	return savedProfileData, nil
}

func InitSystemContextProvider() {
	log.Infof("start init system context provider")
	// #file 系统上下文
	fileContextProvider := ContextProvider{
		BasicComponent: BasicComponent{
			Identifier: "7a0c2792-b1ed-49ec-a82b-d97b16a242b4",
			Name:       "file",
			Version:    "1.0.0",
		},
		DisplayName:   "file",
		ComponentType: ComboBoxType,
		SourceType:    SystemType,
	}

	// #selectedCode 系统上下文
	selectedCodeContextProvider := ContextProvider{
		BasicComponent: BasicComponent{
			Identifier: "858b986a-9b02-4d41-87ef-c7b6b92b44d7",
			Name:       "selectedCode",
			Version:    "1.0.0",
		},
		DisplayName:   "selectedCode",
		ComponentType: ComboBoxType,
		SourceType:    SystemType,
	}

	// #teamDock 系统上下文
	teamDocsContextProvider := ContextProvider{
		BasicComponent: BasicComponent{
			Identifier: "f642ce58-2567-41b8-96e9-83b6c1c59444",
			Name:       "teamDocs",
			Version:    "1.0.0",
		},
		DisplayName:    "team docs",
		ComponentType:  ComboBoxType,
		SourceType:     SystemType,
		RequiredPrompt: "针对以上teamDocs的一组参考文档，你的任务是理解文档的内容，并基于这些内容回答相关的问题。请结合问题和提供文档的相关性来回答问题。",
	}

	// codeChanges 系统上下文
	codeChangesContextProvider := ContextProvider{
		BasicComponent: BasicComponent{
			Identifier: "b462ac98-a4db-11ef-ad6e-1a87772d3c57",
			Name:       "codeChanges",
			Version:    "1.0.0",
		},
		DisplayName:   "codeChanges",
		ComponentType: GeneralType,
		SourceType:    SystemType,
	}

	// getCommits 系统上下文
	gitCommitContextProvider := ContextProvider{
		BasicComponent: BasicComponent{
			Identifier: "ff91e6fc-a4db-11ef-849f-1a87772d3c57",
			Name:       "gitCommit",
			Version:    "1.0.0",
		},
		DisplayName:    "gitCommit",
		ComponentType:  ComboBoxType,
		SourceType:     SystemType,
		RequiredPrompt: "如果需要生成代码，不要使用 git diff格式输出",
	}

	// #codebase 系统上下文
	codebaseContextProvider := ContextProvider{
		BasicComponent: BasicComponent{
			Identifier: "eeed9e9b-31c4-47a3-83a8-38c0861d1098",
			Name:       "codebase",
			Version:    "1.0.0",
		},
		DisplayName:   "codebase",
		ComponentType: GeneralType,
		SourceType:    SystemType,
	}

	// #folder 系统上下文
	folderContextProvider := ContextProvider{
		BasicComponent: BasicComponent{
			Identifier: "ac6256dc-5de4-49c8-980c-6a40d776985f",
			Name:       "folder",
			Version:    "1.0.0",
		},
		DisplayName:   "folders",
		ComponentType: ComboBoxType,
		SourceType:    SystemType,
	}

	// #rule 系统上下文
	projectRuleContextProvider := ContextProvider{
		BasicComponent: BasicComponent{
			Identifier: "hi91e6fc-a4db-11ef-847s-1a87772d3c49",
			Name:       "rule",
			Version:    "1.0.0",
		},
		DisplayName:   "rule",
		ComponentType: ComboBoxType,
		SourceType:    SystemType,
	}

	SystemContextProviders = append(SystemContextProviders, fileContextProvider)
	SystemContextProviderEngineMap[fileContextProvider.Identifier] = &FileContextProvider{}
	SystemContextProviderMap[fileContextProvider.Identifier] = fileContextProvider

	SystemContextProviders = append(SystemContextProviders, folderContextProvider)
	SystemContextProviderEngineMap[folderContextProvider.Identifier] = &FolderContextProvider{}
	SystemContextProviderMap[folderContextProvider.Identifier] = folderContextProvider

	// 公有云场景才开放image上下文
	if !config.OnPremiseMode {
		// #image 系统上下文
		imageContextProvider := ContextProvider{
			BasicComponent: BasicComponent{
				Identifier: "82cea131-ecaa-44be-bf2d-7dcce2149d9c",
				Name:       "image",
				Version:    "1.0.0",
			},
			DisplayName:   "image",
			ComponentType: ComboBoxType,
			SourceType:    SystemType,
		}
		SystemContextProviders = append(SystemContextProviders, imageContextProvider)
		SystemContextProviderMap[imageContextProvider.Identifier] = imageContextProvider
	}

	SystemContextProviders = append(SystemContextProviders, selectedCodeContextProvider)
	SystemContextProviderMap[selectedCodeContextProvider.Identifier] = selectedCodeContextProvider

	if !global.IsQoderProduct() {
		SystemContextProviders = append(SystemContextProviders, teamDocsContextProvider)
		SystemContextProviderMap[teamDocsContextProvider.Identifier] = teamDocsContextProvider

		SystemContextProviders = append(SystemContextProviders, codebaseContextProvider)
		SystemContextProviderMap[codebaseContextProvider.Identifier] = codebaseContextProvider
	}

	SystemContextProviders = append(SystemContextProviders, codeChangesContextProvider)
	SystemContextProviderEngineMap[codeChangesContextProvider.Identifier] = &CodeChangesContextProvider{}
	SystemContextProviderMap[codeChangesContextProvider.Identifier] = codeChangesContextProvider

	SystemContextProviders = append(SystemContextProviders, gitCommitContextProvider)
	SystemContextProviderEngineMap[gitCommitContextProvider.Identifier] = &GetCommitContextProvider{}
	SystemContextProviderMap[gitCommitContextProvider.Identifier] = gitCommitContextProvider

	SystemContextProviders = append(SystemContextProviders, projectRuleContextProvider)
	SystemContextProviderEngineMap[projectRuleContextProvider.Identifier] = &ProjectRuleContextProvider{}
	SystemContextProviderMap[projectRuleContextProvider.Identifier] = projectRuleContextProvider

	// #domElement 系统上下文 选择的页面元素
	domElementCodeContextProvider := ContextProvider{
		BasicComponent: BasicComponent{
			Identifier: "e1b87ba5-d7e2-4a16-84bc-111566fa3cda",
			Name:       "domElement",
			Version:    "1.0.0",
		},
		DisplayName:   "domElement",
		ComponentType: ComboBoxType,
		SourceType:    SystemType,
	}
	SystemContextProviders = append(SystemContextProviders, domElementCodeContextProvider)
	SystemContextProviderMap[domElementCodeContextProvider.Identifier] = domElementCodeContextProvider

	//terminal 系统上下文 选择的页面元素
	terminalContextProvider := ContextProvider{
		BasicComponent: BasicComponent{
			Identifier: "5b7e0ce1-8a99-4fbd-92fa-d8c243c22b5e",
			Name:       "terminal",
			Version:    "1.0.0",
		},
		DisplayName:   "terminal",
		ComponentType: ComboBoxType,
		SourceType:    SystemType,
	}
	SystemContextProviders = append(SystemContextProviders, terminalContextProvider)
	SystemContextProviderMap[terminalContextProvider.Identifier] = terminalContextProvider

	//if log.IsDebugEnabled() {
	//	log.Debugf("SystemContextProviders is %v", SystemContextProviders)
	//}
	log.Infof("init system context provider success")

}

func (e *ExtensionConfig) fetchRequiredContextProviders() {
	if len(e.Commands) == 0 {
		return
	}
	for i, command := range e.Commands {
		if len(command.RequiredContextItems) > 0 {
			requiredContextProviders := make([]ContextProvider, 0)
			for _, requiredContextItem := range command.RequiredContextItems {
				requiredContextProviders = append(requiredContextProviders, ContextProvider{
					BasicComponent: BasicComponent{
						Identifier: requiredContextItem.ContextKey,
						Name:       requiredContextItem.ContextKey,
					},
					Required: requiredContextItem.Required,
				})
			}
			e.Commands[i].RequiredContextProviders = requiredContextProviders
		}
	}
}
