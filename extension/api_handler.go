package extension

import (
	"context"
	"cosy/log"
	"cosy/websocket"
	"encoding/json"
	"time"

	"github.com/sourcegraph/jsonrpc2"
)

type ApiParams struct {
	SdkHash string `json:"sdkHash"`
	Params  []byte `json:"params"`
}

func handleExtensionApi(ctx context.Context, conn *jsonrpc2.Conn, rpcReq *jsonrpc2.Request) {
	// Parse raw jsonrpc messages
	var params ApiParams
	if err := json.Unmarshal(*rpcReq.Params, &params); err != nil {
		log.Info("Failed to parse input params for initialize")
		return
	}
	client, ok := _ideWsClients.Load(params.SdkHash)
	if !ok {
		log.Warn("Failed to find ideWsClient")
		return
	}

	// Add client of current request and websocket server to ctx
	// This ctx is used to control goroutines for processing requests
	reqCtx := context.WithValue(ctx, websocket.ClientCtxKey, client)

	go func() {
		switch rpcReq.Method {
		case "ability/currentFile":
			var fileResult File
			err := websocket.SendRequestWithTimeout(reqCtx, "ability/currentFile", nil, &fileResult, 10*time.Second)
			if err != nil {
				log.Warn("Failed to request ability/currentFile")
				return
			}
			sendResultErr := conn.Reply(reqCtx, rpcReq.ID, fileResult)
			if sendResultErr != nil {
				log.Warn("Failed to replay currentFile to node")
			}
		default:
			log.Error("unknown message method, ignore it", rpcReq.Method)
			return
		}
	}()

}
