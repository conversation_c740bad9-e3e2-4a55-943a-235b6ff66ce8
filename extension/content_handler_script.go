package extension

import (
	"cosy/log"
	"errors"
)

type ContentHandlerScript struct {
	BasicComponent

	// 拦截策略
	Strategy string `json:"strategy"`

	// 业务类型，补全 or 问答
	BizType string `json:"bizType"`

	// 处理阶段 pre:前置处理 post:后置处理
	Stage string `json:"stage"`
}

func GetEnabledContextFilterScripts(scripts []ContentHandlerScript) (enabledScripts []ContentHandlerScript) {
	for _, item := range scripts {
		if item.State == EnableState {
			enabledScripts = append(enabledScripts, item)
		}
	}
	return
}

// CompareContextFilterScriptDiff 比对两次的ContextFilterScript列表，返回新增、修改、删除的列表
func CompareContextFilterScriptDiff(newContextFilterScripts, originContextFilterScripts []ContentHandlerScript) (added, modified, deleted []ContentHandlerScript) {
	newContextFilterScriptMap := make(map[string]ContentHandlerScript)
	for _, item := range newContextFilterScripts {
		newContextFilterScriptMap[item.Identifier] = item
	}

	originContextFilterScriptMap := make(map[string]ContentHandlerScript)
	for _, item := range originContextFilterScripts {
		originContextFilterScriptMap[item.Identifier] = item
	}

	// 检查是否有新增与修改的元素
	for name, item := range newContextFilterScriptMap {
		if _, exists := originContextFilterScriptMap[name]; !exists {
			added = append(added, item)
		} else if originContextFilterScriptMap[name].Version != item.Version {
			modified = append(modified, item)
		} else if originContextFilterScriptMap[name].Version == item.Version && originContextFilterScriptMap[name].Identifier == item.Identifier && originContextFilterScriptMap[name].State != item.State {
			// state状态变更也需要去更新组件类型
			modified = append(modified, item)
		}
	}

	// 检查是否有删除的元素
	for name, _ := range originContextFilterScriptMap {
		if _, exists := newContextFilterScriptMap[name]; !exists {
			deleted = append(deleted, originContextFilterScriptMap[name])
		}
	}
	return
}

func MergeContextFilterScripts(serverScripts, localScripts []ContentHandlerScript) (merged []ContentHandlerScript) {
	serverContextFilterScriptMap := make(map[string]ContentHandlerScript)
	for _, item := range serverScripts {
		serverContextFilterScriptMap[item.Identifier] = item
	}

	merged = append(merged, serverScripts...)
	for _, item := range localScripts {
		if _, exists := serverContextFilterScriptMap[item.Name+item.BizType]; !exists {
			merged = append(merged, item)
		} else {
			log.Warnf("local contextFilterScript%v has conflict with sever config", item.Identifier)
		}
	}
	return
}

func checkContextFilterScript(contextFilterScript *ContentHandlerScript) error {
	if contextFilterScript.Name == "" {
		return errors.New("contextFilterScript name is blank")
	}
	if contextFilterScript.BizType != BizTypeCompletion && contextFilterScript.BizType != BizTypeChatAsk {
		return errors.New("bizType is invalid")
	}
	if contextFilterScript.Stage != "" && contextFilterScript.Stage != PreStage && contextFilterScript.Stage != PostStage {
		return errors.New("stage is invalid")
	}
	return nil
}
