package plan

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/storage/database"
	"encoding/json"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// TaskNode represents a single task node in the tree structure
type TaskNode struct {
	ID                string     `json:"id"`                          // Task unique identifier
	ParentID          string     `json:"parentId,omitempty"`          // ParentID task ID
	Status            TaskStatus `json:"status"`                      // Current task status
	Content           string     `json:"content"`                     // Task content description
	StartTime         int64      `json:"startTime,omitempty"`         // Task start time , [startTime
	EndTime           int64      `json:"endTime,omitempty"`           // Task end time ,   endTime]
	RelatedMessageIDs []string   `json:"relatedMessageIDs,omitempty"` // Associated message ID

	// Tree structure pointers for efficient navigation (not serialized to avoid cycles)
	Parent   *TaskNode   `json:"-"`                  // Parent node pointer
	Children []*TaskNode `json:"children,omitempty"` // Child nodes list
}

// TaskTree represents a complete task tree structure
type TaskTree struct {
	RootNodes []*TaskNode          // Root level task nodes
	NodeMap   map[string]*TaskNode // Node ID to node pointer mapping for O(1) lookup
	Pause     bool
	mutex     sync.RWMutex // Read-write lock for thread safety
}

// NewTaskTree creates a new TaskTree instance
func NewTaskTree() *TaskTree {
	return &TaskTree{
		RootNodes: make([]*TaskNode, 0),
		NodeMap:   make(map[string]*TaskNode),
		Pause:     false,
	}
}

// IsRoot checks if the node is a root node
func (n *TaskNode) IsRoot() bool {
	return n.Parent == nil
}

// HasChildren checks if the node has children
func (n *TaskNode) HasChildren() bool {
	return len(n.Children) > 0
}

// AddChild adds a child node to the current node
func (n *TaskNode) AddChild(child *TaskNode) {
	child.Parent = n
	n.Children = append(n.Children, child)
}

// InsertChildAfter inserts a new child after the specified sibling
func (n *TaskNode) InsertChildAfter(newChild *TaskNode, afterSibling *TaskNode) {
	newChild.Parent = n

	for i, child := range n.Children {
		if child == afterSibling {
			// Insert after this position
			n.Children = append(n.Children[:i+1], append([]*TaskNode{newChild}, n.Children[i+1:]...)...)
			break
		}
	}
}

// GetTaskTree retrieves the TaskTree for the specified session_id
func GetTaskTree(sessionId string) (*TaskTree, bool) {
	taskTree, err := loadTaskTreeFromDB(sessionId)
	if err != nil {
		log.Errorf("Failed to load task tree from database for key %s: %v", sessionId, err)
		return nil, false
	}
	if taskTree != nil {
		return taskTree, true
	}
	return nil, false
}

// GetNode retrieves a node by ID
func (t *TaskTree) GetNode(id string) (*TaskNode, bool) {
	node, exists := t.NodeMap[id]
	return node, exists
}

// AddRootNode adds a new root node to the tree
func (t *TaskTree) AddRootNode(node *TaskNode) {
	node.Parent = nil
	t.RootNodes = append(t.RootNodes, node)
	t.NodeMap[node.ID] = node
}

// InsertRootNodeAfter inserts a new root node after the specified node
func (t *TaskTree) InsertRootNodeAfter(newNode *TaskNode, afterNode *TaskNode) {
	newNode.Parent = nil

	for i, node := range t.RootNodes {
		if node == afterNode {
			// Insert after this position
			t.RootNodes = append(t.RootNodes[:i+1], append([]*TaskNode{newNode}, t.RootNodes[i+1:]...)...)
			break
		}
	}

	t.NodeMap[newNode.ID] = newNode
}

// UpdateBackwardCompatibilityIDs updates ParentID for all nodes
func (t *TaskTree) UpdateBackwardCompatibilityIDs() {
	// Update all nodes' ParentID
	for _, node := range t.NodeMap {
		// Update ParentId
		if node.Parent != nil {
			node.ParentID = node.Parent.ID
		} else {
			node.ParentID = ""
		}
	}
}

// RecordToAddOrUpdate represents a task record to add or update
type RecordToAddOrUpdate struct {
	ID       string     // Task unique identifier
	ParentID string     // Parent task ID
	PrevID   string     // Previous sibling task ID
	Status   TaskStatus // Task status
	Content  string     // Task content
}

type TaskTreeJson struct {
	Tasks []*TaskNode `json:"tasks"`
	Pause bool        `json:"pause"`
}

// AddOrUpdateTaskRecords adds or updates task records to the specified session_id's TaskTree
func AddOrUpdateTaskRecords(sessionId string, records []RecordToAddOrUpdate, isAdding bool) ([]definition.AddOrUpdateResult, []string) {
	var results []definition.AddOrUpdateResult

	// Filter out records with empty IDs
	originalCount := len(records)
	records = filterEmptyIDRecords(records)
	filteredCount := originalCount - len(records)

	// Add skipped results for empty ID records
	for i := 0; i < filteredCount; i++ {
		results = append(results, definition.AddOrUpdateResult{
			ID:      "",
			Success: false,
			Message: "task ID cannot be empty",
			Action:  "skipped",
		})
	}

	// Get or create task tree
	var taskTree *TaskTree
	if treeValue, exists := GetTaskTree(sessionId); exists {
		taskTree = treeValue
	} else {
		taskTree = createNewPlan(records)

		// Save to database
		if err := saveTaskTreeToDB(sessionId, taskTree); err != nil {
			log.Errorf("Failed to save new task tree to database: %v", err)
		}

		// Return results for new plan creation
		for _, record := range records {
			results = append(results, definition.AddOrUpdateResult{
				ID:      record.ID,
				Success: true,
				Action:  "added",
			})
		}

		// Fix parent status
		fixedIds := FixTaskTreeStatus(sessionId)

		return results, fixedIds
	}

	// Process each record for existing tree
	for _, record := range records {
		result := addOrUpdateSingleRecord(taskTree, record, isAdding)
		results = append(results, result)
	}

	// Update backward compatibility IDs
	taskTree.UpdateBackwardCompatibilityIDs()

	// Fix parent status
	fixedIds := FixTaskTreeStatus(sessionId)

	// Save to database after updates
	if err := saveTaskTreeToDB(sessionId, taskTree); err != nil {
		log.Errorf("Failed to save updated task tree to database: %v", err)
	}

	return results, fixedIds
}

// filterEmptyIDRecords filters out records with empty IDs
func filterEmptyIDRecords(recordsToAdd []RecordToAddOrUpdate) []RecordToAddOrUpdate {
	var filteredRecords []RecordToAddOrUpdate
	for _, record := range recordsToAdd {
		if record.ID != "" {
			filteredRecords = append(filteredRecords, record)
		}
	}
	return filteredRecords
}

// addOrUpdateSingleRecord processes a single record addition or update
func addOrUpdateSingleRecord(taskTree *TaskTree, record RecordToAddOrUpdate, isAdding bool) definition.AddOrUpdateResult {
	// Check if task already exists
	existingNode, exists := taskTree.NodeMap[record.ID]

	forbidden := false
	message := ""
	if !isAdding && !exists {
		// 如果是 update 模式，id 在现有列表中不存在，拒绝更新
		message = "Cannot update non-existent task, please add it first"
		forbidden = true
	}
	if isAdding && exists {
		// 如果是 add 模式，id 重复，拒绝更新
		message = "A task with the same ID already exists. Duplicate tasks cannot be added."
		forbidden = true
	}
	if forbidden {
		return definition.AddOrUpdateResult{
			ID:      record.ID,
			Success: false,
			Message: message,
			Action:  "skipped",
		}
	}

	if exists {
		// Update existing task
		if record.Content != "" {
			existingNode.Content = record.Content
		}
		if record.Status != "" {
			existingNode.UpdateTaskStatus(record.Status)
		}
		return definition.AddOrUpdateResult{
			ID:      record.ID,
			Success: true,
			Action:  "updated",
		}
	}

	// Create new task node
	newNode := &TaskNode{
		ID:      record.ID,
		Status:  record.Status,
		Content: record.Content,
	}

	// Handle status default value
	if newNode.Status == "" {
		newNode.Status = PENDING
	}

	// Set start time if the new task is being created with IN_PROGRESS status
	if newNode.Status == IN_PROGRESS {
		newNode.StartTime = time.Now().UnixMilli()
	}

	// Add to NodeMap first
	taskTree.NodeMap[record.ID] = newNode

	// Handle parent-child relationship
	if record.ParentID != "" {
		if parentNode, exists := taskTree.NodeMap[record.ParentID]; exists {
			// Add as child task
			if record.PrevID != "" {
				if prevNode, exists := taskTree.NodeMap[record.PrevID]; exists && prevNode.Parent == parentNode {
					// Insert after specific sibling
					parentNode.InsertChildAfter(newNode, prevNode)
				} else {
					// PrevID invalid, add as last child
					parentNode.AddChild(newNode)
				}
			} else {
				// No PrevID specified, add as last child
				parentNode.AddChild(newNode)
			}
		} else {
			// Parent doesn't exist, treat as root task
			addAsRootTask(taskTree, newNode, record.PrevID)
		}
	} else {
		// Add as root task
		addAsRootTask(taskTree, newNode, record.PrevID)
	}

	return definition.AddOrUpdateResult{
		ID:      record.ID,
		Success: true,
		Action:  "added",
	}
}

// UpdateTaskStatus updates task status and handles time tracking
func (n *TaskNode) UpdateTaskStatus(newStatus TaskStatus) {
	oldStatus := n.Status
	n.Status = newStatus

	// Handle time updates based on status changes
	now := time.Now().UnixMilli()

	// Set start time when task begins (from any status to IN_PROGRESS)
	if newStatus == IN_PROGRESS && oldStatus != IN_PROGRESS {
		n.StartTime = now
	}

	// Set end time when task completes, is cancelled, or has error
	if IsFinishStatus(newStatus) && !IsFinishStatus(oldStatus) {
		n.EndTime = now
		n.fixStartTime()
	}
}

// fixStartTime manually fills in the startTime for engineering purposes
func (n *TaskNode) fixStartTime() {
	// If startTime already exists, no need to process
	if n.StartTime != 0 {
		return
	}

	// Try to get endTime from the previous sibling node as startTime
	if n.Parent != nil {
		// Find the previous sibling by looking through parent's children
		for i, child := range n.Parent.Children {
			if child == n && i > 0 {
				prevSibling := n.Parent.Children[i-1]
				if prevSibling.EndTime != 0 {
					n.StartTime = prevSibling.EndTime + 1
					return
				}
				break
			}
		}
	}

	// If no previous sibling or it has no end time, try to get startTime from parent node
	if n.Parent != nil && n.Parent.StartTime != 0 {
		n.StartTime = n.Parent.StartTime
		return
	}

	// If none of the above, use endTime - 1000 ms as startTime
	if n.EndTime != 0 {
		n.StartTime = n.EndTime - 1000
		return
	}
	// Otherwise use current time as startTime
	n.StartTime = time.Now().UnixMilli()
}

// addAsRootTask adds a node as a root task
func addAsRootTask(taskTree *TaskTree, newNode *TaskNode, prevID string) {
	if prevID != "" {
		if prevNode, exists := taskTree.NodeMap[prevID]; exists && prevNode.IsRoot() {
			// Insert after specific root node
			taskTree.InsertRootNodeAfter(newNode, prevNode)
			return
		}
	}

	// Add as last root node
	taskTree.AddRootNode(newNode)
}

// createNewPlan creates a new TaskTree from the given records
func createNewPlan(recordsToAdd []RecordToAddOrUpdate) *TaskTree {
	taskTree := NewTaskTree()

	// Process records in multiple iterations to handle dependencies
	remainingRecords := make([]RecordToAddOrUpdate, len(recordsToAdd))
	copy(remainingRecords, recordsToAdd)

	maxIterations := len(recordsToAdd) + 1
	iteration := 0

	for len(remainingRecords) > 0 && iteration < maxIterations {
		var processedInThisIteration []int

		for i, record := range remainingRecords {
			// Handle status default value
			if record.Status == "" {
				record.Status = PENDING
			}

			// Create new node
			newNode := &TaskNode{
				ID:      record.ID,
				Status:  record.Status,
				Content: record.Content,
			}

			// Set start time if the task is being created with IN_PROGRESS status
			if newNode.Status == IN_PROGRESS {
				newNode.StartTime = time.Now().UnixMilli()
			}

			// Add to NodeMap
			taskTree.NodeMap[record.ID] = newNode

			canProcess := true

			// Check if parent exists (if ParentID is specified)
			if record.ParentID != "" {
				if parentNode, exists := taskTree.NodeMap[record.ParentID]; exists {
					// Add as child
					if record.PrevID != "" {
						if prevNode, exists := taskTree.NodeMap[record.PrevID]; exists && prevNode.Parent == parentNode {
							parentNode.InsertChildAfter(newNode, prevNode)
						} else {
							parentNode.AddChild(newNode)
						}
					} else {
						parentNode.AddChild(newNode)
					}
				} else {
					// Parent doesn't exist yet, skip this iteration
					canProcess = false
					delete(taskTree.NodeMap, record.ID) // Remove from map since we'll retry
				}
			} else {
				// Root task
				if record.PrevID != "" {
					if prevNode, exists := taskTree.NodeMap[record.PrevID]; exists && prevNode.IsRoot() {
						taskTree.InsertRootNodeAfter(newNode, prevNode)
					} else {
						taskTree.AddRootNode(newNode)
					}
				} else {
					taskTree.AddRootNode(newNode)
				}
			}

			if canProcess {
				processedInThisIteration = append(processedInThisIteration, i)
			}
		}

		// Remove processed records (in reverse order to maintain indices)
		for i := len(processedInThisIteration) - 1; i >= 0; i-- {
			idx := processedInThisIteration[i]
			remainingRecords = append(remainingRecords[:idx], remainingRecords[idx+1:]...)
		}

		iteration++

		// If no records were processed in this iteration, break to avoid infinite loop
		if len(processedInThisIteration) == 0 {
			break
		}
	}

	// Process any remaining records as root tasks (fallback)
	for _, record := range remainingRecords {
		if record.Status == "" {
			record.Status = PENDING
		}

		newNode := &TaskNode{
			ID:      record.ID,
			Status:  record.Status,
			Content: record.Content,
		}

		// Set start time if the task is being created with IN_PROGRESS status
		if newNode.Status == IN_PROGRESS {
			newNode.StartTime = time.Now().UnixMilli()
		}

		taskTree.NodeMap[record.ID] = newNode
		taskTree.AddRootNode(newNode)
	}

	// Update backward compatibility IDs
	taskTree.UpdateBackwardCompatibilityIDs()

	return taskTree
}

// GenerateDetailPlan generates a DetailPlan for the specified key, containing Markdown representation of tasks
func GenerateDetailPlan(key string) (*definition.DetailPlan, bool) {
	taskTree, exists := GetTaskTree(key)
	if !exists {
		return nil, false
	}

	var markdownContent string

	// Recursively build markdown content
	var buildMarkdown func(nodes []*TaskNode, level int)
	buildMarkdown = func(nodes []*TaskNode, level int) {
		for _, node := range nodes {
			// Select status symbol based on task status
			var statusSymbol string
			switch node.Status {
			case PENDING:
				statusSymbol = "[PENDING]"
			case IN_PROGRESS:
				statusSymbol = "[IN_PROGRESS]"
			case CANCELLED:
				statusSymbol = "[CANCELLED]"
			case COMPLETE:
				statusSymbol = "[COMPLETE]"
			case ERROR:
				statusSymbol = "[ERROR]"
			default:
				statusSymbol = "[PENDING]" // Default status is PENDING
			}

			// Generate indentation using dashes for hierarchy
			indent := ""
			for i := 0; i < level; i++ {
				indent += "-"
			}

			// Build current task line
			markdownContent += indent + "- " + statusSymbol + " ID:" + node.ID + " CONTENT:" + node.Content + "\n"

			// Process children recursively
			if len(node.Children) > 0 {
				buildMarkdown(node.Children, level+1)
			}
		}
	}

	// Generate markdown starting from root nodes
	buildMarkdown(taskTree.RootNodes, 0)

	// Convert to JSON
	jsonBytes, err := json.Marshal(TaskTreeJson{
		Tasks: taskTree.RootNodes,
		Pause: taskTree.Pause,
	})
	var taskTreeJson string
	if err != nil {
		taskTreeJson = "{\"tasks\":[]}" // Fallback to empty structure
	} else {
		taskTreeJson = string(jsonBytes)
	}

	return &definition.DetailPlan{
		MarkdownContent: markdownContent,
		TaskTreeJson:    taskTreeJson,
	}, true
}

// WritePlanToFile writes the plan content to .lingma/plan.txt in project root directory
func WritePlanToFile(ctx context.Context, markdown string, json string) {
	workspaceInfo, success := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if !success {
		return
	}
	workDir, success := workspaceInfo.GetWorkspaceFolder()
	if !success {
		return
	}

	// Create .lingma directory if it doesn't exist
	lingmaDir := filepath.Join(workDir, ".lingma")
	if err := os.MkdirAll(lingmaDir, 0755); err != nil {
		// If we can't create directory, skip file writing silently
		return
	}

	// Write content to plan.txt file
	planFile := filepath.Join(lingmaDir, "plan.txt")
	if err := os.WriteFile(planFile, []byte(markdown), 0644); err != nil {
		// If we can't write file, skip silently
		return
	}

	// Write JSON content to plan.json file
	jsonFile := filepath.Join(lingmaDir, "plan.json")
	if err := os.WriteFile(jsonFile, []byte(json), 0644); err != nil {
		// If we can't write file, skip silently
		return
	}
}

// saveTaskTreeToDB saves a TaskTree to database
func saveTaskTreeToDB(sessionId string, taskTree *TaskTree) error {
	// Generate JSON representation of the task tree
	taskTreeResponse := TaskTreeJson{
		Tasks: taskTree.RootNodes,
		Pause: taskTree.Pause,
	}

	jsonBytes, err := json.Marshal(taskTreeResponse)
	if err != nil {
		log.Errorf("Failed to marshal task tree to JSON: %v", err)
		return err
	}
	taskTreeJson := string(jsonBytes)

	// Create TaskTree record
	taskPlan := definition.TaskTree{
		SessionId:    sessionId,
		TaskTreeJSON: taskTreeJson,
		GmtCreate:    time.Now().UnixMilli(),
		GmtModified:  time.Now().UnixMilli(),
	}

	// Check if task tree already exists for this session
	existingPlan, err := database.TaskTreeTemplate.GetTaskTreeBySessionId(sessionId)
	if err != nil && !strings.Contains(err.Error(), "no rows in result set") {
		log.Errorf("Failed to check existing task tree: %v", err)
		return err
	}

	if existingPlan.SessionId != "" {
		// Update existing plan
		existingPlan.TaskTreeJSON = taskTreeJson
		existingPlan.GmtModified = time.Now().UnixMilli()
		err = database.TaskTreeTemplate.UpdateTaskTree(existingPlan)
		if err != nil {
			log.Errorf("Failed to update task tree: %v", err)
			return err
		}
		log.Debugf("Updated task tree for session %s in database", sessionId)
	} else {
		// Insert new plan
		err = database.TaskTreeTemplate.InsertTaskTree(taskPlan)
		if err != nil {
			log.Errorf("Failed to insert task tree: %v", err)
			return err
		}
		log.Debugf("Inserted new task tree for session %s in database", sessionId)
	}

	return nil
}

// loadTaskTreeFromDB loads a TaskTree from database
func loadTaskTreeFromDB(sessionId string) (*TaskTree, error) {
	taskPlan, err := database.TaskTreeTemplate.GetTaskTreeBySessionId(sessionId)
	if err != nil {
		if strings.Contains(err.Error(), "no rows in result set") {
			// No task tree found, return nil (not an error)
			return nil, nil
		}
		log.Errorf("Failed to load task tree for session %s: %v", sessionId, err)
		return nil, err
	}

	// Restore TaskTree from JSON
	var taskTree *TaskTree
	taskTree, err = RestoreFromJSON(taskPlan.TaskTreeJSON)
	if err != nil {
		log.Errorf("Failed to restore task tree from JSON for session %s: %v", sessionId, err)
		return nil, err
	}

	log.Debugf("Loaded task tree for session %s from database", sessionId)
	return taskTree, nil
}

// RestoreFromJSON restores TaskTree from JSON data
func RestoreFromJSON(jsonData string) (*TaskTree, error) {
	// Parse JSON data
	var taskTreeJson TaskTreeJson

	err := json.Unmarshal([]byte(jsonData), &taskTreeJson)
	if err != nil {
		return nil, err
	}

	// Create new TaskTree
	taskTree := NewTaskTree()

	// Set Pause
	taskTree.Pause = taskTreeJson.Pause

	// First pass: create all nodes and add to NodeMap
	var allNodes []*TaskNode
	var collectNodes func([]*TaskNode)
	collectNodes = func(nodes []*TaskNode) {
		for _, node := range nodes {
			// Create a new node instance to avoid reference issues
			newNode := &TaskNode{
				ID:                node.ID,
				ParentID:          node.ParentID,
				Status:            node.Status,
				Content:           node.Content,
				StartTime:         node.StartTime,
				EndTime:           node.EndTime,
				RelatedMessageIDs: make([]string, len(node.RelatedMessageIDs)),
			}

			// Copy RelatedMessageIDs
			copy(newNode.RelatedMessageIDs, node.RelatedMessageIDs)

			if newNode.Status == "" {
				newNode.Status = PENDING
			}

			allNodes = append(allNodes, newNode)
			taskTree.NodeMap[newNode.ID] = newNode

			// Recursively collect children
			if len(node.Children) > 0 {
				collectNodes(node.Children)
			}
		}
	}

	collectNodes(taskTreeJson.Tasks)

	// Second pass: build tree structure
	for _, node := range allNodes {
		if node.ParentID == "" {
			// Root node
			taskTree.RootNodes = append(taskTree.RootNodes, node)
		} else {
			// Child node - find parent and add as child
			if parentNode, exists := taskTree.NodeMap[node.ParentID]; exists {
				parentNode.AddChild(node)
			} else {
				// Parent not found, treat as root node
				taskTree.RootNodes = append(taskTree.RootNodes, node)
			}
		}
	}

	// Update backward compatibility IDs
	taskTree.UpdateBackwardCompatibilityIDs()

	return taskTree, nil
}

// TaskStatistics task Statistics
type TaskStatistics struct {
	Total      int `json:"total"`
	Pending    int `json:"pending"`
	InProgress int `json:"inProgress"`
	Complete   int `json:"complete"`
	Cancelled  int `json:"cancelled"`
	Error      int `json:"error"`
}

// GetTaskStatistics statistics of task status
func GetTaskStatistics(sessionId string, rootOnly bool) TaskStatistics {
	stats := TaskStatistics{}
	taskTree, ok := GetTaskTree(sessionId)
	if !ok {
		return stats
	}
	collectTaskStats(taskTree.RootNodes, &stats, rootOnly)
	stats.Total = stats.Pending + stats.InProgress + stats.Complete + stats.Cancelled + stats.Error
	return stats
}

// collectTaskStats recursively collect task statistics
func collectTaskStats(tasks []*TaskNode, stats *TaskStatistics, rootOnly bool) {
	if tasks == nil {
		return
	}
	for _, node := range tasks {
		switch node.Status {
		case PENDING:
			stats.Pending++
		case IN_PROGRESS:
			stats.InProgress++
		case COMPLETE:
			stats.Complete++
		case CANCELLED:
			stats.Cancelled++
		case ERROR:
			stats.Error++
		}
		// Only recursively collect child statistics if rootOnly is false
		if !rootOnly {
			collectTaskStats(node.Children, stats, rootOnly)
		}
	}
}

// HasAllTasksFinished whether all tasks have been completed
func HasAllTasksFinished(sessionId string) bool {
	taskTree, ok := GetTaskTree(sessionId)
	if !ok {
		return true
	}
	return hasAllFinish(taskTree.RootNodes)
}

// IsPause whether the task tree is running automatically
func IsPause(sessionId string) bool {
	taskTree, ok := GetTaskTree(sessionId)
	if !ok {
		return false
	}
	return taskTree.Pause
}

// SetPause set pause
func SetPause(sessionId string, pause bool) bool {
	taskTree, ok := GetTaskTree(sessionId)
	if !ok {
		return false
	}
	taskTree.Pause = pause
	// Save to database after updates
	if err := saveTaskTreeToDB(sessionId, taskTree); err != nil {
		log.Errorf("Failed to save updated task tree to database: %v", err)
		return false
	}
	log.Debugf("Set pause for session %s to %v", sessionId, pause)
	return true
}

// hasAllFinish whether there are PENDING or IN_PROGRESS tasks
func hasAllFinish(tasks []*TaskNode) bool {
	if tasks == nil || len(tasks) == 0 {
		return true
	}
	for _, node := range tasks {
		if !IsFinishStatus(node.Status) {
			return false
		}
		if !hasAllFinish(node.Children) {
			return false
		}
	}
	return true
}

// FixTaskTreeStatus performs comprehensive task status consistency checks and repairs
// Rules:
// 1. If any child task is IN_PROGRESS, parent must be IN_PROGRESS
// 2. If all child tasks are finished (IsFinishStatus) and parent is not finished, parent becomes COMPLETE
// Returns a map of fix types to modified node IDs
func FixTaskTreeStatus(sessionId string) []string {
	taskTree, ok := GetTaskTree(sessionId)
	if !ok {
		return nil
	}

	fixMap := make(map[string][]string)
	var allModifiedIDs []string

	// Perform tree status fixes recursively
	fixNodeAndChildren(taskTree.RootNodes, &fixMap, &allModifiedIDs)

	// Save to database if any modifications were made
	if len(allModifiedIDs) > 0 {
		if err := saveTaskTreeToDB(sessionId, taskTree); err != nil {
			log.Errorf("Failed to save updated task tree to database: %v", err)
			return nil
		}
		log.Debugf("Fixed task tree status for session %s, total fixes: %d", sessionId, len(allModifiedIDs))
	}

	return allModifiedIDs
}

// fixNodeAndChildren recursively fixes node status based on children status
func fixNodeAndChildren(nodes []*TaskNode, result *map[string][]string, allModifiedIDs *[]string) {
	for _, node := range nodes {
		// First, recursively fix children
		if len(node.Children) > 0 {
			fixNodeAndChildren(node.Children, result, allModifiedIDs)
		}

		// Then fix current node based on children status
		if len(node.Children) > 0 {
			hasInProgressChild := false
			allChildrenFinished := true

			// Check children status
			for _, child := range node.Children {
				if child.Status == IN_PROGRESS {
					hasInProgressChild = true
					allChildrenFinished = false
					break
				}
				if !IsFinishStatus(child.Status) {
					allChildrenFinished = false
				}
			}

			// Rule 1: If any child is IN_PROGRESS, parent must be IN_PROGRESS
			if hasInProgressChild && node.Status != IN_PROGRESS {
				node.UpdateTaskStatus(IN_PROGRESS)
				if (*result)["in_progress_fixes"] == nil {
					(*result)["in_progress_fixes"] = []string{}
				}
				(*result)["in_progress_fixes"] = append((*result)["in_progress_fixes"], node.ID)
				*allModifiedIDs = append(*allModifiedIDs, node.ID)
				log.Debugf("Updated node %s to IN_PROGRESS due to IN_PROGRESS child", node.ID)
			}

			// Rule 2: If all children finished and parent not finished, parent becomes COMPLETE
			if allChildrenFinished && !IsFinishStatus(node.Status) {
				node.UpdateTaskStatus(COMPLETE)
				if (*result)["complete_fixes"] == nil {
					(*result)["complete_fixes"] = []string{}
				}
				(*result)["complete_fixes"] = append((*result)["complete_fixes"], node.ID)
				*allModifiedIDs = append(*allModifiedIDs, node.ID)
				log.Debugf("Updated node %s to COMPLETE as all children are finished", node.ID)
			}
		}
	}
}
