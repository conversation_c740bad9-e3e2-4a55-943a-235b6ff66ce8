package extension

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"errors"
	"fmt"
)

func LoadComboBoxItems(ctx context.Context, identifier string, request definition.GetComboBoxItemsRequest, ideSdk definition.SDKTool) (definition.GetComboBoxItemsResponse, error) {
	// 判断上下文是否为系统上下文，系统上下文调用内置执行引擎执行上下文获取逻辑
	contextProviderExecutor := GetSystemContextProviderExecutor(identifier)
	if contextProviderExecutor != nil {
		getComboBoxItemsResponse, err := contextProviderExecutor.LoadComboBoxItems(LoadComboboxItemsApiRequest{Identifier: identifier, GetComboBoxItemsRequest: request, Sdk: ideSdk})
		if err != nil {
			log.Errorf("Error execute load comboBoxItems: %s", err.Error())
			return definition.GetComboBoxItemsResponse{}, err
		}
		return getComboBoxItemsResponse, nil
	}

	// 为用户自定义上下文则调用node执行引擎执行查询逻辑
	if ApiExecutor == nil {
		return definition.GetComboBoxItemsResponse{}, errors.New("ApiExecutor is not ready, can not execute load comboBoxItems")
	}
	if contextProviderWrapper, exists := ApiExecutor.apiRepo.ContextProviders[identifier]; exists {
		return contextProviderWrapper.loadComboBoxItems(ctx, ApiExecutor.ExtensionCoreService, request, ideSdk)
	}
	return definition.GetComboBoxItemsResponse{}, errors.New(fmt.Sprintf("can not found useDefined contextProvider:%s", identifier))
}

func InvokeContextProvider(ctx context.Context, identifier string, request definition.GetContextRequest, ideSdk definition.SDKTool, payload definition.ContextPayload) (definition.GetContextResponse, error) {
	// 判断上下文是否为系统上下文，系统上下文调用内置执行引擎执行上下文获取逻辑
	contextProviderExecutor := GetSystemContextProviderExecutor(identifier)
	if contextProviderExecutor != nil {
		getContextResponse, err := contextProviderExecutor.GetContext(GetContextResponseApiRequest{Identifier: identifier, ContextProviderRequest: request, Sdk: ideSdk, Payload: payload, ctx: ctx})
		if err != nil {
			log.Errorf("Error executing context provider: %s", err.Error())
			return definition.GetContextResponse{}, err
		}
		return getContextResponse, nil
	}

	// 为用户自定义上下文则调用node执行引擎执行查询逻辑
	if ApiExecutor == nil {
		return definition.GetContextResponse{}, errors.New("ApiExecutor is not ready, can not execute context")
	}
	if contextProviderWrapper, exists := ApiExecutor.apiRepo.ContextProviders[identifier]; exists {
		return contextProviderWrapper.invoke(ctx, ApiExecutor.ExtensionCoreService, request, ideSdk)
	}
	return definition.GetContextResponse{}, errors.New(fmt.Sprintf("can not found useDefined contextProvider:%s", identifier))
}
