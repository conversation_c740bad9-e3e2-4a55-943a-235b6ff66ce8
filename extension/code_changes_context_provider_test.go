package extension

import (
	"cosy/definition"
	"github.com/stretchr/testify/assert"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"testing"
)

const providerIdentifier = "test_identifier"

// test get Diff success with file add,modified,deleted,renamed
func Test_GetCodeChangesContextProvider_getContext(t *testing.T) {
	testContent := "testContent"
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "Test_GetCodeChangesContextProvider_getContext")
	if err != nil {
		t.Fatalf("Failed to create temporary directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建1.txt和2.txt文件
	files := []string{"1.txt", "2.txt", "3.txt"}
	for _, file := range files {
		filePath := filepath.Join(tempDir, file)
		if err := os.WriteFile(filePath, []byte(testContent+file), 0644); err != nil {
			t.Fatalf("Failed to create file %s: %v", file, err)
		}
	}

	// 初始化Git仓库
	cmd := exec.Command("git", "-c",
		"init.defaultBranch=master",
		"init", ".")
	cmd.Dir = tempDir
	if output, err := cmd.CombinedOutput(); err != nil {
		t.Fatalf("Failed to initialize Git repository: %v\nOutput: %s", err, output)
	}

	// 添加文件到暂存区
	cmd = exec.Command("git", "add", ".")
	cmd.Dir = tempDir
	if output, err := cmd.CombinedOutput(); err != nil {
		t.Fatalf("Failed to add files to staging area: %v\nOutput: %s", err, output)
	}

	// 进行初始提交
	cmd = exec.Command("git", "commit", "-m", "Initial commit")
	cmd.Dir = tempDir
	if output, err := cmd.CombinedOutput(); err != nil {
		t.Fatalf("Failed to make initial commit: %v\nOutput: %s", err, output)
	}

	// 修改1.txt文件
	filePath := filepath.Join(tempDir, "1.txt")
	if err := os.WriteFile(filePath, []byte(testContent+" Modified"), 0644); err != nil {
		t.Fatalf("Failed to modify file 1.txt: %v", err)
	}

	// 移除2.txt文件
	filePath = filepath.Join(tempDir, "2.txt")
	if err := os.Remove(filePath); err != nil {
		t.Fatalf("Failed to remove file 2.txt: %v", err)
	}

	// rename 3.txt -> 4.txt
	oldFilePath := filepath.Join(tempDir, "3.txt")
	newFilePath := filepath.Join(tempDir, "4.txt")
	if err := os.Rename(oldFilePath, newFilePath); err != nil {
		t.Fatalf("Failed to rename 3.txt to 4.txt: %v", err)
	}

	// 新增5.txt文件
	filePath = filepath.Join(tempDir, "5.txt")
	if err := os.WriteFile(filePath, []byte(testContent+"new"), 0644); err != nil {
		t.Fatalf("Failed to create file 5.txt: %v", err)
	}

	// 添加修改后的文件到暂存区
	cmd = exec.Command("git", "add", "4.txt", "5.txt")
	cmd.Dir = tempDir
	if output, err := cmd.CombinedOutput(); err != nil {
		t.Fatalf("Failed to add 5.txt and 4.txt to staging area: %v\nOutput: %s", err, output)
	}

	workspaceDir := tempDir
	codeChangesContextProvider := &CodeChangesContextProvider{}

	diffContext, err := codeChangesContextProvider.GetContext(GetContextResponseApiRequest{
		Identifier: providerIdentifier,
		Sdk: definition.SDKTool{
			WorkspaceDir: workspaceDir,
		},
		ContextProviderRequest: definition.GetContextRequest{},
	})
	if err != nil {
		t.Fatal(err)
	}

	assert.Equal(t, 1, len(diffContext.ContextItems))
	diffText := diffContext.ContextItems[0].Value.(string)
	assert.True(t, strings.Contains(diffText, "1.txt\n@@ -1 +1"))
	assert.True(t, strings.Contains(diffText, "deleted file mode 100644\nindex b215a0b..0000000\n--- a/2.txt"))
	assert.True(t, strings.Contains(diffText, "rename from 3.txt\nrename to 4.txt"))
	assert.True(t, strings.Contains(diffText, "+++ b/5.txt\n@@ -0,0 +1 @@\n+testContentnew"))
	assert.True(t, strings.Contains(diffText, "+testContent Modified"))
}

// test get Diff success with largeDiff
func Test_GetCodeChangesContextProvider_getContext_largeDiff(t *testing.T) {
	testContent := "This is a test file.\n"
	largefileSize := 1024 * 1024 // 1 MB
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "Test_GetCodeChangesContextProvider_getContext_largeDiff")
	if err != nil {
		t.Fatalf("Failed to create temporary directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建1.txt和2.txt文件
	files := []string{"1.txt", "2.txt"}
	for _, file := range files {
		filePath := filepath.Join(tempDir, file)
		if err := os.WriteFile(filePath, []byte(testContent), 0644); err != nil {
			t.Fatalf("Failed to create file %s: %v", file, err)
		}
	}

	// 初始化Git仓库
	cmd := exec.Command("git", "-c",
		"init.defaultBranch=master",
		"init", ".")
	cmd.Dir = tempDir
	if output, err := cmd.CombinedOutput(); err != nil {
		t.Fatalf("Failed to initialize Git repository: %v\nOutput: %s", err, output)
	}

	// 添加文件到暂存区
	cmd = exec.Command("git", "add", ".")
	cmd.Dir = tempDir
	if output, err := cmd.CombinedOutput(); err != nil {
		t.Fatalf("Failed to add files to staging area: %v\nOutput: %s", err, output)
	}

	// 进行初始提交
	cmd = exec.Command("git", "commit", "-m", "Initial commit")
	cmd.Dir = tempDir
	if output, err := cmd.CombinedOutput(); err != nil {
		t.Fatalf("Failed to make initial commit: %v\nOutput: %s", err, output)
	}

	// 修改1.txt文件，增加1MB的内容
	filePath := filepath.Join(tempDir, "1.txt")
	oneMBContent := make([]byte, largefileSize)
	for i := range oneMBContent {
		oneMBContent[i] = 'A' // 填充1MB的'A'字符
	}
	newContent := append([]byte(testContent), oneMBContent...)
	if err := os.WriteFile(filePath, newContent, 0644); err != nil {
		t.Fatalf("Failed to modify file 1.txt: %v", err)
	}

	workspaceDir := tempDir
	codeChangesContextProvider := &CodeChangesContextProvider{}

	diffContext, err := codeChangesContextProvider.GetContext(GetContextResponseApiRequest{
		Identifier: providerIdentifier,
		Sdk: definition.SDKTool{
			WorkspaceDir: workspaceDir,
		},
		ContextProviderRequest: definition.GetContextRequest{},
	})
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 50*1024, len(diffContext.ContextItems[0].Value.(string)))
}

// test get diff success when git repo not has HEAD
func Test_GetCodeChangesContextProvider_getContext_bad_revision_head(t *testing.T) {
	testContent := "This is a test file.\n"
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "Test_GetCodeChangesContextProvider_getContext_bad_revision_head")
	if err != nil {
		t.Fatalf("Failed to create temporary directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建1.txt文件
	filePath := filepath.Join(tempDir, "1.txt")
	if err := os.WriteFile(filePath, []byte(testContent), 0644); err != nil {
		t.Fatalf("Failed to create file 1.txt: %v", err)
	}

	// 初始化Git仓库
	cmd := exec.Command("git", "-c",
		"init.defaultBranch=master",
		"init", ".")
	cmd.Dir = tempDir
	if output, err := cmd.CombinedOutput(); err != nil {
		t.Fatalf("Failed to initialize Git repository: %v\nOutput: %s", err, output)
	}

	// 添加1.txt文件到暂存区
	cmd = exec.Command("git", "add", "1.txt")
	cmd.Dir = tempDir
	if output, err := cmd.CombinedOutput(); err != nil {
		t.Fatalf("Failed to add 1.txt to staging area: %v\nOutput: %s", err, output)
	}

	workspaceDir := tempDir
	codeChangesContextProvider := &CodeChangesContextProvider{}

	diffContext, err := codeChangesContextProvider.GetContext(GetContextResponseApiRequest{
		Identifier: providerIdentifier,
		Sdk: definition.SDKTool{
			WorkspaceDir: workspaceDir,
		},
		ContextProviderRequest: definition.GetContextRequest{},
	})

	if err != nil {
		t.Fatal(err)
	}

	assert.True(t, strings.Contains(diffContext.ContextItems[0].Value.(string), "@@ -0,0 +1 @@\n+This is a test file."))
}

// test get Diff with empty info in not git repo
func Test_GetCodeChangesContextProvider_getContext_not_gitRepo(t *testing.T) {
	testContent := "This is a test file.\n"
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "Test_GetCodeChangesContextProvider_getContext_not_gitRepo")
	if err != nil {
		t.Fatalf("Failed to create temporary directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建1.txt文件
	filePath := filepath.Join(tempDir, "1.txt")
	if err := os.WriteFile(filePath, []byte(testContent), 0644); err != nil {
		t.Fatalf("Failed to create file 1.txt: %v", err)
	}

	workspaceDir := tempDir
	codeChangesContextProvider := &CodeChangesContextProvider{}

	diffContext, err := codeChangesContextProvider.GetContext(GetContextResponseApiRequest{
		Identifier: providerIdentifier,
		Sdk: definition.SDKTool{
			WorkspaceDir: workspaceDir,
		},
		ContextProviderRequest: definition.GetContextRequest{},
	})

	if err != nil {
		t.Fatal(err)
	}

	assert.Equal(t, 0, len(diffContext.ContextItems))
}

// test get Diff success with LF and CRLF file
func Test_GetCodeChangesContextProvider_getContext_withLfAndCrlfFile(t *testing.T) {
	testContentLF := "This is a test file with LF line endings.\n"
	testContentCRLF := "This is a test file with CRLF line endings.\r\n"

	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "testgit")
	if err != nil {
		t.Fatalf("Failed to create temporary directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建1.txt文件，使用LF换行符
	filePath := filepath.Join(tempDir, "1.txt")
	if err := os.WriteFile(filePath, []byte(testContentLF), 0644); err != nil {
		t.Fatalf("Failed to create file 1.txt: %v", err)
	}

	// 初始化Git仓库
	cmd := exec.Command("git", "-c",
		"init.defaultBranch=master",
		"init", ".")
	cmd.Dir = tempDir
	if output, err := cmd.CombinedOutput(); err != nil {
		t.Fatalf("Failed to initialize Git repository: %v\nOutput: %s", err, output)
	}

	// 添加1.txt文件到暂存区
	cmd = exec.Command("git", "add", "1.txt")
	cmd.Dir = tempDir
	if output, err := cmd.CombinedOutput(); err != nil {
		t.Fatalf("Failed to add 1.txt to staging area: %v\nOutput: %s", err, output)
	}

	// 进行初始提交
	cmd = exec.Command("git", "commit", "-m", "Initial commit with LF line endings")
	cmd.Dir = tempDir
	if output, err := cmd.CombinedOutput(); err != nil {
		t.Fatalf("Failed to make initial commit: %v\nOutput: %s", err, output)
	}

	// 修改1.txt文件，使用CRLF换行符
	if err := os.WriteFile(filePath, []byte(testContentCRLF), 0644); err != nil {
		t.Fatalf("Failed to modify file 1.txt: %v", err)
	}

	workspaceDir := tempDir
	codeChangesContextProvider := &CodeChangesContextProvider{}

	diffContext, err := codeChangesContextProvider.GetContext(GetContextResponseApiRequest{
		Identifier: providerIdentifier,
		Sdk: definition.SDKTool{
			WorkspaceDir: workspaceDir,
		},
		ContextProviderRequest: definition.GetContextRequest{},
	})

	if err != nil {
		t.Fatal(err)
	}

	assert.True(t, !strings.Contains(diffContext.ContextItems[0].Value.(string), "Warning"))
	assert.True(t, strings.Contains(diffContext.ContextItems[0].Value.(string), "+This is a test file with CRLF line endings"))
}
