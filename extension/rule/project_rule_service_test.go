package rule

import (
	cosyError "cosy/errors"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestAddProjectRule(t *testing.T) {
	// 创建临时工作目录
	tempDir := t.TempDir()

	tests := []struct {
		name          string
		workspacePath string
		params        *AddProjectRuleParams
		wantErr       bool
		expectedErr   string
	}{
		{
			name:          "成功添加手动规则",
			workspacePath: tempDir,
			params: &AddProjectRuleParams{
				Name:    "test-rule",
				Trigger: ManualRule,
				Content: "这是一个测试规则",
			},
			wantErr: false,
		},
		{
			name:          "成功添加glob规则",
			workspacePath: tempDir,
			params: &AddProjectRuleParams{
				Name:    "glob-rule",
				Trigger: GlobRule,
				Glob:    "*.go",
				Content: "Go文件规则",
			},
			wantErr: false,
		},
		{
			name:          "成功添加模型决策规则",
			workspacePath: tempDir,
			params: &AddProjectRuleParams{
				Name:        "model-rule",
				Trigger:     ModelDecisionRule,
				Description: "模型决策规则描述",
				Content:     "模型决策规则内容",
			},
			wantErr: false,
		},
		{
			name:          "成功添加始终开启规则",
			workspacePath: tempDir,
			params: &AddProjectRuleParams{
				Name:    "always-rule",
				Trigger: AlwaysOnRule,
				Content: "始终开启的规则",
			},
			wantErr: false,
		},
		{
			name:          "参数为空",
			workspacePath: tempDir,
			params:        nil,
			wantErr:       true,
			expectedErr:   "Parameters cannot be empty",
		},
		{
			name:          "规则名称为空",
			workspacePath: tempDir,
			params: &AddProjectRuleParams{
				Name:    "",
				Trigger: ManualRule,
				Content: "内容",
			},
			wantErr:     true,
			expectedErr: "File name cannot be empty",
		},
		{
			name:          "触发类型为空",
			workspacePath: tempDir,
			params: &AddProjectRuleParams{
				Name:    "test",
				Trigger: "",
				Content: "内容",
			},
			wantErr:     true,
			expectedErr: "Trigger type cannot be empty",
		},
		{
			name:          "无效的触发类型",
			workspacePath: tempDir,
			params: &AddProjectRuleParams{
				Name:    "test",
				Trigger: "invalid_trigger",
				Content: "内容",
			},
			wantErr:     true,
			expectedErr: "Invalid trigger type: invalid_trigger",
		},
		{
			name:          "模型决策规则缺少描述",
			workspacePath: tempDir,
			params: &AddProjectRuleParams{
				Name:        "test",
				Trigger:     ModelDecisionRule,
				Description: "",
				Content:     "内容",
			},
			wantErr:     true,
			expectedErr: "Description cannot be empty when trigger type is model_decision",
		},
		{
			name:          "glob规则缺少glob表达式",
			workspacePath: tempDir,
			params: &AddProjectRuleParams{
				Name:    "test",
				Trigger: GlobRule,
				Glob:    "",
				Content: "内容",
			},
			wantErr:     true,
			expectedErr: "Glob cannot be empty when trigger type is glob",
		},
		{
			name:          "规则内容为空",
			workspacePath: tempDir,
			params: &AddProjectRuleParams{
				Name:    "test",
				Trigger: ManualRule,
				Content: "",
			},
			wantErr:     true,
			expectedErr: "Rule content cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rule, err := AddProjectRule(tt.workspacePath, tt.params)

			if tt.wantErr {
				if err == nil {
					t.Errorf("AddProjectRule() expected error but got none")
					return
				}

				// 检查错误类型和消息
				if cosyErr, ok := cosyError.IsUnifiedError(err); ok {
					if !strings.Contains(cosyErr.Message, tt.expectedErr) {
						t.Errorf("AddProjectRule() expected error message containing '%s', got '%s'", tt.expectedErr, cosyErr.Message)
					}
				} else {
					t.Errorf("AddProjectRule() expected unified error but got %T: %v", err, err)
				}
			} else {
				if err != nil {
					t.Errorf("AddProjectRule() unexpected error: %v", err)
					return
				}

				if rule == nil {
					t.Errorf("AddProjectRule() returned nil rule")
					return
				}

				// 验证规则内容
				if rule.Name != tt.params.Name {
					t.Errorf("AddProjectRule() rule name = %v, want %v", rule.Name, tt.params.Name)
				}
				if rule.Trigger != tt.params.Trigger {
					t.Errorf("AddProjectRule() rule trigger = %v, want %v", rule.Trigger, tt.params.Trigger)
				}
				if rule.Content != tt.params.Content {
					t.Errorf("AddProjectRule() rule content = %v, want %v", rule.Content, tt.params.Content)
				}
				if rule.Description != tt.params.Description {
					t.Errorf("AddProjectRule() rule description = %v, want %v", rule.Description, tt.params.Description)
				}
				if rule.Glob != tt.params.Glob {
					t.Errorf("AddProjectRule() rule glob = %v, want %v", rule.Glob, tt.params.Glob)
				}

				// 验证文件是否被创建
				expectedFilePath := filepath.Join(GetRuleFileDir(tt.workspacePath), tt.params.Name+".md")
				if !fileExists(expectedFilePath) {
					t.Errorf("AddProjectRule() expected file to be created at %s", expectedFilePath)
				}
			}
		})
	}
}

func TestAddProjectRule_FileExists(t *testing.T) {
	tempDir := t.TempDir()

	// 先创建一个规则
	params := &AddProjectRuleParams{
		Name:    "duplicate-rule",
		Trigger: ManualRule,
		Content: "第一个规则",
	}

	_, err := AddProjectRule(tempDir, params)
	if err != nil {
		t.Fatalf("Failed to create first rule: %v", err)
	}

	// 尝试创建同名规则
	_, err = AddProjectRule(tempDir, params)
	if err == nil {
		t.Errorf("AddProjectRule() expected error for duplicate file but got none")
		return
	}

	if cosyErr, ok := cosyError.IsUnifiedError(err); ok {
		if cosyErr.Code != cosyError.FileExists {
			t.Errorf("AddProjectRule() expected FileExists error, got %d", cosyErr.Code)
		}
	} else {
		t.Errorf("AddProjectRule() expected unified error but got %T: %v", err, err)
	}
}

func TestEditProjectRule(t *testing.T) {
	tempDir := t.TempDir()

	// 先创建一个规则
	originalParams := &AddProjectRuleParams{
		Name:    "edit-test-rule",
		Trigger: ManualRule,
		Content: "原始内容",
	}

	_, err := AddProjectRule(tempDir, originalParams)
	if err != nil {
		t.Fatalf("Failed to create rule for editing: %v", err)
	}

	// 创建另一个规则文件，用于测试重命名冲突
	duplicateParams := &AddProjectRuleParams{
		Name:    "duplicate-rule",
		Trigger: ManualRule,
		Content: "重复的规则",
	}
	_, err = AddProjectRule(tempDir, duplicateParams)
	if err != nil {
		t.Fatalf("Failed to create duplicate rule for testing: %v", err)
	}

	tests := []struct {
		name          string
		workspacePath string
		params        *EditProjectRuleParams
		wantErr       bool
		expectedErr   string
	}{
		{
			name:          "成功编辑规则",
			workspacePath: tempDir,
			params: &EditProjectRuleParams{
				Name:        "edit-test-rule",
				Trigger:     GlobRule,
				Glob:        "*.go",
				Description: "新的描述",
				Content:     "新的内容",
			},
			wantErr: false,
		},
		{
			name:          "参数为空",
			workspacePath: tempDir,
			params:        nil,
			wantErr:       true,
			expectedErr:   "Parameters cannot be empty",
		},
		{
			name:          "规则名称为空",
			workspacePath: tempDir,
			params: &EditProjectRuleParams{
				Name:    "",
				Trigger: ManualRule,
			},
			wantErr:     true,
			expectedErr: "Rule name cannot be empty",
		},
		{
			name:          "触发类型为空",
			workspacePath: tempDir,
			params: &EditProjectRuleParams{
				Name:    "edit-test-rule",
				Trigger: "",
			},
			wantErr:     true,
			expectedErr: "Trigger type cannot be empty",
		},
		{
			name:          "无效的触发类型",
			workspacePath: tempDir,
			params: &EditProjectRuleParams{
				Name:    "edit-test-rule",
				Trigger: "invalid_trigger",
			},
			wantErr:     true,
			expectedErr: "Invalid trigger type: invalid_trigger",
		},
		{
			name:          "模型决策规则缺少描述",
			workspacePath: tempDir,
			params: &EditProjectRuleParams{
				Name:        "edit-test-rule",
				Trigger:     ModelDecisionRule,
				Description: "",
			},
			wantErr:     true,
			expectedErr: "Description cannot be empty when trigger type is model_decision",
		},
		{
			name:          "glob规则缺少glob表达式",
			workspacePath: tempDir,
			params: &EditProjectRuleParams{
				Name:    "edit-test-rule",
				Trigger: GlobRule,
				Glob:    "",
			},
			wantErr:     true,
			expectedErr: "Glob cannot be empty when trigger type is glob",
		},
		{
			name:          "规则文件不存在",
			workspacePath: tempDir,
			params: &EditProjectRuleParams{
				Name:    "non-existent-rule",
				Trigger: ManualRule,
			},
			wantErr:     true,
			expectedErr: "Rule file does not exist: non-existent-rule",
		},
		{
			name:          "成功重命名规则",
			workspacePath: tempDir,
			params: &EditProjectRuleParams{
				Name:    "edit-test-rule",
				NewName: "renamed-test-rule",
				Trigger: ManualRule,
				Content: "重命名后的内容",
			},
			wantErr: false,
		},
		{
			name:          "重命名时新名称已存在",
			workspacePath: tempDir,
			params: &EditProjectRuleParams{
				Name:    "edit-test-rule",
				NewName: "duplicate-rule",
				Trigger: ManualRule,
			},
			wantErr:     true,
			expectedErr: "Rule file with name 'duplicate-rule' already exists",
		},
		{
			name:          "NewName与Name相同，不进行重命名",
			workspacePath: tempDir,
			params: &EditProjectRuleParams{
				Name:    "edit-test-rule",
				NewName: "edit-test-rule",
				Trigger: ManualRule,
				Content: "内容更新但不重命名",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rule, err := EditProjectRule(tt.workspacePath, tt.params)

			if tt.wantErr {
				if err == nil {
					t.Errorf("EditProjectRule() expected error but got none")
					return
				}

				if cosyErr, ok := cosyError.IsUnifiedError(err); ok {
					if !strings.Contains(cosyErr.Message, tt.expectedErr) {
						t.Errorf("EditProjectRule() expected error message containing '%s', got '%s'", tt.expectedErr, cosyErr.Message)
					}
				} else {
					t.Errorf("EditProjectRule() expected unified error but got %T: %v", err, err)
				}
			} else {
				if err != nil {
					t.Errorf("EditProjectRule() unexpected error: %v", err)
					return
				}

				if rule == nil {
					t.Errorf("EditProjectRule() returned nil rule")
					return
				}

				// 验证规则内容是否被正确更新
				if rule.Trigger != tt.params.Trigger {
					t.Errorf("EditProjectRule() rule trigger = %v, want %v", rule.Trigger, tt.params.Trigger)
				}
				if rule.Description != tt.params.Description {
					t.Errorf("EditProjectRule() rule description = %v, want %v", rule.Description, tt.params.Description)
				}
				if rule.Glob != tt.params.Glob {
					t.Errorf("EditProjectRule() rule glob = %v, want %v", rule.Glob, tt.params.Glob)
				}
				if tt.params.Content != "" && rule.Content != tt.params.Content {
					t.Errorf("EditProjectRule() rule content = %v, want %v", rule.Content, tt.params.Content)
				}

				// 验证重命名功能
				if tt.params.NewName != "" && tt.params.NewName != tt.params.Name {
					// 如果进行了重命名，验证新名称
					if rule.Name != tt.params.NewName {
						t.Errorf("EditProjectRule() rule name after rename = %v, want %v", rule.Name, tt.params.NewName)
					}
					// 验证新文件路径
					expectedNewPath := filepath.Join(tempDir, ".lingma/rules", tt.params.NewName+".md")
					if rule.FilePath != expectedNewPath {
						t.Errorf("EditProjectRule() rule file path after rename = %v, want %v", rule.FilePath, expectedNewPath)
					}
					// 验证旧文件不存在
					oldPath := filepath.Join(tempDir, ".lingma/rules", tt.params.Name+".md")
					if _, err := os.Stat(oldPath); err == nil {
						t.Errorf("EditProjectRule() old file still exists after rename: %s", oldPath)
					}
					// 验证新文件存在
					if _, err := os.Stat(rule.FilePath); err != nil {
						t.Errorf("EditProjectRule() new file does not exist after rename: %s", rule.FilePath)
					}
				} else {
					// 如果没有重命名，验证名称保持不变
					if rule.Name != tt.params.Name {
						t.Errorf("EditProjectRule() rule name = %v, want %v", rule.Name, tt.params.Name)
					}
				}
			}
		})
	}
}

func TestDeleteProjectRule(t *testing.T) {
	tempDir := t.TempDir()

	// 先创建一个规则
	params := &AddProjectRuleParams{
		Name:    "delete-test-rule",
		Trigger: ManualRule,
		Content: "要删除的规则",
	}

	_, err := AddProjectRule(tempDir, params)
	if err != nil {
		t.Fatalf("Failed to create rule for deletion: %v", err)
	}

	tests := []struct {
		name          string
		workspacePath string
		params        *DeleteProjectRuleParams
		wantErr       bool
		expectedErr   string
	}{
		{
			name:          "成功删除规则",
			workspacePath: tempDir,
			params: &DeleteProjectRuleParams{
				Name: "delete-test-rule",
			},
			wantErr: false,
		},
		{
			name:          "规则名称为空",
			workspacePath: tempDir,
			params: &DeleteProjectRuleParams{
				Name: "",
			},
			wantErr:     true,
			expectedErr: "Rule name cannot be empty",
		},
		{
			name:          "规则文件不存在",
			workspacePath: tempDir,
			params: &DeleteProjectRuleParams{
				Name: "non-existent-rule",
			},
			wantErr:     true,
			expectedErr: "Rule file does not exist",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := DeleteProjectRule(tt.workspacePath, tt.params)

			if tt.wantErr {
				if err == nil {
					t.Errorf("DeleteProjectRule() expected error but got none")
					return
				}

				if cosyErr, ok := cosyError.IsUnifiedError(err); ok {
					if !strings.Contains(cosyErr.Message, tt.expectedErr) {
						t.Errorf("DeleteProjectRule() expected error message containing '%s', got '%s'", tt.expectedErr, cosyErr.Message)
					}
				} else {
					t.Errorf("DeleteProjectRule() expected unified error but got %T: %v", err, err)
				}
			} else {
				if err != nil {
					t.Errorf("DeleteProjectRule() unexpected error: %v", err)
					return
				}

				// 验证文件是否被删除
				expectedFilePath := filepath.Join(GetRuleFileDir(tt.workspacePath), tt.params.Name+".md")
				if fileExists(expectedFilePath) {
					t.Errorf("DeleteProjectRule() expected file to be deleted at %s", expectedFilePath)
				}
			}
		})
	}
}

func TestGetProjectRuleByName(t *testing.T) {
	tempDir := t.TempDir()

	// 先创建一个规则
	params := &AddProjectRuleParams{
		Name:        "get-test-rule",
		Trigger:     ModelDecisionRule,
		Description: "测试规则描述",
		Content:     "测试规则内容",
	}

	_, err := AddProjectRule(tempDir, params)
	if err != nil {
		t.Fatalf("Failed to create rule for getting: %v", err)
	}

	tests := []struct {
		name          string
		workspacePath string
		params        *QueryProjectRuleParams
		wantErr       bool
		expectedErr   string
	}{
		{
			name:          "成功获取规则",
			workspacePath: tempDir,
			params: &QueryProjectRuleParams{
				Name: "get-test-rule",
			},
			wantErr: false,
		},
		{
			name:          "规则名称为空",
			workspacePath: tempDir,
			params: &QueryProjectRuleParams{
				Name: "",
			},
			wantErr:     true,
			expectedErr: "Rule name cannot be empty",
		},
		{
			name:          "规则文件不存在",
			workspacePath: tempDir,
			params: &QueryProjectRuleParams{
				Name: "non-existent-rule",
			},
			wantErr:     true,
			expectedErr: "Rule file does not exist",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rule, err := GetProjectRuleByName(tt.workspacePath, tt.params)

			if tt.wantErr {
				if err == nil {
					t.Errorf("GetProjectRuleByName() expected error but got none")
					return
				}

				if cosyErr, ok := cosyError.IsUnifiedError(err); ok {
					if !strings.Contains(cosyErr.Message, tt.expectedErr) {
						t.Errorf("GetProjectRuleByName() expected error message containing '%s', got '%s'", tt.expectedErr, cosyErr.Message)
					}
				} else {
					t.Errorf("GetProjectRuleByName() expected unified error but got %T: %v", err, err)
				}
			} else {
				if err != nil {
					t.Errorf("GetProjectRuleByName() unexpected error: %v", err)
					return
				}

				if rule == nil {
					t.Errorf("GetProjectRuleByName() returned nil rule")
					return
				}

				// 验证规则内容
				if rule.Name != tt.params.Name {
					t.Errorf("GetProjectRuleByName() rule name = %v, want %v", rule.Name, tt.params.Name)
				}
				if rule.Trigger != params.Trigger {
					t.Errorf("GetProjectRuleByName() rule trigger = %v, want %v", rule.Trigger, params.Trigger)
				}
				if rule.Content != params.Content {
					t.Errorf("GetProjectRuleByName() rule content = %v, want %v", rule.Content, params.Content)
				}
				if rule.Description != params.Description {
					t.Errorf("GetProjectRuleByName() rule description = %v, want %v", rule.Description, params.Description)
				}
			}
		})
	}
}

func TestListProjectRules(t *testing.T) {
	tempDir := t.TempDir()

	tests := []struct {
		name          string
		workspacePath string
		setupRules    []*AddProjectRuleParams
		params        *QueryProjectRuleParams
		wantCount     int
		wantTotal     int
		wantErr       bool
	}{
		{
			name:          "空目录返回空列表",
			workspacePath: tempDir,
			setupRules:    []*AddProjectRuleParams{},
			params:        &QueryProjectRuleParams{},
			wantCount:     0,
			wantTotal:     0,
			wantErr:       false,
		},
		{
			name:          "单个规则",
			workspacePath: tempDir,
			setupRules: []*AddProjectRuleParams{
				{
					Name:    "rule1",
					Trigger: ManualRule,
					Content: "规则1",
				},
			},
			params:    &QueryProjectRuleParams{},
			wantCount: 1,
			wantTotal: 1,
			wantErr:   false,
		},
		{
			name:          "多个规则",
			workspacePath: tempDir,
			setupRules: []*AddProjectRuleParams{
				{
					Name:    "rule1",
					Trigger: ManualRule,
					Content: "规则1",
				},
				{
					Name:    "rule2",
					Trigger: GlobRule,
					Glob:    "*.go",
					Content: "规则2",
				},
				{
					Name:        "rule3",
					Trigger:     ModelDecisionRule,
					Description: "规则3描述",
					Content:     "规则3",
				},
			},
			params:    &QueryProjectRuleParams{},
			wantCount: 3,
			wantTotal: 3,
			wantErr:   false,
		},
		{
			name:          "分页测试 - 第一页",
			workspacePath: tempDir,
			setupRules: []*AddProjectRuleParams{
				{
					Name:    "rule1",
					Trigger: ManualRule,
					Content: "规则1",
				},
				{
					Name:    "rule2",
					Trigger: GlobRule,
					Glob:    "*.go",
					Content: "规则2",
				},
				{
					Name:        "rule3",
					Trigger:     ModelDecisionRule,
					Description: "规则3描述",
					Content:     "规则3",
				},
			},
			params: &QueryProjectRuleParams{
				Page:     1,
				PageSize: 2,
			},
			wantCount: 2,
			wantTotal: 3,
			wantErr:   false,
		},
		{
			name:          "分页测试 - 第二页",
			workspacePath: tempDir,
			setupRules: []*AddProjectRuleParams{
				{
					Name:    "rule1",
					Trigger: ManualRule,
					Content: "规则1",
				},
				{
					Name:    "rule2",
					Trigger: GlobRule,
					Glob:    "*.go",
					Content: "规则2",
				},
				{
					Name:        "rule3",
					Trigger:     ModelDecisionRule,
					Description: "规则3描述",
					Content:     "规则3",
				},
			},
			params: &QueryProjectRuleParams{
				Page:     2,
				PageSize: 2,
			},
			wantCount: 1,
			wantTotal: 3,
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 为每个测试创建新的临时目录
			testDir := t.TempDir()

			// 设置规则
			for _, ruleParams := range tt.setupRules {
				_, err := AddProjectRule(testDir, ruleParams)
				if err != nil {
					t.Fatalf("Failed to setup rule %s: %v", ruleParams.Name, err)
				}
			}

			response := ListProjectRules(testDir, tt.params)

			if tt.wantErr {
			} else {

				if len(response.Records) != tt.wantCount {
					t.Errorf("ListProjectRules() returned %d rules, want %d", len(response.Records), tt.wantCount)
				}

				if int(response.Total) != tt.wantTotal {
					t.Errorf("ListProjectRules() returned total %d, want %d", response.Total, tt.wantTotal)
				}

				// 验证规则名称
				ruleNames := make(map[string]bool)
				for _, rule := range response.Records {
					ruleNames[rule.Name] = true
				}

				// 只验证当前页的规则
				expectedRules := tt.setupRules
				if tt.params != nil && tt.params.PageSize > 0 && len(tt.setupRules) > tt.params.PageSize {
					page, _ := parsePaginationParams(tt.params)
					start := (page - 1) * tt.params.PageSize
					end := start + tt.params.PageSize
					if end > len(tt.setupRules) {
						end = len(tt.setupRules)
					}
					expectedRules = tt.setupRules[start:end]
				}

				for _, ruleParams := range expectedRules {
					if !ruleNames[ruleParams.Name] {
						t.Errorf("ListProjectRules() missing rule: %s", ruleParams.Name)
					}
				}
			}
		})
	}
}

func TestGetProjectRulesByTrigger(t *testing.T) {
	tempDir := t.TempDir()

	// 设置不同类型的规则
	setupRules := []*AddProjectRuleParams{
		{
			Name:    "manual-rule",
			Trigger: ManualRule,
			Content: "手动规则",
		},
		{
			Name:    "glob-rule",
			Trigger: GlobRule,
			Glob:    "*.go",
			Content: "Glob规则",
		},
		{
			Name:        "model-rule",
			Trigger:     ModelDecisionRule,
			Description: "模型规则描述",
			Content:     "模型规则",
		},
		{
			Name:    "always-rule",
			Trigger: AlwaysOnRule,
			Content: "始终开启规则",
		},
	}

	// 创建规则
	for _, ruleParams := range setupRules {
		_, err := AddProjectRule(tempDir, ruleParams)
		if err != nil {
			t.Fatalf("Failed to setup rule %s: %v", ruleParams.Name, err)
		}
	}

	tests := []struct {
		name          string
		workspacePath string
		triggers      []ProjectRulesTrigger
		wantCount     int
		wantErr       bool
		expectedErr   string
	}{
		{
			name:          "查询手动规则",
			workspacePath: tempDir,
			triggers:      []ProjectRulesTrigger{ManualRule},
			wantCount:     1,
			wantErr:       false,
		},
		{
			name:          "查询glob规则",
			workspacePath: tempDir,
			triggers:      []ProjectRulesTrigger{GlobRule},
			wantCount:     1,
			wantErr:       false,
		},
		{
			name:          "查询多个触发类型",
			workspacePath: tempDir,
			triggers:      []ProjectRulesTrigger{ManualRule, GlobRule},
			wantCount:     2,
			wantErr:       false,
		},
		{
			name:          "查询所有触发类型",
			workspacePath: tempDir,
			triggers:      []ProjectRulesTrigger{ManualRule, GlobRule, ModelDecisionRule, AlwaysOnRule},
			wantCount:     4,
			wantErr:       false,
		},
		{
			name:          "空触发类型列表",
			workspacePath: tempDir,
			triggers:      []ProjectRulesTrigger{},
			wantErr:       true,
			expectedErr:   "triggers length is 0",
		},
		{
			name:          "无效的触发类型",
			workspacePath: tempDir,
			triggers:      []ProjectRulesTrigger{"invalid_trigger"},
			wantErr:       true,
			expectedErr:   "Invalid trigger type: invalid_trigger",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rules, err := GetProjectRulesByTrigger(tempDir, tt.triggers)

			if tt.wantErr {
				if err == nil {
					t.Errorf("GetProjectRulesByTrigger() expected error but got none")
					return
				}

				if cosyErr, ok := cosyError.IsUnifiedError(err); ok {
					if !strings.Contains(cosyErr.Message, tt.expectedErr) {
						t.Errorf("GetProjectRulesByTrigger() expected error message containing '%s', got '%s'", tt.expectedErr, cosyErr.Message)
					}
				} else {
					t.Errorf("GetProjectRulesByTrigger() expected unified error but got %T: %v", err, err)
				}
			} else {
				if err != nil {
					t.Errorf("GetProjectRulesByTrigger() unexpected error: %v", err)
					return
				}

				if len(rules) != tt.wantCount {
					t.Errorf("GetProjectRulesByTrigger() returned %d rules, want %d", len(rules), tt.wantCount)
				}

				// 验证返回的规则都是指定的触发类型
				triggerMap := make(map[ProjectRulesTrigger]bool)
				for _, trigger := range tt.triggers {
					triggerMap[trigger] = true
				}

				for _, rule := range rules {
					if !triggerMap[rule.Trigger] {
						t.Errorf("GetProjectRulesByTrigger() returned rule with unexpected trigger: %s", rule.Trigger)
					}
				}
			}
		})
	}
}

func TestParseRuleContent(t *testing.T) {
	tests := []struct {
		name        string
		content     string
		filePath    string
		wantErr     bool
		expectedErr string
		checkRule   func(*ProjectRule) bool
	}{
		{
			name: "有效的规则内容",
			content: `---
trigger: manual
description: 测试规则描述
---
规则内容`,
			filePath: "/path/to/test-rule.md",
			wantErr:  false,
			checkRule: func(rule *ProjectRule) bool {
				return rule.Name == "test-rule" &&
					rule.Trigger == ManualRule &&
					rule.Description == "测试规则描述" &&
					rule.Content == "规则内容"
			},
		},
		{
			name: "包含glob的规则内容",
			content: `---
trigger: glob
glob: *.go
---
Go文件规则`,
			filePath: "/path/to/glob-rule.md",
			wantErr:  false,
			checkRule: func(rule *ProjectRule) bool {
				return rule.Name == "glob-rule" &&
					rule.Trigger == GlobRule &&
					rule.Glob == "*.go" &&
					rule.Content == "Go文件规则"
			},
		},
		{
			name: "缺少触发类型的规则内容",
			content: `---
description: 测试规则描述
---
规则内容`,
			filePath: "/path/to/default-rule.md",
			wantErr:  false,
			checkRule: func(rule *ProjectRule) bool {
				return rule.Name == "default-rule" &&
					rule.Trigger == ManualRule // 应该使用默认值
			},
		},
		{
			name: "无效的触发类型",
			content: `---
trigger: invalid_trigger
---
规则内容`,
			filePath:    "/path/to/invalid-rule.md",
			wantErr:     true,
			expectedErr: "Invalid trigger type: invalid_trigger",
		},
		{
			name:        "空内容",
			content:     "",
			filePath:    "/path/to/empty-rule.md",
			wantErr:     true,
			expectedErr: "content is empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rule, err := parseRuleString(tt.content, "", tt.filePath)

			if tt.wantErr {
				if err == nil {
					t.Errorf("parseRuleContent() expected error but got none")
					return
				}

				if cosyErr, ok := cosyError.IsUnifiedError(err); ok {
					if !strings.Contains(cosyErr.Message, tt.expectedErr) {
						t.Errorf("parseRuleContent() expected error message containing '%s', got '%s'", tt.expectedErr, cosyErr.Message)
					}
				} else {
					t.Errorf("parseRuleContent() expected unified error but got %T: %v", err, err)
				}
			} else {
				if err != nil {
					t.Errorf("parseRuleContent() unexpected error: %v", err)
					return
				}

				if rule == nil {
					t.Errorf("parseRuleContent() returned nil rule")
					return
				}

				if !tt.checkRule(rule) {
					t.Errorf("parseRuleContent() returned unexpected rule: %+v", rule)
				}
			}
		})
	}
}

func TestBuildRuleContent(t *testing.T) {
	tests := []struct {
		name     string
		rule     *ProjectRule
		expected string
	}{
		{
			name: "完整规则",
			rule: &ProjectRule{
				Name:        "test-rule",
				Trigger:     ModelDecisionRule,
				Description: "测试规则描述",
				Glob:        "*.go",
				Content:     "规则内容",
			},
			expected: `---
trigger: model_decision
description: 测试规则描述
glob: *.go
---

规则内容`,
		},
		{
			name: "只有必需字段的规则",
			rule: &ProjectRule{
				Name:    "simple-rule",
				Trigger: ManualRule,
				Content: "简单规则内容",
			},
			expected: `---
trigger: manual
---

简单规则内容`,
		},
		{
			name: "只有描述没有glob的规则",
			rule: &ProjectRule{
				Name:        "desc-rule",
				Trigger:     ModelDecisionRule,
				Description: "只有描述的规则",
				Content:     "规则内容",
			},
			expected: `---
trigger: model_decision
description: 只有描述的规则
---

规则内容`,
		},
		{
			name: "只有glob没有描述的规则",
			rule: &ProjectRule{
				Name:    "glob-rule",
				Trigger: GlobRule,
				Glob:    "*.js",
				Content: "JavaScript规则",
			},
			expected: `---
trigger: glob
glob: *.js
---

JavaScript规则`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			content := buildRuleContent(tt.rule)
			if content != tt.expected {
				t.Errorf("buildRuleContent() = %v, want %v", content, tt.expected)
			}
		})
	}
}

func TestParseYamlFrontMatter(t *testing.T) {
	tests := []struct {
		name            string
		content         string
		expectedTrigger ProjectRulesTrigger
		expectedDesc    string
		expectedGlob    string
		expectedContent string
		wantErr         bool
		expectedErr     string
	}{
		{
			name: "完整的YAML前置元数据",
			content: `---
trigger: glob
description: 测试描述
glob: *.go
---
规则内容`,
			expectedTrigger: GlobRule,
			expectedDesc:    "测试描述",
			expectedGlob:    "*.go",
			expectedContent: "规则内容",
			wantErr:         false,
		},
		{
			name: "只有触发类型",
			content: `---
trigger: manual
---
规则内容`,
			expectedTrigger: ManualRule,
			expectedDesc:    "",
			expectedGlob:    "",
			expectedContent: "规则内容",
			wantErr:         false,
		},
		{
			name:            "没有YAML前置元数据",
			content:         `规则内容`,
			expectedTrigger: "",
			expectedDesc:    "",
			expectedGlob:    "",
			expectedContent: "",
			wantErr:         true,
			expectedErr:     "Content format is invalid",
		},
		{
			name:            "空内容",
			content:         "",
			expectedTrigger: "",
			expectedDesc:    "",
			expectedGlob:    "",
			expectedContent: "",
			wantErr:         true,
			expectedErr:     "Content is empty",
		},
		{
			name: "YAML区域包含空行",
			content: `---

trigger: glob

glob: *.go

---
规则内容`,
			expectedTrigger: GlobRule,
			expectedDesc:    "",
			expectedGlob:    "*.go",
			expectedContent: "规则内容",
			wantErr:         false,
		},
		{
			name: "字段值包含空格",
			content: `---
trigger: glob
description: 这是一个包含空格的描述
glob: *.go *.js
---
规则内容`,
			expectedTrigger: GlobRule,
			expectedDesc:    "这是一个包含空格的描述",
			expectedGlob:    "*.go *.js",
			expectedContent: "规则内容",
			wantErr:         false,
		},
		{
			name: "字段值前后有空格",
			content: `---
trigger:  glob  
description:  描述内容  
glob:  *.go  
---
规则内容`,
			expectedTrigger: GlobRule,
			expectedDesc:    "描述内容",
			expectedGlob:    "*.go",
			expectedContent: "规则内容",
			wantErr:         false,
		},
		{
			name: "只有开始标记没有结束标记",
			content: `---
trigger: manual
description: 测试描述`,
			expectedTrigger: "",
			expectedDesc:    "",
			expectedGlob:    "",
			expectedContent: "",
			wantErr:         true,
			expectedErr:     "Content format is invalid",
		},
		{
			name: "只有结束标记没有开始标记",
			content: `trigger: manual
description: 测试描述
---`,
			expectedTrigger: "",
			expectedDesc:    "",
			expectedGlob:    "",
			expectedContent: "",
			wantErr:         true,
			expectedErr:     "Content format is invalid",
		},
		{
			name: "glob触发类型缺少glob字段",
			content: `---
trigger: glob
description: 测试描述
---
规则内容`,
			expectedTrigger: "",
			expectedDesc:    "",
			expectedGlob:    "",
			expectedContent: "",
			wantErr:         true,
			expectedErr:     "glob field is required when trigger is glob",
		},
		{
			name: "model_decision触发类型缺少description字段",
			content: `---
trigger: model_decision
glob: *.go
---
规则内容`,
			expectedTrigger: "",
			expectedDesc:    "",
			expectedGlob:    "",
			expectedContent: "",
			wantErr:         true,
			expectedErr:     "description field is required when trigger is model_decision",
		},
		{
			name: "多个空行和空格",
			content: `


---
  trigger:  manual  
  description:  测试描述  
  glob:  *.go  


---
  规则内容  `,
			expectedTrigger: ManualRule,
			expectedDesc:    "测试描述",
			expectedGlob:    "*.go",
			expectedContent: "规则内容",
			wantErr:         false,
		},
		{
			name: "YAML区域只有空行",
			content: `---


---
规则内容`,
			expectedTrigger: ManualRule, // 默认值
			expectedDesc:    "",
			expectedGlob:    "",
			expectedContent: "规则内容",
			wantErr:         false,
		},
		{
			name: "内容包含多个换行符",
			content: `---
trigger: manual
---
第一行内容

第二行内容

第三行内容`,
			expectedTrigger: ManualRule,
			expectedDesc:    "",
			expectedGlob:    "",
			expectedContent: "第一行内容\n\n第二行内容\n\n第三行内容",
			wantErr:         false,
		},
		{
			name: "内容前后有空格",
			content: `---
trigger: manual
---
  规则内容前后有空格  `,
			expectedTrigger: ManualRule,
			expectedDesc:    "",
			expectedGlob:    "",
			expectedContent: "规则内容前后有空格",
			wantErr:         false,
		},
		{
			name:            "只有空格的内容",
			content:         `   `,
			expectedTrigger: "",
			expectedDesc:    "",
			expectedGlob:    "",
			expectedContent: "",
			wantErr:         true,
			expectedErr:     "Content is empty",
		},
		{
			name: "model_decision触发类型完整",
			content: `---
trigger: model_decision
description: 模型决策规则描述
---
模型决策规则内容`,
			expectedTrigger: ModelDecisionRule,
			expectedDesc:    "模型决策规则描述",
			expectedGlob:    "",
			expectedContent: "模型决策规则内容",
			wantErr:         false,
		},
		{
			name: "always_on触发类型",
			content: `---
trigger: always_on
---
始终开启的规则内容`,
			expectedTrigger: AlwaysOnRule,
			expectedDesc:    "",
			expectedGlob:    "",
			expectedContent: "始终开启的规则内容",
			wantErr:         false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			trigger, desc, glob, content, err := getFormatFields(tt.content)

			if tt.wantErr {
				if err == nil {
					t.Errorf("parseYamlFrontMatter() expected error but got none")
					return
				}

				if cosyErr, ok := cosyError.IsUnifiedError(err); ok {
					if !strings.Contains(cosyErr.Message, tt.expectedErr) {
						t.Errorf("parseYamlFrontMatter() expected error message containing '%s', got '%s'", tt.expectedErr, cosyErr.Message)
					}
				} else {
					t.Errorf("parseYamlFrontMatter() expected unified error but got %T: %v", err, err)
				}
			} else {
				if err != nil {
					t.Errorf("parseYamlFrontMatter() unexpected error: %v", err)
					return
				}

				if trigger != tt.expectedTrigger {
					t.Errorf("parseYamlFrontMatter() trigger = %v, want %v", trigger, tt.expectedTrigger)
				}
				if desc != tt.expectedDesc {
					t.Errorf("parseYamlFrontMatter() description = %v, want %v", desc, tt.expectedDesc)
				}
				if glob != tt.expectedGlob {
					t.Errorf("parseYamlFrontMatter() glob = %v, want %v", glob, tt.expectedGlob)
				}
				if content != tt.expectedContent {
					t.Errorf("parseYamlFrontMatter() content = %v, want %v", content, tt.expectedContent)
				}
			}
		})
	}
}

func TestFileExists(t *testing.T) {
	tempDir := t.TempDir()

	// 创建一个测试文件
	testFile := filepath.Join(tempDir, "test.txt")
	err := os.WriteFile(testFile, []byte("test content"), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	tests := []struct {
		name     string
		filePath string
		expected bool
	}{
		{
			name:     "文件存在",
			filePath: testFile,
			expected: true,
		},
		{
			name:     "文件不存在",
			filePath: filepath.Join(tempDir, "non-existent.txt"),
			expected: false,
		},
		{
			name:     "目录存在",
			filePath: tempDir,
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := fileExists(tt.filePath)
			if result != tt.expected {
				t.Errorf("fileExists() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestValidateTrigger(t *testing.T) {
	tests := []struct {
		name        string
		trigger     ProjectRulesTrigger
		wantErr     bool
		expectedErr string
	}{
		{
			name:    "有效的手动触发类型",
			trigger: ManualRule,
			wantErr: false,
		},
		{
			name:    "有效的glob触发类型",
			trigger: GlobRule,
			wantErr: false,
		},
		{
			name:    "有效的模型决策触发类型",
			trigger: ModelDecisionRule,
			wantErr: false,
		},
		{
			name:    "有效的始终开启触发类型",
			trigger: AlwaysOnRule,
			wantErr: false,
		},
		{
			name:        "空的触发类型",
			trigger:     "",
			wantErr:     true,
			expectedErr: "Trigger type cannot be empty",
		},
		{
			name:        "无效的触发类型",
			trigger:     "invalid_trigger",
			wantErr:     true,
			expectedErr: "Invalid trigger type: invalid_trigger",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateTrigger(tt.trigger)

			if tt.wantErr {
				if err == nil {
					t.Errorf("validateTrigger() expected error but got none")
					return
				}

				if cosyErr, ok := cosyError.IsUnifiedError(err); ok {
					if !strings.Contains(cosyErr.Message, tt.expectedErr) {
						t.Errorf("validateTrigger() expected error message containing '%s', got '%s'", tt.expectedErr, cosyErr.Message)
					}
				} else {
					t.Errorf("validateTrigger() expected unified error but got %T: %v", err, err)
				}
			} else {
				if err != nil {
					t.Errorf("validateTrigger() unexpected error: %v", err)
				}
			}
		})
	}
}

func TestParsePaginationParams(t *testing.T) {
	tests := []struct {
		name     string
		params   *QueryProjectRuleParams
		wantPage int
		wantSize int
	}{
		{
			name:     "空参数使用默认值",
			params:   nil,
			wantPage: 1,
			wantSize: 20,
		},
		{
			name:     "空字符串使用默认值",
			params:   &QueryProjectRuleParams{},
			wantPage: 1,
			wantSize: 20,
		},
		{
			name: "指定页码和大小",
			params: &QueryProjectRuleParams{
				Page:     2,
				PageSize: 10,
			},
			wantPage: 2,
			wantSize: 10,
		},

		{
			name: "零页码使用默认值",
			params: &QueryProjectRuleParams{
				Page:     0,
				PageSize: 25,
			},
			wantPage: 1,
			wantSize: 25,
		},
		{
			name: "负页码使用默认值",
			params: &QueryProjectRuleParams{
				Page:     -1,
				PageSize: 30,
			},
			wantPage: 1,
			wantSize: 30,
		},
		{
			name: "页面大小超过上限",
			params: &QueryProjectRuleParams{
				Page:     1,
				PageSize: 150,
			},
			wantPage: 1,
			wantSize: 100,
		},
		{
			name: "页面大小小于下限",
			params: &QueryProjectRuleParams{
				Page:     0,
				PageSize: 0,
			},
			wantPage: 1,
			wantSize: 20,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			page, size := parsePaginationParams(tt.params)
			if page != tt.wantPage {
				t.Errorf("parsePaginationParams() page = %v, want %v", page, tt.wantPage)
			}
			if size != tt.wantSize {
				t.Errorf("parsePaginationParams() size = %v, want %v", size, tt.wantSize)
			}
		})
	}
}

// TestGetFormatFields_CursorCompatibility 测试 Cursor 格式兼容性
func TestGetFormatFields_CursorCompatibility(t *testing.T) {
	tests := []struct {
		name                string
		content             string
		expectedTrigger     ProjectRulesTrigger
		expectedDescription string
		expectedGlob        string
		expectedContent     string
		wantErr             bool
		errorMessage        string
	}{
		{
			name: "Cursor 格式 - alwaysApply: true",
			content: `---
description: java代码注释规范
globs: *.java
alwaysApply: true
---
规则内容`,
			expectedTrigger:     AlwaysOnRule,
			expectedDescription: "java代码注释规范",
			expectedGlob:        "",
			expectedContent:     "规则内容",
			wantErr:             false,
		},
		{
			name: "Cursor 格式 - alwaysApply: false 且有 globs",
			content: `---
description: java代码注释规范
globs: *.java
alwaysApply: false
---
规则内容`,
			expectedTrigger:     GlobRule,
			expectedDescription: "java代码注释规范",
			expectedGlob:        "*.java",
			expectedContent:     "规则内容",
			wantErr:             false,
		},
		{
			name: "Cursor 格式 - 只有 description，无 globs",
			content: `---
description: java代码注释规范
alwaysApply: false
---
规则内容`,
			expectedTrigger:     ModelDecisionRule,
			expectedDescription: "java代码注释规范",
			expectedGlob:        "",
			expectedContent:     "规则内容",
			wantErr:             false,
		},
		{
			name: "Cursor 格式 - 无 description 无 globs",
			content: `---
alwaysApply: false
---
规则内容`,
			expectedTrigger:     ManualRule,
			expectedDescription: "",
			expectedGlob:        "",
			expectedContent:     "规则内容",
			wantErr:             false,
		},
		{
			name: "混合格式 - 既有 Cursor 字段又有 Cosy 字段",
			content: `---
trigger: manual
description: 混合规则
globs: *.py
alwaysApply: false
---
混合规则内容`,
			expectedTrigger:     ManualRule,
			expectedDescription: "混合规则",
			expectedGlob:        "",
			expectedContent:     "混合规则内容",
			wantErr:             false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			trigger, description, glob, content, err := getFormatFields(tt.content)

			if tt.wantErr {
				if err == nil {
					t.Errorf("getFormatFields() expected error but got none")
					return
				}

				if cosyErr, ok := cosyError.IsUnifiedError(err); ok {
					if !strings.Contains(cosyErr.Message, tt.errorMessage) {
						t.Errorf("getFormatFields() expected error message containing '%s', got '%s'", tt.errorMessage, cosyErr.Message)
					}
				} else {
					t.Errorf("getFormatFields() expected unified error but got %T: %v", err, err)
				}
			} else {
				if err != nil {
					t.Errorf("getFormatFields() unexpected error: %v", err)
					return
				}

				if trigger != tt.expectedTrigger {
					t.Errorf("getFormatFields() trigger = %v, want %v", trigger, tt.expectedTrigger)
				}
				if description != tt.expectedDescription {
					t.Errorf("getFormatFields() description = %v, want %v", description, tt.expectedDescription)
				}
				if glob != tt.expectedGlob {
					t.Errorf("getFormatFields() glob = %v, want %v", glob, tt.expectedGlob)
				}
				if content != tt.expectedContent {
					t.Errorf("getFormatFields() content = %v, want %v", content, tt.expectedContent)
				}
			}
		})
	}
}

// TestGetRuleName 测试GetRuleName方法的跨平台兼容性
func TestGetRuleName(t *testing.T) {
	tests := []struct {
		name     string
		filePath string
		expected string
	}{
		{
			name:     "Unix路径格式",
			filePath: "/workspace/.lingma/rules/test-rule.md",
			expected: "test-rule.md",
		},
		{
			name:     "Windows路径格式",
			filePath: "C:\\workspace\\.lingma\\rules\\test-rule.md",
			expected: "test-rule.md",
		},
		{
			name:     "子目录中的规则文件(Unix)",
			filePath: "/workspace/.lingma/rules/subdir/nested-rule.md",
			expected: "subdir/nested-rule.md",
		},
		{
			name:     "子目录中的规则文件(Windows)",
			filePath: "C:\\workspace\\.lingma\\rules\\subdir\\nested-rule.md",
			expected: "subdir/nested-rule.md",
		},
		{
			name:     "深层嵌套子目录(Unix)",
			filePath: "/workspace/.lingma/rules/dir1/dir2/deep-rule.md",
			expected: "dir1/dir2/deep-rule.md",
		},
		{
			name:     "深层嵌套子目录(Windows)",
			filePath: "C:\\workspace\\.lingma\\rules\\dir1\\dir2\\deep-rule.md",
			expected: "dir1/dir2/deep-rule.md",
		},
		{
			name:     ".mdc后缀文件",
			filePath: "/workspace/.lingma/rules/cursor-rule.mdc",
			expected: "cursor-rule.mdc",
		},
		{
			name:     "不在规则目录下的文件",
			filePath: "/workspace/other/test.md",
			expected: "",
		},
		{
			name:     "路径中包含规则目录名但不是实际规则目录",
			filePath: "/workspace/src/.lingma/rules/fake/.lingma/rules/real.md",
			expected: "fake/.lingma/rules/real.md",
		},
		{
			name:     "包含多余分隔符的路径",
			filePath: "/workspace/.lingma//rules///extra-slash.md",
			expected: "extra-slash.md",
		},
		{
			name:     "包含相对路径的路径",
			filePath: "/workspace/.lingma/rules/../rules/relative.md",
			expected: "relative.md",
		},
		{
			name:     "Qoder产品路径",
			filePath: "/workspace/.qoder/rules/qoder-rule.md",
			expected: "qoder-rule.md",
		},
		{
			name:     "空文件路径",
			filePath: "",
			expected: "",
		},
		{
			name:     "仅为规则目录的路径",
			filePath: "/workspace/.lingma/rules",
			expected: "",
		},
		{
			name:     "规则目录后只有分隔符",
			filePath: "/workspace/.lingma/rules/",
			expected: "",
		},
		{
			name:     "混合路径分隔符(Windows风格路径中包含正斜杠)",
			filePath: "C:\\workspace/.lingma\\rules/mixed-rule.md",
			expected: "mixed-rule.md",
		},
		{
			name:     "复杂的Windows绝对路径",
			filePath: "D:\\Projects\\MyProject\\.lingma\\rules\\category\\specific-rule.md",
			expected: "category/specific-rule.md",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetRuleName("", tt.filePath)
			if result != tt.expected {
				t.Errorf("GetRuleName(%q) = %q, want %q", tt.filePath, result, tt.expected)
			}
		})
	}
}
