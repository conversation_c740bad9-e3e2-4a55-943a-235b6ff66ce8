package extension

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_CompareContextFilterRulesDiff(t *testing.T) {
	wantAddedRuleName := "filterMyRepoFile-added"
	wantModifiedRuleName := "filterMyRepoFile-modified"
	wantDeletedRuleName := "filterMyRepoFile-deleted"
	wantModifiedRuleWithStateChange := "filterMyRepoFile-state-changed"
	wantModifiedByWebHookLenChange := "filter-webhookLenChange"
	wantModifiedByWebHookConfigChange := "filter-webhookConfigChange"
	wantModifiedByStrategyChange := "filter-strategyChange"

	newContextFilterRules := make([]ContentHandlerRule, 0)
	newContextFilterRules = append(newContextFilterRules, ContentHandlerRule{
		BasicComponent: BasicComponent{
			Identifier:    wantAddedRuleName + "chat_ask",
			ComponentType: "prompt",
			Name:          wantAddedRuleName,
			Version:       "111",
		},
		BizType:     "chat_ask",
		RegExp:      "a|b|c",
		ReplaceWord: "abc",
		Strategy:    "blocker",
		WebHookList: []string{"https://codeup.aliyun.com"},
	})
	newContextFilterRules = append(newContextFilterRules, ContentHandlerRule{
		BasicComponent: BasicComponent{
			Identifier:    wantModifiedRuleName + "completion",
			ComponentType: "prompt",
			Name:          wantModifiedRuleName,
			Version:       "222",
		},
		BizType:     "completion",
		RegExp:      "a|b|c",
		ReplaceWord: "abc",
		Strategy:    FilterType,
		WebHookList: []string{"https://flow.aliyun.com"},
	})
	newContextFilterRules = append(newContextFilterRules, ContentHandlerRule{
		BasicComponent: BasicComponent{
			Identifier:    "filterMyRepoFile-addedcompletion",
			ComponentType: "prompt",
			Name:          "filterMyRepoFile-added",
			Version:       "111",
		},
		BizType:     "completion",
		RegExp:      "a|b|c",
		ReplaceWord: "abc",
		Strategy:    FilterType,
		WebHookList: []string{"https://codeup.aliyun.com"},
	})
	newContextFilterRules = append(newContextFilterRules, ContentHandlerRule{
		BasicComponent: BasicComponent{
			Identifier:    wantModifiedRuleWithStateChange,
			ComponentType: "prompt",
			Name:          wantModifiedRuleWithStateChange,
			Version:       "111",
			State:         DisableState,
		},
		BizType:     "completion",
		RegExp:      "a|b|c",
		ReplaceWord: "abc",
		Strategy:    FilterType,
		WebHookList: []string{"https://codeup.aliyun.com"},
	})
	newContextFilterRules = append(newContextFilterRules, ContentHandlerRule{
		BasicComponent: BasicComponent{
			Identifier:    wantModifiedByWebHookLenChange,
			ComponentType: "prompt",
			Name:          wantModifiedByWebHookLenChange,
			Version:       "111",
			State:         EnableState,
		},
		BizType:     "completion",
		RegExp:      "a|b|c",
		ReplaceWord: "abc",
		Strategy:    FilterType,
	})
	newContextFilterRules = append(newContextFilterRules, ContentHandlerRule{
		BasicComponent: BasicComponent{
			Identifier:    wantModifiedByWebHookConfigChange,
			ComponentType: "prompt",
			Name:          wantModifiedByWebHookConfigChange,
			Version:       "111",
			State:         DisableState,
		},
		BizType:     "completion",
		RegExp:      "a|b|c",
		ReplaceWord: "abc",
		Strategy:    FilterType,
		WebHookList: []string{"https://codeup.aliyun.com"},
	})
	newContextFilterRules = append(newContextFilterRules, ContentHandlerRule{
		BasicComponent: BasicComponent{
			Identifier:    wantModifiedByStrategyChange,
			ComponentType: "prompt",
			Name:          wantModifiedByStrategyChange,
			Version:       "111",
			State:         EnableState,
		},
		BizType:     "completion",
		RegExp:      "a|b|c",
		ReplaceWord: "abc",
		Strategy:    BlockerType,
		WebHookList: []string{"https://codeup.aliyun.com"},
	})

	originContextFilterRules := make([]ContentHandlerRule, 0)
	originContextFilterRules = append(originContextFilterRules, ContentHandlerRule{
		BasicComponent: BasicComponent{
			Identifier:    wantModifiedRuleName + "completion",
			ComponentType: "prompt",
			Name:          wantModifiedRuleName,
			Version:       "111",
		},
		BizType:     "completion",
		RegExp:      "a|b|c|d|e",
		ReplaceWord: "abc",
		Strategy:    "blocker",
		WebHookList: []string{"https://codeup.aliyun.com"},
	})
	originContextFilterRules = append(originContextFilterRules, ContentHandlerRule{
		BasicComponent: BasicComponent{
			Identifier:    wantDeletedRuleName + "completion",
			ComponentType: "prompt",
			Name:          wantDeletedRuleName,
			Version:       "333",
		},
		BizType:     "completion",
		RegExp:      "a|b|c|d|e",
		ReplaceWord: "abc",
		Strategy:    "blocker",
		WebHookList: []string{"https://codeup.aliyun.com"},
	})
	originContextFilterRules = append(originContextFilterRules, ContentHandlerRule{
		BasicComponent: BasicComponent{
			Identifier:    "filterMyRepoFile-addedcompletion",
			ComponentType: "prompt",
			Name:          "filterMyRepoFile-added",
			Version:       "111",
		},
		BizType:     "completion",
		RegExp:      "a|b|c",
		ReplaceWord: "abc",
		Strategy:    FilterType,
		WebHookList: []string{"https://codeup.aliyun.com"},
	})
	originContextFilterRules = append(originContextFilterRules, ContentHandlerRule{
		BasicComponent: BasicComponent{
			Identifier:    wantModifiedRuleWithStateChange,
			ComponentType: "prompt",
			Name:          wantModifiedRuleWithStateChange,
			Version:       "111",
			State:         EnableState,
		},
		BizType:     "completion",
		RegExp:      "a|b|c",
		ReplaceWord: "abc",
		Strategy:    FilterType,
		WebHookList: []string{"https://codeup.aliyun.com"},
	})
	originContextFilterRules = append(originContextFilterRules, ContentHandlerRule{
		BasicComponent: BasicComponent{
			Identifier:    wantModifiedByWebHookLenChange,
			ComponentType: "prompt",
			Name:          wantModifiedByWebHookLenChange,
			Version:       "111",
			State:         EnableState,
		},
		BizType:     "completion",
		RegExp:      "a|b|c",
		ReplaceWord: "abc",
		Strategy:    FilterType,
		WebHookList: []string{"https://codeup.aliyun.com"},
	})
	originContextFilterRules = append(originContextFilterRules, ContentHandlerRule{
		BasicComponent: BasicComponent{
			Identifier:    wantModifiedByWebHookConfigChange,
			ComponentType: "prompt",
			Name:          wantModifiedByWebHookConfigChange,
			Version:       "111",
			State:         DisableState,
		},
		BizType:     "completion",
		RegExp:      "a|b|c",
		ReplaceWord: "abc",
		Strategy:    FilterType,
		WebHookList: []string{"https://flow.aliyun.com"},
	})
	originContextFilterRules = append(originContextFilterRules, ContentHandlerRule{
		BasicComponent: BasicComponent{
			Identifier:    wantModifiedByStrategyChange,
			ComponentType: "prompt",
			Name:          wantModifiedByStrategyChange,
			Version:       "111",
			State:         EnableState,
		},
		BizType:     "completion",
		RegExp:      "a|b|c",
		ReplaceWord: "abc",
		Strategy:    FilterType,
		WebHookList: []string{"https://codeup.aliyun.com"},
	})

	added, modified, deleted := CompareContextFilterRulesDiff(newContextFilterRules, originContextFilterRules)
	assert.Equal(t, 1, len(added), "one need added")
	assert.Equal(t, 5, len(modified), "three need modified")
	assert.Equal(t, 1, len(deleted), "one need deleted")
	assert.Equal(t, wantAddedRuleName, added[0].Name, wantAddedRuleName+" should be added")
	assert.Equal(t, wantDeletedRuleName, deleted[0].Name, wantDeletedRuleName+" should be deleted")

	modifiedItemNames := []string{wantModifiedRuleName, wantModifiedByWebHookConfigChange, wantModifiedByWebHookLenChange, wantModifiedRuleWithStateChange, wantModifiedByStrategyChange}
	for _, modifiedItemName := range modifiedItemNames {
		matched := false
		for _, modifiedItem := range modified {
			if modifiedItem.Name == modifiedItemName {
				matched = true
				break
			}
		}
		assert.Truef(t, matched, modifiedItemName+" should be added")
	}
}

func Test_checkContextFilterRules(t *testing.T) {
	contextFilterRule := ContentHandlerRule{
		BasicComponent: BasicComponent{
			ComponentType: "prompt",
			Version:       "111",
		},
		BizType:     "completion",
		RegExp:      "a|b|c",
		ReplaceWord: "abc",
		Strategy:    FilterType,
	}
	assert.Equal(t, "contextFilterRule name is blank", checkContextFilterRule(&contextFilterRule).Error(), "should check contextFilterRule name")

	contextFilterRule = ContentHandlerRule{
		BasicComponent: BasicComponent{
			ComponentType: "prompt",
			Version:       "111",
			Name:          "filterMyRepoFile",
		},
		BizType:     "123",
		RegExp:      "a|b|c",
		ReplaceWord: "abc",
		Strategy:    FilterType,
	}
	assert.Equal(t, "bizType is invalid", checkContextFilterRule(&contextFilterRule).Error(), "should check bizType")

	contextFilterRule = ContentHandlerRule{
		BasicComponent: BasicComponent{
			ComponentType: "prompt",
			Version:       "111",
			Name:          "filterMyRepoFile",
		},
		BizType:     "completion",
		RegExp:      "a|b|c",
		ReplaceWord: "abc",
		Strategy:    "1",
	}
	assert.Equal(t, "strategy is invalid", checkContextFilterRule(&contextFilterRule).Error(), "should check strategy")

	contextFilterRule = ContentHandlerRule{
		BasicComponent: BasicComponent{
			ComponentType: "prompt",
			Version:       "111",
			Name:          "filterMyRepoFile",
		},
		BizType:     "completion",
		ReplaceWord: "abc",
		Strategy:    FilterType,
	}
	assert.Equal(t, "contextFilterRule RegExp is blank", checkContextFilterRule(&contextFilterRule).Error(), "should check RegExp")

	contextFilterRule = ContentHandlerRule{
		BasicComponent: BasicComponent{
			ComponentType: "prompt",
			Version:       "111",
			Name:          "filterMyRepoFile",
		},
		BizType:     "completion",
		ReplaceWord: "abc",
		Strategy:    FilterType,
		RegExp:      "a|b|c",
		Stage:       "abc",
	}
	assert.Equal(t, "stage is invalid", checkContextFilterRule(&contextFilterRule).Error(), "should check stage")
}
