package localservice

import (
	"cosy/ls"
	"os/exec"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestNewLanguageService(t *testing.T) {
	s := &Service{}
	s.Command = exec.Command("echo", "1")
	s.Kill()
	s.Command = exec.Command("echo", "1")
	s.Command.Run()
	s.Kill()
}

func TestService_CallWithTimeout(t *testing.T) {
	ss := &Service{}
	ss.LanguageService = *ls.NewLanguageService(".", "pwd")
	re := ""
	err := ss.CallWithTimeout(1*time.Second, "test", nil, &re)
	assert.Equal(t, "Inference session error", err.<PERSON>rror())
}
