package localservice

import (
	"cosy/log"
	"os/exec"
	"syscall"
)

func (s *Service) Kill() {
	if s.Command == nil {
		return
	}
	if s.Command.Process == nil {
		return
	}
	if err := s.Command.Process.Kill(); err != nil {
		log.Warn("Failed to stop local service: ", err)
		// Try call system's kill
		if err := syscall.Kill(-s.Command.Process.Pid, syscall.SIGKILL); err != nil {
			// For linux and darwin
			log.Info("Cannot shutdown local service: ", err)
		}
	}
}

func setProcessGroup(cmd *exec.Cmd) {
	cmd.SysProcAttr = &syscall.SysProcAttr{
		Setpgid: true,
	}
}

func setCmdLine(cmd *exec.Cmd, cmdStr string) {
}
