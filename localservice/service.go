package localservice

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/ls"
	"errors"
	"time"
)

// Service is the local inference service
type Service struct {
	ls.LanguageService
	lastAccess time.Time
}

type InferenceParam struct {
	// Current language
	Language         string                `json:"language"`
	Code             string                `json:"code"`
	LineNum          int                   `json:"row_num"`
	ColNum           int                   `json:"col_num"`
	PrevToken        string                `json:"prev_token"`
	BeamSearchConfig definition.BeamConfig `json:"beam_search_config,omitempty"`
}

var numTimeout = 0

func (s *Service) Predict(rawFileContent, language, prevToken string, pos definition.Position,
	localBeamConfig definition.BeamConfig) (definition.LocalInferenceResult, error) {
	log.Debug("Inference config:", localBeamConfig)
	params := InferenceParam{
		Language:         language,
		Code:             rawFileContent,
		LineNum:          int(pos.Line),
		ColNum:           int(pos.Character),
		PrevToken:        prevToken,
		BeamSearchConfig: localBeamConfig,
	}

	result := definition.LocalInferenceResult{}
	if err := s.CallWithTimeout(1000*time.Millisecond, "completion", params, &result); err != nil {
		// If an unrecoverable error occurs, or cosy-local timeouts over 5 times sequentially
		if err.Error() == "Inference session error" || numTimeout > 5 {
			ServiceManager.restart(s, language)
		}
		return definition.LocalInferenceResult{}, err
	}

	return result, nil
}

// CallWithTimeout calls the local service with timeout
func (s *Service) CallWithTimeout(timeout time.Duration, method string, param, resultPointer interface{}) error {
	done := make(chan int, 1)
	var err error
	go func() {
		err = s.Conn.Call(context.Background(), method, param, resultPointer)
		done <- 1
	}()
	select {
	case <-done:
		numTimeout = 0
		if err != nil {
			log.Error("An error occurred in inference session: ", err)
			// If an unknown error occurred when doing inference, return error
			// Then cosy will kill the local inference service and restart it
			if err.Error() == "jsonrpc2: connection is closed" || err.Error() == "unexpected EOF" {
				return errors.New("Inference session error")
			}
			// If the error is an internal error of cosy-local, just skip this call
			return errors.New("cosy-local internal error")
		}
		return nil
	case <-time.After(timeout):
		numTimeout += 1
		return errors.New("Calling " + method + " timeout")
	}
}
