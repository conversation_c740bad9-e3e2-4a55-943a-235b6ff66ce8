package localservice

import (
	"cosy/log"
	"cosy/update"
	"cosy/util"
	"path/filepath"
	"runtime"
)

// PrepareInferenceEnvironment loads compressed dynamic lib, decompresses it and puts it to user's cache dir
//
// This function is different with `update.DoInferenceEnvUpdate()`, because it checks local files only
// If no dynamic lib exists locally, just return false
func PrepareInferenceEnvironment() (ok bool) {
	log.Info("Preparing inference environment")

	// Check dynamic lib first
	libFilePath, err := util.GetDynamicLibraryPath()
	if err != nil {
		log.Error("Cannot get dynamic lib path")
		return false
	}
	if util.PathExists(libFilePath) {
		log.Info("Inference environment is ready")
		return true
	}

	// Load compressed dynamic lib
	platformAndArch := runtime.GOOS + "-" + runtime.GOARCH

	// Decompress dynamic lib
	compressedEnvFile := filepath.Join(util.GetCosyHomePath(), "model", "env", platformAndArch)
	err = update.DecompressDynamicLib(compressedEnvFile, libFilePath)
	if err != nil {
		return false
	}

	log.Info("Succeed to create inference environment")
	return true
}
