package localservice

import (
	"context"
	"cosy/global"
	"cosy/log"
	"cosy/ls"
	"cosy/stable"
	"cosy/update"
	"cosy/util"
	"cosy/util/download"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
	"github.com/sourcegraph/jsonrpc2"
)

var ServiceManager *LocalServiceManager

// LocalServiceManager maintains local inference services for all supported languages
//
// The default(java) inference service runs forever
// Other services will automatically stop if the service is not active(no inference call more than 15 minutes)
type LocalServiceManager struct {
	serviceMu      sync.RWMutex
	services       map[string]*Service // services MUST be accessed with serviceMu
	wd             string
	localModelRoot string
	restartMutex   sync.Mutex
	startMutex     sync.Mutex
}

// NewLocalServiceManager returns a manager with an empty map of local services
//
// Use `InitializeLocalService` to initialize the service
func NewLocalServiceManager() *LocalServiceManager {
	return &LocalServiceManager{
		serviceMu:    sync.RWMutex{},
		services:     make(map[string]*Service),
		restartMutex: sync.Mutex{},
		startMutex:   sync.Mutex{},
	}
}

// SetWd must be called before initialize specific local service
func (m *LocalServiceManager) SetWd(wd, modelRoot string) {
	m.wd = strings.TrimRight(wd, "/")
	m.localModelRoot = strings.TrimRight(modelRoot, "/")
}

// GetService returns local completion service for given language
// If the service doesn't exist, try to start one
func (m *LocalServiceManager) GetService(language string) (*Service, error) {
	log.Debugf("Getting local %s inference service", language)

	m.serviceMu.RLock()
	service, ok := m.services[language]
	m.serviceMu.RUnlock()
	if ok {
		service.lastAccess = time.Now()
		return service, nil
	}

	// If no corresponding service found, initialize it and return immediately
	log.Infof("No active local %s inference service, starting it", language)
	if err := m.StartLocalService(language); err != nil {
		return nil, errors.New("cannot start local service for " + language + ": " + err.Error())
	}

	// Try again
	m.serviceMu.RLock()
	service, ok = m.services[language]
	m.serviceMu.RUnlock()
	if !ok {
		return nil, errors.New("no local service found for " + language)
	}
	service.lastAccess = time.Now()
	return service, nil
}

func (m *LocalServiceManager) startDefaultService(cosyHome string) {
	// Model is put at `$wd/model`, so we use `wd` as the model root

	log.Infof("Set local model service home: %s", cosyHome)
	m.SetWd(cosyHome, cosyHome)

	//if global.CosyConfig.Local.Enable != nil && !*global.CosyConfig.Local.Enable {
	//    log.Info("Ignore starting local service. Local model is disabled")
	//    return
	//}
	// Set default service according to IDE(PyCharm/IDEA Intellij)
	//if err := m.StartLocalService(global.DefaultLocalLanguage); err != nil {
	//	log.Warn("Cannot start default local service: " + err.Error())
	//	return
	//}

}

// StartLocalService initializes and starts the local completion service for given language
//
// To successfully initialize the local service, the structure under cosy's home path should be like:
// - $home/.lingma
//   - model
//   - config.json
//   - $model_version  # (defined in config.json)
//   - cosy.model
//   - python.model
//   - javascript.model
//   - env  # (depends on platform)
//   - darwin
//   - windows
//   - linux
//   - ...
//
// - os.UserCacheDir()/.lingma/env  # (depends on platform)
//   - libonnxruntime.1.12.0.dylib
//   - libonnxruntime.so.1.12.0
//   - onnxruntime.dll
func (m *LocalServiceManager) StartLocalService(language string) error {
	m.startMutex.Lock()
	defer m.startMutex.Unlock()
	// Check working directories are set
	log.Infof("Starting local %s inference service", language)

	if m.wd == "" || m.localModelRoot == "" {
		return errors.New("Working directory is not set")
	}

	// Check whether the service has started
	m.serviceMu.RLock()
	_, ok := m.services[language]
	m.serviceMu.RUnlock()
	if ok {
		log.Infof("%s local service has been started", language)
		return nil
	}

	binaryPath, modelPath, runtimePath, err := m.getUsedPaths(language)
	if err != nil {
		return errors.New("path error: " + err.Error())
	}

	s, err := m.setupAndStartService(binaryPath, modelPath, runtimePath)
	if err != nil {
		return errors.New("start local service error: " + err.Error())
	}

	// Add language's inference service to map
	m.addService(language, s)

	log.Info("Local inference service initialized")
	return nil
}

// addService informs service manager that given inference service is ready
func (m *LocalServiceManager) addService(language string, s *ls.LanguageService) {
	m.serviceMu.Lock()
	m.services[language] = &Service{*s, time.Now()}
	m.serviceMu.Unlock()
}

func (m *LocalServiceManager) checkServiceState() {
	log.Info("Checking service state")
	m.serviceMu.RLock()
	for language, s := range m.services {
		m.serviceMu.RUnlock()
		// Check last access time of the service
		if time.Since(s.lastAccess).Minutes() > 15.0 {
			log.Info("Stopping in-active service: ", language)
			m.stopService(language, s)
		}
		m.serviceMu.RLock()
	}
	m.serviceMu.RUnlock()
}

func (m *LocalServiceManager) Shutdown() {
	for _, service := range m.services {
		service.Kill()
	}
}

func (m *LocalServiceManager) stopService(language string, service *Service) error {
	log.Debugf("Stopping %s inference service", language)
	if service != nil {
		service.Kill()
	}
	m.serviceMu.Lock()
	delete(m.services, language)

	m.serviceMu.Unlock()
	return nil
}

func (m *LocalServiceManager) setupAndStartService(binaryPath, modelPath, runtimePath string) (*ls.LanguageService, error) {
	// Add modelRoot as a parameter when starting the local service
	// We reuse LangaugeService in ls package
	s := &ls.LanguageService{
		Command: exec.Command(binaryPath, m.localModelRoot, modelPath, runtimePath),
		Ctx:     context.Background(),
	}

	if runtime.GOOS == "windows" {
		s.Command = exec.Command(util.GetWindowsCmdPath())
		// 参考Go官方文档, 使用Command.SysProcAttr来设置参数, 见service_windows.go
		cmdArgs := fmt.Sprintf("/C \"\"%s\" \"%s\" \"%s\" \"%s\"\"", binaryPath, m.localModelRoot, modelPath, runtimePath)
		setCmdLine(s.Command, cmdArgs)
	}
	log.Debug("Starting service using: ", s.Command.String())

	// Bind IO
	stdin, err := s.Command.StdinPipe()
	if err != nil {
		log.Error("Cannot get command stdin & stdout: ", err)
		return nil, err
	}
	stdout, err := s.Command.StdoutPipe()
	if err != nil {
		log.Error("Cannot get command stdin & stdout: ", err)
		return nil, err
	}
	s.StdioConn = &ls.Connection{
		In:  stdout,
		Out: stdin,
	}

	// Set other command options
	s.Command.Dir = m.localModelRoot
	if runtime.GOOS == "windows" {
		s.Command.Dir = ""
	}
	s.Command.Stderr = ls.ErrHandler{}
	setProcessGroup(s.Command)

	// Create jsonrpc connection, start command
	s.Conn = jsonrpc2.NewConn(s.Ctx, jsonrpc2.NewBufferedStream(s.StdioConn, jsonrpc2.VSCodeObjectCodec{}), &ls.ClientHandler{})
	log.Debug("Starting local service using command: ", s.Command.String())
	log.Debug("Command env: ", s.Command.Env)
	err = s.Command.Start()
	if err != nil {
		log.Error("Failed to start local service: ", err)
		return nil, err
	}
	stable.GoSafe(context.Background(), func() {
		err := s.Command.Wait()
		if err != nil {
			log.Error("Unable to serve local completion: ", err)
		}
	}, stable.SceneSystem)

	return s, nil
}

// restart kills given service and start a new service of the language
func (m *LocalServiceManager) restart(s *Service, language string) {
	if !m.restartMutex.TryLock() {
		log.Warn("ignore duplication restart")
		return
	}
	defer m.restartMutex.Unlock()
	log.Info("Checking before restarting cosy local service")

	libFilePath, err := util.GetDynamicLibraryPath()
	if err != nil {
		log.Error("No dynamic library path found: ", err)
		return
	}
	if !util.PathExists(libFilePath) {
		log.Error("Re-download dynamic library is needed")
		dlClient := download.NewDownloadClient()
		if err != nil {
			log.Error("Cannot initialize client: ", err)
			return
		}
		if err = update.DoInferenceEnvUpdate(dlClient); err != nil {
			log.Error("Failed to re-download inference env: ", err)
			return
		}
	}

	// Restart service
	log.Info("Restarting cosy local service")
	ServiceManager.stopService(language, s)
	if err := ServiceManager.StartLocalService(language); err != nil {
		log.Error("Restart error: " + err.Error())
	}
}

func (m *LocalServiceManager) getUsedPaths(language string) (binaryPath, modelPath, runtimePath string, err error) {
	// Check platform
	platform := util.GetPlatform()
	if platform == "" {
		return binaryPath, modelPath, runtimePath, errors.New("unsupported platform")
	}

	// Check binary
	binaryPath = filepath.Join(m.wd, "bin", global.CosyVersion, platform, global.LingmaLocal)
	if platform == "x86_64_windows" {
		binaryPath += ".exe"
	}
	if !util.PathExists(binaryPath) {
		return binaryPath, modelPath, runtimePath, errors.New("binary doesn't exist at: " + binaryPath)
	}
	if err := os.Chmod(binaryPath, os.ModePerm); err != nil {
		log.Error("Failed to chmod LingmaLocal: ", err)
	}

	// Check version
	version, err := update.ReadLocalVersionConfig(filepath.Join(m.localModelRoot, "model"))
	global.ModelVersion = version
	if err != nil {
		return binaryPath, modelPath, runtimePath, errors.New("failed to load model version config")
	}

	// Use only new versions of models
	if global.ModelVersion == "0.0.1" || global.ModelVersion == "0.0.3" {
		return binaryPath, modelPath, runtimePath, errors.New("model file doesn't exist for new version")
	}

	// Check model path
	modelName := language + ".model"
	modelPath = filepath.Join(m.localModelRoot, "model", version)
	modelFullPath := filepath.Join(modelPath, modelName)
	if !util.PathExists(modelFullPath) {
		return binaryPath, modelPath, runtimePath, errors.New("model file doesn't exist at: " + modelFullPath)
	}
	if err := os.Chmod(modelFullPath, 0755); err != nil {
		log.Error("Failed to chmod local model: ", err)
	}

	// Check runtime path
	runtimePath, err = util.GetDynamicLibraryPath()
	if err != nil {
		return binaryPath, modelPath, runtimePath, errors.New("failed to get runtime path")
	}
	if !util.PathExists(runtimePath) {
		return binaryPath, modelPath, runtimePath, errors.New("cannot find env file, please restart IDE and retry")
	}

	return binaryPath, modelFullPath, runtimePath, nil
}

var serviceCheckTimer *cron.Cron

func InitializeLocalService(cosyHome string) {
	// Initialize local service manager
	ServiceManager = NewLocalServiceManager()

	// Start default service(Java service)
	ServiceManager.startDefaultService(cosyHome)

	// Add timer, check service state every 15 minutes
	serviceCheckTimer = cron.New()
	serviceCheckTimer.Start()

	// Report every 15 minutes
	_, _ = serviceCheckTimer.AddFunc("@every 15m", ServiceManager.checkServiceState)
}
