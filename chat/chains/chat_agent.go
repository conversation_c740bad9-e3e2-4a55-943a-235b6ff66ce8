package chains

import (
	"context"
	"cosy/config"
	"cosy/experiment"
	_ "embed"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"cosy/chat"
	"cosy/chat/agents/unittest/agent"
	"cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/chat/chains/quest"
	"cosy/chat/chains/stop"
	"cosy/chat/chains/ui2fecode"
	"cosy/chat/service"
	chatUtil "cosy/chat/util"
	"cosy/client"
	"cosy/components"
	"cosy/deepwiki"
	"cosy/deepwiki/storage"
	"cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/extension"
	"cosy/extension/rule"
	"cosy/filter"
	"cosy/global"
	"cosy/indexing"
	"cosy/log"
	"cosy/memory/eval"
	"cosy/memory/ltm"
	"cosy/memory/stm"
	"cosy/prompt"
	"cosy/remote"
	"cosy/sls"
	"cosy/sls/back_flow"
	"cosy/stable"
	"cosy/util"
	"cosy/websocket"

	"github.com/tmc/langchaingo/chains"
)

const (
	filterTimeout             = 28
	filterDomElementMaxLength = 100000

	defaultQueueTimePollInterval = 60
)

var chatFilterStage = sync.Map{}

type ChatService struct {
	Delegate            chains.Chain
	QuestDelegate       chains.Chain // quest模式流程
	QuestDesignDelegate chains.Chain // quest模式流程
	stopChannels        sync.Map
}

func InitializeChatService() *ChatService {

	requirementAnalysisChain := WorkspaceRagRequirementAnalysisChain{}
	workspaceRetrievalChain := WorkspaceRetrievalChain{
		Embedder: components.NewLingmaEmbedder(),
	}
	knowledgeRagChain := NewKnowledgeRagChain()
	knowledgeRagRequirementAnalysisChain := NewKnowledgeRagRequirementAnalysisChain()
	intentionDetectChain := IntentDetectChain{}
	aiDevelopIntentDetectChain := AIDevelopIntentDetectChain{}
	commonAgentIntentDetectChain := CommonAgentIntentDetectChain{}
	// AI 程序员 中的图生前端代码，需要用到#codebase自动装载，所以将 aiDevelop的意图识别提到了#codebase retrieval前面
	chatProcessChain := NewChatProcessChains([]chains.Chain{
		aiDevelopIntentDetectChain,
		requirementAnalysisChain,
		workspaceRetrievalChain,
		knowledgeRagRequirementAnalysisChain,
		knowledgeRagChain,
		intentionDetectChain,
		commonAgentIntentDetectChain,
	}, []string{common.KeyChatAskParams}, []string{})

	// 图生前端代码流程
	ui2FeCodeGeneratePlan := ui2fecode.UI2FeCodeGenerateChain{}
	devUI2FeCodeChains := ui2fecode.NewDevUI2FeCodeChains([]chains.Chain{ui2FeCodeGeneratePlan}, []string{common.KeyChatAskParams}, []string{})

	refineQueryChain := ltm.MemoryRefineQueryChain{}
	retrieveMemory := ltm.RetrieveLongTermMemoryChain{}
	memoryExtractChain := ltm.ExtractLongTermMemoryChain{}
	memoryEvalChain := eval.MemoryEvalChain{}
	wikiEvalChain := eval.WikiEvalChain{}
	memoryConsolidateChain := ltm.ConsolidateLongTermMemoryChain{}
	chatAskChain := ChatAskChain{}
	testAgentCain := agent.TestAgentChain{}
	commonDevAgentChain := CommonDevAgentChain{}
	projectRuleProcessChain := rule.NewProjectRuleProcessChain()

	chatChains := []chains.Chain{refineQueryChain, retrieveMemory, ChatProcessChains{Delegate: chatProcessChain}, projectRuleProcessChain, chatAskChain, testAgentCain, ui2fecode.DevUI2FeCodeChains{Delegate: devUI2FeCodeChains}, commonDevAgentChain, wikiEvalChain, memoryEvalChain, memoryExtractChain, memoryConsolidateChain}

	chatChains = chain.NewWrapperChains(chatChains)
	delegate, err := chains.NewSequentialChain(chatChains, []string{common.KeyChatAskParams}, []string{})
	if err != nil {
		log.Errorf("init chat service error. reason: %v", err)
		panic(err)
	}

	questAgentChain := quest.QuestChain{}
	questChains := []chains.Chain{
		retrieveMemory,
		questAgentChain,
		wikiEvalChain, memoryEvalChain, memoryExtractChain, memoryConsolidateChain,
	}
	questDelegate, err := chains.NewSequentialChain(questChains, []string{common.KeyChatAskParams}, []string{})
	if err != nil {
		log.Errorf("init chat service error. reason: %v", err)
		panic(err)
	}

	questDesignAgentChain := quest.QuestChain{}
	questDesignChains := []chains.Chain{
		retrieveMemory,
		questDesignAgentChain,
		wikiEvalChain,
		memoryEvalChain,
	}
	questDesignDelegate, err := chains.NewSequentialChain(questDesignChains, []string{common.KeyChatAskParams}, []string{})
	if err != nil {
		log.Errorf("init chat service error. reason: %v", err)
		panic(err)
	}

	c := ChatService{
		Delegate:            delegate,
		QuestDelegate:       questDelegate,
		QuestDesignDelegate: questDesignDelegate,
		stopChannels:        sync.Map{},
	}
	return &c
}

func (c *ChatService) Ask(ctx context.Context, params *definition.AskParams) (definition.AskResult, error) {
	askResult, err := c.doAsk(ctx, params)

	if askResult.ModelQueueStatus != nil && askResult.ModelQueueStatus.IsQueued {
		stopCh := make(chan struct{})
		c.stopChannels.Store(params.RequestId, stopCh)

		modelQueueStatus := askResult.ModelQueueStatus
		modelQueueStatus.RequestId = params.RequestId
		modelQueueStatus.SessionId = params.SessionId
		modelQueueStatus.SessionType = params.SessionType
		modelQueueStatus.Mode = params.Mode
		modelQueueStatusStr, e := json.Marshal(askResult.ModelQueueStatus)
		if e == nil {
			websocket.SendRequestWithTimeout(ctx, "chat/queue", modelQueueStatus, nil, 3*time.Second)
			log.Infof("common agent chat, model queuing, status: %s", string(modelQueueStatusStr))
		}

		stable.GoSafe(ctx, func() {
			pollInterval := experiment.ConfigService.GetIntConfigValue(definition.ExperimentQueuePollTimeInterval, experiment.ConfigScopeClient, defaultQueueTimePollInterval)
			queueTicker := time.NewTicker(time.Duration(pollInterval) * time.Second)
			defer func() {
				c.stopChannels.Delete(params.RequestId)
				queueTicker.Stop()
			}()

			for {
				select {
				case <-stopCh:
					return
				case <-queueTicker.C:
					modelQueueStatus, err := checkModelQueueStatus(
						params.RequestId, askResult.ModelQueueStatus.ModelKey, askResult.ModelQueueStatus.QueueType)
					log.Infof("check model queue status: isQueued = %v", modelQueueStatus.IsQueued)
					if err != nil {
						log.Errorf("Failed to check queue status: %v", err)
					} else {
						if !modelQueueStatus.IsQueued && modelQueueStatus.ServiceAvailable {
							params.Passkey = modelQueueStatus.Passkey
							params.ModelQueueType = modelQueueStatus.ModelKey
							chatStartParam := definition.ChatStart{
								RequestId:   params.RequestId,
								SessionId:   params.SessionId,
								SessionType: params.SessionType,
							}
							websocket.SendRequestWithTimeout(ctx, "chat/start", chatStartParam, nil, 3*time.Second)
							_, _ = c.doAsk(ctx, params)
							log.Infof("model queue passed")
							return
						} else {
							modelQueueStatus.RequestId = params.RequestId
							modelQueueStatus.SessionId = params.SessionId
							modelQueueStatus.SessionType = params.SessionType
							modelQueueStatus.Mode = params.Mode
							websocket.SendRequestWithTimeout(ctx, "chat/queue", modelQueueStatus, nil, 3*time.Second)
						}
					}
				}
			}
		}, stable.SceneChatAsk)

	}

	return askResult, err
}

func checkModelQueueStatus(requestSetId, modelKey, queueType string) (definition.ModelQueueStatusResult, error) {
	statusCheckUrl := fmt.Sprintf(definition.UrlPathAgentQueueStatus+"?requestSetId=%s&modelKey=%s&queueType=%s",
		requestSetId, modelKey, queueType)
	req, err := remote.BuildBigModelAuthRequest(http.MethodGet, statusCheckUrl, nil)
	if err != nil {
		log.Infof("Failed to build queue status request: %v", err)
		return definition.ModelQueueStatusResult{}, err
	}
	resp, e := client.GetDefaultClient().Do(req)
	if e != nil {
		log.Infof("Failed to check queue status: %v", e)
		return definition.ModelQueueStatusResult{}, e
	}
	defer resp.Body.Close()

	bodyBytes, e := io.ReadAll(resp.Body)
	if e != nil {
		log.Infof("Failed to read queue status response: %v", e)
	}
	if resp.StatusCode != http.StatusOK {
		log.Infof("Query model status failed. satatus code: %d. response body: %s", resp.StatusCode, bodyBytes)
	}
	var queueStatus definition.ModelQueueStatusResult
	e = json.Unmarshal(bodyBytes, &queueStatus)
	if err != nil {
		log.Infof("Failed to parse queue status response: %v, response body: %s", err, bodyBytes)
		return definition.ModelQueueStatusResult{}, err
	}
	return queueStatus, nil
}

func (c *ChatService) doAsk(ctx context.Context, params *definition.AskParams) (definition.AskResult, error) {
	fileIndexer, ok := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if !ok {
		log.Warn("no fileIndexer found in context")
	}

	contextProviderExtras := PrepareContextProviderExtras(ctx, params)

	params = chatUtil.TransformChatContext(ctx, params, contextProviderExtras, fileIndexer)

	filteredInputParams, err := doContentFilter(ctx, params)
	if err != nil {
		log.Error("do content filter error.", err)
		return definition.AskResult{}, err
	}
	if filteredInputParams == nil {
		return definition.AskResult{}, err
	}

	//if params.Mode != definition.SessionModeAgent {
	//	params.SessionType = definition.SessionTypeQuest
	//	params.Mode = definition.SessionModeDesign
	//}
	log.Infof("doAsk params.SessionType: %s, SessionMode: %s", params.SessionType, params.Mode)

	inputs, err := c.buildChainInputs(ctx, *filteredInputParams, contextProviderExtras)
	if err != nil {
		log.Errorf("prepare ask input error. error: %v", err)
		return definition.AskResult{}, err
	}

	// 设置语言偏好
	ctx = context.WithValue(ctx, common.KeyPreferredLanguage, inputs[common.KeyLocaleLanguage].(string))
	fillParseUserInputQueryWithContexts(inputs, contextProviderExtras)

	inputs[common.KeyContextProviderExtra] = contextProviderExtras

	var workspacePath = ""
	if workspaceInfo, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo); ok {
		workspacePath, _ = workspaceInfo.GetWorkspaceFolder()
		inputs[common.KeyWorkSpacePath] = workspacePath
	}
	backFlowEnabled := back_flow.BackFlowEnabled()
	var delayedReporter *back_flow.AgentStartReporter
	if backFlowEnabled {
		codebaseId, _ := components.GetCodebaseId(ctx, workspacePath)
		delayedReporter = back_flow.NewAgentStartReporterWithTimeout(
			ctx, params.SessionId, params.RequestId, workspacePath, codebaseId, params,
			back_flow.DefaultAgentStartTimeout,
		)
	}

	runChains := c.Delegate
	if params.SessionType == definition.SessionTypeQuest {
		if params.Mode == definition.SessionModeDesign {
			runChains = c.QuestDesignDelegate
		} else {
			runChains = c.QuestDelegate
		}
	}
	//chains执行完成需要删除停止请求标志
	defer func() {
		stop.StopRequest.Delete(params.RequestId)
	}()
	output, err := chains.Call(ctx, runChains, inputs)

	if global.IsChatCodeUpdateWikiEnable() {
		go deepwiki.UpdateChatCode(ctx, definition.CreateDeepwikiRequest{
			SessionId:         params.SessionId,
			RequestId:         params.RequestId,
			PreferredLanguage: global.PreferredLanguage,
			WorkspacePath:     workspacePath,
		})
	}

	go trackTriggerChat(ctx, params, workspacePath)
	go reportChatTokenMessage(params)

	if err != nil {
		var errReason = "chat process error"
		var errCode = cosyErrors.SystemError

		var chatErr *cosyErrors.Error
		if errors.As(err, &chatErr) {
			errReason = chatErr.Message
			errCode = chatErr.Code
		}

		chatFinish := definition.ChatFinish{
			RequestId:  params.RequestId,
			SessionId:  params.SessionId,
			Reason:     errReason,
			StatusCode: errCode,
		}
		chat.PushChatFinishMessage(ctx, chatFinish)
		return definition.AskResult{}, err
	}
	chainTimeRecorder, ok := output[definition.KeyChainTimeRecorder].(*util.TimeRecorder)
	if ok {
		sls.Report(sls.EventTypeChatChainPerformance, params.RequestId, chainTimeRecorder.Export())
	}
	chatAskResult, ok := output[common.KeyChatAskResult].(definition.AskResult)
	if ok {
		modelQueueStatus, ok2 := output[common.KeyModelQueueStatus].(*definition.ModelQueueStatusResult)
		if ok2 {
			chatAskResult.ModelQueueStatus = modelQueueStatus
		}

		needBackFlow := false
		isQuest := params.SessionType == definition.SessionTypeQuest
		isAgent := params.SessionType == definition.SessionTypeAssistant && params.Mode == definition.SessionModeAgent
		isAsk := params.SessionType == definition.SessionTypeAssistant && params.Mode == definition.SessionModeChat
		isQuestDesign := isQuest && params.Mode == definition.SessionModeDesign
		if backFlowEnabled && delayedReporter != nil {
			needBackFlow = needBackFlow || isQuest // quest design & quest action 都需要回流
			needBackFlow = needBackFlow || isAgent // agent 都需要回流
			needBackFlow = needBackFlow || isAsk   // ask 都需要回流
			has, ok := output[common.KeyChatNeedBackFlow].(bool)
			needBackFlow = needBackFlow || (has && ok)
		}

		// 判断如果修改了代码，则上报回流数据
		if needBackFlow {
			messages, _ := stm.GetMessageHistoryByRequestId(params.RequestId)
			vos, _ := service.WorkingSpaceServiceManager.ListWorkingSpaceFileVOsByChatRecord(ctx, params.SessionId, params.RequestId, true)
			util.GoSafeRoutine(func() {
				// 计算diff可能会比较耗时，新开一个 go routine
				var diffInfos []back_flow.FileDiffInfo
				if params.ChatTask == definition.CHAT_TASK_INLINE_CHAT {
					// inline chat 没有 diffInfos
				} else {
					// agent
					if isQuestDesign {
						// quest design 下，需要回流最终生成的 spec
						info := back_flow.FileDiffInfo{}
						taskInfo, ok := ctx.Value(definition.ContextKeyQuestTaskSessionInfo).(*definition.TaskSessionInfo)
						if ok && taskInfo != nil && taskInfo.TaskInfo != nil && taskInfo.TaskInfo.DesignPath != "" {
							info.FilePath = taskInfo.TaskInfo.DesignPath
							if content, err := util.ReadFileContentByFilePath(taskInfo.TaskInfo.DesignPath, 0); err == nil {
								info.Content = content
							} else {
								log.Errorf("[back-flow][%s][%s] read design file error, path=%s, error=%v", params.SessionId, params.RequestId, taskInfo.TaskInfo.DesignPath, err)
							}
						}
						diffInfos = append(diffInfos, info)
					} else {
						// 获取 diff info
						diffInfos = back_flow.GetChatFileDiffInfos(ctx, params.SessionId, params.RequestId, vos)
					}
				}

				inlineChatResult, _ := output[common.KeyInlineChatResult].(string)
				promptUserInput := output[common.KeyPromptUserInput] // TODO: prompt system input
				promptSystemInput := output[common.KeyPromptSystemInput]

				delayedReporter.ReportAtAgentFinish(
					ctx, messages, diffInfos,
					promptSystemInput, promptUserInput,
					"inline_chat_response", inlineChatResult,
					"ask_use_tool", strconv.FormatBool(config.GetAskModeUseTools()),
				)
			})
		}

		return chatAskResult, nil
	}
	// 如果当前request已经stop，就没有必要在报chat result not found
	if stop.IsUsrStop(params.RequestId) {
		log.Debugf("chat result not found, requestId=%s", params.RequestId)
		return definition.AskResult{
			RequestId: params.RequestId,
		}, nil
	}
	log.Warnf("chat result not found, requestId=%s", params.RequestId)
	return definition.AskResult{
		RequestId: params.RequestId,
	}, errors.New("chat result not found")
}

func (c *ChatService) StopPolling(param *definition.ChatStopParam) bool {
	if ch, ok := c.stopChannels.LoadAndDelete(param.RequestId); ok {
		close(ch.(chan struct{}))
		return true
	}
	return false
}

func reportChatTokenMessage(params *definition.AskParams) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("["+params.RequestId+"] ", "reportChatTokenMessage panic: %v", r, " panic stack: %s", string(debug.Stack()))
		}
	}()

	chatMessages, err := service.SessionServiceManager.GetChatMessageByRequest(params.RequestId)
	if err != nil {
		log.Errorf("reportChatTokenMessage error. error: %v", err)
		return
	}

	totalPromptTokens := 0
	totalCompletionTokens := 0
	totalCachedTokens := 0
	toolCallTimes := 0
	modelCallTimes := 0
	maxInputTokens := 0
	modelConfig := chatUtil.PrepareModelConfig(*params)
	model := ""
	if modelConfig != nil {
		maxInputTokens = modelConfig.MaxInputTokens
		model = modelConfig.Key
	}

	parallelToolCallTimes := 0
	counterAssistantMsg := false

	for index := range chatMessages {
		chatMessage := chatMessages[index]
		if chatMessage.Role == "tool" {
			toolCallTimes++
			if counterAssistantMsg && index > 0 && chatMessages[index-1].Role == "tool" {
				parallelToolCallTimes++
				counterAssistantMsg = false
			}
		} else if chatMessage.Role == "assistant" {
			modelCallTimes++
			counterAssistantMsg = true
			tokenInfo := common.ChatTokenCountInfo{}
			err := json.Unmarshal([]byte(chatMessage.TokenInfo), &tokenInfo)
			if err != nil {
				continue
			}
			totalPromptTokens += tokenInfo.PromptTokens
			totalCompletionTokens += tokenInfo.CompletionTokens
			totalCachedTokens += tokenInfo.CachedTokens
			tokenInfo.MaxInputTokens = maxInputTokens
			newTokenInfo, err := json.Marshal(tokenInfo)
			if err == nil {
				service.SessionServiceManager.UpdateChatMessageTokenInfo(chatMessage.Id, string(newTokenInfo))
			}
		}
	}

	tokenInfo := map[string]string{
		"model":                    model,
		"mode":                     params.Mode,
		"session_id":               params.SessionId,
		"request_id":               params.RequestId,
		"prompt_tokens":            strconv.Itoa(totalPromptTokens),
		"completion_tokens":        strconv.Itoa(totalCompletionTokens),
		"cached_tokens":            strconv.Itoa(totalCachedTokens),
		"tool_call_times":          strconv.Itoa(toolCallTimes),
		"model_call_times":         strconv.Itoa(modelCallTimes),
		"max_input_tokens":         strconv.Itoa(maxInputTokens),
		"parallel_tool_call_times": strconv.Itoa(parallelToolCallTimes),
	}
	tokenInfoJson, err := json.Marshal(tokenInfo)
	if err != nil {
		log.Errorf("reportChatTokenMessage json.Marshal error. error: %v", err)
		return
	}

	log.Debugf("[total usage count]data:%v", string(tokenInfoJson))
	sls.Report(sls.EventTypeChatLLMTokenInfo, params.RequestId, tokenInfo)
}

// trackTriggerChat 函数用于记录触发聊天的统计信息
func trackTriggerChat(ctx context.Context, params *definition.AskParams, workspacePath string) {
	defer func() {
		if r := recover(); r != nil {
			log.Error("["+params.RequestId+"] ", "trackTriggerChat panic: %v", r, " panic stack: %s", string(debug.Stack()))
		}
	}()
	pluginConfigJson, _ := json.Marshal(params.PluginPayloadConfig)
	eventData := map[string]string{
		"session_type":   transformSlsSessionType(params.SessionType),
		"chat_task":      params.ChatTask,
		"session_id":     params.SessionId,
		"request_id":     params.RequestId,
		"request_set_id": params.RequestId,
		"chat_record_id": params.RequestId,
		"chat_mode":      params.Mode,
		"workspacePath":  workspacePath,
		"plugin_config":  string(pluginConfigJson),
	}
	wikiStatusCounts, err := storage.GlobalStorageService.GetWikiItemStatusByWorkspace(workspacePath)
	if err == nil {
		for status, count := range wikiStatusCounts {
			eventData[fmt.Sprintf("wiki_status_%s", status)] = strconv.Itoa(count)
		}
	}
	sls.Report(sls.EventTypeChatTriggerInfo, params.RequestId, eventData)
}

// prepareContextProviderExtras 准备并处理上下文提供者额外信息
// 该函数从 params.Extra 中提取定义好的上下文信息，并根据不同的上下文提供者类型进行处理
// 它使用多个 goroutine 来并行处理每个上下文提供者的解析项，以提高效率
// 最终，它将处理过的上下文提供者信息重新赋值给 params.Extra，以供后续使用
func PrepareContextProviderExtras(ctx context.Context, params *definition.AskParams) []definition.CustomContextProviderExtra {
	// 尝试从 params.Extra 中获取上下文提供者额外信息
	contextProviderExtraValue, ok := params.Extra[definition.ChatExtraKeyContext]
	if !ok {
		// 如果没有找到上下文提供者信息，则记录日志并返回 nil
		log.Debugf("no contextProvider in params: %v", params)
		return nil
	}

	// 初始化上下文提供者额外信息的切片
	var contextProviderExtras []definition.CustomContextProviderExtra

	// 将提取到的上下文提供者额外信息反序列化到切片中
	if err := util.UnmarshalToObject(util.ToJsonStr(contextProviderExtraValue), &contextProviderExtras); err != nil {
		// 如果反序列化过程中出现错误，则记录警告日志并返回 nil
		log.Warnf("unmarshal contextProvider extras error: %v", err)
		return nil
	}

	// 如果反序列化后的上下文提供者额外信息为空，则直接返回 nil
	if len(contextProviderExtras) <= 0 {
		return nil
	}

	// 设置一个延迟函数，用于捕获并记录可能出现的 panic 错误
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("recover from prepare contexts. contextProviderExtras: %+v, err: %+v", contextProviderExtras, r)
		}
	}()

	// 初始化一个过滤后的上下文提供者额外信息的切片
	var filteredContextProviders []definition.CustomContextProviderExtra

	// 启动多个 goroutine 进行读操作
	var wg sync.WaitGroup
	var mu sync.Mutex
	// 遍历每个上下文提供者额外信息
	for i := 0; i < len(contextProviderExtras); i++ {
		contextProvider := &contextProviderExtras[i]

		// 根据不同的上下文提供者类型进行相应的处理
		if contextProvider.Name == definition.PlatformContextProviderSelectedCode {
			//圈选代码场景
			contextProvider.ParsedContextItems = append(contextProvider.ParsedContextItems, definition.ParsedContextItem{
				ContextItem: definition.ContextItem{
					Identifier: contextProvider.SelectedItem.Identifier,
					Key:        contextProvider.SelectedItem.Name,
					Value:      contextProvider.SelectedItem.Content,
					Extra:      contextProvider.SelectedItem.Extra,
				},
			})
			filteredContextProviders = append(filteredContextProviders, *contextProvider)
		} else if contextProvider.Name == definition.PlatformContextProviderImage {
			//图片场景
			contextProvider.ParsedContextItems = append(contextProvider.ParsedContextItems, definition.ParsedContextItem{
				ContextItem: definition.ContextItem{
					Identifier: contextProvider.SelectedItem.Identifier,
					Key:        contextProvider.SelectedItem.Name,
					Value:      contextProvider.SelectedItem.Content,
					Extra:      contextProvider.SelectedItem.Extra,
				},
			})
			filteredContextProviders = append(filteredContextProviders, *contextProvider)
		} else if contextProvider.Name == definition.PlatformContextProviderTeamDocs {
			//teamdoc场景
			if contextProvider.SelectedItem.Identifier != "" {
				contextProvider.ParsedContextItems = append(contextProvider.ParsedContextItems, definition.ParsedContextItem{
					ContextItem: definition.ContextItem{
						Identifier: contextProvider.SelectedItem.Identifier,
						Key:        contextProvider.SelectedItem.Name,
						Extra:      contextProvider.SelectedItem.Extra,
					},
				})
			}
			filteredContextProviders = append(filteredContextProviders, *contextProvider)
		} else if contextProvider.Name == definition.PlatformContextProviderCodebase {
			// 团队文档或代码库场景，直接将上下文提供者信息添加到过滤后的切片中
			filteredContextProviders = append(filteredContextProviders, *contextProvider)
		} else if contextProvider.Name == definition.PlatformContextProviderFile && isImageFile(contextProvider) {
			//图片文件场景
			contextProvider.ParsedContextItems = append(contextProvider.ParsedContextItems, definition.ParsedContextItem{
				ContextItem: definition.ContextItem{
					Identifier: contextProvider.SelectedItem.Identifier,
					Key:        contextProvider.SelectedItem.Name,
					Extra:      contextProvider.SelectedItem.Extra,
				},
			})
			filteredContextProviders = append(filteredContextProviders, *contextProvider)
		} else if contextProvider.Name == definition.PlatformContextProviderDomElement {
			//preview中圈选页面元素
			content := contextProvider.SelectedItem.Content
			if utf8.RuneCountInString(content) > filterDomElementMaxLength {
				truncateContent, err := util.TruncateStringByRuneCount(content, filterDomElementMaxLength)
				if err == nil {
					content = truncateContent
				}
			}
			contextProvider.ParsedContextItems = append(contextProvider.ParsedContextItems, definition.ParsedContextItem{
				ContextItem: definition.ContextItem{
					Identifier: contextProvider.SelectedItem.Identifier,
					Key:        contextProvider.SelectedItem.Name,
					Value:      content,
					Extra:      contextProvider.SelectedItem.Extra,
				},
			})
			filteredContextProviders = append(filteredContextProviders, *contextProvider)
		} else if contextProvider.Name == definition.PlatformContextProviderTerminal {
			content := contextProvider.SelectedItem.Content
			contentAfterTruncate := chatUtil.TruncateTerminalLines(content)
			contextProvider.ParsedContextItems = append(contextProvider.ParsedContextItems, definition.ParsedContextItem{
				ContextItem: definition.ContextItem{
					Identifier: contextProvider.SelectedItem.Identifier,
					Key:        contextProvider.SelectedItem.Name,
					Value:      contentAfterTruncate,
					Extra:      contextProvider.SelectedItem.Extra,
				},
			})
			filteredContextProviders = append(filteredContextProviders, *contextProvider)

		} else if contextProvider.Name == definition.PlatformContextProviderQuestDesign {
			//
		} else {
			// 对于其他类型的上下文提供者，启动一个新的 goroutine 来处理
			wg.Add(1)
			stable.GoSafe(ctx, func() {
				defer wg.Done()
				mu.Lock()
				defer mu.Unlock()

				// 在 goroutine 中调用 getContextItem 函数来获取上下文项
				contextItems, err := getContextItem(ctx, *contextProvider, params)
				if err != nil {
					// 如果出现错误，则记录错误日志
					log.Errorf("get contextItem error. contextProvider: %s, err: %v", contextProvider.Name, err)
				} else {
					// 如果成功获取到上下文项，则将其添加到过滤后的上下文提供者额外信息中z
					contextProvider.ParsedContextItems = contextItems
					filteredContextProviders = append(filteredContextProviders, *contextProvider)
				}
			}, stable.SceneChatAsk)
		}
	}

	// 等待所有 goroutine 完成
	wg.Wait()

	//上报context埋点
	go reportContextToSls(params, contextProviderExtras)

	// 将过滤后的上下文提供者额外信息重新赋值给 params.Extra，覆盖原有的输入上下文
	params.Extra[definition.ChatExtraKeyContext] = filteredContextProviders
	// 返回处理过的上下文提供者额外信息
	return filteredContextProviders
}

func reportContextToSls(params *definition.AskParams, contextProviderExtras []definition.CustomContextProviderExtra) {
	eventData := make(map[string]string)
	eventData["sessionType"] = params.SessionType
	eventData["mode"] = params.Mode
	eventData["chatTask"] = params.ChatTask

	var extraItems []definition.ContextProviderExtraItem
	for _, provider := range contextProviderExtras {
		extraItems = append(extraItems, definition.ContextProviderExtraItem{
			Identifier: provider.Identifier,
			Name:       provider.Name,
			SourceType: "system",
		})
	}

	if valueBytes, err := json.Marshal(extraItems); err == nil {
		eventData["contextExtraItem"] = string(valueBytes)
	} else {
		log.Errorf("marshal contextExtraItem error: %v", err)
		eventData["contextExtraItem"] = "[]"
	}

	sls.Report(sls.EventTypeChatContextManualImport, params.RequestId, eventData)
}

func fillParseUserInputQueryWithContexts(inputs map[string]any, contextProviderExtras []definition.CustomContextProviderExtra) {
	userInputQuery := inputs[common.KeyUserInputQuery]
	userQuery := userInputQuery.(string)
	if len(contextProviderExtras) <= 0 {
		inputs[common.KeyParsedUserInputQueryWithContexts] = userQuery
		return
	}
	contextDetails := chatUtil.ConvertContextProviderExtras(contextProviderExtras)
	parsedUserQuery := prompt.ParseUserQueryWithContexts(userQuery, contextDetails)
	inputs[common.KeyParsedUserInputQueryWithContexts] = parsedUserQuery
}

func getContextItem(ctx context.Context, extra definition.CustomContextProviderExtra, params *definition.AskParams) ([]definition.ParsedContextItem, error) {
	ideSdk := extension.BuildSdkTool(ctx)
	request := buildContextProviderRequest(ctx, extra)
	payload := buildContextPayload(params)
	getContextResponse, invokeErr := extension.InvokeContextProvider(ctx, extra.Identifier, request, ideSdk, payload)
	if invokeErr != nil {
		log.Errorf("invoke context provider error. identifier: %s, err: %+v", extra.Identifier, invokeErr)
		return nil, errors.New("invoke context provider error")
	}
	if getContextResponse.ContextItems == nil {
		return nil, nil
	}
	var parsedContextItems []definition.ParsedContextItem
	for _, contextItem := range getContextResponse.ContextItems {
		parsedContextItems = append(parsedContextItems, definition.ParsedContextItem{
			ContextItem: contextItem,
		})
	}
	return parsedContextItems, nil
}

func buildContextPayload(params *definition.AskParams) definition.ContextPayload {
	chatContext := params.ChatContext.(map[string]interface{})
	userInputUserQuery, ok := chatContext["text"].(string)
	if !ok {
		userInputUserQuery = ""
	}
	userInputUserQuery = definition.ReplaceAllContextInfo(userInputUserQuery)
	payload := definition.ContextPayload{
		UserInputText: userInputUserQuery,
	}
	return payload
}

func buildContextProviderRequest(ctx context.Context, extra definition.CustomContextProviderExtra) definition.GetContextRequest {
	switch extra.ComponentType {
	case extension.ContextProviderTypeGeneral:
		return definition.GetContextRequest{}
	case extension.ContextProviderTypeComboBox:
		return definition.GetContextRequest{
			Query: extra.SelectedItem.Identifier,
		}
	case extension.ContextProviderTypeQuery:
		//TODO
	}
	return definition.GetContextRequest{}
}

func (c *ChatService) buildChainInputs(ctx context.Context, params definition.AskParams, contextProviderExtras []definition.CustomContextProviderExtra) (map[string]any, error) {
	inputs := map[string]any{}
	inputs[common.KeyChatAskParams] = &params
	inputs[common.KeyRequestId] = params.RequestId
	inputs[common.KeySessionId] = params.SessionId

	chatContextFeatures, err := parseChatContextFeatures(params.ChatContext)
	if err != nil {
		return nil, errors.New("parse chat context features error")
	}
	inputs[common.KeyAskParamFeatures] = chatContextFeatures
	inputs[common.KeyLocaleLanguage] = ParseLocalLanguage(params.ChatContext)

	userInputUserQuery := chain.GetUserInputQuery(params.ChatContext)
	inputs[common.KeyUserInputQuery] = userInputUserQuery

	enableWorkspaceRag := false
	workspaceFeatureEnabled := chain.IsWorkspaceFeatureEnabled(chatContextFeatures)
	if workspaceFeatureEnabled || getSpecificContextProvider(definition.PlatformContextProviderCodebase, contextProviderExtras) != nil {
		enableWorkspaceRag = true
	}
	enableKnowledgeRag := false
	if chain.IsTeamDocsFeatureEnabled(chatContextFeatures) || getSpecificContextProvider(definition.PlatformContextProviderTeamDocs, contextProviderExtras) != nil {
		enableKnowledgeRag = true
	}
	inputs[common.KeyEnableWorkspaceRag] = enableWorkspaceRag
	inputs[common.KeyEnableKnowledgeRag] = enableKnowledgeRag
	inputs[common.KeyEnableWorkspace] = workspaceFeatureEnabled
	inputs[definition.KeyChainTimeRecorder] = util.NewTimeRecorder()
	if strings.HasPrefix(params.ChatTask, "QUEST_") {
		if info, ok := params.Extra[definition.ChatExtraKeyQuestTaskInfo].(map[string]interface{}); ok {
			taskInfo := definition.TaskInfo{}
			jsonStr, err := json.Marshal(info)
			if err != nil {
				log.Error(err)
			}
			err = json.Unmarshal(jsonStr, &taskInfo)
			if err != nil {
				log.Error(err)
			}
			inputs[common.KeyQuestTaskInfo] = &taskInfo
		}
	}
	return inputs, nil
}

func parseChatContextFeatures(chatContext interface{}) ([]definition.ChatAskFeature, error) {
	var chatContextMap map[string]any
	if _, ok := chatContext.(map[string]any); !ok {
		err := util.UnmarshalToObject(util.ToJsonStr(chatContext), &chatContextMap)
		if err != nil {
			log.Warn("parsing chat context error", err)
			return nil, err
		}
	} else {
		chatContextMap = chatContext.(map[string]any)
	}
	if chatContextMap == nil {
		return nil, nil
	}
	var chatFeatures []definition.ChatAskFeature
	features, ok := chatContextMap["features"].([]interface{})
	if len(features) <= 0 || !ok {
		return nil, nil
	}
	err := util.UnmarshalToObject(util.ToJsonStr(features), &chatFeatures)
	if err != nil {
		return nil, errors.New("parse chat context features error")
	}
	return chatFeatures, nil
}

func ParseLocalLanguage(chatContext interface{}) string {
	var chatContextMap map[string]any
	if _, ok := chatContext.(map[string]any); !ok {
		err := util.UnmarshalToObject(util.ToJsonStr(chatContext), &chatContextMap)
		if err != nil {
			log.Warn("parsing chat context error", err)
			return definition.LocaleEn
		}
	} else {
		chatContextMap = chatContext.(map[string]any)
	}

	localeLang, ok := chatContextMap["preferredLanguage"].(string)
	if localeLang == "" || !ok {
		return definition.LocaleEn
	}
	//兼容 中文：zh-cn
	if strings.Contains(localeLang, "zh") {
		return definition.LocaleZh
	}
	return localeLang
}

func doContentFilter(ctx context.Context, params *definition.AskParams) (*definition.AskParams, error) {
	if extension.ApiExecutor == nil || !extension.ApiExecutor.CheckContentHandlerExists(filter.BizTypeChat) {
		return params, nil
	}

	time.AfterFunc(filterTimeout*time.Second, func() {
		if _, ok := chatFilterStage.Load(params.RequestId); !ok {
			return
		}
		if stage, ok := chatFilterStage.Load(params.RequestId); ok && stage == filter.ChatStageFiltering {
			log.Debug("Filter timeout")
			chatFilterTimeout := definition.ChatFilterTimeout{
				RequestId:  params.RequestId,
				SessionId:  params.SessionId,
				StatusCode: 200,
			}
			chatFilterStage.Delete(params.RequestId)
			err := websocket.SendRequestWithTimeout(ctx, "chat/filterTimeout",
				chatFilterTimeout, nil, 3*time.Second)
			if err != nil {
				log.Info("Filter timeout. Send chat/filterTimeout error: ", err)
			}
		}
	})

	chatFilterStage.Store(params.RequestId, filter.ChatStageFiltering)

	// 发送过滤中状态事件
	e := websocket.SendRequestWithTimeout(ctx, "chat/doFiltering",
		definition.ChatFilteringStatus{
			RequestId: params.RequestId,
			SessionId: params.SessionId,
		}, nil, 3*time.Second)
	if e != nil {
		log.Debug("Send request chat/doFiltering error:", e)
	}

	filteredInputParams, status, err := filter.FilterAskParams(ctx, *params)
	if err != nil {
		chatFilterStage.Delete(params.RequestId)
		return params, errors.New("error filtering raw chat input")
	}
	if status == filter.StatusBlocked {
		chatFilterStage.Delete(params.RequestId)
		chatFinish := definition.ChatFinish{
			RequestId:  params.RequestId,
			SessionId:  params.SessionId,
			Reason:     "chat blocked by filter",
			StatusCode: cosyErrors.FilterBlocked,
		}
		chat.PushChatFinishMessage(ctx, chatFinish)
		return nil, nil
	}
	filter.SetFilterStatus(params.RequestId, status)

	chatFilterStage.Store(params.RequestId, filter.ChatStageFilterFinish)

	go func() {
		defer func() {
			if err := recover(); err != nil {
				log.Error("Recovered in doContentFilter", err)
			}
		}()
		timer := time.After(1 * time.Minute)

		// 非阻塞地等待信号
		select {
		case <-timer:
			chatFilterStage.Delete(params.RequestId)
		}
	}()

	return &filteredInputParams, nil
}
