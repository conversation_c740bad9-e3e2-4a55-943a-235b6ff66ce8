package util

import (
	"strings"
)

// 参数校验：确保 TerminalHeadLines + TerminalTailLines 不超过 TerminalMaxLine 的剩余空间
const (
	TerminalMaxLine           = 210
	TerminalHeadLines         = 50
	TerminalTailLines         = 150
	TerminalMaxContentLength  = 60000
	TerminalHeadContentLength = 20000
	TerminalTailContentLength = 40000
)

func TruncateTerminalLines(content string) string {
	ellipsis := "// This is the omitted part"
	// 统一换行符为\n，适配各种换行方法(\n, \r\n, \r)
	normalizedContent := normalizeLineEndings(content)

	// 按行分割
	lines := strings.Split(normalizedContent, "\n")
	totalLines := len(lines)
	// 直接返回原内容（无需截断）
	if totalLines <= TerminalMaxLine {
		return content
	}
	// 截取开头和结尾部分
	head := lines[:TerminalHeadLines]
	tail := lines[len(lines)-TerminalTailLines:]
	// 组合截断内容（头 + 省略号 + 尾）
	var truncated []string
	truncated = append(truncated, head...)
	truncated = append(truncated, ellipsis)
	truncated = append(truncated, tail...)
	// 如果组合后的行数仍超过 maxLines（极端情况处理）
	if len(truncated) > TerminalMaxLine {
		// 动态调整头尾行数以确保不超过 maxLines
		excess := len(truncated) - TerminalMaxLine
		if excess > 0 {
			// 优先保留尾部，减少头部行数
			head = head[:TerminalHeadLines-excess]
			truncated = append(head, ellipsis)
			truncated = append(truncated, tail...)
		}
	}

	contentAfterTruncate := strings.Join(truncated, "\n")
	// 如果内容长度小于等于最大长度限制，直接返回原内容
	if len(contentAfterTruncate) <= TerminalMaxContentLength {
		return contentAfterTruncate
	}

	// 如果内容超过最大长度限制，则截取前headContentLength和后tailContentLength部分，中间用ellipsis连接
	headAfterTruncate := contentAfterTruncate[:TerminalHeadContentLength]
	tailAfterTruncate := contentAfterTruncate[len(contentAfterTruncate)-TerminalTailContentLength:]

	return headAfterTruncate + "\n" + ellipsis + "\n" + tailAfterTruncate
}

// normalizeLineEndings 统一换行符为\n，适配各种换行方法(\n, \r\n, \r)
func normalizeLineEndings(content string) string {
	normalizedContent := content
	if strings.Contains(normalizedContent, "\r\n") {
		// Windows换行符\r\n
		normalizedContent = strings.ReplaceAll(normalizedContent, "\r\n", "\n")
	}
	if strings.Contains(normalizedContent, "\r") {
		// Mac旧版换行符\r
		normalizedContent = strings.ReplaceAll(normalizedContent, "\r", "\n")
	}
	return normalizedContent
}
