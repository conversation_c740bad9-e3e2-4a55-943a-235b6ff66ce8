// 在 common_test.go 中添加测试
package support

import (
	"testing"
)

// 定义应该支持的所有语言代码
// 新增LanguageCodeMap语言后需要在这里同步补充，否则会导致测试用例不通过
var expectedLanguageCodes = map[string]string{
	"en":    "English",
	"zh":    "中文",
	"zh-cn": "中文",
	"es":    "Español",
	"fr":    "Français",
	"pt":    "Português",
	"ja":    "日本語",
	"de":    "Deutsch",
	"ko":    "한국어",
}

// 注意!这里的测试是为了保证 quest_requirement_naming_system_prompt.txt 中的语言已经齐全
// 如果这里报错，说明go端新增了语言，需要同步增加 quest_requirement_naming_system_prompt.txt 的语言case
// 否则会导致该语言在quest的标题生成存在效果不佳等问题
func TestConvertLanguageCodeToFullName(t *testing.T) {
	// 检查 expectedLanguageCodes 是否包含 languageCodeMap 中的所有键
	for code := range LanguageCodeMap {
		if _, exists := expectedLanguageCodes[code]; !exists {
			t.<PERSON><PERSON><PERSON>("Language code '%s' exists in languageCodeMap but missing in expectedLanguageCodes", code)
		}
	}

	// 检查 languageCodeMap 是否包含 expectedLanguageCodes 中的所有键
	for code := range expectedLanguageCodes {
		if _, exists := LanguageCodeMap[code]; !exists {
			t.Errorf("Language code '%s' exists in expectedLanguageCodes but missing in languageCodeMap", code)
		}
	}

	// 检查对应的值是否一致
	for code, expectedName := range expectedLanguageCodes {
		if actualName, exists := LanguageCodeMap[code]; exists {
			if actualName != expectedName {
				t.Errorf("Language code '%s': expected name '%s' in expectedLanguageCodes, but got '%s' in languageCodeMap",
					code, expectedName, actualName)
			}
		}
	}
}
