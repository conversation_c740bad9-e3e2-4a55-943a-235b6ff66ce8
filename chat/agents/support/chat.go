package support

import (
	"context"
	"cosy/experiment"
	"cosy/sse"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/tool/web"
	chatUtil "cosy/chat/util"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/sls"
	"cosy/stable"
	"cosy/user"
	cosyUtil "cosy/util"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	agentLlms "code.alibaba-inc.com/cosy/lingma-agent-graph/llms"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/util"

	"github.com/spf13/cast"
)

const ModelSourceSystem = "system"
const Qwen3ThinkModelKeyPrefix = "dashscope_qwen_"
const Qwen3ThinkingModelKeySuffix = "_thinking"

func BuildRemoteChatMessageParam(ctx context.Context, params *definition.AskParams, messages []*agentDefinition.Message, tools []agentLlms.Tool, mode string) (definition.RemoteChatAsk, error) {
	remoteChatAsk := definition.RemoteChatAsk{
		RequestId: params.RequestId,
		//AI Developer本期与requestId一致
		RequestSetId:            params.RequestId,
		ChatRecordId:            params.RequestId,
		ChatTask:                params.ChatTask,
		ChatContext:             params.ChatContext,
		IsReply:                 params.IsReply,
		SessionId:               params.SessionId,
		CodeLanguage:            params.CodeLanguage,
		Source:                  params.Source,
		Stream:                  params.Stream,
		Version:                 "3",
		CustomSystemRoleContent: params.CustomSystemRoleContent,
		Parameters:              params.Parameters,
		UserType:                user.GetUserType(),
		TaskDefinitionType:      params.TaskDefinitionType,
		SessionType:             params.SessionType,
		AgentId:                 definition.AgentCommonAgentId,
		TaskId:                  "common",
		Passkey:                 params.Passkey,
	}

	// ask模式的task id使用单独的
	if mode == definition.SessionModeChat {
		remoteChatAsk.AgentId = definition.AgentAskAgentId
		remoteChatAsk.TaskId = definition.AgentChatModeAgentTaskId
	}

	// 发起问答的时间检查了是否有图片，是否是多模态模型 // TODO 上线前可以去掉
	//if IsMultiVlMessageRequest(messages) && global.IsQoderProduct() {
	//	modelConfig := chatUtil.PrepareModelConfig(*params)
	//	log.Infof("modelConfig: %s", cosyUtil.ToJsonStr(modelConfig))
	//	if modelConfig == nil || !modelConfig.IsVl || "openai" != modelConfig.Format {
	//		remoteChatAsk.AgentId = definition.AgentCommonAgentService
	//		remoteChatAsk.TaskId = definition.TaskIdVlCommon
	//	}
	//}

	remoteChatAsk.Messages = messages
	remoteChatAsk.Tools = tools

	return remoteChatAsk, nil
}

func SubscribeCommonAgentChat(ctx context.Context, params definition.AskParams, ask definition.RemoteChatAsk, streamingContentFunc func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error, toolParsingCallback func(ctx context.Context, toolParseEvent *definition.ToolParseEvent)) (*agentDefinition.Message, error) {
	svcRequest := remote.BigModelSvcRequest{
		ServiceName: definition.AgentCommonAgentService,
		FetchKey:    "llm_model_result",
		Async:       params.Stream,
		RequestBody: ask,
		RequestID:   ask.RequestId,
		AgentID:     ask.AgentId,
	}
	log.Debugf("[ask llm route digest info] service_name:%s, sessionId:%s, agent_id:%s, task_id:%s, request_id:%s, request_set_id:%s,chat_record_id:%s", svcRequest.ServiceName, ask.SessionId, ask.AgentId, ask.TaskId, ask.RequestId, ask.RequestSetId, ask.ChatRecordId)
	sls.Report(sls.EventTypeChatAgentRequest, ask.RequestId, map[string]string{
		"agent_id":       ask.AgentId,
		"task_id":        ask.TaskId,
		"request_id":     ask.RequestId,
		"request_set_id": ask.RequestSetId,
		"chat_record_id": ask.ChatRecordId,
		"mode":           params.Mode,
		"chat_task":      params.ChatTask,
		"session_type":   params.SessionType,
		"model_config":   cosyUtil.ToJsonStr(ask.ModelConfig),
		"session_id":     params.SessionId,
	})

	req, _ := remote.BuildBigModelSvcRequestWithConfig(svcRequest, &ask.ModelConfig, nil)

	// 创建一个字符串生成器，用于累积处理后的内容
	var processedContentBuilder strings.Builder

	// 定义拦截函数，收集处理后的内容
	interceptStreamFunc := func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
		// 只收集内容类型的数据，而不是控制信息
		if contentType == definition.StreamingContentTypeContent && len(chunk) > 0 {
			processedContentBuilder.Write(chunk)
		}
		// 调用原始的流函数
		return streamingContentFunc(ctx, contentType, chunk)
	}

	// 使用处理器链包装原始流函数，包括Markdown链接和打字机效果
	processorChainStreamFunc := ProcessorChain(ask.RequestSetId, params.CloseTypewriter, false, params.Mode, config.GetAskModeUseTools(), interceptStreamFunc)

	llmStart := time.Now().UnixMilli()

	timeoutHandler := func(req *http.Request, rsp *http.Response) {
		eventData := make(map[string]string)
		eventData["session_id"] = params.SessionId
		eventData["request_id"] = params.RequestId
		eventData["timeout_config"] = strconv.Itoa(3 * 57)

		if ideConfig := ctx.Value(definition.ContextKeyIdeConfig); ideConfig != nil {
			ide, ok := ideConfig.(*definition.IdeConfig)
			if ok {
				eventData["ide_type"] = ide.IdePlatform
				eventData["ide_version"] = ide.IdeVersion
				eventData["plugin_version"] = ide.PluginVersion
				eventData["ide_series"] = ide.IdeSeries
			}
		}
		go sls.Report(definition.EventTypeChatTimeout, params.RequestId, eventData)

		go stable.ReportCommonError(ctx, definition.MonitorErrorKeyChatTimeout, params.RequestId, fmt.Errorf("chat timeout"), chatUtil.ToMap(eventData))

		log.Warnf("common agent chat timeout.")
	}

	extras := map[string]string{
		modelKeyKey:     ask.ModelConfig.Key,
		queueTypeKey:    params.ModelQueueType,
		requestSetIdKey: ask.RequestSetId,
		sessionTypeKey:  ask.SessionType,
	}
	sseClient := sse.NewSseChatClient(map[string]string{})
	llmChatResponse, err := ParseStreamingChatResponse(ctx, sseClient, params.RequestId, params.SessionId, extras, req, timeoutHandler, processorChainStreamFunc, toolParsingCallback)
	if err != nil {
		log.Errorf("parse streaming content error: %v", err)
		// 出错时清理整个处理器链
		CleanupProcessorChain(ctx, ask.RequestSetId)
		return nil, err
	}

	logInfo := map[string]string{
		"request_id":        params.RequestId,
		"session_id":        params.SessionId,
		"request_set_id":    ask.RequestSetId,
		"prompt_tokens":     cast.ToString(llmChatResponse.Usage.PromptTokens),
		"completion_tokens": cast.ToString(llmChatResponse.Usage.CompletionTokens),
		"total_tokens":      cast.ToString(llmChatResponse.Usage.TotalTokens),
		"reasoning_tokens":  cast.ToString(llmChatResponse.Usage.CompletionTokensDetails.ReasoningTokens),
		"cached_tokens":     cast.ToString(llmChatResponse.Usage.PromptTokensDetails.CachedTokens),
		"time":              fmt.Sprintf("%d", time.Now().UnixMilli()-llmStart),
	}
	sls.Report(sls.EventTypeAgentLLmRequestPerformance, params.RequestId, logInfo)

	// 响应成功后清理处理器链
	CleanupProcessorChain(ctx, ask.RequestSetId)
	responseMessage := llmChatResponse.Choices[0].Message

	// 获取处理后的内容
	processedContent := processedContentBuilder.String()

	// 如果处理后内容不为空，使用它替换原始内容
	if len(processedContent) <= 0 {
		processedContent = responseMessage.Content
	}

	llmResponseMessage := agentDefinition.Message{
		Content:                   processedContent, // 使用处理后的内容而不是原始内容
		ReasoningContent:          responseMessage.ReasoningContent,
		ReasoningContentSignature: responseMessage.Signature,
		Role:                      agentDefinition.RoleTypeAssistant,
		Name:                      responseMessage.Name,
		ResponseMeta: agentDefinition.ResponseMeta{
			ID: llmChatResponse.ID,
			Usage: agentDefinition.UsageMeta{
				CompletionTokens: llmChatResponse.Usage.CompletionTokens,
				PromptTokens:     llmChatResponse.Usage.PromptTokens,
				TotalTokens:      llmChatResponse.Usage.TotalTokens,
				PromptTokensDetails: struct {
					CachedTokens int `json:"cached_tokens"`
				}{
					CachedTokens: llmChatResponse.Usage.PromptTokensDetails.CachedTokens,
				},
				CompletionTokensDetails: struct {
					ReasoningTokens int `json:"reasoning_tokens"`
				}{
					ReasoningTokens: llmChatResponse.Usage.CompletionTokensDetails.ReasoningTokens,
				},
			},
			FinishReason: string(llmChatResponse.Choices[0].FinishReason),
		},
		Extra: map[string]any{
			"callServerRequestId": ask.RequestId,
		},
	}
	var toolCalls []agentDefinition.ToolCall
	if responseMessage.ToolCalls != nil {
		for _, toolCall := range responseMessage.ToolCalls {
			arguments := GetToolCallArguments(toolCall)
			toolName := toolCall.Function.Name
			if toolName == "" {
				toolName = "unknown"
			}
			toolCalls = append(toolCalls, agentDefinition.ToolCall{
				ID:   toolCall.ID,
				Type: string(toolCall.Type),
				Function: agentDefinition.FunctionCall{
					Name:      toolName,
					Arguments: arguments,
				},
			})
		}
		llmResponseMessage.ToolCalls = toolCalls
	}

	return &llmResponseMessage, nil
}

// CallLingmaServer 判断是否是走lingma-server的调用
func CallLingmaServer(llmConfig *util.LLMConfig) bool {
	if llmConfig != nil && llmConfig.Enable != coderCommon.LLMLingmaServer {
		return false
	}
	return true
}

// PrepareModelConfig 处理模型配置
func PrepareModelConfig(params *definition.AskParams, toolCallCount int) (definition.ModelConfig, bool) {
	modelConfig := chatUtil.PrepareModelConfig(*params)
	if modelConfig == nil {
		return definition.ModelConfig{}, false
	}
	modelConfigInst := *modelConfig
	if !isSystemQwen3ThinkingModel(modelConfig.Source, modelConfig.Key) {
		return modelConfigInst, true
	}
	// 如果是qwen3-thinking模型，只有每个用户query的第一步启动thinking模式
	if toolCallCount == 0 {
		return modelConfigInst, true
	}
	modelConfigInst.Key = strings.ReplaceAll(modelConfig.Key, Qwen3ThinkingModelKeySuffix, "")
	return modelConfigInst, true
}

// isSystemQwen3ThinkingModel 判断是否是系统配置的qwen3-thinking模型
func isSystemQwen3ThinkingModel(source string, key string) bool {
	if source != ModelSourceSystem {
		return false
	}
	if strings.HasSuffix(key, Qwen3ThinkingModelKeySuffix) && strings.HasPrefix(key, Qwen3ThinkModelKeyPrefix) {
		return true
	}
	return false
}

// ConvertToModelToolParam 转换成调用LLM的tool参数
func ConvertToModelToolParam(ctx context.Context, tools []tool.BaseTool) []agentLlms.Tool {
	filterExplanation := !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentKeyToolExplanationEnable, experiment.ConfigScopeClient, false)
	return ConvertToModelToolParamWithFilter(ctx, tools, filterExplanation)
}

func ConvertToModelToolParamWithFilter(ctx context.Context, tools []tool.BaseTool, filterExplanation bool) []agentLlms.Tool {
	param := make([]agentLlms.Tool, 0, len(tools))
	for _, tool := range tools {
		toolInfo, err := tool.Info(ctx)
		if err != nil {
			continue
		}
		if toolInfo.Name == web.FetchContentToolName || toolInfo.Name == web.SearchWebToolName {
			if !IsWebToolEnable() {
				continue
			}
		}

		// 处理工具参数，根据server端配置或者env过滤explanation参数
		parameters := toolInfo.Parameters
		if filterExplanation && parameters != nil {
			parameters = filterExplanationFromParameters(parameters)
		}

		param = append(param, agentLlms.Tool{
			Type: "function",
			Function: &agentLlms.FunctionDefinition{
				Name:        toolInfo.Name,
				Description: toolInfo.Description,
				Parameters:  parameters,
			},
		})
	}
	return param
}

// filterExplanationFromParameters 复制参数并过滤掉explanation字段
func filterExplanationFromParameters(originalParams *agentDefinition.Schema) *agentDefinition.Schema {
	if originalParams == nil {
		return nil
	}

	// 使用 JSON 序列化和反序列化进行深拷贝
	jsonData, err := json.Marshal(originalParams)
	if err != nil {
		// 如果序列化失败，返回原始参数的浅拷贝
		log.Warnf("Failed to marshal schema for deep copy: %v, falling back to shallow copy", err)
		result := *originalParams
		return &result
	}

	var result agentDefinition.Schema
	if err := json.Unmarshal(jsonData, &result); err != nil {
		// 如果反序列化失败，返回原始参数的浅拷贝
		log.Warnf("Failed to unmarshal schema for deep copy: %v, falling back to shallow copy", err)
		result := *originalParams
		return &result
	}

	// 安全地修改 result，不会影响原始的 originalParams
	if result.Properties != nil && result.Properties["explanation"] != nil {
		delete(result.Properties, "explanation")
	}

	// 如果 required 中包含 explanation， 则进行删除
	if result.Required != nil {
		for i, r := range result.Required {
			if r == "explanation" {
				result.Required = append(result.Required[:i], result.Required[i+1:]...)
				break
			}
		}
	}

	return &result
}

func IsMultiVlMessageRequest(messages []*agentDefinition.Message) bool {
	if messages == nil || len(messages) <= 0 {
		return false
	}
	for _, message := range messages {
		if message.Role == agentDefinition.RoleTypeUser {
			if message.MultiContent != nil && len(message.MultiContent) > 0 {
				for _, content := range message.MultiContent {
					if content.Type == agentDefinition.ChatMessagePartTypeImageURL {
						return true
					}
				}
			}
		}
	}
	return false
}
