package support

import (
	"bytes"
	"context"
	"cosy/chat/chains/common"
	chatUtil "cosy/chat/util"
	"cosy/config"
	"cosy/definition"
	"cosy/errors"
	"cosy/global"
	"cosy/log"
	"cosy/sls"
	"cosy/sse"
	"cosy/stable"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"
	"strconv"
	"strings"
	"time"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"github.com/google/uuid"

	"github.com/buger/jsonparser"

	"github.com/spf13/cast"

	agentClient "code.alibaba-inc.com/cosy/lingma-agent-graph/llms/openai/openaiclient"

	definition2 "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/llms/openai/openaiclient"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/memory"
)

const (
	modelKeyKey     = "modelKey"
	queueTypeKey    = "queueType"
	requestSetIdKey = "requestSetId"
	sessionTypeKey  = "sessionType"
)

type BaseMemoryState interface {
	graph.State

	GetShortTermMemory() memory.ShortTermMemory
	GetCtxForClient() context.Context
}

// GetToolCallArguments 获取tool call的arguments字段，如果解析失败，则尝试修复json
func GetToolCallArguments(toolCall agentClient.ToolCall) string {
	originArguments := toolCall.Function.Arguments
	if originArguments == "" {
		// 补成json格式的
		return "{}"
	}
	// TODO 要删除
	//if strings.HasPrefix(originArguments, "\"") {
	//	// 多一次转义，需要处理掉
	//	unquoteStr, err := strconv.Unquote(originArguments)
	//	if err != nil {
	//		log.Errorf("Error unquoting arguments, toolCallId=%s, arguments=%s", toolCall.ID, originArguments)
	//		return originArguments
	//	}
	//	originArguments = unquoteStr
	//}
	var parameters map[string]interface{}
	err := json.Unmarshal([]byte(originArguments), &parameters)
	if err == nil {
		return originArguments
	}
	log.Debugf("Error unmarshalling arguments, toolCallId=%s, toolName=%s, arguments=%s", toolCall.ID, toolCall.Function.Name, originArguments)
	slsFixToolArgs(toolCall.ID, toolCall.Function.Name)
	return RepairArgsJson(toolCall.ID, originArguments)
}

func ParseStreamingChatResponse(ctx context.Context, sseClient *sse.Client, requestId string, sessionId string, extras map[string]string, req *http.Request, timeoutHandler func(req *http.Request, rsp *http.Response), streamingFunc func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error, toolParsingCallback func(ctx context.Context, toolParseEvent *definition.ToolParseEvent)) (*openaiclient.ChatCompletionResponse, error) {
	return ParseStreamingChatResponseWithTimeout(ctx, sseClient, requestId, sessionId, extras, req, timeoutHandler, streamingFunc, toolParsingCallback, time.Duration(287)*time.Second)
}

func ParseStreamingChatResponseWithTimeout(ctx context.Context, sseClient *sse.Client, requestId string, sessionId string, extras map[string]string, req *http.Request, timeoutHandler func(req *http.Request, rsp *http.Response), streamingFunc func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error, toolParsingCallback func(ctx context.Context, toolParseEvent *definition.ToolParseEvent), timeout time.Duration) (*openaiclient.ChatCompletionResponse, error) {
	defer func() {
		if r := recover(); r != nil {

			// 获取当前堆栈信息
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Errorf("recover from crash. unmarshalErr: %+v, stack: %s", r, stack)
		}
	}()

	// Parse completionResponse
	completionResponse := openaiclient.ChatCompletionResponse{
		Choices: []*openaiclient.ChatCompletionChoice{
			{},
		},
	}

	doneChan := make(chan error)
	go func() {
		err := sseClient.SubscribeWithContext(ctx, timeout, req, func(req *http.Request, rsp *http.Response, msg *sse.Event, closeChan chan error) {
			//判断ctx是否done
			select {
			case <-ctx.Done():
				log.Debugf("[LLM-CALL] Response handler context canceled.")
				closeChan <- nil
				return
			default:
			}

			var response definition.ChatResponse
			if string(msg.Event) == "error" {
				//提前结束sse
				closeChan <- errors.ErrInternalServer

				log.Errorf("common agent chat response error, reason=%s", string(msg.Data))
				go sls.Report(sls.EventTypeChatModelCallError, requestId, map[string]string{
					"error":      string(msg.Data),
					"session_id": sessionId,
				})
				return
			}

			if string(msg.Event) == "finish" {

				closeChan <- nil

				if string(msg.Data) == sse.ForceFinishReason {
					log.Warnf("force finish sse event.")
				}

				log.Debugf("common agent chat sse finish.")
				return
			}
			//fmt.Println("time="+time.Now().String()+" msg.Data=", string(msg.Data))
			err := json.Unmarshal(msg.Data, &response)
			//log.Debugf("Ask additional data. completionResponse=%s", string(msg.Data))

			if err != nil {
				log.Error("Unmarshal sse msg data error: ", err)
				return
			}
			if response.StatusCodeValue != 200 {
				message := response.Body

				modelQueueStatus, isModelQueueError := chatUtil.GetQueueErrorFromString(message)
				if isModelQueueError && modelQueueStatus != nil && modelQueueStatus.IsQueued {
					closeChan <- errors.New(errors.ModelQueuing, message)
					log.Infof("common agent chat, model queuing, status: %s", message)
					return
				}

				//提前结束sse
				closeChan <- convertLlmInvokeError(response.StatusCodeValue, message)

				stable.GoSafe(ctx, func() {
					recordInvalidSignatureErr(requestId, req, string(msg.Data))
				}, stable.SceneChatAsk)

				log.Debugf("common agent chat answer finished error, statusCode: %d, message: %s", response.StatusCodeValue, string(msg.Data))
				return
			}

			bodyData := response.Body
			if bodyData == "[DONE]" {
				return
			}
			if strings.HasPrefix(bodyData, "[EXCEED_QUOTA]") {
				if config.GetShowQuotaExceeded() {
					nextResetAt := strings.TrimPrefix(bodyData, "[EXCEED_QUOTA]")
					timeStampMilli, _ := strconv.ParseInt(nextResetAt, 10, 64)
					notificationParams := &definition.ChatNotification{
						IsExceedQuota: true,
						NextResetAt:   timeStampMilli,
						RequestId:     requestId,
						SessionId:     sessionId,
						SessionType:   extras[sessionTypeKey],
					}
					_ = websocket.SendRequestWithTimeout(ctx, "chat/notification", notificationParams, nil, 3*time.Second)
					config.SetShowQuotaExceeded(time.Now().Format(time.RFC3339))
				}
				return
			}
			if bodyData == "[NOT_EXCEED_QUOTA]" {
				config.SetShowQuotaExceeded(time.Now().Add(-24 * time.Hour).Format(time.RFC3339))
				return
			}
			var streamPayload openaiclient.StreamedChatResponsePayload
			err = json.NewDecoder(bytes.NewReader([]byte(bodyData))).Decode(&streamPayload)
			if err != nil {
				log.Errorf("failed to decode stream payload: %v", err)
				return
			}
			// 在这里无法直接访问ask.RequestSetId，我们需要在前面把它存到params.Extra里
			// 使用相同ID作为request_set_id，确保一致性（因为在BuildRemoteChatMessageParam中是相同的）
			requestSetId := requestId
			contentParsingErr := parsingResponseData(ctx, streamPayload, &completionResponse, streamingFunc, toolParsingCallback, requestId, requestSetId)
			if contentParsingErr != nil {
				log.Errorf("common agent chat parsing response data error: %v", contentParsingErr)
			}
		}, timeoutHandler, nil)

		doneChan <- err
	}()

	select {
	case <-ctx.Done():
		log.Debugf("common agent chat canceled: %v", ctx.Err())
	case chatErr := <-doneChan:
		if chatErr != nil {
			log.Errorf("common agent request to remote error. err: %v", chatErr)

			unifiedErr := parseChatErr(chatErr)

			return nil, unifiedErr
		} else {
			log.Debugf("common agent chat finished.")
			break
		}
	}

	return &completionResponse, nil
}

func parsingResponseData(ctx context.Context, streamResponse openaiclient.StreamedChatResponsePayload, response *openaiclient.ChatCompletionResponse, streamingContentFunc func(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error, toolParsingCallback func(ctx context.Context, toolParseEvent *definition.ToolParseEvent), requestId string, requestSetId string) error {
	//是否在解析内容流，内容流需要实时输出
	isParsingContent := true

	if streamResponse.Error != nil {
		return streamResponse.Error
	}

	if streamResponse.ID != "" {
		response.ID = streamResponse.ID
	}
	if streamResponse.Usage != nil {
		response.Usage.CompletionTokens = streamResponse.Usage.CompletionTokens
		response.Usage.PromptTokens = streamResponse.Usage.PromptTokens
		response.Usage.TotalTokens = streamResponse.Usage.TotalTokens
		response.Usage.CompletionTokensDetails.ReasoningTokens = streamResponse.Usage.CompletionTokensDetails.ReasoningTokens
		response.Usage.PromptTokensDetails.CachedTokens = streamResponse.Usage.PromptTokensDetails.CachedTokens
	}

	if len(streamResponse.Choices) == 0 {
		return nil
	}
	choice := streamResponse.Choices[0]
	chunk := []byte(choice.Delta.Content)
	response.Choices[0].Message.Content += choice.Delta.Content
	response.Choices[0].FinishReason = choice.FinishReason
	response.Choices[0].Message.ReasoningContent += choice.Delta.ReasoningContent
	response.Choices[0].Message.Signature += choice.Delta.Signature

	isParsingReasoning := (choice.Delta.ReasoningContent != "" && choice.Delta.Content == "")
	if isParsingReasoning {
		chunk = []byte(choice.Delta.ReasoningContent)
	}

	if choice.Delta.FunctionCall != nil {
		log.Debugf("common agent should not return function call in delta.")
		chunk = updateFunctionCall(response.Choices[0].Message, choice.Delta.FunctionCall)
	}

	if len(choice.Delta.ToolCalls) > 0 {
		var toolParseEvents []*definition.ToolParseEvent

		// 在处理工具调用之前，先强制清空处理器链中的所有处理器
		// 避免工具显示穿插在现有输出中
		if requestSetId != "" {
			// 使用requestSetId清空对应的处理器链（因为处理器是使用这个ID创建的）
			// 这里一定要注意区别于requestId，requestId是每次请求的唯一标识，而requestSetId是每次请求的唯一标识
			err := FlushProcessorChain(ctx, requestSetId)
			if err != nil {
				log.Errorf("[ToolCalls] flush processor chain error: %v", err)
			}
		}

		chunk, response.Choices[0].Message.ToolCalls, toolParseEvents = UpdateToolCalls(response.Choices[0].Message.ToolCalls,
			choice.Delta.ToolCalls)
		for _, e := range toolParseEvents {
			if toolParsingCallback != nil {
				toolParsingCallback(ctx, e)
			}
		}

		isParsingContent = false
	}

	if isParsingContent {
		streamingContentType := definition.StreamingContentTypeContent
		if isParsingReasoning {
			streamingContentType = definition.StreamingContentTypeReasoning
		}

		if streamingContentFunc != nil {
			err := streamingContentFunc(ctx, definition.StreamingContentType(streamingContentType), chunk)
			if err != nil {
				return fmt.Errorf("streaming func returned an error: %w", err)
			}
		}

	}
	return nil
}

func parseChatErr(chatErr error) error {
	cosyErr, is := errors.IsUnifiedError(chatErr)
	if is {
		return cosyErr
	}
	// 检查错误是否是 context deadline exceeded
	// 具体查看 sse/client.go: c.Connection.Do(req) 可能返回 "context deadline exceeded"
	//  - http/client.go#979: err = &timeoutError{err.Error() + " (Client.Timeout or context cancellation while reading body)"}

	//检查错误是否是 dial tcp: lookup api3.qoder.sh: no such host
	if strings.Contains(strings.ToLower(chatErr.Error()), "timeout") {
		log.Warnf("model response timeout detected: %v", chatErr)
		return errors.ErrModelResponseTimeout
	}

	if strings.Contains(strings.ToLower(chatErr.Error()), "no such host") {
		return errors.ErrRequestTimeout
	}

	return errors.ErrInternalServer
}

// UpdateToolCalls 解析ToolCall，支持一次选择多个工具
func UpdateToolCalls(tools []openaiclient.ToolCall, delta []*openaiclient.ToolCall) ([]byte, []openaiclient.ToolCall, []*definition.ToolParseEvent) {
	if len(delta) == 0 {
		return []byte{}, tools, nil
	}
	chunk, _ := json.Marshal(delta) // nolint:errchkjson

	result := make([]*definition.ToolParseEvent, 0)
	delta0 := delta[0]
	// 这里的不能用delta0.ID来做为toolCalls开始的标志，因为gemini没有返回id(claude、openai都有)
	if delta0.Function.Name != "" {
		// 新的toolcall开始，说明上一个toolcall的参数解析结束了，
		// 增加一个EndToolParseEvent的event
		if len(tools) > 0 {
			result = append(result, &definition.ToolParseEvent{
				Type: definition.EndToolParseEvent,
				ToolCall: &definition2.ToolCall{
					ID: tools[len(tools)-1].ID,
					Function: definition2.FunctionCall{
						Name:      tools[len(tools)-1].Function.Name,
						Arguments: tools[len(tools)-1].Function.Arguments,
					},
				},
			})
		}

		if delta0.ID == "" {
			// gemini没有id，要兼容一下
			delta0.ID = uuid.New().String()
		}
		//解析到新tool call
		tools = append(tools, *delta0)
		result = append(result, &definition.ToolParseEvent{
			Type: definition.StartToolParseEvent,
			ToolCall: &definition2.ToolCall{
				ID: delta0.ID,
				Function: definition2.FunctionCall{
					Name:      delta0.Function.Name,
					Arguments: delta0.Function.Arguments, // 不管首次是否有args都放一下，因为有的模型是批式返回args的
				},
			},
		})

		return chunk, tools, result
	}
	if len(tools) < 1 {
		return []byte{}, tools, nil
	}
	// 参数追加到最后一个tool上
	tools[len(tools)-1].Function.Arguments += delta0.Function.Arguments
	wholeArgs := tools[len(tools)-1].Function.Arguments
	// 这个是补全的json args
	parsedArguments := parsePartialArguments(wholeArgs)
	parseEvent := &definition.ToolParseEvent{
		Type: definition.DeltaParsingToolParsingEvent,
		ToolCall: &definition2.ToolCall{
			ID: tools[len(tools)-1].ID,
			Function: definition2.FunctionCall{
				Name: tools[len(tools)-1].Function.Name,
			},
		},
		Extra: map[string]any{
			definition.ToolParseEventExtraWholeArgs: wholeArgs,
			definition.ToolParseEventExtraDeltaArgs: delta0.Function.Arguments,
		},
	}
	if parsedArguments != nil {
		parseEvent.ToolCall.Function.Arguments = util.ToJsonStr(parsedArguments)
	}
	result = append(result, parseEvent)

	return chunk, tools, result
}

// 解析arguments字段，保证是完整json
func parsePartialArguments(argumentsStr string) map[string]any {
	if argumentsStr == "" {
		return nil
	}
	// TODO 要删除
	if strings.HasPrefix(argumentsStr, "\"") {
		if !strings.HasSuffix(argumentsStr, "\"") {
			argumentsStr = argumentsStr + "\""
		}
		// 多一次转义，需要处理掉
		unquoteStr, err := strconv.Unquote(argumentsStr)
		if err != nil {
			return nil
		}
		argumentsStr = unquoteStr
	}
	var parsedJson = make(map[string]any)

	jsonparser.ObjectEach([]byte(argumentsStr), func(key []byte, value []byte, dataType jsonparser.ValueType, offset int) error {
		if dataType == jsonparser.String {
			// Use json.Unmarshal to correctly handle all JSON escape sequences
			var unquotedValue string
			// Add quotes around the value to make it a valid JSON string
			quotedValue := fmt.Sprintf("\"%s\"", strings.ReplaceAll(string(value), "\"", "\\\""))
			if err := json.Unmarshal([]byte(quotedValue), &unquotedValue); err != nil {
				// If unmarshal fails, fall back to the original string with basic replacements
				strValue := string(value)
				strValue = strings.ReplaceAll(strValue, "\\\\", "\\")
				strValue = strings.ReplaceAll(strValue, "\\\"", "\"")
				strValue = strings.ReplaceAll(strValue, "\\/", "/")
				strValue = strings.ReplaceAll(strValue, "\\n", "\n")
				parsedJson[string(key)] = strValue
			} else {
				parsedJson[string(key)] = unquotedValue
			}
		} else if dataType == jsonparser.Number {
			parsedJson[string(key)] = cast.ToInt(string(value))
		} else if dataType == jsonparser.Boolean {
			parsedJson[string(key)] = cast.ToBool(string(value))
		} else {
			parsedJson[string(key)] = value
		}
		return nil
	})
	return parsedJson
}

func convertLlmInvokeError(errCode int, message string) error {
	if errCode == 400 {
		if strings.Contains(message, "\"details\"") {
			// proxy的报错
			errResp := definition.LlmProxyErrorResponse{}
			if err := json.Unmarshal([]byte(message), &errResp); err == nil {
				if errResp.Code == "provider_error" {
					if strings.Contains(errResp.Details, "JSON schema is invalid") ||
						strings.Contains(errResp.Details, "String should match pattern") ||
						strings.Contains(errResp.Details, "Tool names must be unique") {
						return &errors.Error{
							Code:    errors.ModelResponseInvalidToolSchema,
							Message: errResp.Details,
						}
					} else if strings.Contains(message, "too long") || strings.Contains(message, "exceed context limit") {
						return &errors.Error{
							Code:    errors.ModelInputTokenOvertLimit,
							Message: message,
						}
					}
					// 提供商的报错，针对details处理一下某些报错
					return &errors.Error{
						Code:    errors.SystemError,
						Message: errResp.Message,
					}
				}
				return &errors.Error{
					Code:    errors.SystemError,
					Message: errResp.Message,
				}
			}
		} else {
			errResp := definition.LlmErrorResponse{}
			if err := json.Unmarshal([]byte(message), &errResp); err == nil {
				if errResp.Error.Code == "invalid_parameter_error" {
					if strings.Contains(errResp.Error.Message, "The tool call is not supported") {
						return &errors.Error{
							Code:    errors.ToolCallNotSupport,
							Message: errResp.Error.Message,
						}
					}
				}
			}
		}
		//兜底系统内部错误
		return errors.New(errors.SystemError, message)
	} else if errCode == 418 {
		// error_finish结束的，匹配一下错误内容
		err := convertErrCode418(errCode, message)
		if err != nil {
			return err
		}
	}
	return errors.New(errCode, message)
}

func convertErrCode418(errCode int, message string) error {
	var errorResp ErrorCode418Response
	if err := json.Unmarshal([]byte(message), &errorResp); err != nil {
		log.Debugf("Failed to parse 418 error response: %v", err)
		return nil
	}

	// 解析嵌套的 Message 字段
	var innerResp ErrorCode418InnerResponse
	if err := json.Unmarshal([]byte(errorResp.Message), &innerResp); err != nil {
		log.Debugf("Failed to parse 418 inner response: %v", err)
		return nil
	}

	// 如果有 choices，解析第一个 choice 的 content
	if len(innerResp.Choices) > 0 && innerResp.Choices[0].Delta.Content != "" {
		var contentResp ErrorCode418Content
		if err := json.Unmarshal([]byte(innerResp.Choices[0].Delta.Content), &contentResp); err != nil {
			log.Errorf("Failed to parse 418 content response: %v", err)
			return nil
		}

		// 根据具体的错误类型返回对应的错误
		switch contentResp.Error.Code {
		case "array_above_max_length":
			return &errors.Error{
				Code:    errors.ModelResponseToolOvertLimit,
				Message: contentResp.Error.Message,
			}
		// gpt-4o对于请求中的tool格式不规范会报错
		case "invalid_function_parameters":
			return &errors.Error{
				Code:    errors.ModelResponseInvalidToolSchema,
				Message: contentResp.Error.Message,
			}
		default:
			// claude4 对于请求中的tool格式不规范会报错
			// 检查 Message 中是否包含 "JSON schema is invalid"
			if strings.Contains(contentResp.Error.Message, "JSON schema is invalid") {
				return &errors.Error{
					Code:    errors.ModelResponseInvalidToolSchema,
					Message: contentResp.Error.Message,
				}
			}
			return nil
		}
	}
	return nil
}

func IsLLMErrorCodeNeedManualConfirm(errorCode int) bool {
	// 请求超时
	if errors.RequestTimeout == errorCode {
		return true
	}
	// llm 回复超时
	if errors.ModelResponseTimeout == errorCode || errors.ChatTimeout == errorCode {
		return true
	}
	// MCP相关的错误
	if errors.ModelResponseToolOvertLimit == errorCode || errors.ModelResponseInvalidToolSchema == errorCode {
		return true
	}
	// 问答中排队
	if errors.ModelQueuing == errorCode {
		return true
	}
	// 500异常
	if errors.SystemError == errorCode {
		return true
	}
	return false
}

func updateFunctionCall(message agentClient.ChatMessage, functionCall *agentClient.FunctionCall) []byte {
	if message.FunctionCall == nil {
		message.FunctionCall = functionCall
	} else {
		message.FunctionCall.Arguments += functionCall.Arguments
	}
	chunk, _ := json.Marshal(message.FunctionCall) // nolint:errchkjson
	return chunk
}

// ErrorCode418Response 用于解析 errCode == 418 的错误响应
type ErrorCode418Response struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

// ErrorCode418InnerResponse 解析 Message 字段中的嵌套 JSON
type ErrorCode418InnerResponse struct {
	Choices []ErrorCode418Choice `json:"choices"`
}

// ErrorCode418Choice 选择项结构
type ErrorCode418Choice struct {
	Index        int               `json:"index"`
	Delta        ErrorCode418Delta `json:"delta"`
	FinishReason string            `json:"finish_reason"`
}

// ErrorCode418Delta delta 结构
type ErrorCode418Delta struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// ErrorCode418Content 解析 Content 字段中的具体错误信息
type ErrorCode418Content struct {
	Error ErrorCode418ErrorDetail `json:"error"`
}

// ErrorCode418ErrorDetail 具体的错误详情
type ErrorCode418ErrorDetail struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Param   string `json:"param"`
	Code    string `json:"code"`
}

func GetPreferredLanguage(ctx context.Context) string {
	preferredLanguage, _ := ctx.Value(common.KeyPreferredLanguage).(string)
	preferredLanguage = ConvertLanguageCodeToFullName(preferredLanguage)
	if preferredLanguage == "" {
		if global.IsQoderProduct() {
			preferredLanguage = "English"
		} else {
			preferredLanguage = "中文"
		}
	}
	return preferredLanguage
}

// LanguageCodeMap 语言映射map,变动时同步增加 quest_requirement_naming_system_prompt.txt
var LanguageCodeMap = map[string]string{
	"en":    "English",
	"zh":    "中文",
	"zh-cn": "中文",
	"es":    "Español",
	"fr":    "Français",
	"pt":    "Português",
	"ja":    "日本語",
	"de":    "Deutsch",
	"ko":    "한국어",
}

// ConvertLanguageCodeToFullName 将语言简写转换为语言全称
func ConvertLanguageCodeToFullName(langCode string) string {
	if fullName, exists := LanguageCodeMap[strings.ToLower(langCode)]; exists {
		return fullName
	}
	return langCode
}

// slsFixToolArgs 埋点json格式修复
func slsFixToolArgs(toolCallId string, toolName string) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Error occurred in slsFixToolArgs: %v", r)
		}
	}()
	requestId := uuid.NewString()
	eventData := map[string]string{
		"request_id":   requestId,
		"tool_call_id": toolCallId,
		"tool_name":    toolName,
	}
	go sls.Report(sls.EventTypeAgentToolCallFixArg, requestId, eventData)
}

// 在签名异常时记录header中的字段
func recordInvalidSignatureErr(requestId string, req *http.Request, message string) {
	if req != nil && strings.Contains(message, "Signature invalid") {
		// 获取关键的header信息
		cosyUser := req.Header.Get("Cosy-User")
		authorization := req.Header.Get("Authorization")
		cosyKey := req.Header.Get("Cosy-Key")
		headerDate := req.Header.Get("Cosy-Date")
		path := req.URL.Path

		bodyLength := req.ContentLength

		// 解析Authorization header获取signature和payload
		var base64PayLoad, signature string
		if authorization != "" && strings.HasPrefix(authorization, "Bearer") {
			authorization = strings.Replace(authorization, "Bearer ", "", 1)
			parts := strings.Split(authorization, ".")
			if len(parts) == 3 {
				base64PayLoad = parts[1]
				signature = parts[2]
			}
		}

		// 记录签名异常的详细信息
		log.Warnf("MD5 signature is invalid. requestId: %s, \nHeader signature=%s\nbase64PayLoadLenght=%d\ncosyKey=%s\nheaderDate=%s\npath=%s\ncosyUser=%s\nbodyBytesLength=%d",
			requestId, signature, len([]byte(base64PayLoad)), cosyKey, headerDate, path, cosyUser, bodyLength)
	}
}
