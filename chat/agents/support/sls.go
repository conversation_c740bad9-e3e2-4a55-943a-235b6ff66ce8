package support

import (
	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/tool/apply"
	"cosy/chat/agents/tool/common"
	cosyErrors "cosy/errors"
	"cosy/log"
	"cosy/sls"
	"cosy/util"
	"encoding/json"
	"strconv"
	"strings"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
)

// SlsToolCall 工具调用埋点
func SlsToolCall(sessionId string, requestId string, assistantMessage *agentDefinition.Message, toolCall agentDefinition.ToolCall, toolCallResult *coderCommon.ToolCallResult) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Error occurred in slsToolCall: %v", r)
		}
	}()
	// ToolNotFound 不埋点 (用户从 agent 模式切换到 chat 模式时可能出现)
	if toolCallResult == nil || toolCallResult.ErrorCode == cosyErrors.ToolNotFound {
		return
	}
	modelResponseId := ""
	assistantContent := ""
	if assistantMessage != nil {
		modelResponseId = assistantMessage.ResponseMeta.ID
		assistantContent = assistantMessage.Content
	}
	toolCallStatus := toolCallResult.Status
	if toolCallStatus == coderCommon.ToolCallStatusRunningInBackground {
		// 埋点日志直接改写成FINISHED
		toolCallStatus = coderCommon.ToolCallStatusFinished
	}
	eventData := map[string]string{
		"session_id":                         sessionId,
		"request_id":                         requestId,
		"request_set_id":                     requestId,
		"chat_record_id":                     requestId,
		"tool_call_id":                       toolCall.ID,
		"tool_call_name":                     toolCall.Function.Name,
		"tool_call_status":                   string(toolCallStatus),
		"tool_call_error_code":               strconv.Itoa(toolCallResult.ErrorCode),
		"tool_call_error_msg":                toolCallResult.ErrorMsg,
		"tool_call_cost":                     strconv.Itoa(toolCallResult.Cost),
		"tool_call_content_length":           strconv.Itoa(len(toolCallResult.Content)),
		"model_response_id":                  modelResponseId,
		"tool_call_args_length":              strconv.Itoa(len(toolCall.Function.Arguments)),
		"tool_call_assistant_content_length": strconv.Itoa(len(strings.TrimSpace(assistantContent))),
	}
	// search_replace 工具，额外计算一下搜索替换的cost
	if toolCall.Function.Name == "search_replace" {
		request := &apply.SearchReplaceRequest{}
		if err := apply.SmartParse(toolCall.Function.Arguments, request); err == nil {
			original_text := ""
			new_text := ""
			for _, replacement := range request.ReplacementChunks {
				original_text += *replacement.TargetContent
				new_text += *replacement.ReplacementContent
			}
			if len(original_text)+len(new_text) > 0 {
				eventData["tool_call_match_cost"] = strconv.Itoa(len(original_text) * toolCallResult.Cost / (len(original_text) + len(new_text)))
			}
		}
	}
	if util.Contains(common.EditFileTools, toolCall.Function.Name) {
		status := "UNKNOWN"
		if strings.Contains(toolCallResult.Content, "success,") {
			status = "SUCCESS"
		} else if strings.Contains(toolCallResult.Content, "cancelled,") {
			status = "CANCELLED"
		} else if strings.Contains(toolCallResult.Content, "failed,") || strings.Contains(toolCallResult.Content, "error,") {
			status = "ERROR"
		} else if strings.Contains(toolCallResult.Content, "partial success,") {
			status = "PARTIAL_SUCCESS"
		}
		eventData["tool_call_result_status"] = status
		var args struct {
			FilePath string `json:"file_path"`
		}
		if err := json.Unmarshal([]byte(toolCall.Function.Arguments), &args); err == nil {
			eventData["tool_call_key"] = args.FilePath
		}
	}
	go sls.Report(sls.EventTypeAgentToolCallStatistics, requestId, eventData)
}
