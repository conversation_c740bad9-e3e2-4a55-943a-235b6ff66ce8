package summary

import (
	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
	"context"
	"cosy/chat/agents/coder"
	coderCommon "cosy/chat/agents/coder/common"
	agentSupport "cosy/chat/agents/support"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"regexp"
	"runtime/debug"
	"strings"
	"time"
)

var BuilderIdentifier = "long-running-research-agent"

const InitNodeName = "InitNode"
const LLMNodeName = "LLMNode"
const ToolNodeName = "ToolNode"
const ToolOverLimitNodeName = "ToolOverLimitNode"
const FinishToolNodeName = "FinishToolNode"

func InitExecutorBuilder() (*executor.SingleGraphExecutorBuilder, error) {
	graphConfig := &executor.GraphConfig{
		Name:             "SummaryAgent",
		GraphBuilder:     agentGraphBuilder(),
		ProcessorBuilder: &coder.CommonDevProcessorBuilder{},
		Options:          []graph.CallOption{},
	}
	executorBuilder := executor.NewSingleGraphExecutorBuilder(graphConfig)
	return executorBuilder, nil
}

func agentGraphBuilder() *graph.Graph {
	graphBuilder := graph.NewGraph()

	graphBuilder.AddNode(initNode)
	graphBuilder.AddNode(llmNode)
	//graphBuilder.AddNode(toolNode)
	//graphBuilder.AddNode(toolOverLimitNode)
	//graphBuilder.AddNode(finishToolNode)

	graphBuilder.AddEdge("START", InitNodeName)
	graphBuilder.AddEdge(InitNodeName, LLMNodeName)

	graphBuilder.AddEdge(LLMNodeName, "END")
	return graphBuilder
}

// InitAgentContext 初始化agent的context
func InitAgentContext(ctx context.Context, sessionId string, requestId string) (context.Context, error) {

	agentContext := coderCommon.AgentContext{}
	ctx = context.WithValue(ctx, common.KeyCoderAgentContext, &agentContext)
	return ctx, nil
}

// GetNamingInfoByUserQuery 根据用户输入的查询，获取命名信息 taskName+fileName
func GetNamingInfoByUserQuery(ctx context.Context, createParams *definition.CreateChatTaskParams) (string, string, error) {
	userQuery := createParams.Query

	rawConfig := definition.RawConfig{}
	preferredLanguage := ""
	if createParams.ExecutionConfig != nil && len(createParams.ExecutionConfig.RawConfig) != 0 {
		if err := json.Unmarshal([]byte(createParams.ExecutionConfig.RawConfig), &rawConfig); err != nil {
			log.Error("GetNamingInfoByUserQuery Parse Json Failed, error:", err)
			return "", "", errors.New("get preferred language failed")
		}
		preferredLanguage = rawConfig.ChatContext.PreferredLanguage
	}
	ctx = context.WithValue(ctx, common.KeyPreferredLanguage, preferredLanguage)

	sessionId := uuid.NewString()
	requestId := uuid.NewString()
	rawInputParams := &definition.AskParams{
		SessionId:    sessionId,
		RequestId:    requestId,
		SessionType:  definition.SessionTypeChat,
		ChatContext:  userQuery,
		QuestionText: userQuery,
		ChatTask:     definition.QUEST_REQUIREMENT_NAMEING,
		Extra:        nil,
		Stream:       true,
		Mode:         definition.SessionModeDesign,
	}

	systemPrompt, userPrompt := BuildPrompt(ctx, rawInputParams, nil)
	messages := make([]*agentDefinition.Message, 0)
	systemMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeSystem, Content: systemPrompt}
	log.Debugf("[naming] message, requestId=%s, message=%+v", requestId, systemMessage)
	messages = append(messages, systemMessage)
	userMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: userPrompt}
	log.Debugf("[naming] message, requestId=%s, message=%+v", requestId, userMessage)
	messages = append(messages, userMessage)

	remoteAsk, _ := agentSupport.BuildRemoteChatMessageParam(ctx, rawInputParams, messages, nil, "")
	modelConfig, found := agentSupport.PrepareModelConfig(rawInputParams, 0)
	if found {
		remoteAsk.ModelConfig = modelConfig
	}
	// 指定特定路由 qwen-turbo
	remoteAsk.TaskId = definition.CommonAgentSummaryTaskId
	remoteAsk.AgentId = definition.AgentCommonAgentId

	requestParams := remote.CommonAgentRequestParams{
		ServiceName:  definition.AgentCommonAgentService,
		FetchKey:     "llm_model_result",
		ModelRequest: remoteAsk,
		Timeout:      120 * time.Second,
		RequestId:    requestId,
		AgentId:      definition.AgentCommonAgentId,

		OutputFormatVersion: "2",
	}

	resp, err := remote.ExecuteStreamedAgentRequestWithModelConfig(context.Background(), requestParams, &remoteAsk.ModelConfig)
	if err == nil && resp.IsSuccess() && len(resp.Text) != 0 {
		respMsg := resp.Text
		if strings.Contains(respMsg, "<thinking>") && strings.Contains(respMsg, "</thinking>") {
			respMsg = strings.Split(respMsg, "</thinking>")[1]
			respMsg = strings.TrimSpace(respMsg)
		}
		if strings.Contains(respMsg, ";") {
			splitWord := strings.Split(respMsg, ";")
			taskName := normalizedTaskName(splitWord[0])
			fileName := normalizedFileName(splitWord[1])
			if len(taskName) != 0 && len(fileName) != 0 {
				return taskName, fileName, nil
			}
			log.Infof("GetNamingInfoByUserQuery taskName or fileName is empty")
		}
	}
	//模型报错 or 模型有返回,但是格式不正确 => 尝试通过用户输入解析成任务名/文件名
	taskName := normalizedTaskName(userQuery)
	fileName := normalizedFileName(userQuery)
	if len(taskName) == 0 {
		taskName = "unknown task"
	}
	if len(fileName) == 0 {
		fileName = "unknown-task"
	}
	log.Errorf("get name from query:agent execute request finished unexpected, requestId=%s, err=%v", requestId, err)
	//模型调用失败,兜底返回
	return taskName, fileName, nil
}

func normalizedTaskName(taskName string) string {
	normalized := strings.ToLower(strings.TrimSpace(taskName))
	normalized = regexp.MustCompile(`\s+`).ReplaceAllString(normalized, "-")
	normalized = regexp.MustCompile(`-+`).ReplaceAllString(normalized, "-")
	normalized = regexp.MustCompile(`"`).ReplaceAllString(normalized, "")
	normalized = strings.ReplaceAll(normalized, "-", " ")
	runes := []rune(normalized)
	if len(runes) <= 60 {
		return normalized
	}
	return string(runes[:57]) + "..."
}

func normalizedFileName(fileName string) string {
	normalized := strings.ToLower(strings.TrimSpace(fileName))
	normalized = regexp.MustCompile(`\s+`).ReplaceAllString(normalized, "-")
	normalized = regexp.MustCompile(`[^a-z0-9-]`).ReplaceAllString(normalized, "")
	normalized = regexp.MustCompile(`-+`).ReplaceAllString(normalized, "-")
	runes := []rune(normalized)
	if len(runes) <= 60 {
		return normalized
	}
	return string(runes[:57]) + "..."
}

// GetSummaryByUserQuery 根据用户输入的查询，获取命名信息
func GetSummaryByUserQuery(userQuery []string) (string, error) {

	summaryAgentBuilder, _ := InitExecutorBuilder()
	agent := summaryAgentBuilder.Build()
	sessionId := uuid.NewString()
	requestId := uuid.NewString()
	ctx, _ := InitAgentContext(context.Background(), sessionId, requestId)

	totalUserQuery := strings.Join(userQuery, "\n")
	rawInputParams := &definition.AskParams{
		SessionId:    sessionId,
		RequestId:    requestId,
		SessionType:  definition.SessionTypeChat,
		ChatContext:  totalUserQuery,
		QuestionText: totalUserQuery,
		ChatTask:     definition.QUEST_PROMPT_OPTIMIZE,
		Extra:        nil,
		Stream:       true,
		Mode:         definition.SessionModeDesign,
	}
	inputs := map[string]any{
		common.KeyChatAskParams:  rawInputParams,
		common.KeyUserInputQuery: totalUserQuery,
	}
	var runErr error
	func() {
		defer func() {
			if r := recover(); r != nil {
				stack := debug.Stack()
				errMsg := fmt.Sprintf("Fatal error in agent.RunSync: %v\n%s", r, stack)
				log.Error(errMsg)
				runErr = fmt.Errorf("fatal error: %v", r)
			}
		}()
		runErr = agent.RunSync(ctx, inputs)
	}()
	if runErr != nil {
		log.Errorf("run sync error, agent=%s, requestId=%s, err=%v", BuilderIdentifier, requestId, runErr)
		return "", runErr
	}
	finalMessage := ""
	message, ok := inputs["resultMessage"]
	if ok {
		finalMessage = message.(string)
	} else {
		finalMessage = strings.Join(userQuery, "\n")
	}
	return finalMessage, nil

}
