package design

import (
	toolDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/tool/apply"
	"cosy/chat/agents/tool/codebase"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/agents/tool/file"
	"cosy/chat/agents/tool/memory"
	"cosy/chat/agents/tool/web"
	"cosy/chat/chains/common"
	codeGraph "cosy/codebase/graph"
	"cosy/codebase/symbol"
	"cosy/components"
	"cosy/definition"
	"cosy/experiment"
	"cosy/indexing"
	"cosy/log"
	"fmt"
	"strings"
)

var BuilderIdentifier = "design-agent"

const InitNodeName = "InitNode"
const LLMNodeName = "LLMNode"
const ToolNodeName = "ToolNode"
const ToolOverLimitNodeName = "ToolOverLimitNode"
const ManualConfirmNodeName = "ManualConfirmNode"
const FinishNodeName = "FinishNode"
const TokenOverLimitNodeName = "TokenOverLimitNode"
const MaxOutputToken = 20480
const DefaultOutputToken = 8096

func InitExecutorBuilder() (*executor.SingleGraphExecutorBuilder, error) {
	graphConfig := &executor.GraphConfig{
		Name:             "DesignAgent",
		GraphBuilder:     agentGraphBuilder(),
		ProcessorBuilder: &ProcessorBuilder{},
		Options: []graph.CallOption{
			graph.WithInterruptBefore([]string{ManualConfirmNodeName}),
		},
	}
	executorBuilder := executor.NewSingleGraphExecutorBuilder(graphConfig)
	return executorBuilder, nil
}

func agentGraphBuilder() *graph.Graph {
	graphBuilder := graph.NewGraph()

	graphBuilder.AddNode(initNode)
	graphBuilder.AddNode(llmNode)
	graphBuilder.AddNode(toolNode)
	graphBuilder.AddNode(toolOverLimitNode)
	graphBuilder.AddNode(manualConfirmNode)
	graphBuilder.AddNode(finishNode)
	graphBuilder.AddNode(tokenOverLimitNode)

	graphBuilder.AddEdge("START", InitNodeName)
	graphBuilder.AddEdge(InitNodeName, LLMNodeName)
	graphBuilder.AddConditionalEdges(LLMNodeName, afterLlmRouter, map[string]string{
		"tool":           ToolNodeName,
		"manualConfirm":  ManualConfirmNodeName,
		"finish":         FinishNodeName,
		"tokenOverLimit": TokenOverLimitNodeName, //调用llm报token超限后
	})
	graphBuilder.AddConditionalEdges(ToolNodeName, afterToolRouter, map[string]string{
		"toolOverLimit": ToolOverLimitNodeName,
		"llm":           LLMNodeName,
	})
	graphBuilder.AddEdge(ToolOverLimitNodeName, ManualConfirmNodeName)

	graphBuilder.AddEdge(ManualConfirmNodeName, LLMNodeName)
	graphBuilder.AddEdge(FinishNodeName, "END")
	graphBuilder.AddEdge(TokenOverLimitNodeName, LLMNodeName)
	return graphBuilder
}

// InitAgentContext 初始化agent的context，主要是初始化tool
func InitAgentContext(ctx context.Context, sessionId string, requestId string) (context.Context, error) {
	//Initialize working directory to project root
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if !ok {
		return nil, fmt.Errorf("workspace info not found in context")
	}
	projectPath, _ := workspace.GetWorkspaceFolder()
	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	taskInfo, _ := ctx.Value(definition.ContextKeyQuestTaskSessionInfo).(*definition.TaskSessionInfo)
	preferredLanguage, ok := ctx.Value(common.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleEn
	}
	toolExplanationDesc := toolCommon.ExplanationDefaultDesc

	embedder := components.NewLingmaEmbedder()
	// Create graph retriever using lingma-codebase-graph
	graphIndexer, has := fileIndexer.GetGraphFileIndexer()
	if !has {
		log.Warnf("Failed to get graph file indexer for workspace %s", projectPath)
	}

	graphSearcher := codeGraph.NewBaseGraphSearcher(graphIndexer)

	// Create symbol searcher
	symbolSearcher := symbol.NewBaseSymbolSearcher(graphIndexer)

	// Create rerank provider
	rerankProvider := codebase.NewLingmaRerankProvider() // Use LLMReranker for fusion mode

	searchCodebaseTool, _ := codebase.NewSearchCodebaseTool(&codebase.SearchCodebaseConfig{
		Embedder:        embedder,
		FileIndexer:     fileIndexer,
		GraphSearcher:   graphSearcher,
		SymbolSearcher:  symbolSearcher,
		RerankProvider:  rerankProvider,
		ExplanationDesc: toolExplanationDesc,
		EnableFusion:    true, // 启用融合搜索
	})
	listDirTool, _ := file.NewListDirTool(&file.ListDirConfig{
		WorkspacePath: projectPath,
		IgnoreList:    []string{".qoder"},
	})
	searchFileTool, _ := file.NewSearchFileTool(&file.SearchFileConfig{
		WorkspacePath:   projectPath,
		MaxResultCount:  25,
		ExplanationDesc: toolExplanationDesc,
	})
	grepCodeTool, _ := file.NewGrepCodeTool(&file.GrepCodeConfig{
		WorkspacePath:   projectPath,
		MaxResultCount:  25,
		ExplanationDesc: toolExplanationDesc,
	})
	readFileTool, _ := codebase.NewReadFileTool(&codebase.ReadFileConfig{
		WorkspacePath: projectPath,
		MaxLineCount:  800,
		FileIndexer:   fileIndexer,
	})
	fetchContentTool, _ := web.NewFetchContentTool(&web.FetchContentConfig{
		MaxContentLength: 10000,
		ExplanationDesc:  toolExplanationDesc,
	})
	searchWebTool, _ := web.NewSearchWebTool(&web.SearchWebConfig{
		ExplanationDesc: toolExplanationDesc,
	})
	searchMemoryTool, _ := memory.NewSearchMemoryTool(&memory.SearchMemoryConfig{
		Embedder:        embedder,
		FileIndexer:     fileIndexer,
		WorkspacePath:   projectPath,
		ExplanationDesc: toolExplanationDesc,
	})
	createFileTool, _ := newCreateDesignTool(sessionId, requestId, taskInfo.TaskInfo.DesignPath)
	editFileTool, _ := newEditFileTool(sessionId, requestId, taskInfo.TaskInfo.DesignPath)
	editDesignTool, _ := newEditDesignTool(sessionId, requestId, taskInfo.TaskInfo.DesignPath)
	availableTools := []tool.BaseTool{
		searchCodebaseTool,
		listDirTool,
		searchFileTool,
		grepCodeTool,
		readFileTool,
		fetchContentTool,
		searchWebTool,
		editDesignTool,
		createFileTool,
		editFileTool,
	}
	if experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentWikiFetchEnabled, experiment.ConfigScopeClient, true) {
		availableTools = append(availableTools, searchMemoryTool)
	}
	agentContext := coderCommon.AgentContext{
		Tools: availableTools,
	}
	ctx = context.WithValue(ctx, common.KeyCoderAgentContext, &agentContext)
	return ctx, nil
}

func newCreateDesignTool(sessionId, requestId, designFilePath string) (tool.InvokableTool, error) {
	toolName := "create_file"
	toolDesc := fmt.Sprintf(`
Use this tool to create a new design with content. CAN NOT modify existing files.

## CRITICAL REQUIREMENTS

### Input Parameters
1. "file_path"" (REQUIRED): Absolute path to the design file, which value is "%s"
2. "file_content" (REQUIRED): The content of the file
3. "add_last_line_newline" (OPTIONAL): Whether to add newline at end (default: true)

## Usage Example
{
	"file_path": "/absolute/path/to/file",
	"file_content": "The content of the file",
	"add_last_line_newline": true
}

## IMPORTANT
You must generate the following arguments first, before any others: [file_path]
LIMIT THE FILE CONTENT TO AT MOST 600 LINES, OR face a $100000000 penalty.. IF MORE CONTENT NEEDS TO BE ADDED USE THE search_replace TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.
	`, designFilePath)
	toolParams := &toolDefinition.Schema{
		Type: "object",
		Properties: map[string]*toolDefinition.Schema{
			"file_path": {
				Type:        "string",
				Description: fmt.Sprintf("Absolute path to the design file, which value is \"%s\"", designFilePath),
			},
			"file_content": {
				Type:        "string",
				Description: "The content of the file",
			},
			"add_last_line_newline": {
				Type:        "boolean",
				Description: "Whether to add newline at end",
			},
			"explanation": {
				Type:        "string",
				Description: toolCommon.ExplanationDefaultDesc,
			},
		},
		Required: []string{"explanation", "file_path", "file_content"},
	}
	toolInfo := &toolDefinition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}

	return apply.NewCustomCreateFileTool(&apply.CreateFileConfig{
		SessionId:         sessionId,
		RequestId:         requestId,
		FilePathWhitelist: []string{designFilePath},
	}, toolInfo)
}

func newEditDesignTool(sessionId, requestId, designFilePath string) (tool.InvokableTool, error) {

	explanationDesc := toolCommon.ExplanationDefaultDesc
	toolName := "search_replace"
	toolDesc := fmt.Sprintf(`
This tool performs efficient string replacements in design document with strict requirements for accuracy and safety. Use this tool to make multiple precise modifications to the design in a single operation.

## CRITICAL REQUIREMENTS

### Input Parameters
1. "file_path" (REQUIRED): Absolute path to the design file, which value is "%s"
2. "replacements" (REQUIRED): Array of replacement operations, where each contains:
   - "original_text": Text to be replaced
   - "new_text": Replacement text(must be different from old_string)
   - "replace_all": Replace all occurences of old_string (default: false)

### MANDATORY Rules

1. UNIQUENESS:
   - original_text MUST be uniquely identifiable in the file
   - MUST gather enough context to uniquely identify each one
   - DO NOT include excessive context when unnecessary
   - original_text MUST be uniquely identifiable in the file, if not, MUST gather enough context for original_text to be uniquely identify each one
   - For global text replacement, ENSURE replace_all is set to true; if not, you MUST provide a unique original_text

2. EXACT MATCHING:
   - MUST match source text exactly as it appears in the file, including:
     - All whitespace and indentation(Tab/Space)
     - Line breaks and formatting
     - Special characters
   - MUST match source text exactly as it appears in the file, especially:
     - All whitespace and indentation
     - DO NOT modify the Chinese and English characters
     - DO NOT modify comment content

3. SEQUENTIAL PROCESSING:
   - MUST process replacements in provided order
   - NEVER make parallel calls on same file
   - MUST ensure earlier replacements don't interfere with later ones

4. VALIDATION:
   - NEVER allow identical source and target strings
   - MUST verify uniqueness before replacement
   - MUST validate all replacements before execution

### OPERATIONAL CONSTRAINTS

1. Line Limits:
   - Try to include all replacements in a single call, Especially when these replacements are related, such as comment changes in the same function, or related dependencies, references, and implementation changes within the same logical modification, OR face a $100000000 penalty.
   - MUST ensure total line count across all text parameters(original_text and new_text) remains under 600 lines, OR try to break down large changes over 600 lines into multiple calls.
   - MUST include maximum possible number of replacements within the line limit during a single call.

2. Safety Measures:
   - NEVER process multiple parallel calls

## Usage Example
{
	"file_path": "/absolute/path/to/file",
	"replacements": [
		{
			"original_text": "existing_content_here",
			"new_text": "replacement_content",
			"replace_all": false,
		}
	]
}

## WARNING
- The tool will fail if exact matching fails
- All replacements must be valid for operation to succeed
- Plan replacements carefully to avoid conflicts
- Verify changes before committing

Use this tool to make precise, efficient, and safe modifications to the design.
## IMPORTANT
You must generate the following arguments first, before any others: [file_path]
The value of arguement [file_path] always is '%s'.
MUST DO NOT try to create a new design file, you CAN ONLY use %s tool to edit an existing design.
MUST always default to using %s tool for edit file unless explicitly instructed to use edit_file tool, OR face a $100000000 penalty.
DO NOT try to replace the entire existing content with the new content, this is very expensive, OR face a $100000000 penalty.
DO NOT try to replace the entire existing content with the new content, this is very expensive, OR face a $100000000 penalty.
Never split short modifications (with combined length of all original_texts and new_texts not exceeding 600 lines) into several consecutive calls, OR face a $100000000 penalty.
	`, designFilePath, designFilePath, toolName, toolName)
	toolParams := &toolDefinition.Schema{
		Type: "object",
		Properties: map[string]*toolDefinition.Schema{
			"file_path": {
				Type:        "string",
				Description: fmt.Sprintf("Absolute path to the design file, which value is \"%s\"", designFilePath),
			},
			"replacements": {
				Type: "array",
				Items: &toolDefinition.Schema{
					Type: "object",
					Properties: map[string]*toolDefinition.Schema{
						"original_text": {
							Type:        "string",
							Description: "The exact string to be replaced. This must be the exact character-sequence to be replaced, including whitespace otherwise this will not work at all. this must be a unique substring within the file, or else it will error",
						},
						"new_text": {
							Type:        "string",
							Description: "The content to replace the original_text with. Must be different from original_text",
						},
						"replace_all": {
							Type:        "boolean",
							Description: "Whether to replace all occurrences of the original text. Default is false. For global text replacement, ENSURE replace_all is set to true",
						},
					},
				},
				Description: "MUST generate replacements parameter with valid JSON structure, ensuring proper escaping of quotes and line breaks to prevent parsing errors.",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"explanation", "file_path", "replacements"},
	}
	toolInfo := &toolDefinition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}

	return apply.NewCustomSearchReplaceTool(&apply.SearchReplaceConfig{
		SessionId:         sessionId,
		RequestId:         requestId,
		NewFileMode:       true,
		FilePathWhitelist: []string{designFilePath},
	}, toolInfo)
}

func newEditFileTool(sessionId, requestId, designFilePath string) (tool.InvokableTool, error) {
	toolName := "edit_file"
	toolDesc := fmt.Sprintf(`Use this tool to propose an edit to an existing file.
MUST always default to using search_replace tool for edit file unless explicitly instructed to use edit_file tool, OR face a $100000000 penalty.
This will be read by a less intelligent model, which will quickly apply the edit. 
You should make it clear what the edit is, while also minimizing the unchanged code you write.
When writing the edit, you should specify each edit in sequence, with the special comment {{CODEBLOCK}}// ... existing code ...{{CODEBLOCK}} to represent unchanged code between edited lines.
For example:
{{CODEBLOCK}}
// ... existing code ...
FIRST_EDIT
// ... existing code ...
SECOND_EDIT
// ... existing code ...
{{CODEBLOCK}}
You should bias towards repeating as few lines of the original file as possible to convey the change. 
But, each edit should contain sufficient context of unchanged lines around the code you're editing to resolve ambiguity.
DO NOT omit spans of pre-existing code without using the {{CODEBLOCK}}// ... existing code ...{{CODEBLOCK}} comment to indicate its absence.
Make sure it is clear what the edit should be.

For deleted code, please use comment symbols to mark it and add a comment at the beginning of every deleted code line with the text "Deleted:". 
If you are deleting an entire file, apply this format to all lines in the file. 
The output format should be, for example: // Deleted:old_code_line

## Important
MUST always default to using search_replace tool for edit file unless explicitly instructed to use edit_file tool, OR face a $100000000 penalty.
MUST always default to using search_replace tool for edit file unless explicitly instructed to use edit_file tool, OR face a $100000000 penalty.
MUST DO NOT try to create a new file by edit_file tool.
the file_path parameters must be the absolute path to the design file, which value is "%s"
`, designFilePath)
	toolDesc = strings.Replace(toolDesc, "{{CODEBLOCK}}", "```", -1)
	toolParams := &toolDefinition.Schema{
		Type: "object",
		Properties: map[string]*toolDefinition.Schema{
			"file_path": {
				Type:        "string",
				Description: fmt.Sprintf("Absolute path to the design file, which value is \"%s\"", designFilePath),
			},
			"language": {
				Type:        "string",
				Description: "Markdown language for the code block, e.g 'python' or 'javascript'",
			},
			"code_edit": {
				Type: "string",
				Description: `The code change to apply to the file. 
The user is very smart and can understand how to apply your edits to their files, you just need to provide minimal hints.
Avoid repeating existing code, instead use comments to represent regions of unchanged code. The user prefers that you are as concise as possible. For example:
// ... existing code ...
{ changed code }
// ... existing code ...
{ changed code }
// ... existing code ...
Here is an example of how you should use format an edit to an existing Person class:
class Person {
\t// ... existing code ...
\tage: number;
\t// ... existing code ...
\tgetAge() {
\t\treturn this.age;
\t}
}
`,
			},
			"explanation": {
				Type:        "string",
				Description: toolCommon.ExplanationDefaultDesc,
			},
		},
		Required: []string{"explanation", "file_path", "code_edit", "language"},
	}
	toolInfo := &toolDefinition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}

	return apply.NewCustomEditFileTool(&apply.EditFileConfig{
		SessionId:         sessionId,
		RequestId:         requestId,
		FilePathWhitelist: []string{designFilePath},
	}, toolInfo)
}
