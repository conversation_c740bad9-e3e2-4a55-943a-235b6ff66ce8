package design

import (
	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/coder/compact"
	masterSupport "cosy/chat/agents/quest/support"
	"cosy/chat/agents/support"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/agents/tool/mcp"
	"cosy/chat/chains/common"
	chatUtil "cosy/chat/util"
	"cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/global"
	"cosy/log"
	"cosy/longruntask"
	"cosy/memory/stm"
	"cosy/prompt"
	"cosy/remote"
	"cosy/util"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"strings"
	"time"
)

// 初始化上下文
var initNode = graph.NewNode(
	InitNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		// 读一下历史消息
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)

		sessionId := rawInputParams.SessionId
		requestId := rawInputParams.RequestId
		systemPrompt, userPrompt := BuildPrompt(ctx, rawInputParams, agentState.Inputs)
		shortTermMemory := agentState.ShortTermMemory
		shortTermMemory.Clear()
		systemMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeSystem, Content: systemPrompt}
		log.Debugf("[design_agent] message, requestId=%s, message=%+v", requestId, systemMessage)
		shortTermMemory.AddMessage(systemMessage)

		history, ok := masterSupport.GetSessionHistory(ctx, sessionId, definition.SessionModeDesign)
		if ok && len(history) > 0 {
			// 把历史消息保存到chat_message中，系统指令排除
			shortTermMemory.AppendMessages(history...)
		}

		// 判断是否有图片d
		contextProviderExtras := agentState.Inputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra)
		imageUrls := chatUtil.FindImageUrlsFromContext(contextProviderExtras)
		if len(imageUrls) > 0 {
			imageUrl := imageUrls[0]
			chatPartText := agentDefinition.ChatMessagePart{
				Type: agentDefinition.ChatMessagePartTypeText,
				Text: userPrompt,
			}
			chatPartImage := agentDefinition.ChatMessagePart{
				Type: agentDefinition.ChatMessagePartTypeImageURL,
				ImageURL: &agentDefinition.ChatMessageImageURL{
					URL:      imageUrl,
					MIMEType: "image/png",
				},
			}
			multiContent := []agentDefinition.ChatMessagePart{chatPartText, chatPartImage}
			userMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: userPrompt, MultiContent: multiContent}
			shortTermMemory.AddMessage(userMessage)
			saveMessageHistory(agentState, userMessage)
		} else {
			userMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: userPrompt}
			shortTermMemory.AddMessage(userMessage)
			saveMessageHistory(agentState, userMessage)
		}
		return agentState, nil
	}))

// 调用大模型
var llmNode = graph.NewNode(
	LLMNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		//发起llm调用前做上下文长度进行处理
		agentContext, ok := ctx.Value(common.KeyCoderAgentContext).(*coderCommon.AgentContext)
		if !ok {
			return nil, errors.New("AgentContext not found")
		}

		toolParams := support.ConvertToModelToolParam(ctx, agentContext.Tools)
		toolParams = append(toolParams, mcp.ListMcpTools()...)

		toolsToken, _ := support.GetToolsToken(toolParams)
		// 假性增加toolToken的数量，因为下面输出要从8k扩容到20k，这里压缩式需要假定toolsToken增加12k的固定长度。
		toolsToken += MaxOutputToken - DefaultOutputToken
		messages, _ := compact.GlobalModelCompactor.Compact(ctx, agentState.ShortTermMemory, "", rawInputParams.RequestId, toolParams, toolsToken)
		var response *agentDefinition.Message
		var err error
		var callServerRequestId = uuid.NewString()

		requestId := rawInputParams.RequestId
		sessionId := rawInputParams.SessionId
		sessionType := rawInputParams.SessionType
		remoteAsk, _ := support.BuildRemoteChatMessageParam(ctx, rawInputParams, messages, toolParams, rawInputParams.Mode)
		modelConfig, found := support.PrepareModelConfig(rawInputParams, agentState.ToolCallCount)
		if found {
			remoteAsk.ModelConfig = modelConfig
		}
		// 扩大design agent的output到20k, key的配置需要和服务端解析保持一致
		if remoteAsk.Parameters == nil {
			//design mode下，params基本为nil
			remoteAsk.Parameters = make(map[string]any)
		}
		remoteAsk.Parameters["max_tokens"] = MaxOutputToken

		sseCtx, cancelFunc := context.WithCancel(ctx)
		syncer := support.LLMResponseHandler{
			SessionType:   sessionType,
			SessionId:     sessionId,
			RequestId:     requestId,
			ToolCallCount: agentState.ToolCallCount,
			CtxForClient:  agentState.CtxForClient,
			CancelFunc:    cancelFunc,
		}
		remoteAsk.RequestId = callServerRequestId
		response, err = support.SubscribeCommonAgentChat(sseCtx, *rawInputParams, remoteAsk, syncer.OnDeltaContent, syncer.OnToolParseEvent)
		if err != nil {
			log.Infof("[design_agent] finish chat with error, chatRecordId=%s, requestId=%s", requestId, callServerRequestId)
		} else {
			syncer.PostSyncToolCall(sseCtx, response)
			log.Debugf("[design_agent] finish chat, chatRecordId=%s, requestId=%s, responseMeta=%+v", requestId, callServerRequestId, response.ResponseMeta)
			if response.ResponseMeta.FinishReason == definition.FinishReasonErrorFinish {
				if response.ToolCalls != nil && util.Contains(toolCommon.EditFileTools, response.ToolCalls[0].Function.Name) {
					response.ResponseMeta.FinishReason = definition.FinishReasonLength
				} else {
					err = cosyErrors.New(cosyErrors.SystemError, response.Content)
				}
			} else if response.Content == "" && response.ToolCallID == "" &&
				len(response.ToolCalls) == 0 && response.ReasoningContent == "" {
				select {
				case <-ctx.Done():
					response.Content = coderCommon.EmptyMessage
				default:
					//如果lastMessage值为空，则直接退出
					log.Error("assistant response is empty.")
					response.Content = coderCommon.LLMReturnEmpty
				}
			}
		}

		if err != nil {
			if modelQueueStatus, isQueued := chatUtil.GetQueueError(err); isQueued {
				agentState.Inputs[common.KeyModelQueueStatus] = modelQueueStatus
				// 第一轮llm调用，就显示需要排队，发送报错，会触发停止会话
				if !agentState.FirstLLMFinish {
					return agentState, err
				}
			}

			syncer.PostSyncToolCallOnError(sseCtx, response)

			// 调用llm失败，特定报错码，发送继续的消息给客户端
			log.Debugf("[design_agent] llm call failed, requestId=%s, callServerRequestId=%s, error=%s", requestId, callServerRequestId, err.Error())
			var customErr *cosyErrors.Error
			overTokenLimit := chatUtil.IsModelTokenOvertLimit(err)
			if overTokenLimit {
				agentState.InputTokenOverLimit = true
				return agentState, nil
			}
			if errors.As(err, &customErr) && support.IsLLMErrorCodeNeedManualConfirm(customErr.Code) {
				agentState.ManualConfirmErrorCode = customErr.Code
				return agentState, nil
			}
			// 发送报错，会触发停止会话
			return agentState, err
		}

		saveMessageHistory(agentState, response)
		agentState.ShortTermMemory.AddMessage(response)
		if !agentState.FirstLLMFinish {
			agentState.FirstLLMFinish = true
		}
		return agentState, nil
	}))

// 工具使用到达上限制
var toolOverLimitNode = graph.NewNode(
	ToolOverLimitNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		// 设置需要用户点击“继续”按钮，恢复会话的原因：工具调用超过限制
		agentState.ManualConfirmErrorCode = cosyErrors.ToolCallOverLimit
		return agentState, nil
	}))

// 执行工具调用
var toolNode = graph.NewNode(
	ToolNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		agentContext, ok := ctx.Value(common.KeyCoderAgentContext).(*coderCommon.AgentContext)
		if !ok {
			return nil, errors.New("AgentContext not found")
		}
		inputs := agentState.ToChainInput()
		rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
		requestId := rawInputParams.RequestId
		sessionId := rawInputParams.SessionId
		availableTools := agentContext.Tools
		callback := &support.DefaultToolCallbackHandler{
			CtxForClient: agentState.GetCtxForClient(),
			SessionId:    sessionId,
			RequestId:    requestId,
		}
		toolMessages := support.ExecuteTool(ctx, availableTools, agentState, support.DefaultAgentToolParamSupplier, callback)
		// 遍历 toolMessages，对每个消息调用 addMessageHistory
		for _, message := range toolMessages {
			saveMessageHistory(agentState, message)
		}
		// 每次执行完置为false，调用次数+1
		agentState.Approval = false
		agentState.ToolCallCount++
		agentState.ShortTermMemory.AppendMessages(toolMessages...)
		return agentState, nil
	}))

var manualConfirmNode = graph.NewNode(
	ManualConfirmNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		// 用户确认的信息在之前的interrupt事件的时候发出
		agentState := input.(*coderCommon.CoderAgentState)
		log.Warnf("[design_agent] manual confirm,agentState.ManualConfirmErrorCode=%v", agentState.ManualConfirmErrorCode)
		if agentState.ManualConfirmErrorCode == cosyErrors.ToolCallOverLimit {
			// 工具的调用次数归零
			agentState.ToolCallCount = 0
		}
		// 移除待人工确认的错误信息
		agentState.ManualConfirmErrorCode = 0
		return agentState, nil
	}))

var finishNode = graph.NewNode(
	FinishNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)

		var userQuerys []string
		historyMessages := agentState.ShortTermMemory.Messages()
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)

		messageMaxIndex := len(historyMessages) - 1
		// 添加需要总结的历史消息
		firstUserQuery := ""
		for idx, message := range historyMessages {
			if message.Role == agentDefinition.RoleTypeUser {
				indexEnd := strings.LastIndex(message.Content, "</user_query>")
				indexStart := strings.LastIndex(message.Content, "<user_query>")
				if indexStart == -1 || indexEnd == -1 || indexStart > indexEnd {
					continue
				}
				rawUserQuery := message.Content[indexStart+len("<user_query>") : indexEnd]
				if len(firstUserQuery) == 0 {
					firstUserQuery = rawUserQuery
				}

				if idx+2 < messageMaxIndex {
					nextMessage := historyMessages[idx+2]
					if nextMessage.Role == agentDefinition.RoleTypeTool {
						// 简单判断，若发现后续的message有tool调用，说明是有效的userquery
						userQuerys = append(userQuerys, rawUserQuery)
					}
				}

			}
		}
		summaryUserQuery := firstUserQuery
		totalUserQueryCount := len(userQuerys)
		// 调整下userQuery的总结策略，以观后效
		if totalUserQueryCount > 10 {
			summaryUserQuery = strings.Join(userQuerys, "\n")
			if llmSummaryUserQuery, err := userPromptOptimize(ctx, summaryUserQuery, rawInputParams.RequestId); err == nil {
				summaryUserQuery = *llmSummaryUserQuery
			}
		} else if totalUserQueryCount > 1 {
			summaryUserQuery = strings.Join(userQuerys, "\n")
		}

		taskInfo := agentState.Inputs[common.KeyQuestTaskInfo].(*definition.TaskInfo)
		taskId := taskInfo.TaskId

		if len(taskId) < 0 {
			log.Errorf("[design_agent] get empty task id, sessionId=%s, inputExtra: %+v", agentState.Inputs)
		} else {
			log.Debugf("[design_agent] user query summary,  summaryUserQuery=%s", summaryUserQuery)
			taskManager := longruntask.GetTaskManager()
			if _, err := taskManager.SyncDesignDoc(ctx, taskId, &longruntask.SyncDesignContentParams{
				UserRequirements: summaryUserQuery,
			}); err != nil {
				log.Errorf("Failed to update design doc. taskId:%s, err:%s", taskId, err.Error())
			}
		}

		return agentState, nil
	}))

// 请求模型输入token时超限，进行压缩后继续会话
var tokenOverLimitNode = graph.NewNode(
	TokenOverLimitNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		agentState.InputTokenOverLimit = false
		//2. 触发总结
		agentContext, ok := ctx.Value(common.KeyCoderAgentContext).(*coderCommon.AgentContext)
		if !ok {
			return nil, errors.New("AgentContext not found")
		}
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		toolParams := support.ConvertToModelToolParam(ctx, agentContext.Tools)
		lastConversationInfo := stm.NewConversationInfo()
		sessionId := rawInputParams.SessionId
		value, exists := stm.GetConversationInfo(sessionId)
		if exists {
			lastConversationInfo = value
		}
		messages, err := compact.GlobalModelCompactor.CompactDir(ctx, agentState.ShortTermMemory, lastConversationInfo.ProjectRuleAndUserInfo, rawInputParams.RequestId, toolParams)
		if err == nil {
			agentState.ShortTermMemory.SetMessages(messages)
		}
		return agentState, nil
	}))

func saveMessageHistory(sweBenchState *coderCommon.CoderAgentState, lastMessage *agentDefinition.Message) {
	rawInputParams := sweBenchState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
	sessionId := rawInputParams.SessionId
	requestId := rawInputParams.RequestId
	log.Debugf("[design_agent] message, requestId=%s, message=%+v", requestId, lastMessage)
	if lastMessage.Content == coderCommon.EmptyMessage {
		return
	}
	stm.AddMessageHistory(sessionId, requestId, lastMessage)
}

func userPromptOptimize(ctx context.Context, totalUserQuery, requestId string) (*string, error) {
	log.Debugf("Start user query optimize: %s", totalUserQuery)
	preferredLanguage := support.GetPreferredLanguage(ctx)

	input := prompt.CoderAgentSystemPromptInput{
		PreferredLanguage: preferredLanguage,
		CurrentSystemTime: time.Now().Format("2006-01-02 15:04:05"),
		IsLingmaProduct:   global.IsLingmaProduct(),
	}

	// 获取总结提示词
	systemPromptForSummary, err := prompt.Engine.RenderQuestPromptOptimizeSystemPrompt(input)
	if err != nil {
		return nil, fmt.Errorf("failed to get userPrompt Optimize system prompt: %w", err)
	}
	sysMsgForUserPromptOptimize := &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeSystem,
		Content: systemPromptForSummary,
	}

	// 构建用于总结的消息序列
	var messagesToSend []*agentDefinition.Message

	// 添加总结提示词作为最后的用户消息
	summaryUserMessage := &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeUser,
		Content: totalUserQuery,
	}
	messagesToSend = append(messagesToSend, sysMsgForUserPromptOptimize, summaryUserMessage)

	//// Reinforce with prefilled responses
	//// https://docs.anthropic.com/en/docs/test-and-evaluate/strengthen-guardrails/keep-claude-in-character
	//prefilledResponsesMessage := &agentDefinition.Message{
	//	Role:    agentDefinition.RoleTypeAssistant,
	//	Content: "[Summarizer]",
	//}
	//messagesToSend = append(messagesToSend, prefilledResponsesMessage)

	// 构建模型请求
	params := ctx.Value(common.KeyChatAskParams).(*definition.AskParams)

	remoteAsk, _ := support.BuildRemoteChatMessageParam(ctx, params, messagesToSend, nil, "")

	modelConfig, found := support.PrepareModelConfig(params, 0)
	if found {
		remoteAsk.ModelConfig = modelConfig
	}

	requestParams := remote.CommonAgentRequestParams{
		ServiceName:         definition.AgentCommonAgentService,
		FetchKey:            "llm_model_result",
		ModelRequest:        remoteAsk,
		Timeout:             120 * time.Second,
		RequestId:           requestId,
		AgentId:             definition.AgentCommonAgentId,
		OutputFormatVersion: "2",
	}

	// 调用模型进行总结
	resp, err := remote.ExecuteStreamedAgentRequestWithModelConfig(context.Background(), requestParams, &remoteAsk.ModelConfig)
	if err != nil || !resp.IsSuccess() {
		return nil, fmt.Errorf("failed to execute LLM userQuery optimize: %w", err)
	}

	return &resp.Text, nil
}

func userPromptOptimizeByHistoryMessage(ctx context.Context, historyMessages []*agentDefinition.Message, requestId string) (*string, error) {

	preferredLanguage, ok := ctx.Value(common.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleEn
	}

	input := prompt.CoderAgentSystemPromptInput{
		PreferredLanguage: preferredLanguage,
		CurrentSystemTime: time.Now().Format("2006-01-02 15:04:05"),
		IsLingmaProduct:   global.IsLingmaProduct(),
	}

	// 获取总结提示词
	systemPromptForSummary, err := prompt.Engine.RenderQuestPromptOptimizeSystemPrompt(input)
	if err != nil {
		return nil, fmt.Errorf("failed to get userPrompt Optimize system prompt: %w", err)
	}
	sysMsgForUserPromptOptimize := &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeSystem,
		Content: systemPromptForSummary,
	}

	// 构建用于总结的消息序列
	messagesToSend := historyMessages

	messagesToSend[0] = sysMsgForUserPromptOptimize

	// Reinforce with prefilled responses
	// https://docs.anthropic.com/en/docs/test-and-evaluate/strengthen-guardrails/keep-claude-in-character
	prefilledResponsesMessage := &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeAssistant,
		Content: "[Summarizer]",
	}
	messagesToSend = append(messagesToSend, prefilledResponsesMessage)

	// 构建模型请求
	params := ctx.Value(common.KeyChatAskParams).(*definition.AskParams)

	remoteAsk, _ := support.BuildRemoteChatMessageParam(ctx, params, messagesToSend, nil, "")

	modelConfig, found := support.PrepareModelConfig(params, 0)
	if found {
		remoteAsk.ModelConfig = modelConfig
	}

	requestParams := remote.CommonAgentRequestParams{
		ServiceName:         definition.AgentCommonAgentService,
		FetchKey:            "llm_model_result",
		ModelRequest:        remoteAsk,
		Timeout:             120 * time.Second,
		RequestId:           requestId,
		AgentId:             definition.AgentCommonAgentId,
		OutputFormatVersion: "2",
	}

	// 调用模型进行总结
	resp, err := remote.ExecuteStreamedAgentRequestWithModelConfig(context.Background(), requestParams, &remoteAsk.ModelConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to execute LLM userQuery optimize: %w", err)
	}

	return &resp.Text, nil
}
