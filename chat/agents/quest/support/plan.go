package support

import (
	"context"
	"cosy/extension/plan"
	"cosy/log"
	"cosy/longruntask"
)

type UploadPlanRequest struct {
	Tasks []*plan.TaskNode `json:"tasks"`
}

func UploadPlanToServer(sessionId string) {
	taskStatistics := plan.GetTaskStatistics(sessionId, true)
	totalCount := taskStatistics.Total
	completeCount := taskStatistics.Complete

	markdown := ""
	jsonStr := ""
	convertPlan, ok := plan.GenerateDetailPlan(sessionId)
	if ok {
		markdown = convertPlan.MarkdownContent
		jsonStr = convertPlan.TaskTreeJson
	}
	taskId, err := longruntask.GetTaskIdBySessionId(context.TODO(), sessionId)
	if err != nil {
		log.Errorf("GetTaskIdBySessionId error: %s", err.Error())
		return
	}
	if taskId == "" {
		log.Errorf("Cannot find taskId by execution sessionId: %s", sessionId)
		return
	}
	taskManager := longruntask.GetTaskManager()
	log.Debugf("UpdateActionFlow, taskId: %s, markdown: %s, jsonStr: %s", taskId, markdown, jsonStr)
	if _, err = taskManager.UpdateActionFlow(context.Background(), taskId, markdown, jsonStr, totalCount, completeCount); err != nil {
		log.Errorf("UpdateActionFlow err: %s", err.Error())
	}
}
