package longrunning

import (
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/coder/compact"
	questCommon "cosy/chat/agents/quest/common"
	questSupport "cosy/chat/agents/quest/support"
	"cosy/chat/agents/support"
	agentSupport "cosy/chat/agents/support"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/chains/common"
	chatUtil "cosy/chat/util"
	"cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/extension/plan"
	"cosy/global"
	"cosy/log"
	"cosy/longruntask"
	"cosy/memory/stm"
	"cosy/prompt"
	"cosy/util"
	"errors"
	"fmt"
	"strings"
	"time"

	agentLlms "code.alibaba-inc.com/cosy/lingma-agent-graph/llms"

	"github.com/google/uuid"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

// 初始化上下文
var initNode = graph.NewNode(
	InitNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*questCommon.QuestAgentState)
		agentState.LastUpdateTasksToolCallCount = questCommon.GetToolCallLimit()
		// 读一下历史消息
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		sessionId := rawInputParams.SessionId
		requestId := rawInputParams.RequestId
		//systemPrompt, userPrompt := buildPrompt(ctx, rawInputParams, agentState.Inputs, agentState.TaskInfo)
		systemPrompt, userMessage, err := buildPrompt(ctx, rawInputParams, agentState.Inputs, agentState.TaskInfo)
		if err != nil {
			return nil, cosyErrors.New(cosyErrors.SystemError, "failed to build prompt")
		}
		shortTermMemory := agentState.ShortTermMemory
		shortTermMemory.Clear()
		systemMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeSystem, Content: systemPrompt}
		log.Debugf("[long_running_agent] message, requestId=%s, systemMessage=%+v", requestId, systemMessage)
		shortTermMemory.AddMessage(systemMessage)

		history, ok := questSupport.GetSessionHistory(ctx, sessionId, definition.SessionModeLongRunning)

		if ok && len(history) > 0 {
			// 把历史消息保存到chat_message中，系统指令排除
			shortTermMemory.AppendMessages(history...)
		}

		// 判断是否有图片
		contextProviderExtras := agentState.Inputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra)
		imageUrls := chatUtil.FindImageUrlsFromContext(contextProviderExtras)
		if len(imageUrls) > 0 {
			// 遍历图片连接
			for _, imageUrl := range imageUrls {
				userMessage.MultiContent = append(userMessage.MultiContent, agentDefinition.ChatMessagePart{
					Type: agentDefinition.ChatMessagePartTypeImageURL,
					ImageURL: &agentDefinition.ChatMessageImageURL{
						URL:      imageUrl,
						MIMEType: "image/png",
					},
				})
			}
		}
		shortTermMemory.AddMessage(userMessage)
		saveMessageHistory(agentState, userMessage)
		return agentState, nil
	}))

// 调用大模型
var llmNode = graph.NewNode(
	LLMNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*questCommon.QuestAgentState)
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		agentContext, ok := ctx.Value(common.KeyCoderAgentContext).(*coderCommon.AgentContext)
		if !ok {
			return nil, errors.New("AgentContext not found")
		}

		toolParams := support.ConvertToModelToolParam(ctx, agentContext.Tools)
		//toolParams = append(toolParams, mcp.ListMcpTools()...)

		toolsToken, _ := support.GetToolsToken(toolParams)
		lastConversationInfo := stm.NewConversationInfo()
		value, exists := stm.GetConversationInfo(rawInputParams.SessionId)
		if exists {
			lastConversationInfo = value
		}
		//发起llm调用前做上下文长度进行处理
		//messages, _ := coder.TruncateMessages(ctx, agentState.ShortTermMemory, rawInputParams.PluginPayloadConfig.IsEnableProjectRule, rawInputParams.RequestId)

		messages, _ := compact.GlobalModelCompactor.Compact(ctx, agentState.ShortTermMemory, lastConversationInfo.ProjectRuleAndUserInfo, rawInputParams.RequestId, toolParams, toolsToken)

		var response *agentDefinition.Message
		var err error
		var callServerRequestId = uuid.NewString()

		requestId := rawInputParams.RequestId
		sessionId := rawInputParams.SessionId
		sessionType := rawInputParams.SessionType
		remoteAsk, _ := support.BuildRemoteChatMessageParam(ctx, rawInputParams, messages, toolParams, "")
		modelConfig, found := support.PrepareModelConfig(rawInputParams, agentState.ToolCallCount)
		if found {
			remoteAsk.ModelConfig = modelConfig
		}

		sseCtx, cancelFunc := context.WithCancel(ctx)
		syncer := support.LLMResponseHandler{
			SessionType:         sessionType,
			SessionId:           sessionId,
			RequestId:           requestId,
			Mode:                definition.SessionModeLongRunning,
			ToolCallCount:       agentState.ToolCallCount,
			CtxForClient:        agentState.CtxForClient,
			CancelFunc:          cancelFunc,
			CallServerRequestId: callServerRequestId,
			EnableLogTime:       true,
		}
		remoteAsk.RequestId = callServerRequestId
		response, err = support.SubscribeCommonAgentChat(sseCtx, *rawInputParams, remoteAsk, syncer.OnDeltaContent, syncer.OnToolParseEvent)
		if err != nil {
			log.Infof("[long_running_agent] finish chat with error, chatRecordId=%s, requestId=%s", requestId, callServerRequestId)
		} else {
			syncer.PostSyncToolCall(sseCtx, response)
			log.Debugf("[long_running_agent] finish chat, chatRecordId=%s, requestId=%s, responseMeta=%+v", requestId, callServerRequestId, response.ResponseMeta)
			if response.ResponseMeta.FinishReason == definition.FinishReasonErrorFinish {
				if response.ToolCalls != nil && util.Contains(toolCommon.EditFileTools, response.ToolCalls[0].Function.Name) {
					response.ResponseMeta.FinishReason = definition.FinishReasonLength
				} else {
					err = cosyErrors.New(cosyErrors.SystemError, response.Content)
				}
			} else if response.Content == "" && response.ToolCallID == "" &&
				len(response.ToolCalls) == 0 && response.ReasoningContent == "" {
				select {
				case <-ctx.Done():
					response.Content = coderCommon.EmptyMessage
				default:
					//如果lastMessage值为空，则直接退出
					log.Error("assistant response is empty.")
					response.Content = coderCommon.LLMReturnEmpty
				}
			}
		}
		if err != nil {
			syncer.PostSyncToolCallOnError(sseCtx, response)
		}
		if err != nil {
			if modelQueueStatus, isQueued := chatUtil.GetQueueError(err); isQueued {
				agentState.Inputs[common.KeyModelQueueStatus] = modelQueueStatus
				// 第一轮llm调用，就显示需要排队，发送报错，会触发停止会话
				if !agentState.FirstLLMFinish {
					return agentState, err
				}
			}

			// 调用llm失败，特定报错码，发送继续的消息给客户端
			log.Debugf("[long_running_agent] llm call failed, requestId=%s, callServerRequestId=%s, error=%s", requestId, callServerRequestId, err.Error())
			var customErr *cosyErrors.Error
			// 判断是否是token超限的，如果超限了就触发更激进的短期记忆压缩
			overTokenLimit := chatUtil.IsModelTokenOvertLimit(err)
			if overTokenLimit {
				agentState.InputTokenOverLimit = true
				return agentState, nil
			}
			if errors.As(err, &customErr) && agentSupport.IsLLMErrorCodeNeedManualConfirm(customErr.Code) {
				agentState.ManualConfirmErrorCode = customErr.Code
				return agentState, nil
			}
			// 发送报错，会触发停止会话
			return agentState, err
		}
		lastMessage := response
		if lastMessage.ReasoningContent != "" {
			thinkStr := "<think>\n" + lastMessage.ReasoningContent + "\n</think>\n"
			lastMessage.Content = thinkStr + lastMessage.Content
		}
		questSupport.SaveMessageHistory(agentState, lastMessage)
		agentState.ShortTermMemory.AddMessage(lastMessage)
		// 设置第一轮llm完成的信息
		if !agentState.FirstLLMFinish {
			agentState.FirstLLMFinish = true
		}
		return agentState, nil
	}))

// 工具使用到达上限制
var toolOverLimitNode = graph.NewNode(
	ToolOverLimitNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*questCommon.QuestAgentState)
		// 设置需要用户点击“继续”按钮，恢复会话的原因：工具调用超过限制
		agentState.ManualConfirmErrorCode = cosyErrors.ToolCallOverLimit
		return agentState, nil
	}))

//var manualConfirmNode = graph.NewNode(
//	ManualConfirmNodeName,
//	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
//		// 用户确认的信息在之前的interrupt事件的时候发出
//		agentState := input.(*questCommon.QuestAgentState)
//		log.Warnf("[long_running_agent] manual confirm,agentState.ManualConfirmErrorCode=%v", agentState.ManualConfirmErrorCode)
//		if agentState.ManualConfirmErrorCode == cosyErrors.ToolCallOverLimit {
//			// 工具的调用次数归零
//			agentState.ToolCallCount = 0
//		}
//		// 移除待人工确认的错误信息
//		agentState.ManualConfirmErrorCode = 0
//		return agentState, nil
//	}))

// 执行工具调用
var toolNode = graph.NewNode(
	ToolNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*questCommon.QuestAgentState)
		agentContext, ok := ctx.Value(common.KeyCoderAgentContext).(*coderCommon.AgentContext)
		if !ok {
			return nil, errors.New("AgentContext not found")
		}
		inputs := agentState.ToChainInput()
		rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
		requestId := rawInputParams.RequestId
		sessionId := rawInputParams.SessionId
		availableTools := agentContext.Tools

		coderAgentState := &coderCommon.CoderAgentState{
			Inputs:          agentState.Inputs,
			ShortTermMemory: agentState.ShortTermMemory,
			CtxForClient:    agentState.CtxForClient,
		}
		callback := &agentSupport.QuestToolCallbackHandler{
			CtxForClient: agentState.GetCtxForClient(),
			SessionId:    sessionId,
			RequestId:    requestId,
			TaskId:       questCommon.GetTaskIdInGraph(input),
		}
		toolMessages := support.ExecuteTool(ctx, availableTools, coderAgentState, support.DefaultAgentToolParamSupplier, callback)
		// 遍历 toolMessages，对每个消息调用 addMessageHistory
		for _, message := range toolMessages {
			saveMessageHistory(agentState, message)
		}
		// 每次执行完置为false，调用次数+1
		agentState.Approval = false
		agentState.ToolCallCount++
		// 如果调用了update_tasks，记录当前的ToolCallCount
		hasAddTasksCall, hasUpdateTasksCall := hasTaskRelatedCall(agentState)
		if hasAddTasksCall || hasUpdateTasksCall {
			agentState.LastUpdateTasksToolCallCount = agentState.ToolCallCount
		}
		// 检查是否需要添加计划更新提醒
		if _, exists := shouldAddPlanUpdateReminder(agentState, sessionId); exists {
			addPlanUpdateReminder(toolMessages)
			agentState.LastUpdateTasksToolCallCount = agentState.ToolCallCount
		}
		agentState.ShortTermMemory.AppendMessages(toolMessages...)

		// 最后一次使用了ask_user工具，退出agent等待用户的
		historyMessages := agentState.ShortTermMemory.Messages()
		var assistantMessageIndex int = -1 // 初始化索引为-1，表示未找到的情况
		length := len(historyMessages)
		// 倒序遍历
		for i := length - 1; i >= 0; i-- {
			message := historyMessages[i]
			if message.Role == agentDefinition.RoleTypeAssistant {
				assistantMessageIndex = i
				break
			}
		}
		assistantMessage := historyMessages[assistantMessageIndex]
		if assistantMessage.ToolCalls != nil {
			for _, toolCall := range assistantMessage.ToolCalls {
				if toolCall.Function.Name == "ask_user" {
					agentState.Extra[StateExtraExitForMessage] = true
					taskId := questCommon.GetTaskIdInGraph(input)
					if taskId != "" {
						longruntask.UpdateChatTaskStatus(ctx, taskId, sessionId, definition.ChatTaskStatusActionRequired, nil)
					}
					return input, nil
				}
			}
		}

		return agentState, nil
	}))

var handleUserRequestNode = graph.NewNode(
	HandleUserRequestNodeName,
	graph.NewFunctionNodeRunnable(handleUserRequestFunc))

var handleUserRequestBeforeCheckNode = graph.NewNode(
	HandleUserRequestBeforeCheckNodeName,
	graph.NewFunctionNodeRunnable(handleUserRequestFunc))

var checkNode = graph.NewNode(
	CheckNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*questCommon.QuestAgentState)
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		sessionId := rawInputParams.SessionId
		hasFinishPlan := plan.HasAllTasksFinished(sessionId)
		if hasFinishPlan {
			agentState.Extra[StateExtraHasPlanFinish] = true
		} else {
			agentState.Extra[StateExtraHasPlanFinish] = false
			builder := strings.Builder{}
			preferredLanguage := agentSupport.GetPreferredLanguage(ctx)
			builder.WriteString(`<system-reminder>
	The tasks have not been fully completed, please check the task list and continue with the execution.
	Do not attempt to confirm or request information from the user; continue execution until all tasks are complete.
	Your reply must begin with the ` + preferredLanguage + ` translation of \"The system has detected that the task is not yet complete and will continue to execute.\" Then, use relevant tools to assist in completing the task.
</system-reminder>`)
			message := &agentDefinition.Message{
				Role:    agentDefinition.RoleTypeUser,
				Content: builder.String(),
			}
			agentState.ShortTermMemory.AppendMessages(message)
		}
		return agentState, nil
	}))

// 总结agent输出，要求llm按一定格式要求进行总结
var summaryNode = graph.NewNode(
	SummaryNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*questCommon.QuestAgentState)

		params := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		preferredLanguage := support.GetPreferredLanguage(ctx)

		summaryUserInput := prompt.CoderAgentSystemPromptInput{
			BaseInput: prompt.BaseInput{
				RequestId: params.RequestId,
				SessionId: params.SessionId,
			},
			PreferredLanguage: preferredLanguage,
			CurrentSystemTime: time.Now().Format("2006-01-02 15:04:05"),
			IsLingmaProduct:   global.IsLingmaProduct(),
		}

		//发起llm调用前做上下文长度进行处理
		userQuery, promptErr := prompt.Engine.RenderLongRunAgentSummaryNodeUserPrompt(summaryUserInput)
		if promptErr != nil {
			log.Error("Failed to render longRunAgentSummaryNodeUserPrompt ", promptErr)
			return agentState, promptErr
		}

		userMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: userQuery}
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		// 同步FileDiff
		util.GoSafeRoutine(func() {
			longruntask.UploadFileChangesForQuest(util.GetWorkspaceUriFromCtx(ctx), rawInputParams.SessionId, agentState.TaskInfo.TaskId)
		})

		messages, _ := compact.GlobalModelCompactor.Compact(ctx, agentState.ShortTermMemory, "", rawInputParams.RequestId, nil, 0)
		messages = append(messages, userMessage)

		var toolParams []agentLlms.Tool
		////agent下配置mcp tools
		mode := rawInputParams.Mode
		//if mode == definition.SessionModeAgent {
		//	toolParams = append(toolParams, mcp.ListMcpTools()...)
		//}
		var response *agentDefinition.Message
		var err error
		var callServerRequestId = uuid.NewString()

		requestId := rawInputParams.RequestId
		//sessionId := rawInputParams.SessionId
		//sessionType := rawInputParams.SessionType
		remoteAsk, _ := support.BuildRemoteChatMessageParam(ctx, rawInputParams, messages, toolParams, mode)
		remoteAsk.AgentId = definition.AgentCommonAgentId
		remoteAsk.TaskId = definition.AgentQuestTestModeAgentTaskId

		modelConfig, found := support.PrepareModelConfig(rawInputParams, agentState.ToolCallCount)
		if found {
			remoteAsk.ModelConfig = modelConfig
		}
		sseCtx, _ := context.WithCancel(ctx)
		// TODO 临时推流的会话窗口，本地调试效果用
		//tmpSyncer := support.LLMResponseHandler{
		//	SessionType:   sessionType,
		//	SessionId:     sessionId,
		//	RequestId:     requestId,
		//	ToolCallCount: agentState.ToolCallCount,
		//	CtxForClient:  agentState.CtxForClient,
		//	//CancelFunc:    cancelFunc,
		//}
		// TODO 实现单独的summary到服务端的推流，意义可能不大
		syncer := questSupport.SummaryLLMResponseSyncer{}
		remoteAsk.RequestId = callServerRequestId

		//调用模型失败会重试,最多执行3次
		for i := 0; i < 3; i++ {
			response, err = support.SubscribeCommonAgentChat(sseCtx, *rawInputParams, remoteAsk, syncer.SyncContent, syncer.SyncToolCall)
			if err == nil {
				//syncer.PostSyncToolCall(sseCtx, response)
				log.Debugf("[long_running_agent] finish chat, chatRecordId=%s, requestId=%s, responseMeta=%+v", requestId, callServerRequestId, response.ResponseMeta)
				if response.ResponseMeta.FinishReason == definition.FinishReasonErrorFinish {
					err = cosyErrors.New(cosyErrors.SystemError, response.Content)
				} else if response.Content == "" && response.ToolCallID == "" &&
					len(response.ToolCalls) == 0 && response.ReasoningContent == "" {
					select {
					case <-ctx.Done():
						response.Content = coderCommon.EmptyMessage
					default:
						//如果lastMessage值为空，则直接退出
						log.Error("assistant response is empty.")
						err = cosyErrors.New(cosyErrors.SystemError, "assistant response is empty")
					}
				}
				break
			} else {
				log.Infof("[long_running_agent] finish chat with error, chatRecordId=%s, requestId=%s", requestId, callServerRequestId)
				time.Sleep(10 * time.Second)
			}
		}
		if err != nil {
			// 调用llm失败，转换一下错误，通过err退出
			return agentState, err
		}
		lastMessage := response
		if lastMessage.ReasoningContent != "" {
			thinkStr := "<think>\n" + lastMessage.ReasoningContent + "\n</think>\n"
			lastMessage.Content = thinkStr + lastMessage.Content
		}
		syncer.SyncPostSummary(ctx, lastMessage, agentState.TaskInfo.TaskId)
		return agentState, nil
	}))

var finishNode = graph.NewNode(
	FinishNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*questCommon.QuestAgentState)
		return agentState, nil
	}))

// 请求模型输入token时超限，进行压缩后继续会话
var tokenOverLimitNode = graph.NewNode(
	TokenOverLimitNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*questCommon.QuestAgentState)
		agentState.InputTokenOverLimit = false
		//2. 触发总结
		agentContext, ok := ctx.Value(common.KeyCoderAgentContext).(*coderCommon.AgentContext)
		if !ok {
			return nil, errors.New("AgentContext not found")
		}
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		toolParams := agentSupport.ConvertToModelToolParam(ctx, agentContext.Tools)
		lastConversationInfo := stm.NewConversationInfo()
		sessionId := rawInputParams.SessionId
		value, exists := stm.GetConversationInfo(sessionId)
		if exists {
			lastConversationInfo = value
		}
		messages, err := compact.GlobalModelCompactor.CompactDir(ctx, agentState.ShortTermMemory, lastConversationInfo.ProjectRuleAndUserInfo, rawInputParams.RequestId, toolParams)
		if err == nil {
			agentState.ShortTermMemory.SetMessages(messages)
		}
		return agentState, nil
	}))

// handleUserRequestFunc 响应用户请求
func handleUserRequestFunc(ctx context.Context, input graph.State) (graph.State, error) {
	agentState := input.(*questCommon.QuestAgentState)
	rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
	sessionId := rawInputParams.SessionId
	requestId := rawInputParams.RequestId
	taskId := questCommon.GetTaskIdInGraph(input)

	// 从chan中获取通知
	agentStateInChan := agentState.Extra["agent_state_in_chan"].(chan agentSupport.StateInNotice)
	notice := agentSupport.StateInNotice{}
	// 前端展示继续按钮的情况下，默认等待用户点击继续，或者发送新的会话
	if agentState.ManualConfirmErrorCode != 0 {
		select {
		case inNotice := <-agentStateInChan:
			log.Debugf("[long_running_agent] receive notice, sessionId=%s, requestId=%s, notice=%+v", sessionId, requestId, inNotice)
			if inNotice.Type == agentSupport.StateInNoticeTypeResume {
				log.Warnf("[long_running_agent] manual confirm,agentState.ManualConfirmErrorCode=%v", agentState.ManualConfirmErrorCode)
				// 用户点击继续，更新任务状态为running
				if taskId != "" {
					longruntask.UpdateChatTaskStatus(ctx, taskId, sessionId, definition.ChatTaskStatusRunning, nil)
				}

				// 点击继续，需上报sls
				support.ReportAgentActionState(sessionId, requestId, coderCommon.ActionResume, fmt.Sprintf("%d", agentState.ManualConfirmErrorCode), "")

				// 用户点击继续前hang住的原因为工具调用超过限制
				if cosyErrors.ToolCallOverLimit == agentState.ManualConfirmErrorCode {
					// 工具的调用次数归零
					agentState.LastUpdateTasksToolCallCount -= agentState.ToolCallCount
					agentState.ToolCallCount = 0
				}
				// 移除待人工确认的错误信息
				agentState.ManualConfirmErrorCode = 0
				return input, nil
			} else if inNotice.Type == agentSupport.StateInNoticeTypeExitForMessage {
				agentState.Extra[StateExtraExitForMessage] = true
				return input, nil
			}
		case <-ctx.Done():
			log.Debugf("[long_running_agent] cancle while waiting notice, sessionId=%s, requestId=%s", sessionId, requestId)
			return input, nil
		}
	} else {
		select {
		case inNotice := <-agentStateInChan:
			log.Debugf("[long_running_agent] receive notice, sessionId=%s, requestId=%s, notice=%+v", sessionId, requestId, inNotice)
			notice = inNotice
		default:
			log.Debugf("[long_running_agent] receive no notice, sessionId=%s, requestId=%s", sessionId, requestId)
			return input, nil
		}
	}
	if notice.Type == agentSupport.StateInNoticeTypePause {
		// 处理暂停
		if taskId != "" {
			longruntask.UpdateChatTaskStatus(ctx, taskId, sessionId, definition.ChatTaskStatusPaused, nil)
		}
		for {
			select {
			case inNotice := <-agentStateInChan:
				log.Debugf("[long_running_agent] receive notice, sessionId=%s, requestId=%s, notice=%+v", sessionId, requestId, inNotice)
				if inNotice.Type == agentSupport.StateInNoticeTypeResume {
					if taskId != "" {
						longruntask.UpdateChatTaskStatus(ctx, taskId, sessionId, definition.ChatTaskStatusRunning, nil)
					}
					return input, nil
				} else if inNotice.Type == agentSupport.StateInNoticeTypeExitForMessage {
					agentState.Extra[StateExtraExitForMessage] = true
					if taskId != "" {
						longruntask.UpdateChatTaskStatus(ctx, taskId, sessionId, definition.ChatTaskStatusActionRequired, nil)
					}
					return input, nil
				}
			case <-ctx.Done():
				log.Debugf("[long_running_agent] cancle while waiting notice, sessionId=%s, requestId=%s", sessionId, requestId)
				return input, nil
			}
		}
	} else if notice.Type == agentSupport.StateInNoticeTypeExitForMessage {
		// 处理退出
		agentState.Extra[StateExtraExitForMessage] = true
		if taskId != "" {
			longruntask.UpdateChatTaskStatus(ctx, taskId, sessionId, definition.ChatTaskStatusActionRequired, nil)
		}
		return input, nil
	}
	// 其他消息不处理
	return input, nil
}

func saveMessageHistory(sweBenchState *questCommon.QuestAgentState, lastMessage *agentDefinition.Message) {
	rawInputParams := sweBenchState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
	sessionId := rawInputParams.SessionId
	requestId := rawInputParams.RequestId
	log.Debugf("[long_running_agent] message, requestId=%s, message=%+v", requestId, lastMessage)
	if lastMessage.Content == coderCommon.EmptyMessage {
		return
	}
	stm.AddMessageHistory(sessionId, requestId, lastMessage)
}

// hasTaskRelatedCall 检查消息中是否包含add_tasks/update_tasks工具调用
func hasTaskRelatedCall(agentState *questCommon.QuestAgentState) (hasAddTasksCall, hasUpdateTasksCall bool) {
	historyMessages := agentState.GetShortTermMemory().Messages()
	lastMessage := historyMessages[len(historyMessages)-1]
	if !hasToolCalls(lastMessage) {
		return false, false
	}
	hasAddTasksCall = false
	hasAddTasksCall = false
	for _, toolCall := range lastMessage.ToolCalls {
		if toolCall.Function.Name == "add_tasks" {
			hasAddTasksCall = true
		}
		if toolCall.Function.Name == "update_tasks" {
			hasUpdateTasksCall = true
		}
	}
	return hasAddTasksCall, hasUpdateTasksCall
}

// shouldAddPlanUpdateReminder 检查是否需要添加计划更新提醒
func shouldAddPlanUpdateReminder(agentState *questCommon.QuestAgentState, sessionId string) (string, bool) {
	// 检查是否达到10次工具调用且从上次update_tasks调用后已经进行了10次工具调用
	if agentState.ToolCallCount-agentState.LastUpdateTasksToolCallCount >= 10 {
		// 检查是否存在plan
		if detailPlan, exists := plan.GenerateDetailPlan(sessionId); exists && detailPlan != nil {
			return detailPlan.MarkdownContent, true
		}
	}
	return "", false
}

// addPlanUpdateReminder 添加计划更新提醒消息
func addPlanUpdateReminder(toolMessages []*agentDefinition.Message) {
	log.Debugf("[common_dev_agent] Added plan update reminder")
	planUpdateReminder := "\n\n<system-reminder>\nYou haven't updated your task list recently. Consider calling update_tasks to track progress and check off completed tasks.\n</system-reminder>"
	toolMessages[len(toolMessages)-1].Content += planUpdateReminder
}
