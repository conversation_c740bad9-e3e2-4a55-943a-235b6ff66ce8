package coder

import (
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/coder/compact"
	agentSupport "cosy/chat/agents/support"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/agents/tool/mcp"
	"cosy/chat/chains/common"
	chatUtil "cosy/chat/util"
	"cosy/definition"
	cosyDefinition "cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/prompt"
	"cosy/util"
	"errors"
	"strings"

	"cosy/extension/plan"
	"cosy/log"
	"cosy/memory/stm"

	"github.com/google/uuid"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

// 初始化上下文
var initNode = graph.NewNode(
	InitNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		agentState.LastUpdateTasksToolCallCount = coderCommon.GetToolCallLimit()
		// 读一下历史消息
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		sessionId := rawInputParams.SessionId
		requestId := rawInputParams.RequestId
		var (
			systemPrompt     string
			userPrompt       string
			userPromptResult *prompt.UserPromptsResult
			err              error
		)
		history, hasAskMode, hasAgentOrEditMode, ok := stm.GetMessageHistory(ctx, sessionId)
		if ok && len(history) == 0 {
			//没有历史对话，恢复是首次对话，用于组装project_rule和user_info等信息
			stm.SetConversationInfo(sessionId, stm.NewConversationInfo())
		}
		//if ask agent, build ask agent prompt
		mode := rawInputParams.Mode
		//ask 模式下的自定义指令
		if mode == definition.SessionModeChat && rawInputParams.TaskDefinitionType == definition.TaskDefinitionTypeCustom {
			systemPrompt, userPrompt, err = buildCustomInstructsPrompt(ctx, rawInputParams)
			if err != nil {
				return nil, cosyErrors.New(cosyErrors.SystemError, "failed to build custom instructs prompt")
			}
		} else {
			systemPrompt, userPromptResult, err = buildPromptV2(ctx, rawInputParams, agentState)
		}
		if systemPrompt == "" || err != nil {
			return nil, cosyErrors.New(cosyErrors.SystemError, "failed to build prompt")
		}
		shortTermMemory := agentState.ShortTermMemory
		shortTermMemory.Clear()
		systemMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeSystem, Content: systemPrompt}
		log.Debugf("[common_dev_agent] mode=%s, message, requestId=%s, message=%+v", mode, requestId, systemMessage)
		shortTermMemory.AddMessage(systemMessage)


		var historyImageUrls []string
		if ok {
			// 把历史消息保存到chat_message中，系统指令排除
			if len(history) > 0 {
				shortTermMemory.AppendMessages(history...)
				mode := rawInputParams.Mode
				if mode == definition.SessionModeAgent && hasAskMode {
					//避免切换模式时导致不调用edit_file工具的情况
					if userPrompt != "" {
						userPrompt = userPrompt + "\nYour previous responses may not have had access to tools, but now you do. Please call tools like edit_file, run_in_terminal, get_problems and others as appropriate.\nNEVER output CODE_EDIT_BLOCK code to the me, unless requested."
					}
					if userPromptResult != nil {
						userPromptResult.UserPromptTaskBase += "\nYour previous responses may not have had access to tools, but now you do. Please call tools like edit_file, run_in_terminal, get_problems and others as appropriate.\nNEVER output CODE_EDIT_BLOCK code to the me, unless requested."
					}
				}
				// 如果上一轮模式是是agent或者edit模式，提示模型工具列表发生了变化
				if mode == definition.SessionModeChat && hasAgentOrEditMode {
					//避免切换模式时ask模型调用了不支持的工具
					if userPrompt != "" {
						userPrompt = userPrompt + "\n<tips>\nThe list of available tools has changed. Please check the list of available tools carefully and do not call unsupported tools such as run_in_terminal, edit_file, etc.\n</tips>"
					}
					if userPromptResult != nil {
						userPromptResult.UserPromptTaskBase += "\n\n<tips>\nThe list of available tools has changed. Please check the list of available tools carefully and do not call unsupported tools such as run_in_terminal, edit_file, etc.\n</tips>"
					}
				}
				// 获取历史会话中的图片信息
				for _, singleHistoryItem := range history {
					// 只处理角色为"user"且包含多内容的消息
					if singleHistoryItem.Role == agentDefinition.RoleTypeUser && singleHistoryItem.MultiContent != nil {
						for _, historyContent := range singleHistoryItem.MultiContent {
							if historyContent.Type == agentDefinition.ChatMessagePartTypeImageURL && historyContent.ImageURL.URL != "" {
								historyImageUrls = append(historyImageUrls, historyContent.ImageURL.URL)
							}
						}
					}
				}
			}
		}

		// 判断是否有图片
		contextProviderExtras := agentState.Inputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra)
		imageUrls := chatUtil.FindImageUrlsFromContext(contextProviderExtras)

		// 构建多内容消息
		multiContent := buildMultiContent(userPromptResult, userPrompt, imageUrls, historyImageUrls)

		// 创建用户消息
		userMessage := &agentDefinition.Message{
			Role:         agentDefinition.RoleTypeUser,
			Content:      userPrompt,
			MultiContent: multiContent,
		}

		shortTermMemory.AddMessage(userMessage)
		saveMessageHistory(agentState, userMessage)
		return agentState, nil
	}))

// 调用大模型
var llmNode = graph.NewNode(
	LLMNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		agentContext, ok := ctx.Value(common.KeyCoderAgentContext).(*coderCommon.AgentContext)
		if !ok {
			return nil, errors.New("AgentContext not found")
		}

		toolParams := agentSupport.ConvertToModelToolParam(ctx, agentContext.Tools)
		//agent下配置mcp tools
		mode := rawInputParams.Mode
		if mode == definition.SessionModeAgent {
			toolParams = append(toolParams, mcp.ListMcpTools()...)
		}
		agentState.LastLLMTools = &toolParams
		toolsToken, _ := agentSupport.GetToolsToken(toolParams)
		//发起llm调用前做上下文长度进行处理
		//messages, _ := TruncateMessages(ctx, agentState.ShortTermMemory, rawInputParams.PluginPayloadConfig.IsEnableProjectRule, rawInputParams.RequestId)
		lastConversationInfo := stm.NewConversationInfo()
		sessionId := rawInputParams.SessionId
		value, exists := stm.GetConversationInfo(sessionId)
		if exists {
			lastConversationInfo = value
		}
		messages, _ := compact.GlobalModelCompactor.Compact(ctx, agentState.ShortTermMemory, lastConversationInfo.ProjectRuleAndUserInfo, rawInputParams.RequestId, toolParams, toolsToken)

		var response *agentDefinition.Message
		var err error
		var callServerRequestId = uuid.NewString()

		requestId := rawInputParams.RequestId
		sessionType := rawInputParams.SessionType
		remoteAsk, _ := agentSupport.BuildRemoteChatMessageParam(ctx, rawInputParams, messages, toolParams, mode)
		modelConfig, found := agentSupport.PrepareModelConfig(rawInputParams, agentState.ToolCallCount)
		if found {
			remoteAsk.ModelConfig = modelConfig
		}
		sseCtx, cancelFunc := context.WithCancel(ctx)
		responseHandler := agentSupport.LLMResponseHandler{
			SessionType:   sessionType,
			SessionId:     sessionId,
			RequestId:     requestId,
			ToolCallCount: agentState.ToolCallCount,
			CtxForClient:  agentState.CtxForClient,
			CancelFunc:    cancelFunc,
		}
		remoteAsk.RequestId = callServerRequestId
		response, err = agentSupport.SubscribeCommonAgentChat(sseCtx, *rawInputParams, remoteAsk, responseHandler.OnDeltaContent, responseHandler.OnToolParseEvent)
		if err != nil {
			log.Infof("[common_dev_agent] finish chat with error, chatRecordId=%s, requestId=%s", requestId, callServerRequestId)
		} else {
			responseHandler.PostSyncToolCall(sseCtx, response)
			log.Debugf("[common_dev_agent] finish chat, chatRecordId=%s, requestId=%s, responseMeta=%+v", requestId, callServerRequestId, response.ResponseMeta)
			if response.ResponseMeta.FinishReason == definition.FinishReasonErrorFinish {
				if response.ToolCalls != nil && util.Contains(toolCommon.EditFileTools, response.ToolCalls[0].Function.Name) {
					response.ResponseMeta.FinishReason = definition.FinishReasonLength
				} else {
					err = cosyErrors.New(cosyErrors.SystemError, response.Content)
				}
			} else if response.Content == "" && response.ToolCallID == "" &&
				len(response.ToolCalls) == 0 && response.ReasoningContent == "" {
				select {
				case <-ctx.Done():
					response.Content = coderCommon.EmptyMessage
				default:
					//如果lastMessage值为空，则直接退出
					log.Error("assistant response is empty.")
					response.Content = coderCommon.LLMReturnEmpty
				}
			}
		}
		if err != nil {
			responseHandler.PostSyncToolCallOnError(sseCtx, response)
		}

		if err != nil {
			if modelQueueStatus, isQueued := chatUtil.GetQueueError(err); isQueued {
				agentState.Inputs[common.KeyModelQueueStatus] = modelQueueStatus
				// 第一轮llm调用，就显示需要排队，发送报错，会触发停止会话
				if !agentState.FirstLLMFinish {
					return agentState, err
				}
			}

			err = chatUtil.SetToolSchemaError(err, toolParams)

			// 调用llm失败，特定报错码，发送继续的消息给客户端
			log.Debugf("[common_dev_agent] llm call failed, requestId=%s, callServerRequestId=%s, error=%s", requestId, callServerRequestId, err.Error())
			// 判断是否是token超限的，如果超限了就触发更激进的短期记忆压缩
			overTokenLimit := chatUtil.IsModelTokenOvertLimit(err)
			if overTokenLimit {
				agentState.InputTokenOverLimit = true
				return agentState, nil
			}

			var customErr *cosyErrors.Error
			if errors.As(err, &customErr) && agentSupport.IsLLMErrorCodeNeedManualConfirm(customErr.Code) {
				agentState.ManualConfirmErrorCode = customErr.Code
				return agentState, nil
			}
			// 发送报错，会触发停止会话
			return agentState, err
		}
		lastMessage := response
		if lastMessage.ReasoningContentSignature == "" && lastMessage.ReasoningContent != "" {
			thinkStr := "<think>\n" + lastMessage.ReasoningContent + "\n</think>\n"
			lastMessage.Content = thinkStr + lastMessage.Content
		}
		lastMessage.Extra[coderCommon.KeyModelInfo] = &common.ChatModelInfo{
			ModelKey: modelConfig.Key,
		}
		saveMessageHistory(agentState, lastMessage)
		agentState.ShortTermMemory.AddMessage(lastMessage)
		// 设置第一轮llm完成的信息
		if !agentState.FirstLLMFinish {
			agentState.FirstLLMFinish = true
		}
		return agentState, nil
	}))

// 工具使用到达上限制
var toolOverLimitNode = graph.NewNode(
	ToolOverLimitNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		// 设置需要用户点击“继续”按钮，恢复会话的原因：工具调用超过限制
		agentState.ManualConfirmErrorCode = cosyErrors.ToolCallOverLimit
		return agentState, nil
	}))

// 请求模型输入token时超限，进行压缩后继续会话
var tokenOverLimitNode = graph.NewNode(
	TokenOverLimitNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		agentState.InputTokenOverLimit = false
		//2. 触发总结
		agentContext, ok := ctx.Value(common.KeyCoderAgentContext).(*coderCommon.AgentContext)
		if !ok {
			return nil, errors.New("AgentContext not found")
		}
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		toolParams := agentSupport.ConvertToModelToolParam(ctx, agentContext.Tools)
		lastConversationInfo := stm.NewConversationInfo()
		sessionId := rawInputParams.SessionId
		value, exists := stm.GetConversationInfo(sessionId)
		if exists {
			lastConversationInfo = value
		}
		messages, err := compact.GlobalModelCompactor.CompactDir(ctx, agentState.ShortTermMemory, lastConversationInfo.ProjectRuleAndUserInfo, rawInputParams.RequestId, toolParams)
		if err == nil {
			agentState.ShortTermMemory.SetMessages(messages)
		}
		return agentState, nil
	}))

// Deprecated 工具执行待确认
var toolConfirmNode = graph.NewNode(
	ToolConfirmNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		// 用户确认的信息在之前的interrupt事件的时候发出
		agentState := input.(*coderCommon.CoderAgentState)
		if agentState.Approval {
			// 如果通过就返回
			return agentState, nil
		}
		// 如果没通过需要补上拒绝的信息
		historyMessages := agentState.ShortTermMemory.Messages()
		lastMessage := historyMessages[len(historyMessages)-1]
		// 取消工具执行，返回工具取消执行的消息
		messages := make([]*agentDefinition.Message, 0, 2)
		toolCallResult := "The user has canceled this tool invocation. Rethink or ask the user what he expects to do."
		callResponse := &agentDefinition.Message{
			Role:    agentDefinition.RoleTypeTool,
			Content: toolCallResult,
		}
		// 收集所有工具调用的ID和名称
		var toolCallIDs []string
		var toolNames []string
		for _, t := range lastMessage.ToolCalls {
			toolCallIDs = append(toolCallIDs, t.ID)
			toolNames = append(toolNames, t.Function.Name)
		}
		callResponse.ToolCallID = strings.Join(toolCallIDs, ",")
		callResponse.Name = strings.Join(toolNames, ",")
		//emptyAssistantMessage := &agentDefinition.Message{
		//	Role:    agentDefinition.RoleTypeAssistant,
		//	Content: "",
		//}
		messages = append(messages, callResponse)
		for _, message := range messages {
			saveMessageHistory(agentState, message)
		}
		agentState.ShortTermMemory.AppendMessages(messages...)
		return agentState, nil
	}))

var manualConfirmNode = graph.NewNode(
	ManualConfirmNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		// 用户确认的信息在之前的interrupt事件的时候发出
		agentState := input.(*coderCommon.CoderAgentState)
		log.Warnf("[common_dev_agent] manual confirm,agentState.ManualConfirmErrorCode=%v", agentState.ManualConfirmErrorCode)
		if agentState.ManualConfirmErrorCode == cosyErrors.ToolCallOverLimit {
			// 工具的调用次数归零
			agentState.LastUpdateTasksToolCallCount -= agentState.ToolCallCount
			agentState.ToolCallCount = 0
		}
		// 移除待人工确认的错误信息
		agentState.ManualConfirmErrorCode = 0
		return agentState, nil
	}))

// 执行工具调用
var toolNode = graph.NewNode(
	ToolNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		agentContext, ok := ctx.Value(common.KeyCoderAgentContext).(*coderCommon.AgentContext)
		if !ok {
			return nil, errors.New("AgentContext not found")
		}
		inputs := agentState.ToChainInput()
		rawInputParams := inputs[common.KeyChatAskParams].(*cosyDefinition.AskParams)
		requestId := rawInputParams.RequestId
		sessionId := rawInputParams.SessionId
		availableTools := agentContext.Tools
		callback := &agentSupport.DefaultToolCallbackHandler{
			CtxForClient: agentState.GetCtxForClient(),
			SessionId:    sessionId,
			RequestId:    requestId,
		}
		toolMessages := agentSupport.ExecuteTool(ctx, availableTools, agentState, agentSupport.DefaultAgentToolParamSupplier, callback)
		// 遍历 toolMessages，对每个消息调用 addMessageHistory
		for _, message := range toolMessages {
			saveMessageHistory(agentState, message)
		}
		// 每次执行完置为false，调用次数+1
		agentState.Approval = false
		agentState.ToolCallCount++
		// 如果调用了update_tasks，记录当前的ToolCallCount
		hasAddTasksCall, hasUpdateTasksCall := hasTaskRelatedCall(agentState)
		if hasAddTasksCall || hasUpdateTasksCall {
			agentState.LastUpdateTasksToolCallCount = agentState.ToolCallCount
		}
		// 检查是否需要添加计划更新提醒
		if _, exists := shouldAddPlanUpdateReminder(agentState, sessionId); exists {
			addPlanUpdateReminder(toolMessages)
			agentState.LastUpdateTasksToolCallCount = agentState.ToolCallCount
		}
		if has, ok := agentState.Inputs[common.KeyChatNeedBackFlow].(bool); !ok || !has {
			agentState.Inputs[common.KeyChatNeedBackFlow] = agentSupport.HasEditFileToolCalledSuccess(toolMessages)
		}
		agentState.ShortTermMemory.AppendMessages(toolMessages...)
		return agentState, nil
	}))

var finishToolNode = graph.NewNode(
	FinishToolNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		//finishMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeAssistant, Content: "任务已经完成。如果有其他需求，请告诉我。"}
		//agentState.ShortTermMemory.AddMessage(finishMessage)
		return agentState, nil
	}))

var checkNode = graph.NewNode(
	CheckNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		sessionId := rawInputParams.SessionId
		hasFinishPlan := plan.HasAllTasksFinished(sessionId)
		isPause := plan.IsPause(sessionId)
		if hasFinishPlan || isPause {
			agentState.HasPlanFinish = true
		} else {
			agentState.HasPlanFinish = false
			builder := strings.Builder{}
			preferredLanguage := agentSupport.GetPreferredLanguage(ctx)
			builder.WriteString(`<system-reminder>
	The tasks have not been fully completed, please check the task list and continue with the execution.
	Do not attempt to confirm or request information from the user; continue execution until all tasks are complete.
	Your reply must begin with the ` + preferredLanguage + ` translation of \"The system has detected that the task is not yet complete and will continue to execute.\" Then, use relevant tools to assist in completing the task.
</system-reminder>`)
			message := &agentDefinition.Message{
				Role:    agentDefinition.RoleTypeUser,
				Content: builder.String(),
			}
			agentState.ShortTermMemory.AppendMessages(message)
		}
		return agentState, nil
	}))

// sliceToSet converts a string slice to a set (map[string]struct{})
func sliceToSet(slice []string) map[string]struct{} {
	set := make(map[string]struct{})
	for _, item := range slice {
		set[item] = struct{}{}
	}
	return set
}

// buildMultiContent 构建多内容消息，支持用户提示结果和图片
func buildMultiContent(userPromptResult *prompt.UserPromptsResult, userPrompt string, imageUrls []string, historyImageUrls []string) []agentDefinition.ChatMessagePart {
	var multiContent []agentDefinition.ChatMessagePart

	// 添加规则内容
	if userPromptResult != nil && userPromptResult.UserPromptRules != "" {
		multiContent = append(multiContent, agentDefinition.ChatMessagePart{
			Type: agentDefinition.ChatMessagePartTypeText,
			Text: userPromptResult.UserPromptRules,
		})
	}

	// 添加内存和wiki内容
	if userPromptResult != nil && userPromptResult.UserPromptMemoryAndWiki != "" {
		multiContent = append(multiContent, agentDefinition.ChatMessagePart{
			Type: agentDefinition.ChatMessagePartTypeText,
			Text: userPromptResult.UserPromptMemoryAndWiki,
		})
	}

	// 添加主要任务内容
	taskText := userPrompt
	if userPromptResult != nil && userPromptResult.UserPromptTaskBase != "" {
		taskText = userPromptResult.UserPromptTaskBase
	}
	multiContent = append(multiContent, agentDefinition.ChatMessagePart{
		Type: agentDefinition.ChatMessagePartTypeText,
		Text: taskText,
	})

	// 添加图片内容
	if len(imageUrls) > 0 {
		historySet := sliceToSet(historyImageUrls)
		// 遍历图片连接
		for _, imageUrl := range imageUrls {
			if _, exists := historySet[imageUrl]; exists {
				continue
			}
			multiContent = append(multiContent, agentDefinition.ChatMessagePart{
				Type: agentDefinition.ChatMessagePartTypeImageURL,
				ImageURL: &agentDefinition.ChatMessageImageURL{
					URL:      imageUrl,
					MIMEType: "image/png",
				},
			})
		}
	}
	return multiContent
}

// hasTaskRelatedCall 检查消息中是否包含add_tasks/update_tasks工具调用
func hasTaskRelatedCall(agentState *coderCommon.CoderAgentState) (hasAddTasksCall, hasUpdateTasksCall bool) {
	historyMessages := agentState.GetShortTermMemory().Messages()
	lastMessage := historyMessages[len(historyMessages)-1]
	if !hasToolCalls(lastMessage) {
		return false, false
	}
	hasAddTasksCall = false
	hasAddTasksCall = false
	for _, toolCall := range lastMessage.ToolCalls {
		if toolCall.Function.Name == "add_tasks" {
			hasAddTasksCall = true
		}
		if toolCall.Function.Name == "update_tasks" {
			hasUpdateTasksCall = true
		}
	}
	return hasAddTasksCall, hasUpdateTasksCall
}

// shouldAddPlanUpdateReminder 检查是否需要添加计划更新提醒
func shouldAddPlanUpdateReminder(agentState *coderCommon.CoderAgentState, sessionId string) (string, bool) {
	// 检查是否达到10次工具调用且从上次update_tasks调用后已经进行了10次工具调用
	if agentState.ToolCallCount-agentState.LastUpdateTasksToolCallCount >= 10 {
		// 检查是否存在plan
		if detailPlan, exists := plan.GenerateDetailPlan(sessionId); exists && detailPlan != nil {
			return detailPlan.MarkdownContent, true
		}
	}
	return "", false
}

// addPlanUpdateReminder 添加计划更新提醒消息
func addPlanUpdateReminder(toolMessages []*agentDefinition.Message) {
	log.Debugf("[common_dev_agent] Added plan update reminder")
	planUpdateReminder := "\n\n<system-reminder>\nYou haven't updated your task list recently. Consider calling update_tasks to track progress and check off completed tasks.\n</system-reminder>"
	toolMessages[len(toolMessages)-1].Content += planUpdateReminder
}
