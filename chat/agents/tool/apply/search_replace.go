package apply

import (
	"context"
	"cosy/util"
	"cosy/util/chat"
	"encoding/json"
	"fmt"
	"strings"

	coderCommon "cosy/chat/agents/coder/common"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/service"
	"cosy/chat/tools"
	CosyDefinition "cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/log"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"github.com/google/uuid"
	"golang.org/x/exp/slices"
)

type SearchReplaceConfig struct {
	SessionId         string
	RequestId         string
	ExplanationDesc   string // explanation字段的描述
	NewFileMode       bool
	FilePathWhitelist []string
}

type ReplacementChunk struct {
	TargetContent      *string `json:"original_text,omitempty"`
	ReplacementContent *string `json:"new_text,omitempty"`
	ReplaceAll         bool    `json:"replace_all"`
}

type SearchReplaceRequest struct {
	FilePath            string             `json:"file_path"`
	ReplacementChunks   []ReplacementChunk `json:"replacements"`
	Language            string             `json:"language"`
	Explanation         string             `json:"explanation"` // 添加 explanation 字段的透传
	SyncFunc            func(ctx context.Context, request *EditFileResponse)
	GetCtxForClientFunc func() context.Context
	FileId              string
	PathValid           string
	ParseError          error
	ErrorCode           string
}

func NewSearchReplaceTool(config *SearchReplaceConfig) (tool.InvokableTool, error) {
	explanationDesc := toolCommon.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "search_replace"
	toolDesc := `
This tool performs efficient string replacements in files with strict requirements for accuracy and safety. Use this tool to make multiple precise modifications to a file in a single operation.

## CRITICAL REQUIREMENTS

### Input Parameters
1. "file_path"" (REQUIRED): Absolute path to the target file. The file must exist
2. "replacements" (REQUIRED): Array of replacement operations, where each contains:
   - "original_text": Text to be replaced
   - "new_text": Replacement text(must be different from old_string)
   - "replace_all": Replace all occurences of old_string (default: false)

### MANDATORY Rules

1. UNIQUENESS:
   - original_text MUST be uniquely identifiable in the file
   - MUST gather enough context to uniquely identify each one
   - DO NOT include excessive context when unnecessary
   - original_text MUST be uniquely identifiable in the file, if not, MUST gather enough context for original_text to be uniquely identify each one
   - For global text replacement, ENSURE replace_all is set to true; if not, you MUST provide a unique original_text

2. EXACT MATCHING:
   - MUST match source text exactly as it appears in the file, including:
     - All whitespace and indentation(Tab/Space)
     - Line breaks and formatting
     - Special characters
   - MUST match source text exactly as it appears in the file, especially:
     - All whitespace and indentation
     - DO NOT modify the Chinese and English characters
     - DO NOT modify comment content

3. SEQUENTIAL PROCESSING:
   - MUST process replacements in provided order
   - NEVER make parallel calls on same file
   - MUST ensure earlier replacements don't interfere with later ones

4. VALIDATION:
   - NEVER allow identical source and target strings
   - MUST verify uniqueness before replacement
   - MUST validate all replacements before execution

### OPERATIONAL CONSTRAINTS

1. Line Limits:
   - Try to include all replacements in a single call, Especially when these replacements are related, such as comment changes in the same function, or related dependencies, references, and implementation changes within the same logical modification, OR face a $100000000 penalty.
   - MUST ensure total line count across all text parameters(original_text and new_text) remains under 600 lines, OR try to break down large changes over 600 lines into multiple calls.
   - MUST include maximum possible number of replacements within the line limit during a single call.

2. Safety Measures:
   - NEVER process multiple parallel calls

## Usage Example
{
	"file_path": "/absolute/path/to/file",
	"replacements": [
		{
			"original_text": "existing_code_here",
			"new_text": "replacement_code",
			"replace_all": false,
		}
	]
}

## WARNING
- The tool will fail if exact matching fails
- All replacements must be valid for operation to succeed
- Plan replacements carefully to avoid conflicts
- Verify changes before committing

Use this tool to make precise, efficient, and safe modifications to your files while maintaining code integrity.
## IMPORTANT
You must generate the following arguments first, before any others: [file_path]
MUST DO NOT try to create a new file, you CAN ONLY use search_replace tool to edit an existing file.
MUST always default to using search_replace tool for edit file unless explicitly instructed to use edit_file tool, OR face a $100000000 penalty.
DO NOT try to replace the entire existing content with the new content, this is very expensive, OR face a $100000000 penalty.
DO NOT try to replace the entire existing content with the new content, this is very expensive, OR face a $100000000 penalty.
Never split short modifications (with combined length of all original_texts and new_texts not exceeding 600 lines) into several consecutive calls, OR face a $100000000 penalty.
	`
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"file_path": {
				Type:        "string",
				Description: "File absolute path, the file must exist",
			},
			"replacements": {
				Type: "array",
				Items: &definition.Schema{
					Type: "object",
					Properties: map[string]*definition.Schema{
						"original_text": {
							Type:        "string",
							Description: "The exact string to be replaced. This must be the exact character-sequence to be replaced, including whitespace otherwise this will not work at all. this must be a unique substring within the file, or else it will error",
						},
						"new_text": {
							Type:        "string",
							Description: "The content to replace the original_text with. Must be different from original_text",
						},
						"replace_all": {
							Type:        "boolean",
							Description: "Whether to replace all occurrences of the original text. Default is false. For global text replacement, ENSURE replace_all is set to true",
						},
					},
				},
				Description: "MUST generate replacements parameter with valid JSON structure, ensuring proper escaping of quotes and line breaks to prevent parsing errors.",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"explanation", "file_path", "replacements"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	toolInst := &SearchReplaceTool{
		sessionId:         config.SessionId,
		requestId:         config.RequestId,
		NewFileMode:       config.NewFileMode,
		FilePathWhitelist: config.FilePathWhitelist,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.handle, tool.WithInputParser(toolInst.parseInput), tool.WithOutputConverter(toolInst.convertOutput), tool.WithReturnOutputWhileError()), nil
}

func NewCustomSearchReplaceTool(config *SearchReplaceConfig, toolInfo *definition.ToolInfo) (tool.InvokableTool, error) {
	if toolInfo == nil {
		return NewSearchReplaceTool(config)
	}
	toolInst := &SearchReplaceTool{
		sessionId:         config.SessionId,
		requestId:         config.RequestId,
		FilePathWhitelist: config.FilePathWhitelist,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.handle, tool.WithInputParser(toolInst.parseInput), tool.WithOutputConverter(toolInst.convertOutput), tool.WithReturnOutputWhileError()), nil
}

type SearchReplaceTool struct {
	sessionId         string
	requestId         string
	NewFileMode       bool
	FilePathWhitelist []string
}

func (t *SearchReplaceTool) handle(ctx context.Context, request *SearchReplaceRequest) (*EditFileResponse, error) {
	// 先给插件workspaceFileId
	diffApplyResult := CosyDefinition.DiffApplyResult{
		WorkingSpaceFileId: request.FileId,
	}
	response := &EditFileResponse{
		FilePath:    request.FilePath,
		Language:    request.Language,
		ApplyResult: &diffApplyResult,
	}
	response.FileStatus = service.GENERATING_FAILED.String()
	errorMsg := ""
	if request.ErrorCode != "" {
		if request.ErrorCode == CosyDefinition.LockErrorCode {
			// 暂时使用工具调用不支持的错误码
			errorMsg = fmt.Sprintf("File(%s) currently under modification by another task - parallel editing forbidden, please retry later; MUST ensure sequential editing in all future attempts.", request.FilePath)
			response.ErrorMsg = errorMsg
			return response, cosyErrors.New(cosyErrors.FileEditParallel, errorMsg)
		} else {
			errorMsg = "search_replace failed, pleasy retry later."
			response.ErrorMsg = errorMsg
			return response, cosyErrors.New(cosyErrors.ToolInternalError, errorMsg)
		}
	} else if request.PathValid != "" {
		errorMsg = request.PathValid
		errorCode := cosyErrors.ToolInvalidArguments
		if strings.Contains(request.PathValid, "can not edit the file outside the projects") {
			errorCode = cosyErrors.FileEditForbidden
		}

		response.ErrorMsg = errorMsg
		return response, cosyErrors.New(errorCode, errorMsg)
	} else if request.ParseError != nil || len(request.ReplacementChunks) == 0 || request.FileId == "" || (len(t.FilePathWhitelist) > 0 && !slices.Contains(t.FilePathWhitelist, request.FilePath)) {
		errorCode := CosyDefinition.ContentTooLongErrorCode
		// JSON 解析异常
		errorMsg = "maybe output has reached the token limit. Please explain the issue, then use the tool again and reduce the output content by half."
		if len(t.FilePathWhitelist) > 0 && !slices.Contains(t.FilePathWhitelist, request.FilePath) {
			errorCode = CosyDefinition.FileWhitelistErrorCode
			whiteList := ""
			for _, path := range t.FilePathWhitelist {
				whiteList += path + ", "
			}
			errorMsg = fmt.Sprintf("can't modify the file outside the whitelist: [%s]", whiteList[:len(whiteList)-1])
		}

		if request.ParseError != nil {
			errorMsg = "Missing replacements, MUST generate replacements parameter with valid JSON structure, ensuring proper escaping of quotes and line breaks to prevent parsing errors."
			errorCode = CosyDefinition.ArgumentParseErrorCode
		}

		if request.FileId != "" {
			service.WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, CosyDefinition.WorkingSpaceFileOperateParams{
				Id:     request.FileId,
				OpType: service.CANCEL.String(),
				Params: map[string]interface{}{
					service.IS_FAILED:  true,
					service.ERROR_CODE: errorCode,
				},
			})
		}
		response.ErrorMsg = errorMsg
		return response, cosyErrors.New(cosyErrors.ToolInvalidArguments, errorMsg)
	} else if request.FilePath == "" {
		errorMsg = "Missing file_path, please retry with accurate file path"
		response.ErrorMsg = errorMsg
		return response, cosyErrors.New(cosyErrors.ToolInvalidArguments, errorMsg)
	}
	response.FileStatus = service.APPLYING.String()
	if request.SyncFunc != nil {
		request.SyncFunc(ctx, response)
	}
	finishChan := make(chan CosyDefinition.DiffApplyGenerateFinish, 2)
	modification := ""
	for _, replacement_chunk := range request.ReplacementChunks {
		replacePart := "\n>>>>>>> REPLACE\n"
		if replacement_chunk.ReplaceAll {
			replacePart = "\n>>>>>>> REPLACE*\n"
		}
		if replacement_chunk.TargetContent == nil || replacement_chunk.ReplacementContent == nil {
			replacementStr := "nil"
			if replacement_chunk.ReplacementContent != nil {
				replacementStr = *replacement_chunk.ReplacementContent
			}
			targetContentStr := "nil"
			if replacement_chunk.TargetContent != nil {
				targetContentStr = *replacement_chunk.TargetContent
			}
			log.Debugf("replacements error: originText: %s newText: %s", targetContentStr, replacementStr)
			continue
		}
		originText := *replacement_chunk.TargetContent
		// 允许新建文件模式下，如果是新文件或者空文件，则忽略匹配的原文
		if t.NewFileMode {
			originContent := ""
			if content, err := util.GetFileContent(request.FilePath); err == nil {
				originContent = string(content)
			}
			if originContent == "" && originText != "" {
				originText = ""
			}
		}
		modification += "<<<<<<< SEARCH\n" + originText + "\n=======\n" + *replacement_chunk.ReplacementContent + replacePart
	}
	workingSpaceFileId := request.FileId

	response.FileStatus = service.APPLYING.String()
	param := CosyDefinition.DiffApplyParams{
		NeedSave:                 true,
		NeedRecord:               false,
		NeedSyncWorkingSpaceFile: true,
		NeedWebSocketMethod:      false,
		ChatRecordId:             t.requestId,
		SessionId:                t.sessionId,
		RequestId:                uuid.NewString(),
		Stream:                   true,
		Modification:             modification,
		TaskId:                   CosyDefinition.AgentTaskDiffApplyWithSearchReplace,
		WorkingSpaceFile: CosyDefinition.WorkingSpaceFile{
			Id:       workingSpaceFileId,
			FileId:   request.FilePath,
			Language: request.Language,
			Status:   service.GENERATING.String(),
		},
		FinishFunc: func(params CosyDefinition.DiffApplyGenerateFinish) {
			finishChan <- params
		},
	}

	diffApplyCtx := ctx
	if request.GetCtxForClientFunc != nil {
		diffApplyCtx = request.GetCtxForClientFunc()
	}
	diffApplyResult = tools.DiffApply(diffApplyCtx, param)
	response.FileStatus = service.APPLYING_FAILED.String()
	log.Infof("diffApply result: %+v", diffApplyResult)

	if !diffApplyResult.IsSuccess {
		service.WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, CosyDefinition.WorkingSpaceFileOperateParams{
			Id:     request.FileId,
			OpType: service.CANCEL.String(),
			Params: map[string]interface{}{
				service.IS_FAILED:  true,
				service.ERROR_CODE: diffApplyResult.ErrorCode,
			},
		})
		errorCode := cosyErrors.FileEditParallel
		if diffApplyResult.ErrorCode == CosyDefinition.LockErrorCode {
			// 暂时使用工具调用不支持的错误码
			errorMsg = fmt.Sprintf("File(%s) currently under modification by another task - parallel editing forbidden, please retry later; MUST ensure sequential editing in all future attempts.", request.FilePath)
		} else {
			errorMsg = "search_replace failed, pleasy retry later."
			errorCode = cosyErrors.ToolInternalError
		}
		response.ErrorMsg = errorMsg
		return response, cosyErrors.New(errorCode, errorMsg)
	}
	// edit_file触发成功后，就不需要报错了，所有结果通过response.DiffApplyGenerateFinish透出
	select {
	case result := <-finishChan:
		response.DiffApplyGenerateFinish = &result
		if response.ErrorMsg == "" && response.DiffApplyGenerateFinish.ErrorCode != "" {
			response.ErrorMsg = chat.GetErrorMessage(ctx, response.DiffApplyGenerateFinish.ErrorCode)
		}
	case <-ctx.Done():
	}

	if response.DiffApplyGenerateFinish != nil && response.DiffApplyGenerateFinish.StatusCode == 200 {
		response.FileStatus = service.APPLIED.String()
	}

	return response, nil
}

// 临时修复列表类型的json解析异常
func SmartParse(argument string, request *SearchReplaceRequest) error {
	var finalErr error
	finalErr = nil
	if err := json.Unmarshal([]byte(argument), request); err == nil {
		return nil
	} else {
		finalErr = err
	}
	body := map[string]interface{}{}
	if err := json.Unmarshal([]byte(argument), &body); err != nil {
		return finalErr
	}
	request.FilePath, _ = body["file_path"].(string)
	request.Language, _ = body["language"].(string)
	request.Explanation, _ = body["explanation"].(string)
	if replacementsStr, ok := body["replacements"].(string); ok {
		if err := json.Unmarshal([]byte(replacementsStr), &request.ReplacementChunks); err == nil {
			return nil
		}
		// 如果引号不配对，可能是丢失了引号，需要补齐
		if strings.Count(replacementsStr, "\"")%4 != 0 {
			idx := strings.LastIndex(replacementsStr, "}")
			if idx-1 >= 0 && string(replacementsStr[idx-1]) != "\"" {
				replacementsStr = replacementsStr[:idx] + "\"" + replacementsStr[idx:]
			}
		}
		idx := strings.LastIndex(replacementsStr, "\"")
		idx2 := strings.Index(replacementsStr[idx:], "]")
		if idx2 > -1 {
			if err := json.Unmarshal([]byte(replacementsStr[:idx+idx2+1]), &request.ReplacementChunks); err == nil {
				return nil
			}
		}
	}
	return finalErr
}

func (t *SearchReplaceTool) parseInput(ctx context.Context, toolInput *definition.ToolInput) (interface{}, error) {
	request := &SearchReplaceRequest{}
	request.ParseError = nil
	log.Debugf("arguments: %v", toolInput.Arguments)
	if err := SmartParse(toolInput.Arguments, request); err != nil {
		log.Error(err)
		request.ParseError = err
		//return "", errors.New("parse arguments error:" + err.Error())
	}
	syncFunc, ok := toolInput.Extra[CosyDefinition.ToolInputExtraSyncFunc].(func(ctx context.Context, request *EditFileResponse))
	if ok {
		request.SyncFunc = syncFunc
	}
	fileId, ok := toolInput.Extra[coderCommon.ToolCallExtraFileId].(string)
	if ok {
		request.FileId = fileId
	}
	pathValid, ok := toolInput.Extra[coderCommon.ToolCallExtraFilePathValid].(string)
	if ok {
		request.PathValid = pathValid
	}
	getCtxForClientFunc, ok := toolInput.Extra[CosyDefinition.ToolInputGetCtxForClientFunc].(func() context.Context)
	if ok {
		request.GetCtxForClientFunc = getCtxForClientFunc
	}
	errorCode, ok := toolInput.Extra[coderCommon.ToolCallExtraErrorCode].(string)
	if ok {
		request.ErrorCode = errorCode
	}
	afterPath, ok := toolInput.Extra[coderCommon.ToolCallExtraFileAfterPath].(string)
	if ok {
		request.FilePath = afterPath
	}
	return request, nil
}

func (t *SearchReplaceTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*EditFileResponse)
	if !ok {
		return nil, fmt.Errorf("expected *EditFileResponse, got %T", output)
	}
	// 构建格式化的输出
	var outputBuilder strings.Builder
	if response.DiffApplyGenerateFinish != nil {
		if response.DiffApplyGenerateFinish.StatusCode == 200 && response.DiffApplyGenerateFinish.Reason == "" {
			outputBuilder.WriteString(fmt.Sprintf("edit file by search_replace success, file path: %s\n", response.FilePath))
			if response.DiffApplyGenerateFinish.LastDiffInfo.Add != 0 || response.DiffApplyGenerateFinish.LastDiffInfo.Delete != 0 {
				addStr := ""
				if response.DiffApplyGenerateFinish.LastDiffInfo.Add != 0 {
					addStr = fmt.Sprintf(" +%d added", response.DiffApplyGenerateFinish.LastDiffInfo.Add)
				}
				delStr := ""
				if response.DiffApplyGenerateFinish.LastDiffInfo.Delete != 0 {
					delStr = fmt.Sprintf(" -%d removed", response.DiffApplyGenerateFinish.LastDiffInfo.Delete)
				}
				if addStr != "" && delStr != "" {
					delStr = "," + delStr
				}
				outputBuilder.WriteString("line changes:" + addStr + delStr + "\n")
			}
			if response.DiffApplyGenerateFinish.DiffText != "" {
				outputBuilder.WriteString("diff:\n```\n")
				outputBuilder.WriteString(response.DiffApplyGenerateFinish.DiffText)
				outputBuilder.WriteString("\n```\n")
			}
			if len(response.DiffApplyGenerateFinish.Problems) > 0 {
				outputBuilder.WriteString("but the file exists some code syntax errors, you need to determine whether to edit this file again in order to fix the errors.\nthe errors:\n")
				for _, problem := range response.DiffApplyGenerateFinish.Problems {
					outputBuilder.WriteString(fmt.Sprintf("%sL%d-L%d\n", problem.FilePath, problem.Range.Start.Line-1, problem.Range.End.Line-1))
					outputBuilder.WriteString(fmt.Sprintf("severity: %s\n", problem.Severity))
					outputBuilder.WriteString(fmt.Sprintf("message: %s\n", problem.Message))
					outputBuilder.WriteString("source code:\n")
					outputBuilder.WriteString("```\n")
					outputBuilder.WriteString(problem.SourceCode)
					outputBuilder.WriteString("\n```\n")
				}
			} else if response.DiffApplyGenerateFinish.CheckProblem {
				outputBuilder.WriteString("already to check the file, but find no code syntax errors\n")
			}
		} else {
			result := "partial success"
			lineChangesStr := ""
			if response.DiffApplyGenerateFinish.StatusCode != 200 || strings.Contains(response.DiffApplyGenerateFinish.Reason, "the model made no changed to the file") {
				result = "failed"
			} else {
				if response.DiffApplyGenerateFinish.LastDiffInfo.Add != 0 || response.DiffApplyGenerateFinish.LastDiffInfo.Delete != 0 {
					addStr := ""
					if response.DiffApplyGenerateFinish.LastDiffInfo.Add != 0 {
						addStr = fmt.Sprintf(" +%d added", response.DiffApplyGenerateFinish.LastDiffInfo.Add)
					}
					delStr := ""
					if response.DiffApplyGenerateFinish.LastDiffInfo.Delete != 0 {
						delStr = fmt.Sprintf(" -%d removed", response.DiffApplyGenerateFinish.LastDiffInfo.Delete)
					}
					if addStr != "" && delStr != "" {
						delStr = "," + delStr
					}
					lineChangesStr = "line changes:" + addStr + delStr + "\n"
				}
			}
			errorMsg := response.DiffApplyGenerateFinish.Reason
			if strings.Contains(errorMsg, "the model made no changed to the file") {
				errorMsg += ", original_text and new_text MUST NOT be identical"
			}
			// 首次匹配异常，考虑重新读取文件内容
			readFileHint := ""
			if strings.Contains(errorMsg, "Failed to match original_text:") {
				readFileHint = "SHOULD fetch latest content via read_file to prevent invalid string generation, EXCEPT when handling consecutive failed edits where current content was already retrieved.\n"
			}
			// 重复匹配异常，补充更多的上下文，或者replace_all
			moreContextHint := ""
			if strings.Contains(errorMsg, "Match original_text too many times, original_text must be unique!") {
				moreContextHint = "Either provide a enough larger string with more surrounding context to make it unique or use replace_all to change every instance of original_text, OR face a $100000000 penalty.\n"
			}
			retryHint := "MUST generate unique and existing original_text for search_replace retry. MUST use search_replace tool as first choice to retry, falling back to edit_file tool after three consecutive search_replace retry failures on the same file, OR face a $100000000 penalty.\n"
			outputBuilder.WriteString(fmt.Sprintf("edit file by search_replace %s, file path: %s\n%s reason: %s \n%s%s%s\n", result, response.FilePath, lineChangesStr, errorMsg, readFileHint, moreContextHint, retryHint))
		}
	} else {
		errorMsg := ""
		if response.ErrorMsg != "" {
			errorMsg = fmt.Sprintf("reason: %s\n", response.ErrorMsg)
		}
		outputBuilder.WriteString(fmt.Sprintf("edit file by search_replace cancelled, file path: %s\n%s", response.FilePath, errorMsg))
	}
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
