package service

import (
	"context"
	"cosy/chat/provider"
	"cosy/definition"
	"cosy/global"
	"cosy/ide/common"
	"cosy/log"
	"cosy/sls"
	"cosy/stable"
	"cosy/storage/database"
	"cosy/util"
	"cosy/util/chat"
	"cosy/util/encrypt"
	"cosy/util/session"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/google/uuid"
	"golang.org/x/exp/slices"
)

type CheckableMutex struct {
	mu     sync.Mutex
	locked int64
}

func (cm *CheckableMutex) Lock() {
	cm.mu.Lock()
	atomic.StoreInt64(&cm.locked, time.Now().UnixMilli())
}

func (cm *CheckableMutex) Unlock() {
	if atomic.LoadInt64(&cm.locked) > 0 {
		atomic.StoreInt64(&cm.locked, 0)
		cm.mu.Unlock()
	}
}

func (cm *CheckableMutex) TryLock() bool {
	if atomic.LoadInt64(&cm.locked) == 0 {
		cm.Lock()
		return true
	} else {
		return false
	}
}

func (cm *CheckableMutex) IsLocked() bool {
	return atomic.LoadInt64(&cm.locked) > 0
}

func (cm *CheckableMutex) LockTime() int64 {
	lockTime := atomic.LoadInt64(&cm.locked)
	if lockTime == 0 {
		return -1
	} else {
		return time.Now().UnixMilli() - lockTime
	}
}

// 会话、工作区文件的并发锁
var SessionLock sync.Map
var WorkingSpaceFileLock sync.Map

var WorkingSpaceServiceManager *ChatWorkingSpaceServiceManager
var CurrentSnapshotMap map[string]definition.Snapshot

// 会话状态列表，基于 ProjectURI 区分，状态0表示历史会话或者不存在会话，状态1表示历史会话激活中，状态2表示当前会话激活中
var CurrentSessionStatusMap map[string]map[string]int

// 工作区文件处理中状态表，存在键值对表示正在处理中(GENERATING/APPLYING)
var WorkingSpaceFileIngMap sync.Map

// 工作区文件状态表
var WorkingSpaceFileStatusMap sync.Map

// 工作区文件处理中内容表，存储生成中的文件内容
var WorkingSpaceFileGeneratingContentMap map[string]*definition.GenerateStreamContent

// 工作区文件处理中内容表，存储采纳中的文件内容
var WorkingSpaceFileApplyingContentMap map[string]*definition.ApplyStreamContent

// 工作区文件消息表
var WorkingSpaceFileMessageMap map[string]string

// 工作区文件应用类型表
var WorkingSpaceFileMethodMap map[string]string

// 工作区文件版本表: {sessionId: {fileId: versionCount} }
var WorkingSpaceFileVersionMap map[string]map[string]int

// 工作区文件 DiffInfo 表
var WorkingSpaceFileDiffInfoMap map[string]map[string]definition.DiffInfo

// 工作区文件 LastDiffInfo 表
var WorkingSpaceFileLastDiffInfoMap map[string]map[string]definition.DiffInfo

type STATUS string

type Event struct {
	Type string
	Data interface{}
}

type Listener interface {
	SupportedTypes() []string
	HandleEvent(event Event)
}

var listeners []Listener

const (
	ACTIVE_INIT           STATUS = "ACTIVE_INIT"
	INIT                  STATUS = "INIT"
	GENERATING            STATUS = "GENERATING"
	PENDING               STATUS = "PENDING"
	APPLYING              STATUS = "APPLYING"
	APPLIED               STATUS = "APPLIED"
	ACCEPTED              STATUS = "ACCEPTED"
	PARTIALLY_ACCEPTED    STATUS = "PARTIALLY_ACCEPTED"
	GENERATING_CANCELLED  STATUS = "GENERATING_CANCELLED"
	APPLYING_CANCELLED    STATUS = "APPLYING_CANCELLED"
	GENERATING_FAILED     STATUS = "GENERATING_FAILED"
	APPLYING_FAILED       STATUS = "APPLYING_FAILED"
	APPLYING_CHECK_FAILED STATUS = "APPLYING_CHECK_FAILED"
	REJECTED              STATUS = "REJECTED"
	OUTDATED              STATUS = "OUTDATED"
	FAILED                STATUS = "FAILED"
	DELETING              STATUS = "DELETING"
	DELETED               STATUS = "DELETED"
	DELETING_FAILED       STATUS = "DELETING_FAILED"
	START_APPLYING        STATUS = "START_APPLYING"
	COMPLETE_APPLYING     STATUS = "COMPLETE_APPLYING"
)

const (
	CHAT_MODE = "CHAT_MODE"
)

// 终态表
var finishStatusMap = map[string]int{
	ACCEPTED.String():           1,
	PARTIALLY_ACCEPTED.String(): 1,
	REJECTED.String():           1,
}

// 失败状态表
var failedStatusMap = map[string]int{
	GENERATING_CANCELLED.String(): 1,
	GENERATING_FAILED.String():    1,
	APPLYING_CANCELLED.String():   1,
	APPLYING_FAILED.String():      1,
}

// 可以Apply的状态表
var canApplyStatusMap = map[string]int{
	GENERATING.String():         1,
	PENDING.String():            1,
	APPLYING_CANCELLED.String(): 1,
	APPLYING_FAILED.String():    1,
}

type OP_TYPE string

const (
	GENERATE           OP_TYPE = "GENERATE"
	ACCEPT_ALL         OP_TYPE = "ACCEPT_ALL"
	REJECT_ALL         OP_TYPE = "REJECT_ALL"
	APPLY_ALL          OP_TYPE = "APPLY_ALL"
	CANCEL             OP_TYPE = "CANCEL"
	SWITCH             OP_TYPE = "SWITCH"
	UPDATE_CHAT_RECORD OP_TYPE = "UPDATE_CHAT_RECORD"
	ACCEPT             OP_TYPE = "ACCEPT"
	REJECT             OP_TYPE = "REJECT"
	APPLY              OP_TYPE = "APPLY"
	LOAD               OP_TYPE = "LOAD"
	COMPLETE_APPLY     OP_TYPE = "COMPLETE_APPLY"
	ACTIVATE           OP_TYPE = "ACTIVATE"
	SYNC               OP_TYPE = "SYNC"
	COMPLETE_APPLIED   OP_TYPE = "COMPLETE_APPLIED"
	START_APPLIED      OP_TYPE = "START_APPLIED"
)

// 操作对应的文件保存行为表: 1表示后续操作会覆盖工作区文件内容，无需提前保存，2表示全保存;0表示不保存
var operateSaveTypeMap = map[string]int{
	ACCEPT_ALL.String():         2,
	REJECT_ALL.String():         1,
	APPLY_ALL.String():          2,
	CANCEL.String():             1,
	SWITCH.String():             2,
	UPDATE_CHAT_RECORD.String(): 2,
	ACCEPT.String():             2,
	REJECT.String():             1,
	APPLY.String():              2,
	LOAD.String():               0,
	COMPLETE_APPLY.String():     2,
	SYNC.String():               2,
	ACTIVATE.String():           0,
	COMPLETE_APPLIED.String():   0,
	START_APPLIED.String():      0,
}

// 需要埋点的行为表
var needRecordOpTypeMap = map[string]int{
	ACCEPT_ALL.String(): 1,
	REJECT_ALL.String(): 1,
	SWITCH.String():     1,
	ACCEPT.String():     1,
	REJECT.String():     1,
	ACTIVATE.String():   1,
}

const (
	MODIFIED                        = "MODIFIED"
	ADD                             = "ADD"
	DELETE                          = "DELETE"
	FRUSH                           = "FRUSH"
	INHERIT                         = "INHERIT"
	NEWED                           = "NEWED"
	FULL                            = "FULL"
	LOCAL_FILE                      = "LOCAL_FILE"
	CONTENT_USE_WORKING_SPACE_LOCAL = "LINGMA_TAG::CONTENT_USE_WORKING_SPACE_LOCAL"
	CONTENT_USE_EMPTY_FILE          = "LINGMA_TAG::EMPTY_FILE"
	BASE_MD5                        = "baseMd5"
	NO_SAVE                         = "NO_SAVE"
	NO_RECORD                       = "NO_RECORD"
	NEED_CANCEL                     = "NEED_CANCEL"
	SKIP_SAVE                       = "SKIP_SAVE"
	IS_FAILED                       = "FAILED"
	ERROR_CODE                      = "ERROR_CODE"
	REFRESH_LIST                    = "REFRESH_LIST"
	REFRESH_CURRENT                 = "REFRESH_CURRENT"
	REFRESH_CURRENT_STATUS          = "REFRESH_CURRENT_STATUS"
	REFRESH_CURRENT_CHAT_RECORD     = "REFRESH_CURRENT_CHAT_RECORD"
	SWITCH_FRUSH_END                = "SWITCH_FRUSH_END"
	SYNC_WORKING_SPACE_FILE         = "SYNC_WORKING_SPACE_FILE"
	SYNC_SNAPSHOT_LIST              = "SYNC_SNAPSHOT_LIST"
)

const (
	APPLY_MODE_AGENT = "AGENT"
	APPLY_MODE_EDIT  = "EDIT"
	APPLY_MODE_IDE   = "IDE"
)

var APPLY_MODE_AGENT_SERIES = []string{APPLY_MODE_AGENT, APPLY_MODE_IDE}

func (status STATUS) String() string {
	return string(status)
}

func (op OP_TYPE) String() string {
	return string(op)
}

func InitWorkingSpaceService() {

	WorkingSpaceServiceManager = &ChatWorkingSpaceServiceManager{
		chatSnapshotTemplate:                  database.ChatSnapshotTemplate,
		chatWorkingSpaceFileTemplate:          database.ChatWorkingSpaceFileTemplate,
		chatWorkingSpaceFileReferenceTemplate: database.ChatWorkingSpaceFileReferenceTemplate,
	}

	CurrentSnapshotMap = make(map[string]definition.Snapshot)
	CurrentSessionStatusMap = make(map[string]map[string]int)
	WorkingSpaceFileGeneratingContentMap = make(map[string]*definition.GenerateStreamContent)
	WorkingSpaceFileApplyingContentMap = make(map[string]*definition.ApplyStreamContent)
	WorkingSpaceFileMessageMap = make(map[string]string)
	WorkingSpaceFileMethodMap = make(map[string]string)
	WorkingSpaceFileVersionMap = make(map[string]map[string]int)
	WorkingSpaceFileDiffInfoMap = make(map[string]map[string]definition.DiffInfo)
	WorkingSpaceFileLastDiffInfoMap = make(map[string]map[string]definition.DiffInfo)

	listeners = []Listener{}

	// 注册内置埋点监听器
	WorkingSpaceServiceManager.RegisterListener(EventTrackerListener{})
}

type ChatWorkingSpaceServiceManager struct {
	chatSnapshotTemplate                  *database.ChatSnapshotOrmTemplate
	chatWorkingSpaceFileTemplate          *database.ChatWorkingSpaceFileOrmTemplate
	chatWorkingSpaceFileReferenceTemplate *database.ChatWorkingSpaceFileReferenceOrmTemplate
}

func ReportFileEditError(sessionId string, chatRecordId string, filePath string, op_type string, chatMode string, errorCode string, method string) {
	data := map[string]string{
		"session_id":     sessionId,
		"chat_record_id": chatRecordId,
		"file_path":      filePath,
		"op_type":        op_type,
		"result":         "false",
		"chat_mode":      chatMode,
		"error_code":     errorCode,
		"method":         method,
	}
	go sls.Report(sls.EventTypeChatAiDeveloperWorkspaceFileOperation, chatRecordId, data)
}

func saveFile(ctx context.Context, filePath string) (bool, error) {
	ideToolRequest := &definition.ToolInvokeRequest{
		RequestId:  uuid.NewString(),
		ToolCallId: uuid.NewString(),
		Name:       "save_file",
		Parameters: map[string]any{
			"file_path": filePath,
		},
		Async: false,
	}
	ideToolResponse, err := common.InvokeTool(ctx, ideToolRequest, 1)
	if err != nil || ideToolResponse == nil || !ideToolResponse.Success {
		errorMsg := ""
		if err != nil {
			errorMsg = err.Error()
		} else if ideToolResponse == nil {
			errorMsg = "response is nil"
		} else {
			errorMsg = ideToolResponse.ErrorMessage
		}
		if err == nil {
			err = errors.New(errorMsg)
		}
		log.Error("saveFile Error:", errorMsg)
		return false, err
	}
	saved, ok := ideToolResponse.Result["saved"].(bool)
	if ok {
		return saved, nil
	}
	return false, errors.New("saveFile failed")
}

/**
 * 注册监听器
 */
func (service *ChatWorkingSpaceServiceManager) RegisterListener(listener Listener) {
	listeners = append(listeners, listener)
}

/**
 * 获取工作区文件存储提供者
 */
func (service *ChatWorkingSpaceServiceManager) GetWorkingSpaceFileProvider(workingSpaceFile definition.WorkingSpaceFile) provider.WorkingSpaceFileProvider {
	var targetProvider provider.WorkingSpaceFileProvider
	switch workingSpaceFile.Type {
	case LOCAL_FILE:
		targetProvider = &provider.LocalFileWorkingSpaceFileProvider{}
	default:
		targetProvider = &provider.LocalFileWorkingSpaceFileProvider{}
	}
	return targetProvider
}

/**
 * 同步快照列表
 * 触发时机：
 *  1. 快照列表变更
 *  2. 当前快照变更
 *  3. 当前快照状态变更
 *  4. 当前快照 ChatRecordId 变更
 */
func (service *ChatWorkingSpaceServiceManager) SyncSnapshotList(ctx context.Context, sessionId string, refreshType string) {
	currentSnapshotId := ""
	currentSnapshot, exists := CurrentSnapshotMap[sessionId]
	if exists {
		currentSnapshotId = currentSnapshot.Id
	}

	chatSnapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(sessionId)
	if err != nil {
		return
	}
	reversedSnapshots := []definition.Snapshot{}
	for i := len(chatSnapshots) - 1; i >= 0; i-- {
		reversedSnapshots = append(reversedSnapshots, chatSnapshots[i])
	}

	result := definition.SnapshotListSyncResult{
		Snapshots:         reversedSnapshots,
		CurrentSnapshotId: currentSnapshotId,
		CurrentSessionId:  sessionId,
		Type:              refreshType,
		ProjectPath:       SessionServiceManager.GetChatSessionProjectURI(sessionId),
	}
	currentSnapshotStatus := ""
	for _, snapshot := range chatSnapshots {
		if snapshot.Id == currentSnapshotId {
			currentSnapshotStatus = snapshot.Status
			break
		}
	}
	// INIT 或者 切换快照时，返回文件列表
	if currentSnapshotStatus == INIT.String() || refreshType == REFRESH_CURRENT {
		if workingSpaceFiles, err := service.ListWorkingSpaceFileVOsBySnapshot(ctx, currentSnapshotId, false, true, false); err == nil {
			result.WorkingSpaceFiles = workingSpaceFiles
		}
	} else if len(reversedSnapshots) == 0 && currentSnapshotId == "" {
		result.WorkingSpaceFiles = []definition.WorkingSpaceFileVO{}
	}
	log.Infof("Send request snapshot/syncAll CurrentSessionId: %s, CurrentSnapshotId: %s, CurrentSnapshotStatus: %s, Type: %s", sessionId, currentSnapshotId, currentSnapshotStatus, refreshType)
	e := websocket.SendRequestWithTimeout(ctx, "snapshot/syncAll",
		result, nil, 3*time.Second)
	if e != nil {
		log.Error("send request snapshot/syncAll error:", e)
	}

	// 发送监听事件
	for _, listener := range listeners {
		supportedTypes := listener.SupportedTypes()
		if supportedTypes == nil {
			supportedTypes = []string{}
		}
		for _, supportedType := range supportedTypes {
			if supportedType == SYNC_SNAPSHOT_LIST {
				listener.HandleEvent(Event{
					Type: SYNC_SNAPSHOT_LIST,
					Data: result,
				})
				break
			}
		}
	}
}

/**
 * 工作区文件状态变化，引发同步快照列表
 * 流程:
 *  1. 根据工作区文件状态，计算快照状态
 *  2. 判断快照状态是否改动，改动则更新
 *  3. 判断快照是否为当前快照，仅当前快照的状态变更需要同步
 *  4. 触发同步快照列表
 */
func (service *ChatWorkingSpaceServiceManager) syncSnapshotListByFile(ctx context.Context, sessionId string, snapshot definition.Snapshot) {
	// 过期快照不更新状态
	if snapshot.Status == OUTDATED.String() {
		return
	}

	// 计算快照状态
	snapshotStatus, err := service.GetSnapshotStatus(ctx, snapshot.Id)
	if err != nil {
		return
	}

	if snapshot.Status != snapshotStatus.String() {
		// 更新快照状态
		snapshot.Status = snapshotStatus.String()
		service.chatSnapshotTemplate.UpdateSnapshot(snapshot)

		// 判断是否为当前快照
		currentSnapshot, exists := CurrentSnapshotMap[sessionId]
		if exists && currentSnapshot.Id == snapshot.Id {
			// 同步快照列表
			service.SyncSnapshotList(ctx, sessionId, REFRESH_CURRENT_STATUS)
		}
	}
}

/**
 * 同步工作区文件变化
 * 触发时机：
 *  1.	新增工作区文件
 *  2.	工作区文件状态变化
 *  3.  工作区文件版本数变化
 *  4.	工作区文件Diff信息变化
 *  5.  处理中文件的流式返回
 * @param mode: 工作区文件变化模式，ADD 首次加入工作区；MODIFIED 已在工作区，需要修改
 * @param versionCount: 工作区文件版本数变化时传入，0表示不修改版本数
 * @param diffInfo: 工作区文件Diff信息变化时传入, 空表示未修改 Diff信息
 * @param content: 工作区文件流式返回内容，空表示非流式返回触发
 * @param snapshotId: 非空时，表示需要触发对应的快照状态同步
 * @param errorCode: 工作区状态为异常时，错误状态码
 */
func (service *ChatWorkingSpaceServiceManager) SyncWorkingSpaceFile(ctx context.Context, workingSpaceFile definition.WorkingSpaceFile, mode string, versionCount int, diffInfo definition.DiffInfo, lastDiffInfo definition.DiffInfo, content string, snapshotId string, errorCode string) {
	// 本地文件不触发同步
	if workingSpaceFile.LocalId == "" {
		return
	}
	// 初始文件的非 FRUSH 事件不触发同步
	if workingSpaceFile.Version == "0" && mode != FRUSH {
		return
	}
	// 获取 versionCount
	if WorkingSpaceFileVersionMap[workingSpaceFile.SessionId] == nil {
		WorkingSpaceFileVersionMap[workingSpaceFile.SessionId] = make(map[string]int)
	}
	if versionCount == 0 {
		versionCount = WorkingSpaceFileVersionMap[workingSpaceFile.SessionId][workingSpaceFile.FileId]
	} else {
		WorkingSpaceFileVersionMap[workingSpaceFile.SessionId][workingSpaceFile.FileId] = versionCount
	}
	// 获取 DiffInfo
	if WorkingSpaceFileDiffInfoMap[workingSpaceFile.SessionId] == nil {
		WorkingSpaceFileDiffInfoMap[workingSpaceFile.SessionId] = make(map[string]definition.DiffInfo)
	}
	if diffInfo.Add == 0 && diffInfo.Delete == 0 && diffInfo.SourceMd5 == "" && diffInfo.TargetMd5 == "" {
		if savedDiffInfo, ok := WorkingSpaceFileDiffInfoMap[workingSpaceFile.SessionId][workingSpaceFile.FileId]; ok {
			diffInfo = savedDiffInfo
		} else if workingSpaceFile.Status == APPLIED.String() || workingSpaceFile.Status == ACCEPTED.String() || workingSpaceFile.Status == REJECTED.String() {
			// 从db中加载
			extraMap := map[string]interface{}{}
			err := json.Unmarshal([]byte(workingSpaceFile.Extra), &extraMap)
			if diffInfoMap, ok := extraMap["diffInfo"].(map[string]interface{}); err == nil && ok {
				diffInfo.Add, _ = diffInfoMap["add"].(int)
				diffInfo.Delete, _ = diffInfoMap["delete"].(int)
				diffInfo.AddCounts, _ = diffInfoMap["addCounts"].(int)
				diffInfo.DelCounts, _ = diffInfoMap["delCounts"].(int)
				diffInfo.ModCounts, _ = diffInfoMap["modCounts"].(int)
				diffInfo.SourceMd5, _ = diffInfoMap["sourceMd5"].(string)
				diffInfo.TargetMd5, _ = diffInfoMap["targetMD5"].(string)
				diffInfo.AddChars, _ = diffInfoMap["addChars"].(int)
				diffInfo.DelChars, _ = diffInfoMap["delChars"].(int)
				WorkingSpaceFileDiffInfoMap[workingSpaceFile.SessionId][workingSpaceFile.FileId] = diffInfo
			}
		}
	} else {
		WorkingSpaceFileDiffInfoMap[workingSpaceFile.SessionId][workingSpaceFile.FileId] = diffInfo
	}
	// 获取 LastDiffInfo
	if WorkingSpaceFileLastDiffInfoMap[workingSpaceFile.SessionId] == nil {
		WorkingSpaceFileLastDiffInfoMap[workingSpaceFile.SessionId] = make(map[string]definition.DiffInfo)
	}
	if lastDiffInfo.Add == 0 && lastDiffInfo.Delete == 0 && lastDiffInfo.SourceMd5 == "" && lastDiffInfo.TargetMd5 == "" {
		if savedDiffInfo, ok := WorkingSpaceFileLastDiffInfoMap[workingSpaceFile.SessionId][workingSpaceFile.Id]; ok {
			lastDiffInfo = savedDiffInfo
		} else if workingSpaceFile.Status == APPLIED.String() || workingSpaceFile.Status == ACCEPTED.String() || workingSpaceFile.Status == REJECTED.String() {
			// 从db中加载
			extraMap := map[string]interface{}{}
			err := json.Unmarshal([]byte(workingSpaceFile.Extra), &extraMap)
			if diffInfoMap, ok := extraMap["lastDiffInfo"].(map[string]interface{}); err == nil && ok {
				lastDiffInfo.Add, _ = diffInfoMap["add"].(int)
				lastDiffInfo.Delete, _ = diffInfoMap["delete"].(int)
				lastDiffInfo.AddCounts, _ = diffInfoMap["addCounts"].(int)
				lastDiffInfo.DelCounts, _ = diffInfoMap["delCounts"].(int)
				lastDiffInfo.ModCounts, _ = diffInfoMap["modCounts"].(int)
				lastDiffInfo.SourceMd5, _ = diffInfoMap["sourceMd5"].(string)
				lastDiffInfo.TargetMd5, _ = diffInfoMap["targetMD5"].(string)
				lastDiffInfo.AddChars, _ = diffInfoMap["addChars"].(int)
				lastDiffInfo.DelChars, _ = diffInfoMap["delChars"].(int)
				WorkingSpaceFileLastDiffInfoMap[workingSpaceFile.SessionId][workingSpaceFile.Id] = lastDiffInfo
			}
		}
	} else {
		WorkingSpaceFileLastDiffInfoMap[workingSpaceFile.SessionId][workingSpaceFile.Id] = lastDiffInfo
	}

	snapshot := definition.Snapshot{}
	if snapshotId != "" {
		// 获取快照信息
		snapshot, _ = service.chatSnapshotTemplate.GetSnapshot(snapshotId)
	}

	workingSpaceFileVO := workingSpaceFile.ToVO(versionCount, content, diffInfo, lastDiffInfo, snapshot.ChatRecordId)
	if workingSpaceFile.Version == "-" {
		workingSpaceFileVO.VersionCount = "-"
	}
	var extraMap map[string]interface{}
	if err := json.Unmarshal([]byte(workingSpaceFile.Extra), &extraMap); err == nil {
		if chatMode, ok := extraMap[CHAT_MODE].(string); ok {
			workingSpaceFileVO.ChatMode = chatMode
		}
	}
	// 获取错误信息
	errorMessage := ""
	if errorCode != "" {
		errorMessage = chat.GetErrorMessage(ctx, errorCode)
		WorkingSpaceFileMessageMap[workingSpaceFile.Id] = errorMessage
	} else {
		errorMessage = WorkingSpaceFileMessageMap[workingSpaceFile.Id]
	}
	workingSpaceFileVO.Message = errorMessage

	result := definition.WorkingSpaceFileSyncResult{
		WorkingSpaceFile: workingSpaceFileVO,
		Type:             mode,
		// 是否处于流式返回中
		IsStream:    content != "",
		ProjectPath: SessionServiceManager.GetChatSessionProjectURI(workingSpaceFile.SessionId),
	}
	// 流式输出的时候不打印日志
	if content == "" {
		log.Debugf("Send request workingSpaceFile/sync id: %s, "+
			"path: %s, "+
			"status: %s, "+
			"Type: %s, "+
			"VersionCount: %d, "+
			"Add: %d, "+
			"Delete: %d, "+
			"LastAdd: %d, LastDelete: %d, "+
			"content: %s",
			workingSpaceFile.Id,
			workingSpaceFile.Key,
			workingSpaceFile.Status,
			mode,
			versionCount,
			diffInfo.Add,
			diffInfo.Delete,
			lastDiffInfo.Add,
			lastDiffInfo.Delete,
			content,
		)
	}
	notifyIDE := true
	if session.IsQuestLongRunningSession(workingSpaceFileVO.SessionId) {
		// Quest模式下不需要通知IDE，避免IDE打开文件
		notifyIDE = false
	}
	if notifyIDE {
		e := websocket.SendRequestWithTimeout(ctx, "workingSpaceFile/sync",
			result, nil, 3*time.Second)
		if e != nil {
			log.Error("send request snapshot/syncAll error:", e)
		}
	}

	// 触发快照列表同步
	if snapshot.Id != "" {
		service.syncSnapshotListByFile(ctx, workingSpaceFile.SessionId, snapshot)
	}

	// 补充埋点依赖的信息
	if errorCode != "" {
		result.WorkingSpaceFile.ErrorCode = errorCode
	}
	result.WorkingSpaceFile.Method = WorkingSpaceFileMethodMap[workingSpaceFile.Id]

	// 发送监听事件
	for _, listener := range listeners {
		supportedTypes := listener.SupportedTypes()
		if supportedTypes == nil {
			supportedTypes = []string{}
		}
		for _, supportedType := range supportedTypes {
			if supportedType == SYNC_WORKING_SPACE_FILE {
				listener.HandleEvent(Event{
					Type: SYNC_WORKING_SPACE_FILE,
					Data: result,
				})
				break
			}
		}
	}
}

/**
 * 计算快照状态
 */
func (service *ChatWorkingSpaceServiceManager) GetSnapshotStatus(ctx context.Context, snapshotId string) (STATUS, error) {
	if snapshot, err := service.chatSnapshotTemplate.GetSnapshot(snapshotId); err != nil {
		return FAILED, err
	} else if snapshot.Status == ACTIVE_INIT.String() {
		return ACTIVE_INIT, nil
	} else if snapshot.Status == OUTDATED.String() {
		return OUTDATED, nil
	}
	files, err := service.ListWorkingSpaceFilesBySnapshot(ctx, snapshotId, false, true)
	if err != nil {
		return FAILED, err
	}
	// 空文件，初始状态
	if len(files) == 0 {
		return INIT, nil
	}
	status := FAILED
	statusMap := make(map[string]int)
	for _, file := range files {
		cnt := statusMap[file.Status]
		statusMap[file.Status] = cnt + 1
	}
	if statusMap[GENERATING.String()]+statusMap[DELETING.String()] > 0 {
		// 如果存在生成中文件，则为快照为生成中
		status = GENERATING
	} else if statusMap[APPLYING.String()] > 0 {
		// 如果不存在生成中文件，且存在应用中文件，则快照为应用中
		status = APPLYING
	} else if statusMap[APPLIED.String()]+statusMap[DELETED.String()] > 0 {
		// 如果不存在生成中文件，且不存在应用中文件，则存在APPLIED文件，则快照为APPLIED
		status = APPLIED
	} else if statusMap[GENERATING_CANCELLED.String()]+statusMap[GENERATING_FAILED.String()] == len(files) {
		// 全为 GENERATING_CANCELLED
		status = GENERATING_CANCELLED
	} else if statusMap[GENERATING_CANCELLED.String()]+statusMap[APPLYING_CANCELLED.String()]+statusMap[GENERATING_FAILED.String()]+statusMap[APPLYING_FAILED.String()]+statusMap[DELETING_FAILED.String()] == len(files) {
		// 全为 GENERATING_CANCELLED或者APPLYING_CANCELLED，且存在 APPLYING_CANCELLED
		status = APPLYING_CANCELLED
	} else if statusMap[ACCEPTED.String()] == len(files) {
		// 全为 ACCEPTED
		status = ACCEPTED
	} else if statusMap[ACCEPTED.String()] == 0 {
		// 不存在 ACCEPTED
		status = REJECTED
	} else {
		// 均在终态，且存在 ACCEPTED，且非全是 ACCEPTED，则为PARTIALLY_ACCEPTED
		status = PARTIALLY_ACCEPTED
	}
	return status, nil
}

/**
 * 创建快照
 * 流程如下：
 *	1.	取消旧的当前快照
 *  2.  插入快照
 *  3.  更新当前快照
 *  4.  继承旧的当前快照的工作区文件
 *  5.  同步快照列表
 * @return (snapshotId, errorCode, errorMsg)
 */
func (service *ChatWorkingSpaceServiceManager) CreateSnapshot(ctx context.Context, sessionId string, chatRecordId string, chatMode string) (string, string, string) {
	// 除了初始快照外
	if chatRecordId != "" {
		// 同会话同时只允许一个进程创建快照
		actual, _ := SessionLock.LoadOrStore(sessionId, &CheckableMutex{})
		mutex := actual.(*CheckableMutex)
		if !mutex.TryLock() {
			// 创建快照锁超过2分钟，认为可能出现异常，强制解锁后重新上锁
			if mutex.LockTime() > 2*60*1000 {
				mutex.Unlock()
				mutex.TryLock()
			} else {
				return "", definition.LockErrorCode, fmt.Sprintf("CreateSnapshot for ChatRecordId[%s] failed: Session [%s] Lock", chatRecordId, sessionId)
			}
		}
		defer mutex.Unlock()
	}
	if WorkingSpaceFileVersionMap[sessionId] == nil {
		WorkingSpaceFileVersionMap[sessionId] = make(map[string]int)
	}
	if WorkingSpaceFileDiffInfoMap[sessionId] == nil {
		WorkingSpaceFileDiffInfoMap[sessionId] = make(map[string]definition.DiffInfo)
	}

	if chatRecordId != "" {
		// 判断是否存在初始快照，不存在则需要创建
		if snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(sessionId); err == nil && len(snapshots) == 0 {
			_, errorCode, errorMsg := service.CreateSnapshot(ctx, sessionId, "", chatMode)
			if errorCode != "" {
				return "", errorCode, errorMsg
			}
		}
	}

	sessionStatus := 0
	targetSnapshotStatus := INIT.String()

	oldCurrentSnapshot, exists := CurrentSnapshotMap[sessionId]

	idx := 0
	if exists {
		// 历史快照激活场景
		if oldCurrentSnapshot.Status == ACTIVE_INIT.String() {
			sessionStatus = 1
		} else {
			sessionStatus = 2
		}
		// 取消旧的快照
		service.cancelSnapshot(ctx, oldCurrentSnapshot.Id, false)
	}

	// 计算快照序号
	snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(sessionId)
	if err == nil {
		// 跳过无效快照计次
		for _, snapshot := range snapshots {
			// 历史快照开始提问， ACTIVE_INIT->INIT
			if sessionStatus == 1 && snapshot.Status == ACTIVE_INIT.String() {
				snapshot.Status = INIT.String()
				service.chatSnapshotTemplate.UpdateSnapshot(snapshot)
			}
			if snapshot.Status != OUTDATED.String() {
				idx++
			}
		}
	}

	// 清理同工程其余的进行中会话
	if sessionStatus != 2 {
		projectUri := SessionServiceManager.GetChatSessionProjectURI(sessionId)
		if projectUri != "" {
			if CurrentSessionStatusMap[projectUri] == nil {
				CurrentSessionStatusMap[projectUri] = make(map[string]int)
			}
			// 可能需要激活新的会话场景，获取 session 关联的 ProjecURI，判断是否需要关闭旧的会话
			if sessionStatus != 1 && CurrentSessionStatusMap[projectUri][sessionId] == 1 {
				// 如果是历史快照激活，更新状态
				sessionStatus = 1
				targetSnapshotStatus = ACTIVE_INIT.String()
			} else {
				// 不然，关闭同工程下进行中的会话
				for targetSessionId := range CurrentSessionStatusMap[projectUri] {
					log.Debugf("Clear Session in Same ProjectUri with current session: projectUri: %v, currentSession: %v, clearedSession: %v", projectUri, sessionId, targetSessionId)
					if CurrentSessionStatusMap[projectUri][targetSessionId] == 2 {
						// 关闭会话
						//异步流程，copy context
						/*
							asyncCtx := websocket.CopyContext(ctx)
							stable.GoSafe(asyncCtx, func() {
								service.CloseSession(asyncCtx, targetSessionId)
							}, stable.SceneInlineEdit, targetSessionId)
						*/
					} else if CurrentSessionStatusMap[projectUri][targetSessionId] == 1 {
						// 清空会话状态
						delete(CurrentSnapshotMap, targetSessionId)
						delete(WorkingSpaceFileVersionMap, targetSessionId)
						delete(WorkingSpaceFileDiffInfoMap, targetSessionId)
						delete(CurrentSessionStatusMap[projectUri], targetSessionId)
						delete(SessionProjectUriMap, targetSessionId)
					}
				}
				CurrentSessionStatusMap[projectUri][sessionId] = 2
			}
		}
	}

	// 插入快照
	snapshotId := uuid.NewString()
	snapshot := definition.Snapshot{
		Id:           snapshotId,
		SessionId:    sessionId,
		ChatRecordId: chatRecordId,
		Name:         strconv.Itoa(idx),
		Status:       targetSnapshotStatus,
		GmtCreate:    time.Now().UnixMilli(),
		GmtModified:  time.Now().UnixMilli(),
	}
	err = service.chatSnapshotTemplate.InsertSnapshot(snapshot)
	if err != nil {
		return "", definition.SqlExecErrorCode, err.Error()
	}

	// 更新当前快照
	CurrentSnapshotMap[sessionId] = snapshot

	errorCode := ""
	errorMsg := ""

	// 继承之前快照的工作区文件列表
	if exists {
		files, err := service.ListWorkingSpaceFilesBySnapshot(ctx, oldCurrentSnapshot.Id, true, true)
		if err != nil {
			return "", definition.SqlQueryErrorCode, err.Error()
		}
		for _, file := range files {
			err = service.chatWorkingSpaceFileReferenceTemplate.InsertWorkingSpaceFileReference(definition.WorkingSpaceFileReference{
				Id:         uuid.NewString(),
				SnapshotId: snapshot.Id,
				ItemId:     file.Id,
				FileId:     file.FileId,
				Mode:       INHERIT,
			})
			if err != nil {
				errorCode = definition.BatchOperateErrorCode
				errorMsg += err.Error() + ";"
			}
		}
	}

	// 同步快照列表
	service.SyncSnapshotList(ctx, sessionId, REFRESH_LIST)

	// 记录埋点
	if sessionStatus != 1 {
		data := map[string]string{
			"session_id":     snapshot.SessionId,
			"chat_record_id": snapshot.ChatRecordId,
			"chat_mode":      chatMode,
		}
		stable.GoSafe(context.Background(), func() {
			sls.Report(sls.EventTypeChatAiDeveloperCreateSnapshot, snapshot.ChatRecordId, data)
		}, stable.SceneInlineEdit, snapshot.ChatRecordId)
	}

	return snapshotId, errorCode, errorMsg
}

/**
 * 根据会话ID获取快照列表
 * 对于历史会话，对于激活快照，但未进行新的提问的场景，无需返回激活的新快照
 */
func (service *ChatWorkingSpaceServiceManager) ListSnapshot(sessionId string) ([]definition.Snapshot, error) {
	reversedSnapshots := []definition.Snapshot{}
	snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(sessionId)
	if err != nil {
		return reversedSnapshots, err
	}
	snapshotLen := len(snapshots)
	if snapshotLen == 0 {
		return reversedSnapshots, nil
	}
	// 如果最新快照为 ACTIVE_INIT 状态，判断会话是否处于历史激活状态，如果不属于，则不返回激活的快照
	if snapshots[snapshotLen-1].Status == ACTIVE_INIT.String() {
		if currentSnapshot, exists := CurrentSnapshotMap[sessionId]; !exists || currentSnapshot.Id == snapshots[snapshotLen-1].Id {
			snapshotLen -= 1
		}
	}
	for i := snapshotLen - 1; i >= 0; i-- {
		reversedSnapshots = append(reversedSnapshots, snapshots[i])
	}
	return reversedSnapshots, err
}

/**
* 根据会话ID、文件ID获取文件完整变更逻辑
 */
func (service *ChatWorkingSpaceServiceManager) GetFileChangeBySessionId(ctx context.Context, sessionId, fileId string) (*definition.WorkingSpaceFileVO, error) {
	originContent := ""
	latestContent := ""
	mode := ""

	fileHistories, err := service.chatWorkingSpaceFileTemplate.ListWorkingSpaceFileByFileId(sessionId, fileId)
	if err != nil {
		return nil, err
	}
	if len(fileHistories) == 0 {
		return nil, fmt.Errorf("no modification for session %s, file: %s", sessionId, fileId)
	}
	if v, err := service.getFullContent(ctx, fileHistories[0]); err == nil {
		originContent = v
	} else {
		log.Errorf("get first content err: %v", err)
	}
	lastSnapshot := fileHistories[len(fileHistories)-1]
	if v, err := service.getFullContent(ctx, lastSnapshot); err == nil {
		latestContent = v
	} else {
		log.Errorf("get latest content err: %v", err)
	}
	if lastSnapshot.Status == DELETED.String() {
		mode = DELETE
		latestContent = ""
	} else {
		if originContent == "" {
			mode = ADD
		} else {
			mode = MODIFIED
		}
	}

	return &definition.WorkingSpaceFileVO{
		SessionId:     sessionId,
		FileId:        fileId,
		BeforeContent: originContent,
		AfterContent:  latestContent,
		Mode:          mode,
	}, nil
}

func (service *ChatWorkingSpaceServiceManager) ListFileChangesBySessionId(ctx context.Context, sessionId string) ([]definition.WorkingSpaceFileVO, error) {
	files, err := service.chatWorkingSpaceFileTemplate.ListWorkingSpaceFileBySession(sessionId)
	if err != nil {
		log.Errorf("list file changes by session id err: %v", err)
		return nil, err
	}
	var fileIds []string
	for _, file := range files {
		if slices.Contains(fileIds, file.FileId) {
			continue
		}
		fileIds = append(fileIds, file.FileId)
	}
	var results []definition.WorkingSpaceFileVO
	var errs []error
	for _, fileId := range fileIds {
		fileChange, err := service.GetFileChangeBySessionId(ctx, sessionId, fileId)
		if err != nil {
			log.Errorf("get file %s change by session id %s err: %v", fileId, sessionId, err)
			errs = append(errs, err)
			continue
		}
		results = append(results, *fileChange)
	}
	if len(errs) > 0 {
		return results, errors.Join(errs...)
	}
	return results, nil
}

/**
 * 根据 ChatRecordId 查询快照
 */
func (service *ChatWorkingSpaceServiceManager) GetSnapshotByChatRecordId(sessionId string, chatRecordId string) (definition.Snapshot, error) {
	return service.chatSnapshotTemplate.GetSnapshotByChatRecordId(sessionId, chatRecordId)
}

func (service *ChatWorkingSpaceServiceManager) GetSnapshot(snapshotId string) (definition.Snapshot, error) {
	return service.chatSnapshotTemplate.GetSnapshot(snapshotId)
}

/**
 * 清理无效快照
 * 无效快照：1. Outdated 状态的快照 2. 当前快照之后的快照
 */
func (service *ChatWorkingSpaceServiceManager) ClearSnapshot(ctx context.Context, sessionId string, chatMode string) error {
	snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(sessionId)
	if err != nil {
		return err
	}

	// 默认激活快照
	if len(snapshots) > 0 {
		service.activateSnapshot(ctx, snapshots[len(snapshots)-1].Id)
	}

	// 快照列表是否发生变更
	updated := false
	var sumErr error

	// 清理 Outdated 状态的快照
	for i := 0; i < len(snapshots); i++ {
		if snapshots[i].Status != OUTDATED.String() {
			break
		}
		updated = true
		err = service.deleteSnapshot(snapshots[i].Id)
		if err != nil {
			sumErr = err
		}
	}

	// 清理当前快照之后的快照
	currentSnapshot, exists := CurrentSnapshotMap[sessionId]
	if exists {
		// 记录废弃快照的数量
		deletedSnapshotCounts := 0
		for i := len(snapshots) - 1; i >= 0; i-- {
			if snapshots[i].Id == currentSnapshot.Id {
				break
			}
			updated = true
			err = service.deleteSnapshot(snapshots[i].Id)
			deletedSnapshotCounts += 1
			if err != nil {
				sumErr = err
			}
		}
		if deletedSnapshotCounts > 0 {
			data := map[string]string{
				"session_id":             currentSnapshot.SessionId,
				"delete_snapshot_counts": strconv.Itoa(deletedSnapshotCounts),
				"chat_mode":              chatMode,
			}
			stable.GoSafe(context.Background(), func() {
				sls.Report(sls.EventTypeChatAiDeveloperClearSnapshot, currentSnapshot.ChatRecordId, data)
			}, stable.SceneInlineEdit, currentSnapshot.ChatRecordId)
		}
	}

	// 清空快照，强制刷新
	if !updated && len(snapshots) == 0 {
		updated = true
	}

	// 同步快照状态
	if updated {
		service.SyncSnapshotList(ctx, sessionId, REFRESH_LIST)
	}
	return sumErr
}

/**
 * 删除快照
 * 流程:
 *  1. 通过引用，获取当前快照新增的工作区文件列表
 *  2. 批量删除工作区文件列表
 *  3. 删除引用
 *  4. 删除快照
 */
func (service *ChatWorkingSpaceServiceManager) deleteSnapshot(snapshotId string) error {
	references, err := service.chatWorkingSpaceFileReferenceTemplate.ListWorkingSpaceFileReferencesBySnapshotId(snapshotId)
	if err != nil {
		return err
	}
	// 获取当前快照新增的工作区文件列表
	var workingSpaceFileIds []string
	for _, reference := range references {
		// 只删除当前快照新增
		if reference.Mode == NEWED {
			workingSpaceFileIds = append(workingSpaceFileIds, reference.ItemId)
		}
	}

	// 批量删除工作区文件列表
	err = service.batchDeleteWorkingSpaceFiles(workingSpaceFileIds)
	if err != nil {
		return err
	}

	// 删除引用关系
	err = service.chatWorkingSpaceFileReferenceTemplate.DeleteWorkingSpaceFileReferenceBySnapshotId(snapshotId)
	if err != nil {
		return err
	}

	// 删除快照
	return service.chatSnapshotTemplate.DeleteSnapshot(snapshotId)
}

/**
 * 接受快照
 * 对快照下工作区的文件中，未到终态的文件选择接受
 * workingSpaceFileContents 参数用于限定接受的文件范围，nil表示全量接受
 */
func (service *ChatWorkingSpaceServiceManager) acceptSnapshot(ctx context.Context, snapshotId string, workingSpaceFileContents []definition.WorkingSpaceFileContent, needCancel bool, skipSave bool) (string, string) {
	files, err := service.ListWorkingSpaceFilesBySnapshot(ctx, snapshotId, false, true)
	if err != nil {
		return definition.SqlQueryErrorCode, err.Error()
	}

	contentMap := make(map[string]string)
	if workingSpaceFileContents != nil {
		for _, fileContent := range workingSpaceFileContents {
			contentMap[fileContent.Id] = fileContent.Content
		}
	}

	sumErrorCode := ""
	sumErrorMsg := ""
	for _, file := range files {

		// 只有 APPLIED 应用中状态可以接受
		if file.Status == APPLIED.String() || file.Status == DELETED.String() {
			// 全量更新或者属于部分接受的范围
			if _, ok := contentMap[file.Id]; workingSpaceFileContents == nil || ok {
				// 如果是
				errorCode, errorMsg := service.acceptWorkingSpaceFile(ctx, file, skipSave)
				if errorCode != "" {
					sumErrorCode = definition.BatchOperateErrorCode
					sumErrorMsg += errorMsg + ";"
				}
			}
		} else if needCancel && (file.Status == GENERATING.String() || file.Status == APPLYING.String()) {
			// 对于ing状态，取消
			errorCode, errorMsg := service.cancelWorkingSpaceFile(ctx, file, false, false, "")
			if errorCode != "" {
				sumErrorCode = definition.BatchOperateErrorCode
				sumErrorMsg += errorMsg + ";"
			}
		}
	}

	if snapshot, err := service.chatSnapshotTemplate.GetSnapshot(snapshotId); err == nil {
		service.syncSnapshotListByFile(ctx, snapshot.SessionId, snapshot)
	}
	return sumErrorCode, sumErrorMsg
}

/**
 * 拒绝快照
 * 对快照下工作区的文件中，未到终态的文件选择拒绝
 */
func (service *ChatWorkingSpaceServiceManager) rejectSnapshot(ctx context.Context, snapshotId string, skipSave bool) (string, string) {
	files, err := service.ListWorkingSpaceFilesBySnapshot(ctx, snapshotId, false, true)
	if err != nil {
		return definition.SqlQueryErrorCode, err.Error()
	}

	sumErrorCode := ""
	sumErrorMsg := ""
	for _, file := range files {

		// 未到终态
		if finishStatusMap[file.Status] == 0 {
			errorCode, errorMsg := service.rejectWorkingSpaceFile(ctx, file, skipSave)
			if errorCode != "" {
				sumErrorCode = definition.BatchOperateErrorCode
				sumErrorMsg += errorMsg + ";"
			}
		}
	}

	if snapshot, err := service.chatSnapshotTemplate.GetSnapshot(snapshotId); err == nil {
		service.syncSnapshotListByFile(ctx, snapshot.SessionId, snapshot)
	}
	return sumErrorCode, sumErrorMsg
}

/**
 * 取消快照
 * 流程如下：
 * 	1. 取消快照下的所有文件
 *	2. 计算快照状态，判断是否同步快照列表
 * @param needSync: 是否需要同步快照列表
 */
func (service *ChatWorkingSpaceServiceManager) cancelSnapshot(ctx context.Context, snapshotId string, needSync bool) (string, string) {
	files, err := service.ListWorkingSpaceFilesBySnapshot(ctx, snapshotId, false, true)
	if err != nil {
		return definition.SqlQueryErrorCode, err.Error()
	}

	sumErrorCode := ""
	sumErrorMsg := ""
	for _, file := range files {
		errorCode, errorMsg := service.cancelWorkingSpaceFile(ctx, file, false, false, "")
		if errorCode != "" {
			sumErrorCode = definition.BatchOperateErrorCode
			sumErrorMsg += errorMsg + ";"
		}
	}

	if needSync {
		// 同步快照列表
		snapshot, err := service.chatSnapshotTemplate.GetSnapshot(snapshotId)
		if err == nil {
			service.syncSnapshotListByFile(ctx, snapshot.SessionId, snapshot)
		}
	}

	return sumErrorCode, sumErrorMsg
}

/**
 * 应用快照
 * 流程如下：
 *  1. 判断状态：只有应用中取消，支持应用
 * 	2. 应用快照下的所有文件
 *	2. 计算快照状态，判断是否同步快照列表
 */
func (service *ChatWorkingSpaceServiceManager) applySnapshot(ctx context.Context, snapshotId string) (string, string) {
	snapshot, err := service.chatSnapshotTemplate.GetSnapshot(snapshotId)
	if err != nil {
		return definition.SqlQueryErrorCode, err.Error()
	}
	// 判断状态：只有应用中取消，支持应用
	if snapshot.Status != APPLYING_CANCELLED.String() {
		return definition.UnSupportedErrorCode, "applySnapshot only avail on APPLYING_CANCELLED"
	}

	files, err := service.ListWorkingSpaceFilesBySnapshot(ctx, snapshotId, false, true)
	if err != nil {
		return definition.SqlQueryErrorCode, err.Error()
	}

	sumErrorCode := ""
	sumErrorMsg := ""
	// 应用快照下的所有文件
	for _, file := range files {
		errorCode, errorMsg := service.applyWorkingSpaceFile(ctx, file, definition.DiffApplyParams{})
		if errorCode != "" {
			sumErrorCode = definition.BatchOperateErrorCode
			sumErrorMsg += errorMsg + ";"
		}
	}

	service.syncSnapshotListByFile(ctx, snapshot.SessionId, snapshot)

	return sumErrorCode, sumErrorMsg
}

/**
 * 激活历史快照
 * 流程:
 *  1. 判断之前是否已经激活过，未激活的话，创建新的快照；已激活的话，拿到激活的新快照
 *  2. 将工作区文件添加至新的快照
 */
func (service *ChatWorkingSpaceServiceManager) activateSnapshot(ctx context.Context, snapshotId string) (string, string) {
	snapshot, err := service.chatSnapshotTemplate.GetSnapshot(snapshotId)
	if err != nil {
		return definition.SqlQueryErrorCode, err.Error()
	}
	// 如果不是 OUTDATED 状态，则不支持激活
	if snapshot.Status != OUTDATED.String() {
		return definition.UnSupportedErrorCode, "activateSnapshot only avail on OUTDATED"
	}

	projectUri := SessionServiceManager.GetChatSessionProjectURI(snapshot.SessionId)

	snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(snapshot.SessionId)
	if err != nil {
		return definition.SqlQueryErrorCode, err.Error()
	}

	errorCode := ""
	errorMsg := ""

	// 未激活快照
	if projectUri != "" && CurrentSessionStatusMap[projectUri] == nil || CurrentSessionStatusMap[projectUri][snapshot.SessionId] == 0 {
		if CurrentSessionStatusMap[projectUri] == nil {
			CurrentSessionStatusMap[projectUri] = make(map[string]int)
		}
		CurrentSessionStatusMap[projectUri][snapshot.SessionId] = 1
		// 创建新的快照
		_, errorCode, errorMsg = service.CreateSnapshot(ctx, snapshot.SessionId, snapshot.ChatRecordId, "")
		if errorCode != "" {
			return errorCode, errorMsg
		}
		// 间隔 100 ms，由于目前 websocket 消息不能保证消息顺序，避免工作区文件消息先于快照信息到达
		time.Sleep(100 * time.Millisecond)
		// 将工作区文件添加至新的快照
		if files, err := service.ListWorkingSpaceFilesBySnapshot(ctx, snapshotId, false, true); err == nil {
			// 将工作区文件添加至新的快照
			for _, file := range files {
				// TODO: 记录异常
				_, err := service.CreateWorkingSpaceFile(ctx, snapshot.SessionId, file.FileId, file.Language, file.Mode, ACCEPTED, "", "")
				if err != nil {
					errorMsg += err.Error() + ";"
					errorCode = definition.BatchOperateErrorCode
				}
			}
		}
	} else if len(snapshots) > 0 {
		// 已激活，同步快照列表
		CurrentSnapshotMap[snapshot.SessionId] = snapshots[len(snapshots)-1]
		WorkingSpaceFileVersionMap[snapshot.SessionId] = make(map[string]int)
		WorkingSpaceFileDiffInfoMap[snapshot.SessionId] = make(map[string]definition.DiffInfo)
		service.SyncSnapshotList(ctx, snapshot.SessionId, REFRESH_LIST)
	}

	return errorCode, errorMsg
}

/**
 * 获取当前快照对应的最新卡片
 */
func (service *ChatWorkingSpaceServiceManager) getLatestChatRecord(sessionId string, snapshotId string) string {
	targetChatRecordId := ""
	validRecords, err := SessionServiceManager.GetSessionRecordsForHistoryBuild(sessionId)
	if err != nil || len(validRecords) == 0 {
		return ""
	}
	snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(sessionId)
	if err != nil || len(snapshots) == 0 {
		return ""
	}
	if snapshots[len(snapshots)-1].Id == snapshotId {
		targetChatRecordId = validRecords[len(validRecords)-1].RequestId
	} else {
		nextChatRecordId := ""
		for i := len(snapshots) - 1; i >= 0; i-- {
			if snapshots[i].Id == snapshotId {
				nextChatRecordId = snapshots[i+1].ChatRecordId
				break
			}
		}
		if nextChatRecordId != "" {
			if nextChatRecordId != validRecords[0].RequestId {
				for i := len(validRecords) - 1; i >= 0; i-- {
					if validRecords[i].RequestId == nextChatRecordId {
						targetChatRecordId = validRecords[i-1].RequestId
						break
					}
				}
			}
		}
	}
	return targetChatRecordId
}

/**
 * 更新快照对应的问答卡片
 */
func (service *ChatWorkingSpaceServiceManager) updateSnapshotChatRecord(ctx context.Context, snapshotId string, targetChatRecordId string) (string, string) {
	snapshot, err := service.chatSnapshotTemplate.GetSnapshot(snapshotId)
	if err != nil {
		return definition.SqlQueryErrorCode, err.Error()
	}
	if targetChatRecordId == "" {
		targetChatRecordId = service.getLatestChatRecord(snapshot.SessionId, snapshotId)
	}
	if targetChatRecordId == "" || targetChatRecordId == snapshot.ChatRecordId {
		return "", ""
	}
	snapshot.ChatRecordId = targetChatRecordId
	if currentSnapshot, exists := CurrentSnapshotMap[snapshot.SessionId]; exists {
		if currentSnapshot.Id == snapshot.Id {
			CurrentSnapshotMap[snapshot.SessionId] = snapshot
		}
	} else {
		CurrentSnapshotMap[snapshot.SessionId] = snapshot
	}
	if err = service.chatSnapshotTemplate.UpdateSnapshot(snapshot); err != nil {
		return definition.SqlExecErrorCode, err.Error()
	}
	// 同步快照列表
	service.SyncSnapshotList(ctx, snapshot.SessionId, REFRESH_CURRENT_CHAT_RECORD)
	return "", ""
}

/**
 * 切换快照
 * 流程:
 * 	1. 取消之前的快照
 *  2. 更新之前快照的ChatRecordId标识
 *  3. 更新当前快照标识
 *  4. 触发当前快照下的文件加载
 *  5. 同步快照列表
 */
func (service *ChatWorkingSpaceServiceManager) switchSnapshot(ctx context.Context, snapshotId string, targetSnapshotId string, targetChatRecordId string) (string, string) {
	snapshot, err := service.chatSnapshotTemplate.GetSnapshot(snapshotId)
	if err != nil {
		return definition.SqlQueryErrorCode, err.Error()
	}
	targetSnapshot, err := service.chatSnapshotTemplate.GetSnapshot(targetSnapshotId)
	if err != nil {
		return definition.SqlQueryErrorCode, err.Error()
	}
	// 判断是否同会话
	if snapshot.SessionId != targetSnapshot.SessionId {
		return definition.UnSupportedErrorCode, fmt.Sprintf("%s and %s not in same session", snapshotId, targetSnapshotId)
	}

	// 取消之前的快照
	service.cancelSnapshot(ctx, snapshotId, true)

	// 更新之前快照的ChatRecordId标识
	if targetChatRecordId == "" {
		targetChatRecordId = service.getLatestChatRecord(snapshot.SessionId, snapshotId)
	}
	if targetChatRecordId != "" && snapshot.ChatRecordId != targetChatRecordId {
		snapshot.ChatRecordId = targetChatRecordId
		service.chatSnapshotTemplate.UpdateSnapshot(snapshot)
	}

	// 更新当前快照标识
	CurrentSnapshotMap[snapshot.SessionId] = targetSnapshot

	// 触发当前快照下的文件加载
	if files, err := service.ListWorkingSpaceFilesBySnapshot(ctx, targetSnapshotId, true, true); err == nil {
		asyncCtx := websocket.CopyContext(ctx)
		wg := sync.WaitGroup{}
		stable.GoSafe(asyncCtx, func() {
			for _, file := range files {
				// 异步加载
				wg.Add(1)
				stable.GoSafe(asyncCtx, func(f definition.WorkingSpaceFile) func() {
					return func() {
						service.loadWorkingSpaceFile(asyncCtx, f, &wg)
					}
				}(file), stable.SceneInlineEdit, file.Id)
			}

			wg.Wait()
			service.SyncSnapshotList(asyncCtx, snapshot.SessionId, SWITCH_FRUSH_END)
		}, stable.SceneInlineEdit, snapshot.SessionId)
	} else {
		log.Errorf("ListWorkingSpaceFilesBySnapshot error in switchSnapshot: ", err.Error())
		return definition.UnknownErrorCode, err.Error()
	}

	service.SyncSnapshotList(ctx, snapshot.SessionId, REFRESH_CURRENT)
	return "", ""
}

/**
 * 批量删除工作区文件
 * 流程:
 *   1. 异步批量删除工作区文件存储内容：包括对应的工作区本地文件
 *   2. 批量删除工作区文件记录
 */
func (service *ChatWorkingSpaceServiceManager) batchDeleteWorkingSpaceFiles(ids []string) error {
	// 异步批量删除工作区文件存储内容
	ids, err := service.batchDeleteWorkingSpaceFileContentSync(ids)
	if err != nil {
		return err
	}
	// 批量删除工作区文件记录
	return service.chatWorkingSpaceFileTemplate.BatchDeleteWorkingSpaceFiles(ids)
}

/**
 * 异步批量删除工作区文件存储内容
 * 由于删除工作区文件存储内容通常和删除工作区文件同时进行，所以需要先获取到工作区文件，避免异步过程中工作区文件被删除
 * @return (ids, error) localFileIds 返回需要删除的工作区id列表
 */
func (service *ChatWorkingSpaceServiceManager) batchDeleteWorkingSpaceFileContentSync(ids []string) ([]string, error) {
	workingSpaceFiles, err := service.chatWorkingSpaceFileTemplate.BatchGetWorkingSpaceFiles(ids)
	if err != nil {
		return ids, err
	}
	var localFileIds []string
	for _, workingSpaceFile := range workingSpaceFiles {
		localFileIds = append(localFileIds, workingSpaceFile.LocalId)
		ids = append(ids, workingSpaceFile.LocalId)
		stable.GoSafe(context.Background(), func() {
			service.deleteSingleWorkingSpaceFileContent(workingSpaceFile)
		}, stable.SceneInlineEdit, workingSpaceFile.Id)
	}
	localFiles, err := service.chatWorkingSpaceFileTemplate.BatchGetWorkingSpaceFiles(localFileIds)
	if err != nil {
		return ids, err
	}
	for _, localFile := range localFiles {
		stable.GoSafe(context.Background(), func() {
			service.deleteSingleWorkingSpaceFileContent(localFile)
		}, stable.SceneInlineEdit, localFile.Id)
	}
	return ids, nil
}

/**
 * 删除工作区文件子项存储内容
 */
func (service *ChatWorkingSpaceServiceManager) deleteSingleWorkingSpaceFileContent(workingSpaceFile definition.WorkingSpaceFile) error {
	fileProvider := service.GetWorkingSpaceFileProvider(workingSpaceFile)
	return fileProvider.Delete(workingSpaceFile.Id, workingSpaceFile.Key)
}

/**
 * 创建工作区文件子项
 * 工作区文件 = 工作区文件主项 + 对应的工作区本地文件
 * 流程：
 *  1. 计算额外信息：对于工作区文件主项，判断是否使用工作区本地文件存储；对于工作区本地文件，计算 MD5 值
 *  2. 插入工作区文件子项
 *  3. 对于工作区文件主项，先清理重复工作区文件，然后创建引用
 *  4. 异步存储文件内容
 *
 * @param localId: localId = "" 时，表示为工作区本地文件；localId != "" 时，表示为工作区文件主项
 * @param content: 文件存储内容，工作区本地文件从本地文件获取；对于工作区文件主项，如果 content=CONTENT_USE_WORKING_SPACE_LOCAL,表示不额外存储，直接使用本地文件的存储
 * @param baseMd5: 文件变动基于的MD5值
 *
 * @return (itemId, content, error): content: 最终文件存储的内容
 */
func (service *ChatWorkingSpaceServiceManager) createSingleWorkingSpaceFile(ctx context.Context, sessionId string, snapshotId string, filePath string, mode string, version string, localId string, language string, content string, status STATUS, baseMd5 string, id string, chatMode string) (string, string, error) {
	if id == "" {
		id = uuid.NewString()
	}
	// 计算额外信息
	extraMap := make(map[string]interface{})
	if localId != "" {
		// 判断是否使用工作区本地文件存储
		if content == CONTENT_USE_WORKING_SPACE_LOCAL {
			extraMap[CONTENT_USE_WORKING_SPACE_LOCAL] = true
		}
		extraMap[BASE_MD5] = baseMd5
	}
	if chatMode != "" {
		extraMap[CHAT_MODE] = chatMode
	}
	extra := "{}"
	jsonData, err := json.Marshal(extraMap)
	if err == nil {
		extra = string(jsonData)
	}

	// 插入工作区文件子项
	// 目前 FileId 即为文件绝对路径
	fileId := filePath
	workingSpaceFile := definition.WorkingSpaceFile{
		Id:         id,
		SessionId:  sessionId,
		SnapshotId: snapshotId,
		FileId:     fileId,
		// 目前默认使用 LOCAL_FILE 存储方式
		Type: LOCAL_FILE,
		// 目前默认使用 全文 存储方式
		ContentType: FULL,
		Language:    language,
		Mode:        mode,
		Status:      status.String(),
		Version:     version,
		LocalId:     localId,
		Extra:       extra,
		GmtCreate:   time.Now().UnixMilli(),
		GmtModified: time.Now().UnixMilli(),
	}
	// 生成文件存储 Key
	fileProvider := service.GetWorkingSpaceFileProvider(workingSpaceFile)
	key := fileProvider.GenerateKey(id, fileId)
	workingSpaceFile.Key = key

	err = service.chatWorkingSpaceFileTemplate.InsertWorkingSpaceFile(workingSpaceFile)
	if err != nil {
		return "", content, err
	}

	if localId != "" {
		/*
			// 清理同文件下的相关工作区文件，保证一个快照内同文件只有一个工作区文件
			references, err := service.chatWorkingSpaceFileReferenceTemplate.ListWorkingSpaceFileReferencesByFileId(snapshotId, fileId)
			if err == nil {
				// 清理重复的工作区文件
				var toRemoveIds []string
				for _, reference := range references {
					// 只有 NEWED 的文件需要被清理存储内容
					if reference.Mode == NEWED {
						toRemoveIds = append(toRemoveIds, reference.ItemId)
					}
				}
				service.batchDeleteWorkingSpaceFiles(toRemoveIds)
			}
			// 清理引用
			err = service.chatWorkingSpaceFileReferenceTemplate.DeleteWorkingSpaceFileReferenceByFileId(snapshotId, fileId)
		*/

		// 清理可能存在的引用依赖
		if references, err := service.chatWorkingSpaceFileReferenceTemplate.ListWorkingSpaceFileReferencesByFileId(snapshotId, fileId); err == nil {
			for _, reference := range references {
				if reference.Mode == INHERIT {
					service.chatWorkingSpaceFileReferenceTemplate.DeleteWorkingSpaceFileReferenceById(reference.Id)
				}
			}
		}

		// 创建引用关系
		err = service.chatWorkingSpaceFileReferenceTemplate.InsertWorkingSpaceFileReference(definition.WorkingSpaceFileReference{
			Id:         uuid.NewString(),
			SnapshotId: snapshotId,
			ItemId:     id,
			FileId:     fileId,
			Mode:       NEWED,
		})
		if err != nil {
			return "", content, err
		}
	}

	// 空文本、使用工作区本地文件存储、工作区文件主项且为终态情况下，无需存储
	if content != CONTENT_USE_WORKING_SPACE_LOCAL {
		content, err = service.saveSingleWorkingSpaceFileContent(ctx, workingSpaceFile, content)
	}
	return id, content, err
}

func (service *ChatWorkingSpaceServiceManager) GetWorkingSpaceFile(id string) (definition.WorkingSpaceFile, error) {
	return service.chatWorkingSpaceFileTemplate.GetWorkingSpaceFile(id)
}

/**
 * 创建工作区文件
 * 流程：
 *  1. 判断是否首次加入工作区，是则在之前的快照插入初始版本
 *  2. 创建对应的工作区本地文件
 *  3. 创建对应的工作区文件及其引用
 *  4. 同步工作区文件变化
 *  5. 同步 VersionCount 变化
 *
 * @param: mode: 工作区文件类型，ADD新增/MODIFIED修改
 */
func (service *ChatWorkingSpaceServiceManager) CreateWorkingSpaceFile(ctx context.Context, sessionId string, filePath string, language string, mode string, status STATUS, id string, chatMode string) (string, error) {
	if status != ACCEPTED {
		// 同文件仅允许一个进程进行编辑
		actual, _ := WorkingSpaceFileLock.LoadOrStore(filePath, &CheckableMutex{})
		mutex := actual.(*CheckableMutex)
		if !mutex.TryLock() {
			// 文件编辑锁超过10分钟，认为可能出现异常，强制解锁后重新上锁
			if mutex.LockTime() > 10*60*1000 {
				mutex.Unlock()
				mutex.TryLock()
			} else {
				return "", errors.New(definition.LockErrorCode)
			}
		}
	}

	// 更新记录 chatMode
	if chatMode == definition.SessionModeAgent {
		if util.IsQuestMode(ctx) {
			if util.IsQuestDesignSession(ctx) {
				chatMode = definition.SessionModeDesign
			} else {
				chatMode = definition.SessionModeQuest
			}
		}
	}

	// 目前只允许往当前快照创建工作区文件
	currentSnapshot, exists := CurrentSnapshotMap[sessionId]
	if !exists {
		log.Errorf("Current snapshot not found. sessionId: %s", sessionId)
		//return "", errors.New("current snapshot not found")
		// 找不到快照，则默认在最后的快照添加
		if snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(sessionId); err == nil && len(snapshots) > 0 {
			currentSnapshot = snapshots[len(snapshots)-1]
		} else {
			return "", errors.New("current snapshot not found")
		}
	}
	// 目前 FileId 即为文件绝对路径
	fileId := filePath
	if mode != DELETE {
		// 根据当前文件是否存在，判断mode
		if util.FileExists(filePath) {
			mode = MODIFIED
		} else {
			mode = ADD
		}
	}
	// 获取当前会话下指定文件相关的工作区文件列表
	files, err := service.chatWorkingSpaceFileTemplate.ListWorkingSpaceFileByFileId(sessionId, fileId)
	if err != nil {
		return "", err
	}
	version := 0
	// 仅记录非失败/取消状态的文件版本
	lastFinish := false
	for i := len(files) - 1; i >= 0; i-- {
		file := files[i]
		if mode == MODIFIED && !lastFinish {
			if finishStatusMap[file.Status] != 0 {
				lastFinish = true
			} else {
				mode = file.Mode
			}
		}
		if file.Version != "-" {
			version++
		}
	}
	// 如果是历史激活的工作区文件，则版本为0
	if currentSnapshot.Status == ACTIVE_INIT.String() {
		version = 0
	}

	// 文件编辑前，保存文件
	if status == GENERATING && util.FileExists(filePath) {
		saveFile(ctx, filePath)
	}

	// 判断是否首次加入工作区，是则在之前的快照插入初始版本
	if len(files) == 0 {
		snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(sessionId)
		if err == nil && len(snapshots) > 0 && snapshots[0].Status != OUTDATED.String() {
			initItemId := ""
			localId, _, err := service.createSingleWorkingSpaceFile(ctx, sessionId, snapshots[0].Id, filePath, mode, "0", "", language, "", ACCEPTED, "", "", chatMode)
			if err == nil {
				initItemId, _, err = service.createSingleWorkingSpaceFile(ctx, sessionId, snapshots[0].Id, filePath, mode, "0", localId, language, CONTENT_USE_WORKING_SPACE_LOCAL, ACCEPTED, "", "", chatMode)
				if err != nil {
					initItemId = ""
				}
			}
			if initItemId != "" {
				version++
				for i, snapshot := range snapshots {
					// 在初始快照和当前快照之间，插入文件 v0 版本的引用继承
					if i == 0 {
						continue
					}
					if snapshot.Id == currentSnapshot.Id {
						break
					}
					service.chatWorkingSpaceFileReferenceTemplate.InsertWorkingSpaceFileReference(definition.WorkingSpaceFileReference{
						Id:         uuid.NewString(),
						SnapshotId: snapshot.Id,
						ItemId:     initItemId,
						FileId:     fileId,
						Mode:       INHERIT,
					})
				}
			}
		}
	}

	// 创建对应的工作区本地文件
	localId, content, err := service.createSingleWorkingSpaceFile(ctx, sessionId, currentSnapshot.Id, filePath, mode, strconv.Itoa(version), "", language, "", ACCEPTED, "", "", chatMode)
	if err != nil {
		return "", err
	}

	// BaseMd5 计算逻辑：如果上一次为终态，则以本次文件的MD5为BaseMd5；否则继承上一次文件的BaseMd5
	baseMd5 := encrypt.Md5EncodeBytes([]byte(content))

	// 工作区文件的初始内容为上一个版本的内容;如果是首次创建或者历史恢复场景，即本地文件内容
	if len(files) > 0 && status != ACCEPTED {
		lastFile := files[len(files)-1]
		if lastContent, err := service.getFullContent(ctx, lastFile); err == nil {
			content = lastContent
		}
		// 非终态，则继承上一个文件的 BaseMd5
		if finishStatusMap[lastFile.Status] == 0 {
			// 更新 Md5
			var extraMap map[string]interface{}
			if err = json.Unmarshal([]byte(lastFile.Extra), &extraMap); err == nil {
				if md5, ok := extraMap[BASE_MD5].(string); ok {
					baseMd5 = md5
				}
			}
		}
	}

	// 创建对应的工作区文件及其引用
	itemId, _, err := service.createSingleWorkingSpaceFile(ctx, sessionId, currentSnapshot.Id, filePath, mode, strconv.Itoa(version), localId, language, content, status, baseMd5, id, chatMode)
	if err != nil {
		return "", err
	}

	// 更新工作区文件处理中状态表
	WorkingSpaceFileGeneratingContentMap[itemId] = &definition.GenerateStreamContent{
		FullContent: "",
	}
	WorkingSpaceFileIngMap.Store(itemId, GENERATING.String())

	// 同步工作区文件变化
	syncMode := MODIFIED
	// 第一次插入
	if version == 1 {
		syncMode = ADD
	}
	workingSpaceFile, err := service.chatWorkingSpaceFileTemplate.GetWorkingSpaceFile(itemId)
	if err != nil {
		return "", err
	}
	// 同步 VersionCount 变化
	for _, file := range files {
		// 初始版本不需要同步
		if file.Version == "0" {
			continue
		}
		service.SyncWorkingSpaceFile(ctx, file, MODIFIED, version, definition.DiffInfo{}, definition.DiffInfo{}, "", "", "")
	}
	service.SyncWorkingSpaceFile(ctx, workingSpaceFile, syncMode, version, definition.DiffInfo{}, definition.DiffInfo{}, "", currentSnapshot.Id, "")
	return itemId, nil
}

func (service *ChatWorkingSpaceServiceManager) ListWorkingSpaceFilesBySnapshot(ctx context.Context, snapshotId string, containInit bool, filterDuplicate bool) ([]definition.WorkingSpaceFile, error) {
	snapshot, err := service.chatSnapshotTemplate.GetSnapshot(snapshotId)
	if err != nil {
		return nil, err
	}
	references, err := service.chatWorkingSpaceFileReferenceTemplate.ListWorkingSpaceFileReferencesBySnapshotId(snapshotId)
	if err != nil {
		return nil, err
	}
	var ids []string
	for _, reference := range references {
		ids = append(ids, reference.ItemId)
	}
	files, err := service.chatWorkingSpaceFileTemplate.BatchGetWorkingSpaceFiles(ids)
	if err != nil {
		return nil, err
	}
	// 每个文件路径只保留最新的
	sort.SliceStable(files, func(i, j int) bool {
		return files[i].GmtCreate > files[j].GmtCreate
	})
	fileIdMaps := make(map[string]bool)
	var results []definition.WorkingSpaceFile
	// 过滤相同路径
	for _, file := range files {
		// 判断是否需要过滤重复文件
		if filterDuplicate {
			if ok, exists := fileIdMaps[file.FileId]; exists && ok {
				continue
			}
			fileIdMaps[file.FileId] = true
		}
		results = append(results, file)
	}
	// 按照时间顺序返回
	files = []definition.WorkingSpaceFile{}
	for i := len(results) - 1; i >= 0; i-- {
		files = append(files, results[i])
	}
	results = []definition.WorkingSpaceFile{}
	for _, file := range files {
		// 不包含初始版本
		if !containInit && file.Version == "0" && snapshot.Status != ACTIVE_INIT.String() {
			continue
		}
		results = append(results, file)
	}
	// 过期的激活初始快照，也返回全部列表
	if len(results) == 0 && snapshot.Status == OUTDATED.String() {
		results = files
	}
	return results, nil
}

/**
 * 获取快照下的工作区文件列表
 * 工作区文件的snapshotId改成此快照的id
 */
func (service *ChatWorkingSpaceServiceManager) ListWorkingSpaceFileVOsBySnapshot(ctx context.Context, snapshotId string, containInit bool, filterDuplicate bool, fillContent bool) ([]definition.WorkingSpaceFileVO, error) {
	files, err := service.ListWorkingSpaceFilesBySnapshot(ctx, snapshotId, containInit, filterDuplicate)
	results := []definition.WorkingSpaceFileVO{}
	if err != nil {
		return results, err
	}
	for _, file := range files {
		if fillContent && file.SnapshotId != snapshotId {
			continue
		}
		versionCount := 0
		diffInfo := definition.DiffInfo{}
		lastDiffInfo := definition.DiffInfo{}
		// TODO: 历史恢复场景
		if WorkingSpaceFileVersionMap[file.SessionId] != nil {
			if target, ok := WorkingSpaceFileVersionMap[file.SessionId][file.FileId]; ok {
				versionCount = target
			}
		}
		if WorkingSpaceFileDiffInfoMap[file.SessionId] != nil {
			if target, ok := WorkingSpaceFileDiffInfoMap[file.SessionId][file.FileId]; ok {
				diffInfo = target
			}
		}
		if WorkingSpaceFileLastDiffInfoMap[file.SessionId] != nil {
			if target, ok := WorkingSpaceFileLastDiffInfoMap[file.SessionId][file.Id]; ok {
				lastDiffInfo = target
			}
		}
		file.SnapshotId = snapshotId
		fileVO := file.ToVO(versionCount, "", diffInfo, lastDiffInfo, "")
		fileVO.Message = WorkingSpaceFileMessageMap[file.Id]
		if fillContent {
			if file.Status == APPLIED.String() || file.Status == ACCEPTED.String() || file.Status == DELETED.String() {
				content, err := service.getFullContent(ctx, file)
				if err == nil {
					fileVO.AfterContent = content
				}
				if localFile, err := service.chatWorkingSpaceFileTemplate.GetWorkingSpaceFile(file.LocalId); err == nil {
					content, err = service.getFullContent(ctx, localFile)
					if err == nil {
						fileVO.BeforeContent = content
					}
				}
			}
		}
		results = append(results, fileVO)
	}
	return results, nil
}

func (service *ChatWorkingSpaceServiceManager) ListWorkingSpaceFileVOsByChatRecord(ctx context.Context, sessionId string, chatRecordId string, fillContent bool) ([]definition.WorkingSpaceFileVO, error) {
	results := []definition.WorkingSpaceFileVO{}
	chatRecords, err := SessionServiceManager.chatRecordOrmTemplate.GetSessionChats(sessionId, definition.PageInfoAll)
	if err != nil {
		return results, err
	}
	snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(sessionId)
	if err != nil {
		return results, err
	}
	idx := 0
	for _, snapshot := range snapshots {
		if snapshot.ChatRecordId == "" {
			continue
		}
		for ; idx < len(chatRecords); idx++ {
			if chatRecords[idx].RequestId == chatRecordId {
				return service.ListWorkingSpaceFileVOsBySnapshot(ctx, snapshot.Id, false, false, fillContent)
			}
			if chatRecords[idx].RequestId == snapshot.ChatRecordId {
				break
			}
		}
		if idx == len(chatRecords) {
			break
		}
	}
	return results, errors.New("can't found snapshot for chatRecordId: " + chatRecordId)
}

func (service *ChatWorkingSpaceServiceManager) UpdateWorkingSpaceFileStatusAndVersion(ctx context.Context, itemId string, status STATUS, version string) error {
	err := service.chatWorkingSpaceFileTemplate.UpdateWorkingSpaceFileStatusAndVersion(itemId, status.String(), version)
	if err != nil {
		return err
	}
	workingSpaceFile, err := service.chatWorkingSpaceFileTemplate.GetWorkingSpaceFile(itemId)
	if err != nil {
		return err
	}
	if currentSnapshot, exists := CurrentSnapshotMap[workingSpaceFile.SessionId]; exists {
		service.SyncWorkingSpaceFile(ctx, workingSpaceFile, MODIFIED, 0, definition.DiffInfo{}, definition.DiffInfo{}, "", currentSnapshot.Id, "")
	}
	return nil
}

/**
 * 更新工作区文件存储内容
 *   工作区文件主项：如果工作区文件进入终态，或者使用工作区本地文件存储，则不需要更新
 *		如果工作区文件主项内容发生改动，则触发 DiffInfo 计算
 *   工作区本地文件: 获取本地文件内容，判断 md5 是否需要更新；
 *		如果本地文件发生更新，但工作区文件未更新 或者与工作区文件不相同，则认为工作区本地文件 OUTDATED
 * 如果 APPLIED 状态的工作区文件内容发生更新，则重新计算 DiffInfo
 *
 * @param content: 对应的工作区文件主项更新内容
 */
func (service *ChatWorkingSpaceServiceManager) saveSingleWorkingSpaceFileContent(ctx context.Context, workingSpaceFile definition.WorkingSpaceFile, content string) (string, error) {
	applyMode := GetApplyMode(ctx)
	// 新建文件标识：新建文件时，创建空文件存储，以与异常情况区分
	if workingSpaceFile.LocalId == "" || (slices.Contains(APPLY_MODE_AGENT_SERIES, applyMode) && content == "") {
		// 工作区本地文件
		// 判断文件是否存在
		isExists := util.FileExists(workingSpaceFile.FileId)
		if isExists {
			// 获取本地文件内容
			localContent, err := util.GetFileContent(workingSpaceFile.FileId)
			/* 无需计算 OUTDATED
			md5 := encrypt.Md5EncodeBytes(localContent)
			// 更新 Md5
			var extraMap map[string]interface{}
			if err = json.Unmarshal([]byte(workingSpaceFile.Extra), &extraMap); err == nil {
				// 如果本地文件发生更新，但工作区文件未更新 或者与工作区文件不相同，否则说明本地文件产生工作区外改动，置为 OUTDATED
				if content != CONTENT_USE_WORKING_SPACE_LOCAL {
					if targetMd5, ok := extraMap["md5"].(string); ok && targetMd5 != md5 && string(localContent) != content {
						workingSpaceFile.Status = OUTDATED.String()
						service.chatWorkingSpaceFileTemplate.UpdateWorkingSpaceFileStatus(workingSpaceFile.Id, workingSpaceFile.Status)
					}
				}
				extraMap["md5"] = md5
				jsonData, _ := json.Marshal(extraMap)
				workingSpaceFile.Extra = string(jsonData)
				err = service.chatWorkingSpaceFileTemplate.UpdateWorkingSpaceFileExtra(workingSpaceFile.Id, workingSpaceFile.Extra)
			}
			*/
			if err == nil {
				content = string(localContent)
			}
			if content == "" {
				content = CONTENT_USE_EMPTY_FILE
			}
		} else {
			content = ""
		}
	} else {
		// 工作区文件主项
		// TODO: 触发 DiffInfo 变化
		if applyMode == APPLY_MODE_EDIT && (finishStatusMap[workingSpaceFile.Status] != 0 || content == CONTENT_USE_WORKING_SPACE_LOCAL) {
			return content, nil
		}
	}
	if content != "" {
		// 更新文件存储
		fileProvider := service.GetWorkingSpaceFileProvider(workingSpaceFile)
		log.Debugf("save workingSpaceFile content: id:%s content size: %d", workingSpaceFile.Id, len(content))
		// 写入必须是同步，避免对后续步骤产生影响
		fileProvider.Update(workingSpaceFile.Id, workingSpaceFile.Key, content)
		// 更新 DiffInfo 信息
		service.updateWorkingSpaceFileDiffInfo(ctx, workingSpaceFile, content)
	}
	return content, nil
}

/**
 * 更新工作区文件存储内容
 * 分别触发工作区文件主项和工作区本地文件的更新
 *
 * @param skip 是否跳过工作区文件主项的更新
 */
func (service *ChatWorkingSpaceServiceManager) saveWorkingSpaceFileContent(ctx context.Context, workingSpaceFile definition.WorkingSpaceFile, content string, skip bool) error {
	applyMode := GetApplyMode(ctx)
	if workingSpaceFile.LocalId == "" {
		return errors.New("only support workingSpaceFile")
	}
	var sumErr error
	// 如果工作区文件主项状态为终态，则不需要更新
	if finishStatusMap[workingSpaceFile.Status] != 0 && applyMode == APPLY_MODE_EDIT {
		content = CONTENT_USE_WORKING_SPACE_LOCAL
	}

	if !skip || slices.Contains(APPLY_MODE_AGENT_SERIES, applyMode) {
		// 保存工作区文件主项
		if _, err := service.saveSingleWorkingSpaceFileContent(ctx, workingSpaceFile, content); err != nil {
			sumErr = err
		}
	}

	if applyMode == APPLY_MODE_EDIT {
		// 保存工作区本地文件
		if localFile, err := service.chatWorkingSpaceFileTemplate.GetWorkingSpaceFile(workingSpaceFile.LocalId); err == nil {
			if _, err = service.saveSingleWorkingSpaceFileContent(ctx, localFile, content); err != nil {
				sumErr = err
			}
		} else {
			sumErr = err
		}
	}

	return sumErr
}

/**
 * 接受工作区文件
 * 流程：
 *  1. 判断是否 OUTDATED，进行二次确认
 *  2. 将工作区文件，写入本地文件
 *  3. 将工作区文件存储，写入对应的本地文件存储
 *  4. 更新工作区文件状态
 *  5. 触发同步工作区文件
 *  6. 进入终态，清理工作区文件存储
 */
func (service *ChatWorkingSpaceServiceManager) acceptWorkingSpaceFile(ctx context.Context, workingSpaceFile definition.WorkingSpaceFile, skipSave bool) (string, string) {
	if workingSpaceFile.LocalId == "" {
		return definition.UnSupportedErrorCode, "only support workingSpaceFile"
	}

	// 只有已应用状态可以接受
	if workingSpaceFile.Status != APPLIED.String() && workingSpaceFile.Status != DELETED.String() {
		log.Debugf("accept workingSpace failed: workingSpaceFile status is not APPLIED, status:" + workingSpaceFile.Status)
		return "", ""
		//return definition.UnSupportedErrorCode, "workingSpaceFile status is not APPLIED, status:" + workingSpaceFile.Status
	}

	// 终态文件不允许改变状态
	if finishStatusMap[workingSpaceFile.Status] != 0 {
		return definition.UnSupportedErrorCode, "workingSpaceFile is finished, status:" + workingSpaceFile.Status
	}

	// 判断是否 OUTDATED，进行二次确认
	localFile, err := service.chatWorkingSpaceFileTemplate.GetWorkingSpaceFile(workingSpaceFile.LocalId)
	if err != nil {
		return definition.DataCorruptErrorCode, "workingSpaceFile localFile not found: " + workingSpaceFile.LocalId
	}
	// TODO：二次确认通过
	if localFile.Status == OUTDATED.String() {
		//return definition.WorkingSpaceFileOutdatedErrorCode, "workingSpaceFile localFile is outdated"
	}

	applyMode := GetApplyMode(ctx)

	finalContent := ""
	// 将工作区文件，写入本地文件
	if content, err := service.getFullContentWithEmptyFile(ctx, workingSpaceFile); err == nil {
		finalContent = content
		if finalContent == CONTENT_USE_EMPTY_FILE {
			finalContent = ""
		}
		if !skipSave && content != "" {
			// 将工作区文件，写入本地文件
			if err = util.NewFile(workingSpaceFile.FileId, finalContent); err != nil {
				return definition.UnknownErrorCode, err.Error()
			}
		}
		// 将工作区文件存储，写入对应的本地文件存储
		localFileProvider := service.GetWorkingSpaceFileProvider(localFile)
		// 异步更新存储
		asyncCtx := websocket.CopyContext(ctx)
		stable.GoSafe(asyncCtx, func() {
			// 目前只有 EDIT 模式需要更新对应的工作区本地文件
			if applyMode == APPLY_MODE_EDIT {
				localFileProvider.Update(localFile.Id, localFile.Key, content)
			}
			if !skipSave {
				if file, err := service.chatWorkingSpaceFileTemplate.GetWorkingSpaceFile(workingSpaceFile.Id); err == nil {
					service.SyncWorkingSpaceFile(asyncCtx, file, FRUSH, 0, definition.DiffInfo{}, definition.DiffInfo{}, "", "", "")
				}
			}
		}, stable.SceneInlineEdit, workingSpaceFile.Id)
		/*
			// 更新 md5 值
			var extraMap map[string]interface{}
			if err = json.Unmarshal([]byte(localFile.Extra), &extraMap); err == nil {
				md5 := encrypt.Md5EncodeBytes([]byte(content))
				extraMap["md5"] = md5
				jsonData, _ := json.Marshal(extraMap)
				localFile.Extra = string(jsonData)
				err = service.chatWorkingSpaceFileTemplate.UpdateWorkingSpaceFileExtra(localFile.Id, localFile.Extra)
			}
		*/
	} else {
		return definition.UnknownErrorCode, err.Error()
	}

	// 更新工作区文件状态
	workingSpaceFile.Status = ACCEPTED.String()
	if err = service.chatWorkingSpaceFileTemplate.UpdateWorkingSpaceFileStatusAndVersion(workingSpaceFile.Id, workingSpaceFile.Status, ""); err != nil {
		return definition.SqlExecErrorCode, err.Error()
	}

	// 触发同步工作区文件
	currentSnapshot, exists := CurrentSnapshotMap[workingSpaceFile.SessionId]
	if !exists {
		if snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(workingSpaceFile.SessionId); err == nil && len(snapshots) > 0 {
			currentSnapshot = snapshots[len(snapshots)-1]
		}
	}
	service.SyncWorkingSpaceFile(ctx, workingSpaceFile, MODIFIED, 0, definition.DiffInfo{}, definition.DiffInfo{}, "", currentSnapshot.Id, "")

	// TODO: 进入终态，清理工作区文件存储

	// 终态记录增删改数量
	diffInfo := definition.DiffInfo{}
	if diffInfoMap, ok := WorkingSpaceFileDiffInfoMap[workingSpaceFile.SessionId]; ok && diffInfoMap != nil {
		diffInfo, _ = diffInfoMap[workingSpaceFile.FileId]
	}
	data := map[string]string{
		"resource_type":  "workingSpaceFile",
		"resource_id":    workingSpaceFile.Id,
		"action_type":    ACCEPT.String(),
		"session_id":     workingSpaceFile.SessionId,
		"chat_record_id": currentSnapshot.ChatRecordId,
		"snapshot_id":    currentSnapshot.Id,
		"add_counts":     strconv.Itoa(diffInfo.AddCounts),
		"del_counts":     strconv.Itoa(diffInfo.DelCounts),
		"mod_counts":     strconv.Itoa(diffInfo.ModCounts),
		"all_counts":     strconv.Itoa(len(strings.Split(finalContent, "\n"))),
	}
	stable.GoSafe(context.Background(), func() {
		sls.Report(sls.EventTypeChatAiDeveloperDiffInfo, currentSnapshot.ChatRecordId, data)
	}, stable.SceneInlineEdit, currentSnapshot.ChatRecordId)

	return "", ""
}

/**
 * 拒绝工作区文件
 * 流程：
 *  1. 判断是否 OUTDATED，进行二次确认
 *  2. 获取工作区文件稳态版本内容
 *  3. 将工作区文件，写入本地文件
 *  4. 将工作区文件存储，写入对应的本地文件存储
 *  5. 更新工作区文件状态
 *  6. 触发同步工作区文件
 *  7. 进入终态，清理工作区文件存储
 */
func (service *ChatWorkingSpaceServiceManager) rejectWorkingSpaceFile(ctx context.Context, workingSpaceFile definition.WorkingSpaceFile, skipSave bool) (string, string) {
	if workingSpaceFile.LocalId == "" {
		return definition.UnSupportedErrorCode, "only support workingSpaceFile"
	}

	// 终态文件不允许改变状态
	if finishStatusMap[workingSpaceFile.Status] != 0 {
		return definition.UnSupportedErrorCode, "workingSpaceFile is finished, status:" + workingSpaceFile.Status
	}

	// 判断是否 OUTDATED，进行二次确认
	localFile, err := service.chatWorkingSpaceFileTemplate.GetWorkingSpaceFile(workingSpaceFile.LocalId)
	if err != nil {
		return definition.DataCorruptErrorCode, "workingSpaceFile localFile not found: " + workingSpaceFile.LocalId
	}
	// TODO：二次确认通过
	if localFile.Status == OUTDATED.String() {
		//return definition.WorkingSpaceFileOutdatedErrorCode, "workingSpaceFile localFile is outdated"
	}

	// 修改本地文件
	// 获取工作区文件稳态版本内容
	if _, content, _, err := service.getLastStableContentWithEmptyFile(ctx, workingSpaceFile.SessionId, workingSpaceFile.FileId, workingSpaceFile.Version, true); err == nil {
		// 如果原始文件不存在，则需要删除
		targetContent := content
		if content == "" {
			os.Remove(workingSpaceFile.FileId)
		} else if !skipSave || workingSpaceFile.Mode == DELETE {
			// 删除文件目前端侧不支持回退，先通过go端回退
			if content == CONTENT_USE_EMPTY_FILE {
				targetContent = ""
			}
			// 将工作区文件，写入本地文件
			util.NewFile(workingSpaceFile.FileId, targetContent)
		}
		if GetApplyMode(ctx) == APPLY_MODE_EDIT {
			// 将工作区文件存储，写入对应的本地文件存储
			localFileProvider := service.GetWorkingSpaceFileProvider(localFile)
			// 异步更新存储
			// 新增文件场景，无需写入
			if !strings.HasPrefix(localFile.Mode, ADD) || util.FileExists(localFile.FileId) {
				stable.GoSafe(context.Background(), func() {
					localFileProvider.Update(localFile.Id, localFile.Key, content)
				}, stable.SceneInlineEdit, localFile.Id)
			}
		} else {
			workingSpaceFileProvider := service.GetWorkingSpaceFileProvider(workingSpaceFile)
			if content == "" {
				stable.GoSafe(context.Background(), func() {
					workingSpaceFileProvider.Delete(workingSpaceFile.Id, workingSpaceFile.Key)
				}, stable.SceneInlineEdit, workingSpaceFile.Id)
			} else {
				stable.GoSafe(context.Background(), func() {
					workingSpaceFileProvider.Update(workingSpaceFile.Id, workingSpaceFile.Key, content)
				}, stable.SceneInlineEdit, workingSpaceFile.Id)
			}
		}
		// 更新 md5 值
		var extraMap map[string]interface{}
		if err = json.Unmarshal([]byte(localFile.Extra), &extraMap); err == nil {
			md5 := encrypt.Md5EncodeBytes([]byte(content))
			extraMap["md5"] = md5
			jsonData, _ := json.Marshal(extraMap)
			localFile.Extra = string(jsonData)
			// TODO: 待确认 MD5 逻辑
			err = service.chatWorkingSpaceFileTemplate.UpdateWorkingSpaceFileExtra(localFile.Id, localFile.Extra)
		}
	} else {
		return definition.UnknownErrorCode, err.Error()
	}

	// 更新工作区文件处理中状态表
	WorkingSpaceFileIngMap.Delete(workingSpaceFile.Id)
	ing := false
	if workingSpaceFile.Status == GENERATING.String() || workingSpaceFile.Status == DELETING.String() {
		WorkingSpaceFileGeneratingContentMap[workingSpaceFile.Id] = nil
		delete(WorkingSpaceFileGeneratingContentMap, workingSpaceFile.Id)
		ing = true
	} else if workingSpaceFile.Status == APPLYING.String() {
		WorkingSpaceFileApplyingContentMap[workingSpaceFile.Id] = nil
		delete(WorkingSpaceFileApplyingContentMap, workingSpaceFile.Id)
		ing = true
	}

	// 如果是进行中状态被拒绝，需要解锁
	if ing {
		if actual, ok := WorkingSpaceFileLock.Load(workingSpaceFile.FileId); ok {
			mutex := actual.(*CheckableMutex)
			mutex.Unlock()
		}
	}

	// 更新工作区文件状态
	workingSpaceFile.Status = REJECTED.String()
	if err = service.chatWorkingSpaceFileTemplate.UpdateWorkingSpaceFileStatusAndVersion(workingSpaceFile.Id, workingSpaceFile.Status, ""); err != nil {
		return definition.SqlExecErrorCode, err.Error()
	}

	// 触发同步工作区文件
	currentSnapshot, exists := CurrentSnapshotMap[workingSpaceFile.SessionId]
	if !exists {
		if snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(workingSpaceFile.SessionId); err == nil && len(snapshots) > 0 {
			currentSnapshot = snapshots[len(snapshots)-1]
		}
	}
	service.SyncWorkingSpaceFile(ctx, workingSpaceFile, MODIFIED, 0, definition.DiffInfo{}, definition.DiffInfo{}, "", currentSnapshot.Id, "")

	// TODO: 进入终态，清理工作区文件存储

	// 终态记录增删改数量
	diffInfo := definition.DiffInfo{}
	if diffInfoMap, ok := WorkingSpaceFileDiffInfoMap[workingSpaceFile.SessionId]; ok && diffInfoMap != nil {
		diffInfo, _ = diffInfoMap[workingSpaceFile.FileId]
	}
	data := map[string]string{
		"resource_type":  "workingSpaceFile",
		"resource_id":    workingSpaceFile.Id,
		"action_type":    REJECT.String(),
		"session_id":     workingSpaceFile.SessionId,
		"chat_record_id": currentSnapshot.ChatRecordId,
		"snapshot_id":    currentSnapshot.Id,
		"add_counts":     strconv.Itoa(diffInfo.AddCounts),
		"del_counts":     strconv.Itoa(diffInfo.DelCounts),
		"mod_counts":     strconv.Itoa(diffInfo.ModCounts),
	}
	go sls.Report(sls.EventTypeChatAiDeveloperDiffInfo, currentSnapshot.ChatRecordId, data)

	return "", ""
}

/**
 * 取消工作区文件
 * 流程如下:
 *  1. 判断是否处于处理中状态 GENERATEING/APPLYING
 *. 2. 更新工作区文件状态
 *  3. 更新工作区文件处理中状态表
 *  4. 同步工作区文件状态
 */
func (service *ChatWorkingSpaceServiceManager) cancelWorkingSpaceFile(ctx context.Context, workingSpaceFile definition.WorkingSpaceFile, needSync bool, failed bool, errorCode string) (string, string) {
	var status STATUS
	// 目前仅 JB 支持处理failed状态
	if util.GetIdeSeries(ctx) != string(global.IDE_SERIES_JETBRAINS) && util.GetIdeSeries(ctx) != string(global.IDE_SERIES_QODER) {
		failed = false
	}
	if errorCode == definition.UserCancelErrorCode {
		failed = false
		errorCode = ""
	}
	// 判断是否处于处理中状态 GENERATEING/APPLYING
	if workingSpaceFile.Status == GENERATING.String() {
		if failed {
			status = GENERATING_FAILED
		} else {
			status = GENERATING_CANCELLED
		}
	} else if workingSpaceFile.Status == APPLYING.String() {
		if failed {
			status = APPLYING_FAILED
		} else {
			status = APPLYING_CANCELLED
		}
	} else if workingSpaceFile.Status == DELETING.String() {
		status = DELETING_FAILED
	} else {
		log.Warnf("CancelWorkingSpaceFile Failed: Status: %v", workingSpaceFile.Status)
		return "", ""
	}

	// 解锁
	if actual, ok := WorkingSpaceFileLock.Load(workingSpaceFile.FileId); ok {
		mutex := actual.(*CheckableMutex)
		mutex.Unlock()
	}

	/* 忽略版本变成-，避免上一个稳定版本获取异常
	// 更新工作区文件状态
	version := ""
	// 目前仅 JB、Qoder 支持：失败/取消状态，不进版本，版本为-
	if util.GetIdeSeries(ctx) == string(global.IDE_SERIES_JETBRAINS) || util.GetIdeSeries(ctx) == string(global.IDE_SERIES_QODER) {
		version = "-"
		workingSpaceFile.Version = version
	}
	*/
	err := service.chatWorkingSpaceFileTemplate.UpdateWorkingSpaceFileStatusAndVersion(workingSpaceFile.Id, status.String(), "")
	if err != nil {
		return definition.SqlExecErrorCode, err.Error()
	}

	// 对于流式写入的场景，需要支持内容的回退
	// 目前仅 design 模式下的 create_file 支持流式写入
	if util.IsQuestDesignSession(ctx) && WorkingSpaceFileMethodMap[workingSpaceFile.Id] == "create_file" {
		modificationStreamContent := WorkingSpaceFileGeneratingContentMap[workingSpaceFile.Id]
		if modificationStreamContent != nil && modificationStreamContent.FullContent != "" {
			os.Remove(workingSpaceFile.FileId)
		}
	} else {
		// 修改失败，保存文件
		saveFile(ctx, workingSpaceFile.FileId)
	}

	// 更新工作区文件处理中状态表
	WorkingSpaceFileIngMap.Delete(workingSpaceFile.Id)
	if workingSpaceFile.Status == GENERATING.String() || workingSpaceFile.Status == DELETING.String() {
		WorkingSpaceFileGeneratingContentMap[workingSpaceFile.Id] = nil
		delete(WorkingSpaceFileGeneratingContentMap, workingSpaceFile.Id)
	} else {
		WorkingSpaceFileApplyingContentMap[workingSpaceFile.Id] = nil
		delete(WorkingSpaceFileApplyingContentMap, workingSpaceFile.Id)
	}

	// 采纳取消的文件，保存解决方案以备 ReApply
	// 暂时不支持宕机后恢复，所以不存储到db
	/*
		var extraMap map[string]interface{}
		if err = json.Unmarshal([]byte(workingSpaceFile.Extra), &extraMap); err == nil {
			extraMap["modification"] = modification
			jsonData, _ := json.Marshal(extraMap)
			workingSpaceFile.Extra = string(jsonData)
			service.chatWorkingSpaceFileTemplate.UpdateWorkingSpaceFileExtra(workingSpaceFile.Id, workingSpaceFile.Extra)
		}
	*/

	// 同步工作区文件状态
	workingSpaceFile.Status = status.String()
	currentSnapshotId := ""
	if needSync {
		if currentSnapshot, exists := CurrentSnapshotMap[workingSpaceFile.SessionId]; exists {
			currentSnapshotId = currentSnapshot.Id
		} else {
			if snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(workingSpaceFile.SessionId); err == nil && len(snapshots) > 0 {
				currentSnapshotId = snapshots[len(snapshots)-1].Id
			}
		}
	}
	service.SyncWorkingSpaceFile(ctx, workingSpaceFile, MODIFIED, 0, definition.DiffInfo{}, definition.DiffInfo{}, "", currentSnapshotId, errorCode)
	return "", ""
}

/**
 * 采纳工作区文件
 * 流程如下:
 *  1. 触发文件采纳
 *. 2. 更新工作区文件状态
 *  3. 更新工作区文件处理中状态表
 *  4. 同步工作区文件状态
 */
func (service *ChatWorkingSpaceServiceManager) applyWorkingSpaceFile(ctx context.Context, workingSpaceFile definition.WorkingSpaceFile, params definition.DiffApplyParams) (string, string) {
	// 如果不能采纳，则直接返回
	if canApplyStatusMap[workingSpaceFile.Status] == 0 {
		return definition.UnSupportedErrorCode, "unsupported apply status: " + workingSpaceFile.Status
	}
	errorCode := ""
	errorMsg := ""
	modification := ""
	initStatus := workingSpaceFile.Status
	failedStatus := APPLYING_CANCELLED.String()
	if util.GetIdeSeries(ctx) == string(global.IDE_SERIES_JETBRAINS) || util.GetIdeSeries(ctx) == string(global.IDE_SERIES_QODER) {
		failedStatus = APPLYING_FAILED.String()
	}
	if workingSpaceFile.Status != failedStatus {
		// 更新工作区文件状态: PENDING - 等待 Apply
		workingSpaceFile.Status = PENDING.String()
	}
	if WorkingSpaceFileGeneratingContentMap[workingSpaceFile.Id] != nil && WorkingSpaceFileGeneratingContentMap[workingSpaceFile.Id].FullContent != "" {
		// 从工作区文件处理内容表获取解决方案
		modification = WorkingSpaceFileGeneratingContentMap[workingSpaceFile.Id].FullContent
	} else {
		// 从db存储中获取
		var extraMap map[string]interface{}
		if err := json.Unmarshal([]byte(workingSpaceFile.Extra), &extraMap); err == nil {
			modification, _ = extraMap["modification"].(string)
		}
	}
	// 触发文件采纳
	if content, err := service.getFullContent(ctx, workingSpaceFile); err == nil {
		workingSpaceFile.Status = APPLYING.String()
		// 更新工作区文件处理中状态表
		WorkingSpaceFileApplyingContentMap[workingSpaceFile.Id] = &definition.ApplyStreamContent{
			OriginalContent: content,
			AppliedContent:  "",
			FullContent:     "",
			LastContent:     "",
			StreamContent:   []string{},
		}
		WorkingSpaceFileIngMap.Store(workingSpaceFile.Id, APPLYING.String())
		chatRecordId := ""
		if snapshot, err := service.chatSnapshotTemplate.GetSnapshot(workingSpaceFile.SnapshotId); err == nil {
			chatRecordId = snapshot.ChatRecordId
		}
		if params.ChatRecordId == "" {
			params = definition.DiffApplyParams{
				RequestId:        uuid.NewString(),
				RequestSetId:     chatRecordId,
				ChatRecordId:     chatRecordId,
				Stream:           true,
				OriginalCode:     content,
				Modification:     modification,
				SessionId:        workingSpaceFile.SessionId,
				WorkingSpaceFile: workingSpaceFile,
			}
		} else {
			params.OriginalCode = content
			params.WorkingSpaceFile = workingSpaceFile
		}
		result := DiffApply(ctx, params)
		// 如果提前结束，则直接返回
		if result.IsFinish {
			return "", ""
		}
		// 如果 DiffApply 出错，认为取消
		if result.IsSuccess == false {
			workingSpaceFile.Status = failedStatus
			if result.ErrorCode != "" {
				errorCode = result.ErrorCode
			} else {
				errorCode = definition.ApplyUnknownErrorCode
			}
			errorMsg = result.ErrorMsg
		}
	} else {
		// 获取工作区文件内容失败，取消采纳
		workingSpaceFile.Status = failedStatus
		errorCode = definition.GetContentErrorCode
		errorMsg = err.Error()
	}
	if workingSpaceFile.Status == failedStatus {
		// 失败时，解锁
		if actual, ok := WorkingSpaceFileLock.Load(workingSpaceFile.FileId); ok {
			mutex := actual.(*CheckableMutex)
			mutex.Unlock()
		}
		// 修改失败，保存文件
		saveFile(ctx, workingSpaceFile.FileId)
	}

	/* 忽略失败状态的-版本，避免上一个稳态获取异常
	// 写入工作区文件状态
	version := ""
	// 目前仅 JB 支持：失败状态认为不进版本
	if (util.GetIdeSeries(ctx) == string(global.IDE_SERIES_JETBRAINS) || util.GetIdeSeries(ctx) == string(global.IDE_SERIES_QODER)) && workingSpaceFile.Status == failedStatus {
		version = "-"
		workingSpaceFile.Version = version
	}
	if err := service.chatWorkingSpaceFileTemplate.UpdateWorkingSpaceFileStatusAndVersion(workingSpaceFile.Id, workingSpaceFile.Status, version); err != nil {
		return definition.SqlExecErrorCode, err.Error()
	}
	*/
	if workingSpaceFile.Status == failedStatus {
		// JB、Qoder 支持错误状态，不需要通知
		if util.GetIdeSeries(ctx) != string(global.IDE_SERIES_JETBRAINS) && util.GetIdeSeries(ctx) != string(global.IDE_SERIES_QODER) {
			errorNotification := definition.NotificationError{
				Code:    errorCode,
				Message: errorMsg,
			}
			go func() {
				e := websocket.SendRequestWithTimeout(ctx,
					"error/notificationError", errorNotification, nil, 3*time.Second)
				if e != nil {
					log.Error("Send request error/notificationError error:", e)
				}
			}()
		}
		WorkingSpaceFileIngMap.Delete(workingSpaceFile.Id)
		WorkingSpaceFileApplyingContentMap[workingSpaceFile.Id] = nil
		delete(WorkingSpaceFileApplyingContentMap, workingSpaceFile.Id)
		if initStatus != failedStatus {
			// 采纳取消的文件，保存解决方案以备 ReApply
			// 暂时不支持宕机后恢复，所以不存储到db
			/*
				var extraMap map[string]interface{}
				if err = json.Unmarshal([]byte(workingSpaceFile.Extra), &extraMap); err == nil {
					extraMap["modification"] = modification
					jsonData, _ := json.Marshal(extraMap)
					workingSpaceFile.Extra = string(jsonData)
					service.chatWorkingSpaceFileTemplate.UpdateWorkingSpaceFileExtra(workingSpaceFile.Id, workingSpaceFile.Extra)
				}
			*/
		}
	}

	// 同步工作区文件状态
	currentSnapshot, exists := CurrentSnapshotMap[workingSpaceFile.SessionId]
	if !exists {
		if snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(workingSpaceFile.SessionId); err == nil && len(snapshots) > 0 {
			currentSnapshot = snapshots[len(snapshots)-1]
		}
	}
	service.SyncWorkingSpaceFile(ctx, workingSpaceFile, MODIFIED, 0, definition.DiffInfo{}, definition.DiffInfo{}, "", currentSnapshot.Id, errorCode)

	return errorCode, errorMsg
}

func waitApplied(workingSpaceFile definition.WorkingSpaceFile, i int) (int, int) {
	startCost := 4 * 1000
	completeCost := 4 * 1000
	if status, ok := WorkingSpaceFileStatusMap.Load(workingSpaceFile.Id); ok {
		if status == START_APPLYING.String() {
			log.Debugf("wait for start applied: %dms", (i+1)*1000)
			startCost = (i + 1) * 1000
		} else if status == COMPLETE_APPLYING.String() {
			log.Debugf("wait for applied: %dms", (i+1)*1000)
			completeCost = (i + 1) * 1000
		}
	}
	return startCost, completeCost
}

/**
 * 采纳完成工作区文件
 * 流程如下:
 *. 1. 更新工作区文件状态
 *  2. 更新工作区文件处理中状态表
 *  3. 计算 DiffInfo 并存储
 *  4. 同步工作区文件状态
 */
func (service *ChatWorkingSpaceServiceManager) completeApplyWorkingSpaceFile(ctx context.Context, workingSpaceFile definition.WorkingSpaceFile) (string, string) {
	// 解锁
	if actual, ok := WorkingSpaceFileLock.Load(workingSpaceFile.FileId); ok {
		mutex := actual.(*CheckableMutex)
		mutex.Unlock()
	}

	// 更新工作区文件状态
	workingSpaceFile.Status = APPLIED.String()
	if workingSpaceFile.Mode == DELETE {
		workingSpaceFile.Status = DELETED.String()
	}

	if err := service.chatWorkingSpaceFileTemplate.UpdateWorkingSpaceFileStatusAndVersion(workingSpaceFile.Id, workingSpaceFile.Status, ""); err != nil {
		return definition.SqlExecErrorCode, err.Error()
	}

	// 更新工作区文件处理中状态表
	WorkingSpaceFileIngMap.Delete(workingSpaceFile.Id)
	WorkingSpaceFileGeneratingContentMap[workingSpaceFile.Id] = nil
	WorkingSpaceFileApplyingContentMap[workingSpaceFile.Id] = nil
	delete(WorkingSpaceFileApplyingContentMap, workingSpaceFile.Id)
	delete(WorkingSpaceFileGeneratingContentMap, workingSpaceFile.Id)

	// 计算 DiffInfo 并存储
	diffInfo := definition.DiffInfo{}
	lastDiffInfo := definition.DiffInfo{}
	currentSnapshotId := ""
	applyMode := GetApplyMode(ctx)
	targetContent := ""
	flag := false
	// 删除场景不需要写入
	if workingSpaceFile.Status != DELETED.String() {
		if currentContent, err := service.getFullContent(ctx, workingSpaceFile); err == nil {
			flag = true
			targetContent = currentContent
			if applyMode == APPLY_MODE_AGENT {
				// 完成变更文件的写入
				util.NewFile(workingSpaceFile.FileId, currentContent)
			}
			diffInfo, lastDiffInfo, _ = service.updateWorkingSpaceFileDiffInfo(ctx, workingSpaceFile, currentContent)
		}
	}

	// 同步工作区文件状态
	currentSnapshot, exists := CurrentSnapshotMap[workingSpaceFile.SessionId]
	if !exists {
		if snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(workingSpaceFile.SessionId); err == nil && len(snapshots) > 0 {
			currentSnapshot = snapshots[len(snapshots)-1]
		}
	}
	currentSnapshotId = currentSnapshot.Id

	service.SyncWorkingSpaceFile(ctx, workingSpaceFile, MODIFIED, 0, diffInfo, lastDiffInfo, "", currentSnapshotId, "")
	log.Debugf("completeApplyWorkingSpaceFile: %s, filePath: %s, exists: %v", workingSpaceFile.Id, workingSpaceFile.FileId, util.FileExists(workingSpaceFile.FileId))

	// 等待 COMPLETE_APPLIED 后，未等到则go写入
	if flag && applyMode == APPLY_MODE_IDE {
		ticker := time.NewTicker(time.Millisecond * 1000)
		defer ticker.Stop()

		startCost := 4 * 1000
		completeCost := 4 * 1000
		startCost, completeCost = waitApplied(workingSpaceFile, -1)
		// 最多等待3s
		for i := 0; i < 3; i++ {
			if completeCost != 4*1000 {
				break
			}
			select {
			case <-ctx.Done():
				break
			case <-ticker.C:
				startCost, completeCost = waitApplied(workingSpaceFile, i)
			}
		}
		if completeCost == 4*1000 {
			log.Debugf("wait for applied timeout")
			// 未等到 COMPLETE_APPLIED 事件，兜底使用 go 端写入
			util.NewFile(workingSpaceFile.FileId, targetContent)
		} else if startCost == 4*1000 {
			startCost = completeCost
		}
		data := map[string]string{
			"session_id":     workingSpaceFile.SessionId,
			"chat_record_id": workingSpaceFile.SnapshotId,
			"file_path":      workingSpaceFile.FileId,
			"op_type":        START_APPLIED.String(),
			"result":         strconv.Itoa(startCost),
			"chat_mode":      definition.SessionModeAgent,
		}
		go sls.Report(sls.EventTypeChatAiDeveloperWorkspaceFileOperation, workingSpaceFile.SnapshotId, data)
		data = map[string]string{
			"session_id":     workingSpaceFile.SessionId,
			"chat_record_id": workingSpaceFile.SnapshotId,
			"file_path":      workingSpaceFile.FileId,
			"op_type":        COMPLETE_APPLIED.String(),
			"result":         strconv.Itoa(completeCost),
			"chat_mode":      definition.SessionModeAgent,
		}
		go sls.Report(sls.EventTypeChatAiDeveloperWorkspaceFileOperation, workingSpaceFile.SnapshotId, data)
		WorkingSpaceFileStatusMap.Store(workingSpaceFile.Id, APPLIED.String())
	}

	return "", ""
}

/**
 * 更新 DiffInfo 信息
 */
func (service *ChatWorkingSpaceServiceManager) updateWorkingSpaceFileDiffInfo(ctx context.Context, workingSpaceFile definition.WorkingSpaceFile, content string) (definition.DiffInfo, definition.DiffInfo, error) {
	diffInfo := definition.DiffInfo{}
	lastDiffInfo := definition.DiffInfo{}
	// 仅 APPLIED 状态的文件需要更新
	if workingSpaceFile.Status != APPLIED.String() {
		return diffInfo, lastDiffInfo, nil
	}
	var finalError error
	// 计算 DiffInfo 并存储
	// 获取稳定版本内容，以计算 diffInfo
	if _, stableContent, _, err := service.getLastStableContent(ctx, workingSpaceFile.SessionId, workingSpaceFile.FileId, workingSpaceFile.Version, true); err == nil {
		// 计算 DiffInfo
		if content == CONTENT_USE_EMPTY_FILE {
			content = ""
		}
		addLines, delLines, addCounts, delCounts, modCounts, addChars, delChars := util.ComputeDiff(stableContent, content)
		sourceMd5 := encrypt.Md5EncodeBytes([]byte(stableContent))
		targetMd5 := encrypt.Md5EncodeBytes([]byte(content))
		diffInfo = definition.DiffInfo{
			Add:       len(addLines),
			Delete:    len(delLines),
			SourceMd5: sourceMd5,
			TargetMd5: targetMd5,
			AddCounts: addCounts,
			DelCounts: delCounts,
			ModCounts: modCounts,
			AddChars:  addChars,
			DelChars:  delChars,
		}
		// 存储 DiffInfo 信息
		var extraMap map[string]interface{}
		if err := json.Unmarshal([]byte(workingSpaceFile.Extra), &extraMap); err == nil {
			extraMap["diffInfo"] = diffInfo
			if jsonData, err := json.Marshal(extraMap); err == nil {
				workingSpaceFile.Extra = string(jsonData)
				service.chatWorkingSpaceFileTemplate.UpdateWorkingSpaceFileExtra(workingSpaceFile.Id, workingSpaceFile.Extra)
			} else {
				finalError = err
			}
		} else {
			finalError = err
		}
	} else {
		finalError = err
	}
	// 获取上一个版本内容，以计算 diffInfo
	if _, stableContent, _, err := service.getLastStableContent(ctx, workingSpaceFile.SessionId, workingSpaceFile.FileId, workingSpaceFile.Version, false); err == nil {
		// 计算 DiffInfo
		if content == CONTENT_USE_EMPTY_FILE {
			content = ""
		}
		addLines, delLines, addCounts, delCounts, modCounts, addChars, delChars := util.ComputeDiff(stableContent, content)
		sourceMd5 := encrypt.Md5EncodeBytes([]byte(stableContent))
		targetMd5 := encrypt.Md5EncodeBytes([]byte(content))
		lastDiffInfo = definition.DiffInfo{
			Add:       len(addLines),
			Delete:    len(delLines),
			SourceMd5: sourceMd5,
			TargetMd5: targetMd5,
			AddCounts: addCounts,
			DelCounts: delCounts,
			ModCounts: modCounts,
			AddChars:  addChars,
			DelChars:  delChars,
		}
		// 存储 DiffInfo 信息
		var extraMap map[string]interface{}
		if err := json.Unmarshal([]byte(workingSpaceFile.Extra), &extraMap); err == nil {
			extraMap["lastDiffInfo"] = diffInfo
			if jsonData, err := json.Marshal(extraMap); err == nil {
				workingSpaceFile.Extra = string(jsonData)
				service.chatWorkingSpaceFileTemplate.UpdateWorkingSpaceFileExtra(workingSpaceFile.Id, workingSpaceFile.Extra)
			} else {
				finalError = err
			}
		} else {
			finalError = err
		}
	} else {
		finalError = err
	}
	return diffInfo, lastDiffInfo, finalError
}

/**
 * 加载工作区文件
 * 获取工作区对应的本地文件内容，写入本地文件
 */
func (service *ChatWorkingSpaceServiceManager) loadWorkingSpaceFile(ctx context.Context, workingSpaceFile definition.WorkingSpaceFile, wg *sync.WaitGroup) (string, string) {
	defer wg.Done()

	applyMode := GetApplyMode(ctx)

	targetFile := workingSpaceFile

	if applyMode == APPLY_MODE_EDIT {
		localFile, err := service.chatWorkingSpaceFileTemplate.GetWorkingSpaceFile(workingSpaceFile.LocalId)
		if err != nil {
			return definition.DataCorruptErrorCode, "workingSpaceFile localFile not found: " + workingSpaceFile.LocalId
		}
		targetFile = localFile
	}

	// 获取工作区对应的本地文件内容
	if content, err := service.getFullContentWithEmptyFile(ctx, targetFile); err == nil {
		if applyMode == APPLY_MODE_EDIT {
			// 新建文件场景，且为空文件
			if content == "" && strings.HasPrefix(workingSpaceFile.Mode, ADD) {
				// 新建的空文件无需写入
				return "", ""
			}
		} else {
			// 如果文件不存在，认为是删除
			if content == "" || workingSpaceFile.Status == DELETED.String() {
				os.Remove(targetFile.FileId)
				return "", ""
			}
			if content == CONTENT_USE_EMPTY_FILE {
				content = ""
			}
		}
		//写入本地文件
		if err = util.NewFile(targetFile.FileId, content); err != nil {
			return definition.UnknownErrorCode, err.Error()
		}
		if file, err := service.chatWorkingSpaceFileTemplate.GetWorkingSpaceFile(workingSpaceFile.Id); err == nil {
			service.SyncWorkingSpaceFile(ctx, file, FRUSH, 0, definition.DiffInfo{}, definition.DiffInfo{}, "", "", "")
		}
	}

	return "", ""
}

func (service *ChatWorkingSpaceServiceManager) GetFullContent(ctx context.Context, params definition.WorkingSpaceFileGetContentParams) (string, error) {
	var workingSpaceFile definition.WorkingSpaceFile
	if params.ItemId == "" {
		workingSpaceFile, _ = service.chatWorkingSpaceFileTemplate.GetWorkingSpaceFileByVersion(params.SessionId, params.FileId, params.Version)
	} else {
		workingSpaceFile, _ = service.chatWorkingSpaceFileTemplate.GetWorkingSpaceFile(params.ItemId)
	}
	if workingSpaceFile.Id == "" {
		return "", errors.New("workingSpaceFile not found")
	}
	return service.getFullContent(ctx, workingSpaceFile)
}

/**
 * 获取工作区文件内容
 * 返回为 CONTENT_USE_EMPTY_FILE 时，表示文件存在但是为空；返回为""时，表示文件不存在
 */
func (service *ChatWorkingSpaceServiceManager) getFullContentWithEmptyFile(ctx context.Context, workingSpaceFile definition.WorkingSpaceFile) (string, error) {
	if workingSpaceFile.ContentType != FULL {
		// 目前只支持获取全文存储类型的工作区文件
		return "", errors.New("workingSpaceFile contentType not full")
	}
	status, ok := WorkingSpaceFileIngMap.Load(workingSpaceFile.Id)
	log.Debugf("getFullContentWithEmptyFile: %s, fileStatus: %v, status: %v, ok: %v", workingSpaceFile.Id, workingSpaceFile.Status, status, ok)
	// 判断是否为处理中，是则从 工作区文件处理中内容表 中获取
	if ok && status == GENERATING.String() && workingSpaceFile.Status == GENERATING.String() {
		if content, ok := WorkingSpaceFileGeneratingContentMap[workingSpaceFile.Id]; ok && content != nil {
			return content.FullContent, nil
		}
		return "", errors.New("workingSpaceFile generating content not found")
	}
	// Applying 阶段，获取 apply 后全文
	if ok && status == APPLYING.String() && workingSpaceFile.Status == APPLYING.String() {
		if content, ok := WorkingSpaceFileApplyingContentMap[workingSpaceFile.Id]; ok && content != nil {
			return content.AppliedContent, nil
		}
		return "", errors.New("workingSpaceFile applying content not found")
	}
	if workingSpaceFile.Status == DELETED.String() {
		return "", nil
	}
	// 否则，从文件存储中获取
	// 如果为工作区文件主项，且为终态，或者使用工作区本地存储，则从对应的工作区本地文件获取
	if workingSpaceFile.LocalId != "" {
		useLocal := finishStatusMap[workingSpaceFile.Status] != 0 && GetApplyMode(ctx) == APPLY_MODE_EDIT
		if !useLocal {
			var extraMap map[string]interface{}
			if err := json.Unmarshal([]byte(workingSpaceFile.Extra), &extraMap); err == nil {
				if flag, ok := extraMap[CONTENT_USE_WORKING_SPACE_LOCAL].(bool); ok {
					useLocal = flag
				}
			}
		}
		log.Debugf("getFullContentWithEmptyFile: %s, useLocal: %v", workingSpaceFile.Id, useLocal)
		if useLocal {
			if localFile, err := service.chatWorkingSpaceFileTemplate.GetWorkingSpaceFile(workingSpaceFile.LocalId); err == nil {
				return service.getFullContentWithEmptyFile(ctx, localFile)
			}
		}
	}
	fileProvider := service.GetWorkingSpaceFileProvider(workingSpaceFile)
	return fileProvider.Get(workingSpaceFile.Id, workingSpaceFile.Key)
}

func (service *ChatWorkingSpaceServiceManager) getFullContent(ctx context.Context, workingSpaceFile definition.WorkingSpaceFile) (string, error) {
	content, err := service.getFullContentWithEmptyFile(ctx, workingSpaceFile)
	log.Debugf("getFullContent: %s, path: %s, content: %v, err: %v", workingSpaceFile.Id, workingSpaceFile.FileId, len(content), err)
	if content == CONTENT_USE_EMPTY_FILE {
		content = ""
	}
	return content, err
}

/**
 * 获取工作区文件稳定版本内容
 * 流程:
 *  1. 获取工作区文件所在会话下相关的工作区文件列表
 *  2. 遍历工作区文件列表，比指定工作区文件版本低的最近的终态版本的工作区文件
 *  3. 获取终态版本的工作区文件的文件内容
 *
 * @return (workingSpaceFile, content, version, err): version: 稳定版本号
 */
func (service *ChatWorkingSpaceServiceManager) getLastStableContentWithEmptyFile(ctx context.Context, sessionId string, fileId string, version string, stable bool) (definition.WorkingSpaceFile, string, string, error) {
	workingSpaceFile := definition.WorkingSpaceFile{}
	// 获取相关的工作区文件列表
	files, err := service.chatWorkingSpaceFileTemplate.ListWorkingSpaceFileByFileId(sessionId, fileId)
	if err != nil {
		return workingSpaceFile, "", "", err
	}

	// 遍历工作区文件列表，找到最近的终态版本
	targetVersion := ""
	var targetWorkingSpaceFile definition.WorkingSpaceFile
	for _, file := range files {
		if file.Version == version {
			workingSpaceFile = file
			break
		}
		if !stable || finishStatusMap[file.Status] != 0 {
			targetVersion = file.Version
			targetWorkingSpaceFile = file
		}
	}

	// 获取终态版本的工作区文件的文件内容
	stableContent, err := service.getFullContentWithEmptyFile(ctx, targetWorkingSpaceFile)
	if err != nil {
		return workingSpaceFile, "", "", err
	}

	return workingSpaceFile, stableContent, targetVersion, err
}

func (service *ChatWorkingSpaceServiceManager) getLastStableContent(ctx context.Context, sessionId string, fileId string, version string, stable bool) (definition.WorkingSpaceFile, string, string, error) {
	workingSpaceFile, content, version, err := service.getLastStableContentWithEmptyFile(ctx, sessionId, fileId, version, stable)
	if content == CONTENT_USE_EMPTY_FILE {
		content = ""
	}
	return workingSpaceFile, content, version, err
}

/**
 * 获取工作区文件稳定版本内容
 *  判断稳定版本版本是否发生变动，发生则触发 DiffInfo 变化
 *
 * @return (content, version, err): version: 稳定版本号
 */
func (service *ChatWorkingSpaceServiceManager) GetLastStableContent(ctx context.Context, params definition.WorkingSpaceFileGetContentParams) (string, string, error) {
	if params.SessionId == "" || params.FileId == "" || params.Version == "" {
		if workingSpaceFile, err := service.chatWorkingSpaceFileTemplate.GetWorkingSpaceFile(params.ItemId); err == nil {
			params.SessionId = workingSpaceFile.SessionId
			params.FileId = workingSpaceFile.FileId
			params.Version = workingSpaceFile.Version
		} else {
			return "", "", err
		}
	}

	_, stableContent, targetVersion, err := service.getLastStableContent(ctx, params.SessionId, params.FileId, params.Version, true)

	// 判断稳定版本版本是否发生变动，发生则触发 DiffInfo 变化
	/*
		if workingSpaceFile.Status == APPLIED.String() {
			var extraMap map[string]interface{}
			err = json.Unmarshal([]byte(workingSpaceFile.Extra), &extraMap)
			if diffInfo, ok := extraMap["diffInfo"].(map[string]interface{}); err == nil && ok {
				if originSourceMd5, ok := diffInfo["sourceMd5"].(string); ok {
					sourceMd5 := encrypt.Md5EncodeBytes([]byte(stableContent))
					if originSourceMd5 != sourceMd5 {
						if currentContent, err := service.getFullContent(ctx, workingSpaceFile); err == nil {
							service.updateWorkingSpaceFileDiffInfo(workingSpaceFile, currentContent)
						}
					}
				}
			}
		}
	*/

	return stableContent, targetVersion, err
}

/**
 * 获取工作区文件基于的原始文件 md5
 */
func (service *ChatWorkingSpaceServiceManager) GetBaseMd5(ctx context.Context, params definition.WorkingSpaceFileGetContentParams) (string, error) {
	var workingSpaceFile definition.WorkingSpaceFile
	if params.ItemId == "" {
		workingSpaceFile, _ = service.chatWorkingSpaceFileTemplate.GetWorkingSpaceFileByVersion(params.SessionId, params.FileId, params.Version)
	} else {
		workingSpaceFile, _ = service.chatWorkingSpaceFileTemplate.GetWorkingSpaceFile(params.ItemId)
	}
	if workingSpaceFile.Id == "" {
		return "", errors.New("workingSpaceFile not found")
	}
	var extraMap map[string]interface{}
	if err := json.Unmarshal([]byte(workingSpaceFile.Extra), &extraMap); err == nil {
		if md5, ok := extraMap[BASE_MD5].(string); ok {
			return md5, nil
		}
	}
	return "", errors.New("baseMd5 not found")
}

/**
 * 查询工作区中工作区文件列表
 */
func (service *ChatWorkingSpaceServiceManager) GetCurrentWorkingSpaceFiles(ctx context.Context, sessionId string) ([]definition.WorkingSpaceFile, error) {
	result := []definition.WorkingSpaceFile{}

	currentSnapshot, exists := CurrentSnapshotMap[sessionId]
	if !exists {
		if snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(sessionId); err == nil && len(snapshots) > 0 {
			currentSnapshot = snapshots[len(snapshots)-1]
		}
	}
	if currentSnapshot.Id == "" {
		return result, errors.New("current snapshot not exists")
	}

	return service.ListWorkingSpaceFilesBySnapshot(ctx, currentSnapshot.Id, false, true)
}

/**
 * 查询工作区中文件内容列表
 */
func (service *ChatWorkingSpaceServiceManager) GetCurrentContexts(ctx context.Context, sessionId string) ([]definition.ContextItem, error) {
	result := []definition.ContextItem{}

	currentSnapshot, exists := CurrentSnapshotMap[sessionId]
	if !exists {
		if snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(sessionId); err == nil && len(snapshots) > 0 {
			currentSnapshot = snapshots[len(snapshots)-1]
		}
	}
	if currentSnapshot.Id == "" {
		return result, errors.New("current snapshot not exists")
	}

	if files, err := service.ListWorkingSpaceFilesBySnapshot(ctx, currentSnapshot.Id, false, true); err == nil {
		for _, file := range files {
			content, err := service.getFullContent(ctx, file)
			if err != nil {
				continue
			}
			result = append(result, definition.ContextItem{
				Identifier: file.FileId,
				Key:        file.FileId,
				Value:      fmt.Sprintf("%s\n%s", filepath.Base(file.FileId), content),
			})
		}
	} else {
		return result, err
	}

	return result, nil
}

/**
 * 查询工作区中当前文件内容
 */
func (service *ChatWorkingSpaceServiceManager) GetCurrentContent(ctx context.Context, sessionId string, fileId string) (string, error) {
	currentSnapshot, exists := CurrentSnapshotMap[sessionId]
	if !exists {
		if snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(sessionId); err == nil && len(snapshots) > 0 {
			currentSnapshot = snapshots[len(snapshots)-1]
		}
	}
	if currentSnapshot.Id == "" {
		return "", errors.New("current snapshot not exists")
	}

	if files, err := service.ListWorkingSpaceFilesBySnapshot(ctx, currentSnapshot.Id, true, true); err == nil {
		for _, file := range files {
			if file.FileId == fileId {
				return service.getFullContent(ctx, file)
			}
		}
	} else {
		return "", err
	}

	return "", errors.New("not found")
}

/**
 * 关闭会话
 * 流程:
 *   1. 当前快照，拒绝所有变更
 *   2. 清理存储：清理最后快照之外的工作区文件存储
 *   3. 更新快照状态为 OUTDATED
 */
func (service *ChatWorkingSpaceServiceManager) CloseSession(ctx context.Context, sessionId string) (string, string) {

	currentSnapshot, exists := CurrentSnapshotMap[sessionId]
	if !exists {
		if snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(sessionId); err == nil && len(snapshots) > 0 {
			currentSnapshot = snapshots[len(snapshots)-1]
		}
	}
	if currentSnapshot.Id != "" {
		// 当前快照，认为是接受快照，对于进行中，认为是取消
		service.OperateSnapshot(ctx, definition.SnapshotOperateParams{
			Id:     currentSnapshot.Id,
			OpType: ACCEPT_ALL.String(),
			//OpType: REJECT_ALL.String(),
			Params: map[string]interface{}{
				NEED_CANCEL: true,
				SKIP_SAVE:   true,
			},
		})
	}

	/*
		// 清理存储：清理最后快照之外的工作区文件存储
		if workingSpaceFiles, err := service.chatWorkingSpaceFileTemplate.ListWorkingSpaceFileBySession(sessionId); err == nil {
			var toRemoveIds []string
			for _, workingSpaceFile := range workingSpaceFiles {
				versionCount := WorkingSpaceFileVersionMap[workingSpaceFile.SessionId][workingSpaceFile.FileId]
				if version, err := strconv.Atoi(workingSpaceFile.Version); err == nil {
					if versionCount != version {
						toRemoveIds = append(toRemoveIds, workingSpaceFile.Id)
					}
				}
			}
			service.batchDeleteWorkingSpaceFileContentSync(toRemoveIds)
		}
	*/

	// 清理工作区状态
	if workingSpaceFiles, err := service.chatWorkingSpaceFileTemplate.ListWorkingSpaceFileBySession(sessionId); err == nil {
		for _, workingSpaceFile := range workingSpaceFiles {
			delete(WorkingSpaceFileMessageMap, workingSpaceFile.Id)
			delete(WorkingSpaceFileMethodMap, workingSpaceFile.Id)
		}
	}

	// 清空会话状态
	delete(CurrentSnapshotMap, sessionId)
	delete(WorkingSpaceFileVersionMap, sessionId)
	delete(WorkingSpaceFileDiffInfoMap, sessionId)
	projectUri := SessionServiceManager.GetChatSessionProjectURI(sessionId)
	log.Debugf("Close session. sessionId: %s, projectUri: %s", sessionId, projectUri)
	if projectUri != "" {
		if CurrentSessionStatusMap[projectUri] != nil {
			delete(CurrentSessionStatusMap[projectUri], sessionId)
		}
	}
	delete(SessionProjectUriMap, sessionId)

	/*
		if snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(sessionId); err == nil {
			for _, snapshot := range snapshots {
				snapshot.Status = OUTDATED.String()
				service.chatSnapshotTemplate.UpdateSnapshot(snapshot)
			}
		}
	*/

	/* 暂时不发送 session/closed 消息
	result := definition.SessionClosedResult{
		ClosedSessionId: sessionId,
		ProjectPath:     SessionServiceManager.GetChatSessionProjectURI(sessionId),
	}
	e := websocket.SendRequestWithTimeout(ctx,
		"session/closed", result, nil, 3*time.Second)
	if e != nil {
		log.Error("Send request session/closed error:", e)
	}
	*/

	// 先不考虑并发关闭会话
	sessions := SessionServiceManager.ListAllChatSessionsByProjectUri(projectUri, 15)
	// 清理第十一轮会话
	if len(sessions) > 10 {
		service.ExpireSession(sessions[10].SessionId)
	}

	return "", ""

}

func (service *ChatWorkingSpaceServiceManager) ExpireSession(sessionId string) (string, string) {
	// 清理存储：清理所有工作区文件存储
	if workingSpaceFiles, err := service.chatWorkingSpaceFileTemplate.ListWorkingSpaceFileBySession(sessionId); err == nil {
		var toRemoveIds []string
		for _, workingSpaceFile := range workingSpaceFiles {
			toRemoveIds = append(toRemoveIds, workingSpaceFile.Id)
		}
		service.batchDeleteWorkingSpaceFileContentSync(toRemoveIds)
	}
	if snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(sessionId); err == nil {
		for _, snapshot := range snapshots {
			snapshot.Status = OUTDATED.String()
			service.chatSnapshotTemplate.UpdateSnapshot(snapshot)
		}
	}
	return "", ""
}

/**
 * 清楚会话下的所有快照&工作区文件
 */
func (service *ChatWorkingSpaceServiceManager) ClearSession(sessionId string) (string, string) {
	// 清空会话状态
	delete(CurrentSnapshotMap, sessionId)
	delete(WorkingSpaceFileVersionMap, sessionId)
	delete(WorkingSpaceFileDiffInfoMap, sessionId)
	projectUri := SessionServiceManager.GetChatSessionProjectURI(sessionId)
	if projectUri != "" {
		if CurrentSessionStatusMap[projectUri] != nil {
			delete(CurrentSessionStatusMap[projectUri], sessionId)
		}
	}
	delete(SessionProjectUriMap, sessionId)

	if snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(sessionId); err == nil {
		for i := len(snapshots) - 1; i >= 0; i-- {
			service.deleteSnapshot(snapshots[i].Id)
		}
	}

	return "", ""

}

/**
 * 获取当前活跃的会话列表
 */
func (service *ChatWorkingSpaceServiceManager) GetCurrentSessions() []string {
	result := []string{}
	for key := range CurrentSessionStatusMap {
		if CurrentSessionStatusMap[key] != nil {
			for sessionId := range CurrentSessionStatusMap[key] {
				if CurrentSessionStatusMap[key][sessionId] == 2 {
					result = append(result, sessionId)
				}
			}
		}
	}
	return result
}

/**
 * 更新工作区存储
 * @param: skip 是否跳过工作区文件主项的更新
 */
func (service *ChatWorkingSpaceServiceManager) saveSnapshot(ctx context.Context, snapshotId string, workingSpaceFileContents []definition.WorkingSpaceFileContent, skip bool) error {
	files, err := service.ListWorkingSpaceFilesBySnapshot(ctx, snapshotId, true, true)
	if err != nil {
		return err
	}
	contentMap := make(map[string]string)
	if workingSpaceFileContents != nil {
		for _, fileContent := range workingSpaceFileContents {
			contentMap[fileContent.Id] = fileContent.Content
		}
	}

	var sumErr error
	for _, file := range files {
		if err = service.saveWorkingSpaceFileContent(ctx, file, contentMap[file.Id], skip); err != nil {
			sumErr = err
		}
	}
	return sumErr
}

func (service *ChatWorkingSpaceServiceManager) OperateSnapshot(ctx context.Context, params definition.SnapshotOperateParams) (string, string) {
	log.Infof("OperateSnapshot snapshot id:%s, type:%s", params.Id, params.OpType)
	// 埋点日志
	if _, exists := needRecordOpTypeMap[params.OpType]; exists {
		if noRecord, ok := params.Params[NO_RECORD].(bool); !ok || !noRecord {
			if snapshot, err := service.chatSnapshotTemplate.GetSnapshot(params.Id); err == nil {
				data := map[string]string{
					"resource_type":  "snapshot",
					"resource_id":    params.Id,
					"action_type":    params.OpType,
					"session_id":     snapshot.SessionId,
					"chat_record_id": snapshot.ChatRecordId,
					"snapshot_id":    snapshot.Id,
				}
				go sls.Report(sls.EventTypeChatAiDeveloperOperate, snapshot.ChatRecordId, data)
			}
		}
	}
	applyMode := GetApplyMode(ctx)
	if mode := operateSaveTypeMap[params.OpType]; mode != 0 {
		if noSave, ok := params.Params[NO_SAVE].(bool); !ok || !noSave {
			// Qoder写入策略下，延迟100ms，避免本地文件获取的内容不对
			if (params.OpType == ACCEPT_ALL.String() || params.OpType == REJECT_ALL.String()) && applyMode == APPLY_MODE_IDE {
				time.Sleep(100 * time.Millisecond)
			}
			// 操作之前保存快照
			service.saveSnapshot(ctx, params.Id, params.WorkingSpaceFiles, mode == 1)
		}
	}
	errorCode, errorMsg := "", ""
	skipSave := false
	switch params.OpType {
	case ACCEPT_ALL.String():
		needCancel, ok := params.Params[NEED_CANCEL].(bool)
		if !ok {
			needCancel = false
		}
		skipSave, ok = params.Params[SKIP_SAVE].(bool)
		if !ok {
			skipSave = applyMode == APPLY_MODE_IDE
		}
		errorCode, errorMsg = service.acceptSnapshot(ctx, params.Id, nil, needCancel, skipSave)
	case ACCEPT.String():
		errorCode, errorMsg = service.acceptSnapshot(ctx, params.Id, params.WorkingSpaceFiles, false, false)
	case REJECT_ALL.String():
		ok := false
		skipSave, ok = params.Params[SKIP_SAVE].(bool)
		if !ok {
			skipSave = applyMode == APPLY_MODE_IDE
		}
		errorCode, errorMsg = service.rejectSnapshot(ctx, params.Id, skipSave)
	case CANCEL.String():
		errorCode, errorMsg = service.cancelSnapshot(ctx, params.Id, true)
	case SWITCH.String():
		if targetSnapshotId, ok := params.Params["targetSnapshotId"].(string); ok {
			targetChatRecordId := ""
			if chatRecordId, ok := params.Params["targetChatRecordId"].(string); ok {
				targetChatRecordId = chatRecordId
			}
			// 忽略端侧传的 targetChatRecordId，自己计算保证正确性
			targetChatRecordId = ""
			errorCode, errorMsg = service.switchSnapshot(ctx, params.Id, targetSnapshotId, targetChatRecordId)
		} else {
			errorCode, errorMsg = definition.ParamErrorCode, "targetSnapshotId not found"
		}
	case UPDATE_CHAT_RECORD.String():
		targetChatRecordId, ok := params.Params["targetChatRecordId"].(string)
		if !ok {
			targetChatRecordId = ""
		}
		// 忽略端侧传的 targetChatRecordId，自己计算保证正确性
		targetChatRecordId = ""
		errorCode, errorMsg = service.updateSnapshotChatRecord(ctx, params.Id, targetChatRecordId)
	case APPLY.String():
		errorCode, errorMsg = service.applySnapshot(ctx, params.Id)
	case ACTIVATE.String():
		errorCode, errorMsg = service.activateSnapshot(ctx, params.Id)
	case SYNC.String():
	default:
		errorCode, errorMsg = definition.UnSupportedErrorCode, "unsupported operation type"
	}
	if errorCode != "" {
		log.Debugf("OperateSnapshot snapshot failed, id:%s, type:%s, errorCode: %s, errorMsg: %s", params.Id, params.OpType, errorCode, errorMsg)
		// 接受文件异常返回报错信息
		if params.OpType == ACCEPT_ALL.String() && !skipSave && (util.GetIdeSeries(ctx) == string(global.IDE_SERIES_JETBRAINS) || util.GetIdeSeries(ctx) == string(global.IDE_SERIES_QODER)) {
			// 暂时返回未知异常，待确认明确错误信息后补充
			errorNotification := definition.NotificationError{
				Code:    definition.ApplyUnknownErrorCode,
				Message: errorMsg,
			}
			go func() {
				e := websocket.SendRequestWithTimeout(ctx,
					"error/notificationError", errorNotification, nil, 3*time.Second)
				if e != nil {
					log.Error("Send request error/notificationError error:", e)
				}
			}()
		}
	}
	return errorCode, errorMsg
}

func (service *ChatWorkingSpaceServiceManager) OperateWorkingSpaceFile(ctx context.Context, params definition.WorkingSpaceFileOperateParams) (string, string) {
	log.Infof("OperateWorkingSpaceFile file id:%s, type:%s", params.Id, params.OpType)
	workingSpaceFile, err := service.chatWorkingSpaceFileTemplate.GetWorkingSpaceFile(params.Id)
	if err != nil {
		return definition.SqlQueryErrorCode, err.Error()
	}
	// 埋点日志
	if _, exists := needRecordOpTypeMap[params.OpType]; exists {
		if noRecord, ok := params.Params[NO_RECORD].(bool); !ok || !noRecord {
			currentSnapshot, exists := CurrentSnapshotMap[workingSpaceFile.SessionId]
			if !exists {
				if snapshots, err := service.chatSnapshotTemplate.ListSnapshotsBySession(workingSpaceFile.SessionId); err == nil && len(snapshots) > 0 {
					currentSnapshot = snapshots[len(snapshots)-1]
				}
			}
			if currentSnapshot.Id != "" {
				data := map[string]string{
					"resource_type":  "workingSpaceFile",
					"resource_id":    params.Id,
					"action_type":    params.OpType,
					"session_id":     currentSnapshot.SessionId,
					"chat_record_id": currentSnapshot.ChatRecordId,
					"snapshot_id":    currentSnapshot.Id,
				}
				go sls.Report(sls.EventTypeChatAiDeveloperOperate, currentSnapshot.ChatRecordId, data)
			}
		}
	}
	applyMode := GetApplyMode(ctx)
	if mode := operateSaveTypeMap[params.OpType]; mode != 0 {
		if noSave, ok := params.Params[NO_SAVE].(bool); !ok || !noSave {
			// Qoder写入策略下，延迟100ms，避免本地文件获取的内容不对
			if (params.OpType == ACCEPT.String() || params.OpType == REJECT.String()) && applyMode == APPLY_MODE_IDE {
				time.Sleep(100 * time.Millisecond)
			}
			// 操作之前更新文件
			service.saveWorkingSpaceFileContent(ctx, workingSpaceFile, params.Content, mode == 1)
		}
	}
	errorCode, errorMsg := "", ""
	switch params.OpType {
	case ACCEPT.String():
		skipSave, ok := params.Params[SKIP_SAVE].(bool)
		if !ok {
			skipSave = applyMode == APPLY_MODE_IDE
		}
		errorCode, errorMsg = service.acceptWorkingSpaceFile(ctx, workingSpaceFile, skipSave)
	case REJECT.String():
		skipSave, ok := params.Params[SKIP_SAVE].(bool)
		if !ok {
			skipSave = applyMode == APPLY_MODE_IDE
		}
		errorCode, errorMsg = service.rejectWorkingSpaceFile(ctx, workingSpaceFile, skipSave)
	case CANCEL.String():
		failed := false
		if isFailed, ok := params.Params[IS_FAILED].(bool); ok {
			failed = isFailed
		}
		code := ""
		if errorCode, ok := params.Params[ERROR_CODE].(string); ok {
			code = errorCode
		}
		errorCode, errorMsg = service.cancelWorkingSpaceFile(ctx, workingSpaceFile, true, failed, code)
	case APPLY.String():
		diffApplyParams, _ := params.Params["diffApplyParams"].(definition.DiffApplyParams)
		errorCode, errorMsg = service.applyWorkingSpaceFile(ctx, workingSpaceFile, diffApplyParams)
	case COMPLETE_APPLY.String():
		errorCode, errorMsg = service.completeApplyWorkingSpaceFile(ctx, workingSpaceFile)
	case START_APPLIED.String():
		if status, ok := WorkingSpaceFileStatusMap.Load(workingSpaceFile.Id); ok {
			if status == APPLYING.String() || status == APPLIED.String() {
				WorkingSpaceFileStatusMap.Store(workingSpaceFile.Id, START_APPLYING.String())
			}
		}
	case COMPLETE_APPLIED.String():
		if status, ok := WorkingSpaceFileStatusMap.Load(workingSpaceFile.Id); ok {
			if status == START_APPLYING.String() || status == APPLYING.String() || status == APPLIED.String() {
				WorkingSpaceFileStatusMap.Store(workingSpaceFile.Id, COMPLETE_APPLYING.String())
			}
		}
	case SYNC.String():
	default:
		errorCode, errorMsg = definition.UnSupportedErrorCode, "unsupported operation type"
	}
	if errorCode != "" {
		log.Debugf("OperateWorkingSpaceFile failed, id:%s, type:%s, errorCode: %s, errorMsg: %s", params.Id, params.OpType, errorCode, errorMsg)
		// 接受文件异常返回报错信息
		if params.OpType == ACCEPT.String() && (util.GetIdeSeries(ctx) == string(global.IDE_SERIES_JETBRAINS) || util.GetIdeSeries(ctx) == string(global.IDE_SERIES_QODER)) {
			// 暂时返回未知异常，待确认明确错误信息后补充
			errorNotification := definition.NotificationError{
				Code:    definition.ApplyUnknownErrorCode,
				Message: errorMsg,
			}
			go func() {
				e := websocket.SendRequestWithTimeout(ctx,
					"error/notificationError", errorNotification, nil, 3*time.Second)
				if e != nil {
					log.Error("Send request error/notificationError error:", e)
				}
			}()
		}
	}
	return errorCode, errorMsg
}

func GetApplyMode(ctx context.Context) string {
	applyMode := APPLY_MODE_AGENT
	// IDE下非Quest模式由IDE写入
	if util.IsFromIde(ctx) && !util.IsQuestMode(ctx) {
		applyMode = APPLY_MODE_IDE
	} else if !util.IsIncludeAgentRecord(ctx) {
		applyMode = APPLY_MODE_EDIT
	}
	if util.IsQuestMode(ctx) {
		if util.IsQuestDesignSession(ctx) {
			// Quest模式下 Design阶段由IDE写入，产生InlineDiff效果
			applyMode = APPLY_MODE_IDE
		} else {
			// Quest模式其他阶段，不需要InlineDiff，由Go写入
			applyMode = APPLY_MODE_AGENT
		}
	}
	return applyMode
}

// GetFileContent 从本地获得文件内容，
func GetFileContent(filePath string) (string, error) {
	f, err := os.Open(filePath)
	if err != nil {
		log.Errorf("Failed to open file %s: %s", filePath, err)
		return "", err
	}
	contentBytes, err := io.ReadAll(f)
	if err != nil {
		log.Errorf("Failed to read file %s: %s", filePath, err)
		return "", err
	}
	return string(contentBytes), nil
}
