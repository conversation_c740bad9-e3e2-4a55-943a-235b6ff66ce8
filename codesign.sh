#!/usr/bin/env bash

project_path=$(cd "$(dirname "${0}")"; pwd)
version_filename=${project_path}/global/common.go

bindir=$1
version=$2


if [ -z $bindir ];then
  bindir="$HOME/.lingma/bin"
  echo "bindir not set, using ${bindir}"
fi

if [ -z $version ];then
  version=$(grep "^const CosyVersion =" "${version_filename}" | awk '{gsub(/"/,""); print $4}')
  echo "version not set, using ${version}"
fi

if [ -z $S4W_USER ];then
  echo "windows签名的账号不存在，请配置S4W_USER环境变量"
  exit 1
fi

if [ -z $S4W_KEY ];then
  echo "windows签名的秘钥不存在，请配置S4W_KEY环境变量"
  exit 1
fi

# add signature to bins
echo "start to sign apple application, using output $bindir/$version ..."
codesign -f -o runtime --timestamp -s "Developer ID Application: Alibaba Cloud Computing Ltd. (QBMN2BBW3K)" $bindir/$version/x86_64_darwin/Lingma
codesign -f -o library --timestamp -s "Developer ID Application: Alibaba Cloud Computing Ltd. (QBMN2BBW3K)" $bindir/$version/x86_64_darwin/LingmaLocal
codesign -f -o runtime --timestamp -s "Developer ID Application: Alibaba Cloud Computing Ltd. (QBMN2BBW3K)" $bindir/$version/aarch64_darwin/Lingma
codesign -f -o library --timestamp -s "Developer ID Application: Alibaba Cloud Computing Ltd. (QBMN2BBW3K)" $bindir/$version/aarch64_darwin/LingmaLocal

echo "start to validate apple application.."
codesign -vv -d $bindir/$version/x86_64_darwin/Lingma
codesign -vv -d $bindir/$version/x86_64_darwin/LingmaLocal
codesign -vv -d $bindir/$version/aarch64_darwin/Lingma
codesign -vv -d $bindir/$version/aarch64_darwin/LingmaLocal

cd $bindir/$version
s4w hash x86_64_windows

echo "===================================="
echo "通过 s4w hash x86_64_windows 获取二进制文件hash"
echo "正式签名审批流URL：https://bpms.alibaba-inc.com/workdesk/instStart?processCode=PROC-BF6665B1-9432B2HWSMNYWNE8ALYX1-FDESOCCI-5"
read -p "确认已提交正式签名申请，且审批已通过[y/N] " response
case $response in [yY][eE][sS]|[yY]|[jJ]|'')
    read -p "输入BPMS流程ID:" bpmsId
    s4w sign x86_64_windows -t aliyun -s secsign-aliyun.alibaba-inc.com -i $bpmsId
    if [ ! -d $bindir"/"$version"/x86_64_windows-s4w/signed/x86_64_windows" ];then
      echo "无效的签名文件"
      exit 1
    else
      rm -r x86_64_windows
      if [ ! -d $bindir"/"$version"/x86_64_windows-s4w/signed" ];then
        unzip x86_64_windows-s4w/signed.zip
      fi
      cp -r x86_64_windows-s4w/signed/x86_64_windows x86_64_windows
      echo "===================================="
      echo "! 检查是否为 Release Mode:"
      cat x86_64_windows-s4w/signed/results.txt
      rm -r x86_64_windows-s4w
    fi
    ;;
    *)
    echo "未审批通过，不能用于正式发布！"
    s4w sign x86_64_windows -t aliyun -s secsign-aliyun.alibaba-inc.com
    rm -r x86_64_windows
    if [ ! -d $bindir"/"$version"/x86_64_windows-s4w/signed" ];then
      unzip x86_64_windows-s4w/signed.zip
    fi
    cp -r x86_64_windows-s4w/signed/x86_64_windows x86_64_windows
    echo "===================================="
    echo "检查是否为 Daily Mode:"
    cat x86_64_windows-s4w/signed/results.txt
    rm -rf x86_64_windows-s4w
    ;;
esac

echo "===================================="
echo "打包中..."

cd $bindir
zip -r lingma-$version.zip $version config.json -x "*.DS_Store" -x "__MACOSX"