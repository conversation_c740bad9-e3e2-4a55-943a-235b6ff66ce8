#!/bin/bash

# Sign Binary Script
# This script signs binaries for different platforms
# Migrated from codesign.sh with enhanced functionality

set -e

# Source utilities
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/utils.sh"

sign_binary() {
    local bindir="$1"
    local version="$2"
    local platform="$3"
    local type="$4"
    
    # Check if signing is enabled (default: false)
    local enable_signing="${ENABLE_SIGNING:-false}"
    if [[ "$enable_signing" != "true" ]]; then
        log_info "💨 Code signing is disabled (ENABLE_SIGNING=$enable_signing), skipping signing process"
        return 0
    fi
    
    log_info "🖊️ Signing binaries for version: $version, platform: $platform, type: $type"
    log_info "Current working directory: $(pwd)"
    log_info "Script directory: $(dirname "${BASH_SOURCE[0]}")"
    
    if [[ -z "$version" ]]; then
        log_error "Version must be set"
        return 1
    fi
    
    # Check for Windows platforms (with or without leading colon)
    if echo "$platform" | grep -q "win_"; then
        sign_windows_binaries "$bindir" "$version"
    fi
    
    # Check for macOS platforms (with or without leading colon)
    if echo "$platform" | grep -q "mac_"; then
        sign_macos_binaries "$bindir" "$version" "$type" "$platform"
    fi
    
    # Check for Linux platforms (with or without leading colon)
    if echo "$platform" | grep -q "linux_"; then
        log_info "Linux binaries typically don't require code signing"
    fi
}

sign_macos_binaries() {
    local bindir="$1"
    local version="$2"
    local type="$3"
    local platform_string="$4"  # Add platform string parameter
    
    log_info "✒️ Signing MacOS release binaries"
    log_info "  Current working directory: $(pwd)"
    log_info "  Script directory: $SCRIPT_DIR"
    log_info "  bindir: $bindir"
    log_info "  version: $version"
    log_info "  type: $type"
    log_info "  platform_string: $platform_string"
    
    # Signature configuration
    local signature_name="Developer ID Application: Hangzhou Yundian Technology Company Limited (9DFNGU9AK5)"
    
    # Determine executable names and signing options based on type
    local main_exe
    local local_exe
    local has_local_exe
    local sign_option
    
    # 默认使用 Qoder 配置
    if [[ "$type" == "aiIde" ]]; then
        # 传统 AI IDE（Lingma）配置
        main_exe="Lingma"
        local_exe="LingmaLocal"
        has_local_exe=true
        sign_option="runtime"
    else
        # Qoder IDE 配置（默认）
        main_exe="Qoder"
        has_local_exe=false
        sign_option="library"
    fi
    
    log_info "🍎 Start to sign apple application, using output $bindir/$version ..."
    log_info "  Executable: ${main_exe}"
    log_info "  Has local exe: ${has_local_exe}"
    log_info "  Sign option: ${sign_option}"
    
    # Determine which architectures to sign based on platform_string
    local sign_amd64=false
    local sign_arm64=false
    
    if [[ "$platform_string" == *"mac_amd64"* ]]; then
        sign_amd64=true
    fi
    if [[ "$platform_string" == *"mac_arm64"* ]]; then
        sign_arm64=true
    fi
    
    log_info "  Will sign amd64: $sign_amd64"
    log_info "  Will sign arm64: $sign_arm64"
    
    # Sign x86_64 (amd64) if requested
    if [[ "$sign_amd64" == true ]]; then
        # Try multiple path formats
        local binary_paths=(
            "${bindir}/${version}/x86_64_darwin/${main_exe}"
            "${bindir}/darwin-amd64/${main_exe}"
        )
        
        local binary_path=""
        for path in "${binary_paths[@]}"; do
            if [[ -f "$path" ]]; then
                binary_path="$path"
                break
            fi
        done
        
        if [[ -n "$binary_path" ]]; then
            local sign_cmd="sudo codesign -f -o runtime --timestamp -s '${signature_name}' '$binary_path'"
            log_info "  Signing x86_64: $sign_cmd"
            if ! sudo codesign -f -o runtime --timestamp -s "${signature_name}" "$binary_path"; then
                log_error "Failed to sign x86_64_darwin/${main_exe}"
                return 1
            fi
        else
            log_warning "Binary not found in any expected location:"
            for path in "${binary_paths[@]}"; do
                log_warning "  Tried: $path"
            done
            # Show actual directory structure to help debug
            log_info "  Actual directory structure for amd64:"
            if [[ -d "$bindir" ]]; then
                log_info "    Contents of $bindir:"
                find "$bindir" -name "Qoder*" -type f 2>/dev/null | head -10 | while read -r file; do
                    log_info "      Found: $file"
                done
            else
                log_warning "    Base directory $bindir does not exist"
            fi
        fi
    fi
    
    # Sign aarch64 (arm64) if requested
    if [[ "$sign_arm64" == true ]]; then
        # Try multiple path formats
        local binary_paths=(
            "${bindir}/${version}/aarch64_darwin/${main_exe}"
            "${bindir}/darwin-arm64/${main_exe}"
        )
        
        local binary_path=""
        for path in "${binary_paths[@]}"; do
            if [[ -f "$path" ]]; then
                binary_path="$path"
                break
            fi
        done
        
        if [[ -n "$binary_path" ]]; then
            local sign_cmd="sudo codesign -f -o runtime --timestamp -s '${signature_name}' '$binary_path'"
            log_info "  Signing aarch64: $sign_cmd"
            if ! sudo codesign -f -o runtime --timestamp -s "${signature_name}" "$binary_path"; then
                log_error "Failed to sign aarch64_darwin/${main_exe}"
                return 1
            fi
        else
            log_warning "Binary not found in any expected location:"
            for path in "${binary_paths[@]}"; do
                log_warning "  Tried: $path"
            done
            # Show actual directory structure to help debug
            log_info "  Actual directory structure for arm64:"
            if [[ -d "$bindir" ]]; then
                log_info "    Contents of $bindir:"
                find "$bindir" -name "Qoder*" -type f 2>/dev/null | head -10 | while read -r file; do
                    log_info "      Found: $file"
                done
            else
                log_warning "    Base directory $bindir does not exist"
            fi
        fi
    fi
    
    # Sign local executables (only for legacy AI IDE)
    if [[ "$has_local_exe" == true ]]; then
        # Sign x86_64 local executable if amd64 platform was requested
        if [[ "$sign_amd64" == true ]]; then
            local binary_paths=(
                "${bindir}/${version}/x86_64_darwin/${local_exe}"
                "${bindir}/darwin-amd64/${local_exe}"
            )
            
            local binary_path=""
            for path in "${binary_paths[@]}"; do
                if [[ -f "$path" ]]; then
                    binary_path="$path"
                    break
                fi
            done
            
            if [[ -n "$binary_path" ]]; then
                local sign_cmd="sudo codesign -f -o '${sign_option}' --timestamp -s '${signature_name}' '$binary_path'"
                log_info "  Signing x86_64 local: $sign_cmd"
                if ! sudo codesign -f -o "${sign_option}" --timestamp -s "${signature_name}" "$binary_path"; then
                    log_error "Failed to sign x86_64_darwin/${local_exe}"
                    return 1
                fi
            else
                log_warning "Local binary not found in any expected location"
            fi
        fi
        
        # Sign aarch64 local executable if arm64 platform was requested
        if [[ "$sign_arm64" == true ]]; then
            local binary_paths=(
                "${bindir}/${version}/aarch64_darwin/${local_exe}"
                "${bindir}/darwin-arm64/${local_exe}"
            )
            
            local binary_path=""
            for path in "${binary_paths[@]}"; do
                if [[ -f "$path" ]]; then
                    binary_path="$path"
                    break
                fi
            done
            
            if [[ -n "$binary_path" ]]; then
                local sign_cmd="sudo codesign -f -o '${sign_option}' --timestamp -s '${signature_name}' '$binary_path'"
                log_info "  Signing aarch64 local: $sign_cmd"
                if ! sudo codesign -f -o "${sign_option}" --timestamp -s "${signature_name}" "$binary_path"; then
                    log_error "Failed to sign aarch64_darwin/${local_exe}"
                    return 1
                fi
            else
                log_warning "Local binary not found in any expected location"
            fi
        fi
    fi
    
    log_info "🔍 Start to validate apple application.."
    
    # Validate main executables (only those that were signed)
    if [[ "$sign_amd64" == true ]]; then
        local binary_paths=(
            "${bindir}/${version}/x86_64_darwin/${main_exe}"
            "${bindir}/darwin-amd64/${main_exe}"
        )
        for path in "${binary_paths[@]}"; do
            if [[ -f "$path" ]]; then
                sudo codesign -vv -d "$path"
                break
            fi
        done
    fi
    
    if [[ "$sign_arm64" == true ]]; then
        local binary_paths=(
            "${bindir}/${version}/aarch64_darwin/${main_exe}"
            "${bindir}/darwin-arm64/${main_exe}"
        )
        for path in "${binary_paths[@]}"; do
            if [[ -f "$path" ]]; then
                sudo codesign -vv -d "$path"
                break
            fi
        done
    fi
    
    # Validate local executables (only for legacy AI IDE and only those that were signed)
    if [[ "$has_local_exe" == true ]]; then
        if [[ "$sign_amd64" == true ]]; then
            local binary_paths=(
                "${bindir}/${version}/x86_64_darwin/${local_exe}"
                "${bindir}/darwin-amd64/${local_exe}"
            )
            for path in "${binary_paths[@]}"; do
                if [[ -f "$path" ]]; then
                    sudo codesign -vv -d "$path"
                    break
                fi
            done
        fi
        
        if [[ "$sign_arm64" == true ]]; then
            local binary_paths=(
                "${bindir}/${version}/aarch64_darwin/${local_exe}"
                "${bindir}/darwin-arm64/${local_exe}"
            )
            for path in "${binary_paths[@]}"; do
                if [[ -f "$path" ]]; then
                    sudo codesign -vv -d "$path"
                    break
                fi
            done
        fi
    fi
    
    log_info "✅ MacOS binary signing completed successfully"
}

sign_windows_binaries() {
    local bindir="$1"
    local version="$2"
    
    log_info "✒️ Signing Windows release binaries"
    log_info "  bindir: $bindir"
    log_info "  version: $version"
    
    # Load s4w credentials from file if available
    if [[ -f "${HOME}/.lingma-builder/s4w.info" ]]; then
        log_info "s4w info file exist, try load to env"
        source "${HOME}/.lingma-builder/s4w.info"
    fi
    
    # Check required credentials
    if [[ -z "${S4W_USER}" ]]; then
        log_error "Windows signature account not exists, please set S4W_USER environment variable"
        return 1
    fi
    
    if [[ -z "${S4W_KEY}" ]]; then
        log_error "Windows signature key not exists, please set S4W_KEY environment variable"
        return 1
    fi
    
    cd "${bindir}/${version}"
    
    # Define Windows architectures to sign
    local windows_archs=("x86_64_windows" "aarch64_windows")
    local signed_any=false
    
    for arch_dir in "${windows_archs[@]}"; do
        if [[ -d "$arch_dir" ]]; then
            log_info "Found $arch_dir directory, proceeding with signing"
            signed_any=true
            
            # Create zip file for signing
            local zip_name="${arch_dir}.zip"
            local signed_zip_name="signed_${arch_dir}.zip"
            
            zip -r "$zip_name" "$arch_dir"
            
            # Send to signing service
            log_info "Sending $arch_dir to signature service..."
            curl --location 'http://**************:8089/sign' \
                 --form "file=@\"$zip_name\"" \
                 --form 'server="secsign-aliyun.alibaba-inc.com"' \
                 --form "user=${S4W_USER}" \
                 --form "key=${S4W_KEY}" \
                 --form 'sigtype="aliyun"' \
                 --output "$signed_zip_name"
            
            if [[ ! -f "$signed_zip_name" ]]; then
                log_error "⚠️ Signing failed for $arch_dir, please check the signature user and key"
                continue
            fi
            
            # Clean unsigned files
            rm -rf "$arch_dir"
            rm -rf "$zip_name"
            
            # Extract signed files
            unzip "$signed_zip_name"
            log_info "Invoke signature service success for $arch_dir, verifying result"
            
            # Verify signing results
            if ! grep -q -E "(OK|SKIP)[[:space:]]+(Lingma|Qoder)\.exe" results.txt; then
                log_error "💀 Invoke service success, but signature is incorrect for $arch_dir"
                cat results.txt
                continue
            fi
            
            log_info "Windows binary signing verification successful for $arch_dir:"
            cat results.txt | grep -E "(OK|SKIP)[[:space:]]+(Lingma|Qoder)\.exe" || true
            
            # Clean up temporary files
            rm -rf results.txt
            rm -rf "$signed_zip_name"
            
            log_info "$arch_dir signing completed successfully"
        else
            log_info "💨 No $arch_dir directory found, skipping"
        fi
    done
    
    if [[ "$signed_any" == true ]]; then
        log_info "✅ Windows binary signing completed successfully"
    else
        log_info "💨 No Windows binaries found, skipping signing"
    fi
    
    cd "${SCRIPT_DIR}"
}

main() {
    local bindir="$1"
    local version="$2"
    local platform="$3"
    local type="$4"
    
    if [[ -z "$bindir" || -z "$version" || -z "$platform" ]]; then
        log_error "Usage: $0 <bindir> <version> <platform> [type]"
        log_error "Example: $0 /path/to/output 1.0.0 'linux/amd64:mac_aarch64:win_amd64' qoderIde"
        return 1
    fi
    
    # Convert relative path to absolute path
    if [[ "$bindir" != /* ]]; then
        bindir="$(pwd)/$bindir"
        log_info "Converted relative bindir to absolute: $bindir"
    fi
    
    if [[ ! -d "$bindir" ]]; then
        log_error "Binary directory not found: $bindir"
        log_info "  Current directory: $(pwd)"
        log_info "  Absolute bindir: $bindir"
        log_info "  Listing parent directory:"
        ls -la "$(dirname "$bindir")" 2>/dev/null || log_error "  Parent directory not accessible"
        log_info "  Searching for binaries in current directory and subdirectories:"
        find "$(pwd)" -name "Qoder*" -type f 2>/dev/null | head -10 | while read -r file; do
            log_info "    Found: $file"
        done
        return 1
    fi
    
    if [[ ! -d "${bindir}/${version}" ]]; then
        log_error "Version directory not found: ${bindir}/${version}"
        log_info "  Listing bindir contents:"
        ls -la "$bindir" 2>/dev/null || log_error "  Cannot list bindir"
        return 1
    fi
    
    log_info "🎆 Starting binary signing process..."
    log_info "Binary directory: $bindir"
    log_info "Version: $version"
    log_info "Platform: $platform"
    log_info "Type: ${type:-default}"
    
    sign_binary "$bindir" "$version" "$platform" "$type"
    
    log_info "👌 Signature completed"
}

# Only run main if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi