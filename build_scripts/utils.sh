#!/bin/bash

# Utility Functions for Build System
# This file contains common utility functions used across all build scripts

# Source the configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/config.sh"

# Logging functions
log_info() {
    # Ensure log directory exists
    mkdir -p "${LOG_DIR}"
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "${LOG_DIR}/${LOG_FILE}"
}

log_success() {
    # Ensure log directory exists
    mkdir -p "${LOG_DIR}"
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "${LOG_DIR}/${LOG_FILE}"
}

log_warning() {
    # Ensure log directory exists
    mkdir -p "${LOG_DIR}"
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "${LOG_DIR}/${LOG_FILE}"
}

log_error() {
    # Ensure log directory exists
    mkdir -p "${LOG_DIR}"
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "${LOG_DIR}/${ERROR_LOG_FILE}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check required tools
check_dependencies() {
    local missing_deps=()
    local required_tools=("git" "go" "tar" "zip")
    
    for tool in "${required_tools[@]}"; do
        if ! command_exists "$tool"; then
            missing_deps+=("$tool")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "Missing required dependencies: ${missing_deps[*]}"
        return 1
    fi
    
    log_success "All required dependencies are available"
    return 0
}

# Extract platform components
extract_platform() {
    local platform="$1"
    echo "${platform}" | tr '/' ' '
}

# Get platform architecture name (for build output directory)
get_platform_arch_name() {
    local platform="$1"
    case "$platform" in
        "linux/amd64")
            echo "x86_64_linux"
            ;;
        "linux/arm64")
            echo "aarch64_linux"
            ;;
        "darwin/amd64")
            echo "x86_64_darwin"
            ;;
        "darwin/arm64")
            echo "aarch64_darwin"
            ;;
        "windows/amd64")
            echo "x86_64_windows"
            ;;
        "windows/arm64")
            echo "aarch64_windows"
            ;;
        *)
            # Fallback to simple conversion
            local os arch
            read -r os arch <<< "$(extract_platform "$platform")"
            echo "${arch}_${os}"
            ;;
    esac
}

# Get binary name for platform
get_binary_name() {
    local os="$1"
    case "$os" in
        "linux"|"darwin")
            echo "Qoder"
            ;;
        "windows")
            echo "Qoder.exe"
            ;;
        *)
            echo "Qoder"
            ;;
    esac
}

# Get archive format for platform
get_archive_format() {
    local os="$1"
    case "$os" in
        "linux"|"darwin")
            echo "tar.gz"
            ;;
        "windows")
            echo "zip"
            ;;
        *)
            echo "tar.gz"
            ;;
    esac
}

# Check if platform is supported
is_platform_supported() {
    local platform="$1"
    
    for supported_platform in "${SUPPORTED_PLATFORMS[@]}"; do
        if [[ "$platform" == "$supported_platform" ]]; then
            return 0
        fi
    done
    
    return 1
}

# Create archive based on platform
create_archive() {
    local source_dir="$1"
    local target_file="$2"
    local format="$3"
    
    log_info "Creating archive: $target_file"
    
    # Convert target file to absolute path
    if [[ "$target_file" != /* ]]; then
        target_file="$(pwd)/$target_file"
    fi
    
    case "$format" in
        "tar.gz")
            tar $TAR_OPTIONS "$target_file" -C "$(dirname "$source_dir")" "$(basename "$source_dir")"
            ;;
        "zip")
            (cd "$(dirname "$source_dir")" && zip $ZIP_OPTIONS "$target_file" "$(basename "$source_dir")")
            ;;
        *)
            log_error "Unsupported archive format: $format"
            return 1
            ;;
    esac
    
    if [[ $? -eq 0 ]]; then
        log_success "Archive created successfully: $target_file"
        return 0
    else
        log_error "Failed to create archive: $target_file"
        return 1
    fi
}

# Calculate file hash
calculate_hash() {
    local file="$1"
    local algorithm="${2:-sha256}"
    
    case "$algorithm" in
        "md5")
            if command_exists "md5sum"; then
                md5sum "$file" | cut -d' ' -f1
            elif command_exists "md5"; then
                md5 -q "$file"
            fi
            ;;
        "sha256")
            if command_exists "sha256sum"; then
                sha256sum "$file" | cut -d' ' -f1
            elif command_exists "shasum"; then
                shasum -a 256 "$file" | cut -d' ' -f1
            fi
            ;;
        *)
            log_error "Unsupported hash algorithm: $algorithm"
            return 1
            ;;
    esac
}

# Cleanup function
cleanup() {
    log_info "Cleaning up temporary files..."
    if [[ -d "$BUILD_DIR" ]]; then
        rm -rf "${BUILD_DIR}/tmp_"*
    fi
}

# Trap for cleanup on exit
trap cleanup EXIT

# Wait for background processes
wait_for_jobs() {
    local max_wait="${1:-300}"  # Default 5 minutes
    local waited=0
    
    while [[ $(jobs -r | wc -l) -gt 0 ]] && [[ $waited -lt $max_wait ]]; do
        sleep 1
        waited=$((waited + 1))
    done
    
    if [[ $waited -ge $max_wait ]]; then
        log_warning "Timeout waiting for background jobs"
        jobs -p | xargs -r kill
        return 1
    fi
    
    return 0
}

# JSON validation function
validate_json() {
    local json_file="$1"
    local description="${2:-JSON file}"
    
    if [[ ! -f "$json_file" ]]; then
        log_error "$description not found: $json_file"
        return 1
    fi
    
    log_info "Validating $description: $json_file"
    
    # Try different JSON validation methods in order of preference
    local validation_result=1
    local error_output=""
    
    # Method 1: Try jq (most reliable)
    if command -v jq >/dev/null 2>&1; then
        error_output=$(jq empty "$json_file" 2>&1)
        validation_result=$?
        if [[ $validation_result -eq 0 ]]; then
            log_info "✓ $description is valid JSON (validated with jq)"
            return 0
        else
            log_error "✗ $description contains invalid JSON (jq validation failed):"
            log_error "  Error: $error_output"
            return 1
        fi
    fi
    
    # Method 2: Try python (widely available)
    if command -v python3 >/dev/null 2>&1; then
        error_output=$(python3 -c "import json; json.load(open('$json_file'))" 2>&1)
        validation_result=$?
        if [[ $validation_result -eq 0 ]]; then
            log_info "✓ $description is valid JSON (validated with python3)"
            return 0
        else
            log_error "✗ $description contains invalid JSON (python3 validation failed):"
            log_error "  Error: $error_output"
            return 1
        fi
    fi
    
    # Method 3: Try node (if available)
    if command -v node >/dev/null 2>&1; then
        error_output=$(node -e "JSON.parse(require('fs').readFileSync('$json_file', 'utf8'))" 2>&1)
        validation_result=$?
        if [[ $validation_result -eq 0 ]]; then
            log_info "✓ $description is valid JSON (validated with node)"
            return 0
        else
            log_error "✗ $description contains invalid JSON (node validation failed):"
            log_error "  Error: $error_output"
            return 1
        fi
    fi
    
    # No JSON validator available
    log_warning "⚠ No JSON validator found (jq, python3, or node), skipping validation for $description"
    log_warning "  Please install jq for reliable JSON validation"
    return 0  # Don't fail the build if no validator is available
}

# Validate multiple JSON files
validate_json_files() {
    local files=("$@")
    local all_valid=true
    
    if [[ ${#files[@]} -eq 0 ]]; then
        log_info "No JSON files to validate"
        return 0
    fi
    
    log_info "Validating ${#files[@]} JSON files..."
    
    for json_file in "${files[@]}"; do
        if [[ -f "$json_file" ]]; then
            if ! validate_json "$json_file"; then
                all_valid=false
            fi
        else
            log_warning "JSON file not found (skipping): $json_file"
        fi
    done
    
    if [[ "$all_valid" == true ]]; then
        log_success "All JSON files are valid"
        return 0
    else
        log_error "One or more JSON files contain invalid JSON"
        return 1
    fi
}