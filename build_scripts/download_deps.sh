#!/bin/bash

# Download Dependencies Script
# This script handles downloading project dependencies including Go modules from private repositories

# Source utilities
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/utils.sh"
source "${SCRIPT_DIR}/config.sh"

# Setup Git authentication for Go modules
setup_git_auth() {
    log_info "Setting up Git authentication for Go modules..."
    
    # Check if Git authentication is configured
    if [[ -f ~/.netrc ]]; then
        log_info "Found ~/.netrc file, using existing Git authentication"
        return 0
    fi
    
    # Check for environment variables
    if [[ -n "$GIT_USERNAME" && -n "$GIT_PASSWORD" && -n "$GIT_HOST" ]]; then
        log_info "Configuring Git authentication from environment variables"
        
        # Create .netrc file
        cat > ~/.netrc << EOF
machine ${GIT_HOST}
login ${GIT_USERNAME}
password ${GIT_PASSWORD}
EOF
        chmod 600 ~/.netrc
        
        # Configure Git URL rewriting
        git config --global url."https://${GIT_USERNAME}:${GIT_PASSWORD}@${GIT_HOST}/".insteadOf "git@${GIT_HOST}:"
        git config --global url."https://${GIT_USERNAME}:${GIT_PASSWORD}@${GIT_HOST}/".insteadOf "https://${GIT_HOST}/"
        
        log_success "Git authentication configured successfully"
        return 0
    fi
    
    log_info "No Git authentication configured, proceeding with public repositories only"
    return 0
}

# Configure Go environment for private repositories (supports multiple domains)
setup_go_private() {
    log_info "Configuring Go environment for private repositories (multi-domain support)..."
    
    # Set GOPRIVATE for known private hosts
    local private_hosts=()
    
    # Method 1: Use GOPRIVATE if already set (from CI)
    if [[ -n "$GOPRIVATE" ]]; then
        log_info "Using existing GOPRIVATE configuration: $GOPRIVATE"
        export GOPRIVATE="$GOPRIVATE"
    else
        log_info "Building GOPRIVATE configuration from available sources..."
        
        # Method 2: Use GIT_CONFIGURED_HOSTS from CI (multi-domain setup)
        if [[ -n "$GIT_CONFIGURED_HOSTS" ]]; then
            log_info "Found configured Git hosts from CI: $GIT_CONFIGURED_HOSTS"
            
            # Convert comma-separated hosts to GOPRIVATE format
            IFS=',' read -ra HOST_ARRAY <<< "$GIT_CONFIGURED_HOSTS"
            for host in "${HOST_ARRAY[@]}"; do
                # Clean whitespace
                host=$(echo "$host" | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
                if [[ -n "$host" ]]; then
                    private_hosts+=("${host}/*")
                fi
            done
        fi
        
        # Method 3: Add configured Git host if available (single domain fallback)
        if [[ -n "$GIT_HOST" ]]; then
            private_hosts+=("${GIT_HOST}/*")
            log_info "Added single Git host: ${GIT_HOST}"
        fi
        
        # Method 4: Add common private hosts as fallback
        private_hosts+=("gitlab.alibaba-inc.com/*")
        private_hosts+=("code.alibaba-inc.com/*")
        
        # Join array with commas and remove duplicates
        local goprivate=$(IFS=','; echo "${private_hosts[*]}")
        # Simple deduplication (remove adjacent duplicates after sorting)
        goprivate=$(echo "$goprivate" | tr ',' '\n' | sort -u | tr '\n' ',' | sed 's/,$//')
        
        export GOPRIVATE="$goprivate"
    fi
    
    export GOPROXY="https://goproxy.cn"
    export GOSUMDB="off"
    
    log_info "Go private configuration:"
    log_info "  GOPRIVATE: $GOPRIVATE"
    log_info "  GOPROXY: $GOPROXY"
    log_info "  GOSUMDB: $GOSUMDB"
    
    # Count configured domains
    local domain_count=$(echo "$GOPRIVATE" | tr ',' '\n' | wc -l)
    log_info "  Configured private domains: $domain_count"
}

# Download Go modules with retry logic
download_go_modules() {
    log_info "📦 Downloading Go modules..."
    
    if ! command_exists "go"; then
        log_error "Go not found in PATH"
        return 1
    fi
    
    if [[ ! -f "go.mod" ]]; then
        log_info "No go.mod file found - skipping Go module download"
        return 0
    fi
    
    # Setup private repository access
    setup_go_private
    
    # Try to download modules with retry
    local max_retries=3
    local retry_count=0
    
    while [[ $retry_count -lt $max_retries ]]; do
        log_info "Attempting to download Go modules (attempt $((retry_count + 1))/$max_retries)..."
        
        if go mod download; then
            log_success "Go modules downloaded successfully"
            return 0
        else
            retry_count=$((retry_count + 1))
            if [[ $retry_count -lt $max_retries ]]; then
                log_warning "Go module download failed, retrying in 5 seconds..."
                sleep 5
            fi
        fi
    done
    
    log_error "Failed to download Go modules after $max_retries attempts"
    
    # Show more detailed error information
    log_info "Debugging information:"
    log_info "Current directory: $(pwd)"
    log_info "Go version: $(go version)"
    log_info "Go environment:"
    go env | grep -E '(GOPRIVATE|GOPROXY|GOSUMDB|GONOPROXY|GONOSUMDB)'
    
    return 1
}

# Download vendor dependencies
download_vendor() {
    if [[ -d "vendor" ]]; then
        log_info "Vendor directory exists, syncing vendor modules..."
        if go mod vendor; then
            log_success "Vendor modules synced successfully"
        else
            log_warning "Failed to sync vendor modules (continuing anyway)"
        fi
    else
        log_info "No vendor directory found, skipping vendor sync"
    fi
}

# Download additional build tools
download_build_tools() {
    log_info "🔧 Downloading additional build tools..."
    
    # Check for specific tools that might be needed
    local tools_to_check=("zig" "x86_64-linux-musl-gcc" "aarch64-linux-musl-gcc")
    local missing_tools=()
    
    for tool in "${tools_to_check[@]}"; do
        if ! command_exists "$tool"; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_warning "Missing build tools: ${missing_tools[*]}"
        log_info "Please install missing tools:"
        log_info "  macOS: brew install FiloSottile/musl-cross/musl-cross zig"
        log_info "  Linux: Install musl-cross-make and zig"
    else
        log_success "All required build tools are available"
    fi
}

# Clean module cache if needed
clean_module_cache() {
    if [[ "$CLEAN_MODULES" == "true" ]]; then
        log_info "Cleaning Go module cache..."
        go clean -modcache
        log_success "Go module cache cleaned"
    fi
}

main() {
    log_info "Starting dependency download process..."
    
    # Clean module cache if requested
    clean_module_cache
    
    # Setup Git authentication
    setup_git_auth
    
    # Download Go modules
    if ! download_go_modules; then
        log_error "Failed to download Go modules"
        return 1
    fi
    
    # Download vendor dependencies
    download_vendor
    
    # Download additional build tools (check only)
    download_build_tools
    
    log_success "✅ Dependency download completed successfully"
    return 0
}

# Only run main if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi