#!/bin/bash

# Cross-Compilation Script
# This script handles Qoder Go cross-compilation for different platforms
# Supports: Windows, macOS, Linux for both AMD64 and ARM64 architectures

# Source utilities and version management
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/utils.sh"
source "${SCRIPT_DIR}/config.sh"
source "${SCRIPT_DIR}/version.sh"

# Compile single platform
compile_platform() {
    local platform="$1"
    local edition="$2"
    local output_dir="$3"
    
    log_info "🔨 Starting compilation for platform: $platform, edition: $edition"
    
    # Extract OS and architecture
    local os arch
    read -r os arch <<< "$(extract_platform "$platform")"
    
    if [[ -z "$os" || -z "$arch" ]]; then
        log_error "Invalid platform format: $platform"
        return 1
    fi
    
    # Validate platform support
    if ! is_platform_supported "$platform"; then
        log_error "Unsupported platform: $platform"
        return 1
    fi
    
    # Create qoder-specific platform output directory
    local qoder_platform_dir
    qoder_platform_dir=$(get_qoder_platform_dir "$platform")
    local platform_output_dir="${output_dir}/${qoder_platform_dir}"
    mkdir -p "$platform_output_dir"
    
    # Get binary name for platform
    local binary_name
    binary_name=$(get_binary_name "$os")
    
    log_info "Compiling for ${os}/${arch}, binary: $binary_name, output: $platform_output_dir"
    
    # Call external build script with qoder-specific parameters
    if ! "${SCRIPT_DIR}/${BUILD_BINARY_SCRIPT}" "$os" "$arch" "$platform_output_dir" "$edition"; then
        log_error "Failed to build binary for $platform"
        return 1
    fi
    
    # Verify binary was created
    local binary_path="${platform_output_dir}/${binary_name}"
    if [[ ! -f "$binary_path" ]]; then
        log_error "Binary not found after build: $binary_path"
        return 1
    fi
    
    log_success "Binary compiled successfully: $binary_path"
    
    # Store compilation info
    export "COMPILED_${os}_${arch}=true"
    export "BINARY_PATH_${os}_${arch}=$binary_path"
    
    return 0
}

# Check if platform is supported
is_platform_supported() {
    local platform="$1"
    
    for supported_platform in "${SUPPORTED_PLATFORMS[@]}"; do
        if [[ "$platform" == "$supported_platform" ]]; then
            return 0
        fi
    done
    
    return 1
}

# Check if a platform needs code signing
should_sign_binary() {
    local os="$1"
    case "$os" in
        "darwin"|"windows")
            return 0
            ;;
        *)
            return 1
            ;;
    esac
}

# Check if any platform needs signing
needs_signing() {
    local platforms_to_build=("${@}")
    
    for platform in "${platforms_to_build[@]}"; do
        local os="${platform%%/*}"
        if should_sign_binary "$os"; then
            return 0
        fi
    done
    
    return 1
}

# Sign all binaries after compilation
sign_all_binaries() {
    local output_dir="$1"
    local platforms_to_build=("${@:2}")
    local edition="${COSY_EDITION:-all}"
    
    # Check if signing is enabled (default: false)
    local enable_signing="${ENABLE_SIGNING:-false}"
    if [[ "$enable_signing" != "true" ]]; then
        log_info "💨 Code signing is disabled (ENABLE_SIGNING=$enable_signing), skipping signing process"
        return 0
    fi
    
    # Check if any platforms need signing
    if ! needs_signing "${platforms_to_build[@]}"; then
        log_info "No platforms require signing, skipping"
        return 0
    fi
    
    # Get version for signing
    local version
    version=$(get_version 2>&1 | tail -1)
    if [[ -z "$version" ]]; then
        log_error "Cannot determine version for signing"
        return 1
    fi
    
    # Determine binary type from edition
    local binary_type=""
    case "$edition" in
        "qoderIde")
            binary_type="qoderIde"
            ;;
        "aiIde")
            binary_type="aiIde"
            ;;
        *)
            binary_type=""
            ;;
    esac
    
    # Construct platform string for signing
    local platform_string=""
    for platform in "${platforms_to_build[@]}"; do
        local os="${platform%%/*}"
        local arch="${platform##*/}"
        
        if should_sign_binary "$os"; then
            if [[ -n "$platform_string" ]]; then
                platform_string="${platform_string}:"
            fi
            
            case "$os" in
                "darwin")
                    platform_string="${platform_string}mac_${arch}"
                    ;;
                "windows")
                    platform_string="${platform_string}win_${arch}"
                    ;;
                "linux")
                    platform_string="${platform_string}linux_${arch}"
                    ;;
            esac
        fi
    done
    
    if [[ -n "$platform_string" ]]; then
        log_info "Signing binaries for platforms: $platform_string"
        log_info "Binary type: ${binary_type:-default}"
        
        # The output_dir passed here is already the base directory (from compile_all_platforms)
        local signing_dir="$output_dir"
        log_info "sign_all_binaries paths:"
        log_info "  Current working directory: $(pwd)"
        log_info "  signing_dir: $signing_dir"
        log_info "  version: $version"
        log_info "  Checking if signing_dir exists: $(test -d "$signing_dir" && echo "YES" || echo "NO")"
        
        if ! "${SCRIPT_DIR}/${SIGN_SCRIPT}" "$signing_dir" "$version" "$platform_string" "$binary_type"; then
            log_warning "Failed to sign binaries (continuing anyway)"
            return 1
        else
            log_success "All binaries signed successfully"
            return 0
        fi
    else
        log_info "No platforms require signing"
        return 0
    fi
}

# Compile all supported platforms with qoder structure
compile_all_platforms() {
    local edition="$1"
    local output_dir="$2"
    local platforms_to_build=("${@:3}")
    
    # If no specific platforms provided, use all supported platforms
    if [[ ${#platforms_to_build[@]} -eq 0 ]]; then
        platforms_to_build=("${SUPPORTED_PLATFORMS[@]}")
    fi
    
    log_info "🚀 Starting cross-compilation for ${#platforms_to_build[@]} platforms"
    log_info "Edition: $edition"
    log_info "Output directory: $output_dir"
    
    local failed_platforms=()
    local successful_platforms=()
    
    # Create output directory
    mkdir -p "$output_dir"
    
    # Validate configuration before starting
    if ! validate_config; then
        log_error "Configuration validation failed"
        return 1
    fi
    
    # Create qoder version structure
    local version
    version=$(get_version 2>&1 | tail -1)
    if [[ -n "$version" ]]; then
        local version_dir="${output_dir}/${version}"
        mkdir -p "$version_dir"
        
        # Create all platform directories
        for platform in "${platforms_to_build[@]}"; do
            local qoder_platform_dir
            qoder_platform_dir=$(get_qoder_platform_dir "$platform")
            mkdir -p "${version_dir}/${qoder_platform_dir}"
        done
        
        # Create config.json
        printf '{\n    "cosy.core.version": "%s"\n}' "$version" > "${output_dir}/config.json"
        
        # Update output_dir to include version
        output_dir="$version_dir"
    fi
    
    # Download dependencies first
    log_info "📦 Downloading dependencies..."
    if ! "${SCRIPT_DIR}/${DOWNLOAD_DEPS_SCRIPT}"; then
        log_error "Failed to download dependencies"
        return 1
    fi
    
    # Compile each platform
    for platform in "${platforms_to_build[@]}"; do
        log_info "⚙️ Processing platform: $platform"
        
        if compile_platform "$platform" "$edition" "$output_dir"; then
            successful_platforms+=("$platform")
            log_success "✓ $platform compilation successful"
        else
            failed_platforms+=("$platform")
            log_error "✗ $platform compilation failed"
        fi
        
        # Add separator between platforms
        echo "----------------------------------------"
    done
    
    # Report compilation results
    log_info "📊 Cross-compilation completed"
    log_info "Successful platforms (${#successful_platforms[@]}): ${successful_platforms[*]}"
    
    if [[ ${#failed_platforms[@]} -gt 0 ]]; then
        log_warning "Failed platforms (${#failed_platforms[@]}): ${failed_platforms[*]}"
    fi
    
    # Continue with signing and other operations for successful platforms
    local final_result=0
    if [[ ${#successful_platforms[@]} -gt 0 ]]; then
        log_info "Proceeding with post-compilation steps for successful platforms..."
        
        # Sign all binaries for successful platforms
        log_info "🔏 Starting binary signing process..."
        # Pass the base output directory (without version) to signing since
        # the signing script expects to find bindir/version/platform structure
        local base_output_dir
        base_output_dir=$(dirname "$output_dir")
        log_info "Cross-compile signing paths:"
        log_info "  Current working directory: $(pwd)"
        log_info "  output_dir: $output_dir"
        log_info "  base_output_dir: $base_output_dir"
        if sign_all_binaries "$base_output_dir" "${successful_platforms[@]}"; then
            log_success "Binary signing completed successfully"
        else
            log_warning "Binary signing failed, but continuing..."
            # Don't fail the entire process for signing issues
        fi
    else
        log_error "No platforms compiled successfully"
        final_result=1
    fi
    
    # Set final result based on whether we had any failures
    if [[ ${#failed_platforms[@]} -gt 0 ]] && [[ ${#successful_platforms[@]} -eq 0 ]]; then
        # All platforms failed
        final_result=1
    elif [[ ${#failed_platforms[@]} -gt 0 ]]; then
        # Some platforms failed, but some succeeded - this is a partial success
        log_info "Partial compilation success: ${#successful_platforms[@]} succeeded, ${#failed_platforms[@]} failed"
        # Return success (0) to allow packaging and other operations to continue
        final_result=0
    else
        # All platforms succeeded
        log_success "All platforms compiled successfully"
        final_result=0
    fi
    
    return $final_result
}

# Parallel compilation (experimental)
compile_parallel() {
    local edition="$1"
    local output_dir="$2"
    local platforms_to_build=("${@:3}")
    
    # If no specific platforms provided, use all supported platforms
    if [[ ${#platforms_to_build[@]} -eq 0 ]]; then
        platforms_to_build=("${SUPPORTED_PLATFORMS[@]}")
    fi
    
    log_info "🚀 Starting parallel cross-compilation for ${#platforms_to_build[@]} platforms"
    
    # Create output directory
    mkdir -p "$output_dir"
    
    # Download dependencies first
    log_info "📦 Downloading dependencies..."
    if ! "${SCRIPT_DIR}/${DOWNLOAD_DEPS_SCRIPT}"; then
        log_error "Failed to download dependencies"
        return 1
    fi
    
    # Start compilation jobs in background
    local pids=()
    local platform_logs=()
    
    for platform in "${platforms_to_build[@]}"; do
        local log_file="${LOG_DIR}/compile_${platform//\//_}.log"
        platform_logs+=("$log_file")
        
        (
            compile_platform "$platform" "$edition" "$output_dir" 2>&1 | tee "$log_file"
            echo $? > "${log_file}.exit_code"
        ) &
        
        pids+=($!)
        log_info "Started background compilation for $platform (PID: ${pids[-1]})"
    done
    
    # Wait for all jobs to complete
    log_info "⏳ Waiting for ${#pids[@]} compilation jobs to complete..."
    
    local failed_count=0
    local success_count=0
    
    for i in "${!pids[@]}"; do
        local pid="${pids[$i]}"
        local platform="${platforms_to_build[$i]}"
        local log_file="${platform_logs[$i]}"
        
        wait "$pid"
        local exit_code
        if [[ -f "${log_file}.exit_code" ]]; then
            exit_code=$(cat "${log_file}.exit_code")
            rm -f "${log_file}.exit_code"
        else
            exit_code=1
        fi
        
        if [[ $exit_code -eq 0 ]]; then
            log_success "✓ $platform compilation successful"
            ((success_count++))
        else
            log_error "✗ $platform compilation failed"
            ((failed_count++))
        fi
    done
    
    # Report results
    log_info "📊 Parallel compilation completed"
    log_info "Successful: $success_count, Failed: $failed_count"
    
    if [[ $failed_count -gt 0 ]]; then
        return 1
    else
        log_success "All parallel compilations successful"
        return 0
    fi
}

# Generate compilation summary
generate_compilation_summary() {
    local output_dir="$1"
    local summary_file="${output_dir}/compilation_summary.txt"
    
    log_info "Generating compilation summary: $summary_file"
    
    {
        echo "Compilation Summary"
        echo "=================="
        echo "Date: $(date)"
        echo "Build Type: ${BUILD_TYPE}"
        echo "Version: ${BUILD_VERSION}"
        echo "Edition: ${EDITION}"
        echo ""
        echo "Compiled Binaries:"
        echo "------------------"
        
        for platform in "${SUPPORTED_PLATFORMS[@]}"; do
            local os arch
            read -r os arch <<< "$(extract_platform "$platform")"
            local binary_name
            binary_name=$(get_binary_name "$os")
            local binary_path="${output_dir}/${os}-${arch}/${binary_name}"
            
            if [[ -f "$binary_path" ]]; then
                local file_size
                file_size=$(stat -c%s "$binary_path" 2>/dev/null || stat -f%z "$binary_path" 2>/dev/null)
                local file_hash
                file_hash=$(calculate_hash "$binary_path" "sha256")
                
                echo "✓ ${platform}: ${binary_name} (${file_size} bytes)"
                echo "  Path: ${binary_path}"
                echo "  SHA256: ${file_hash}"
            else
                echo "✗ ${platform}: Binary not found"
            fi
        done
        
    } > "$summary_file"
    
    if [[ -f "$summary_file" ]]; then
        log_success "Compilation summary created: $summary_file"
        
        # Also display summary
        echo ""
        cat "$summary_file"
        
        return 0
    else
        log_error "Failed to create compilation summary"
        return 1
    fi
}

# Main cross-compilation function
main() {
    local edition="${1:-$EDITION_ALL}"
    local output_dir="${2:-$BUILD_DIR/binaries}"
    local parallel_mode="${3:-false}"
    shift 3
    local platforms=("$@")
    
    log_info "Starting cross-compilation process"
    log_info "Edition: $edition"
    log_info "Output directory: $output_dir"
    log_info "Parallel mode: $parallel_mode"
    
    # Validate inputs
    if [[ ! "$edition" =~ ^(cn|intl|all)$ ]]; then
        log_error "Invalid edition: $edition (must be cn, intl, or all)"
        return 1
    fi
    
    # Check dependencies
    if ! check_dependencies; then
        return 1
    fi
    
    # Perform compilation
    local compilation_result
    if [[ "$parallel_mode" == "true" ]]; then
        compile_parallel "$edition" "$output_dir" "${platforms[@]}"
        compilation_result=$?
    else
        compile_all_platforms "$edition" "$output_dir" "${platforms[@]}"
        compilation_result=$?
    fi
    
    # Generate summary regardless of compilation result
    generate_compilation_summary "$output_dir"
    
    if [[ $compilation_result -eq 0 ]]; then
        log_success "Cross-compilation process completed successfully"
    else
        log_error "Cross-compilation process completed with errors"
    fi
    
    return $compilation_result
}

# Only run main if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi