#!/bin/bash

# Test Script for Build System
# This script tests the functionality of all build system components

# Source utilities
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/utils.sh"

# Test configuration
TEST_START_TIME=$(date +%s)
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_SKIPPED=0
TEST_RESULTS=()

# Test result tracking
record_test_result() {
    local test_name="$1"
    local result="$2"
    local message="$3"
    
    case "$result" in
        "PASS")
            ((TESTS_PASSED++))
            log_success "✓ $test_name: $message"
            ;;
        "FAIL")
            ((TESTS_FAILED++))
            log_error "✗ $test_name: $message"
            ;;
        "SKIP")
            ((TESTS_SKIPPED++))
            log_warning "⚠ $test_name: $message"
            ;;
    esac
    
    TEST_RESULTS+=("$result $test_name: $message")
}

# Test 1: Configuration validation
test_configuration() {
    log_info "Testing configuration validation..."
    
    # Test config loading
    if source "${SCRIPT_DIR}/config.sh" 2>/dev/null; then
        record_test_result "config_loading" "PASS" "Configuration loaded successfully"
    else
        record_test_result "config_loading" "FAIL" "Failed to load configuration"
        return 1
    fi
    
    # Test required variables
    local required_vars=(
        "PROJECT_NAME"
        "BUILD_DIR" 
        "DIST_DIR"
        "SUPPORTED_PLATFORMS"
        "BUILD_TYPE_RELEASE"
        "BUILD_TYPE_DEV"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -n "${!var}" ]]; then
            record_test_result "config_var_$var" "PASS" "$var is defined: ${!var}"
        else
            record_test_result "config_var_$var" "FAIL" "$var is not defined"
        fi
    done
    
    # Test validate_config function
    if validate_config >/dev/null 2>&1; then
        record_test_result "config_validation" "PASS" "Configuration validation passed"
    else
        record_test_result "config_validation" "FAIL" "Configuration validation failed"
    fi
}

# Test 2: Utility functions
test_utilities() {
    log_info "Testing utility functions..."
    
    # Test command_exists function
    if command_exists "bash"; then
        record_test_result "command_exists" "PASS" "command_exists function works correctly"
    else
        record_test_result "command_exists" "FAIL" "command_exists function failed"
    fi
    
    # Test platform extraction
    local test_platform="linux/amd64"
    local os arch
    read -r os arch <<< "$(extract_platform "$test_platform")"
    
    if [[ "$os" == "linux" && "$arch" == "amd64" ]]; then
        record_test_result "extract_platform" "PASS" "Platform extraction works correctly"
    else
        record_test_result "extract_platform" "FAIL" "Platform extraction failed: $os/$arch"
    fi
    
    # Test hash calculation (if file exists)
    local temp_file="/tmp/test_hash_$$"
    echo "test content" > "$temp_file"
    
    if hash_result=$(calculate_hash "$temp_file" "sha256"); then
        if [[ -n "$hash_result" ]]; then
            record_test_result "calculate_hash" "PASS" "Hash calculation works"
        else
            record_test_result "calculate_hash" "FAIL" "Hash calculation returned empty result"
        fi
    else
        record_test_result "calculate_hash" "FAIL" "Hash calculation failed"
    fi
    
    rm -f "$temp_file"
}

# Test 3: Environment detection (mock test)
test_environment_detection() {
    log_info "Testing environment detection..."
    
    # Test in a Git repository context
    if git rev-parse --git-dir >/dev/null 2>&1; then
        record_test_result "git_repo_detection" "PASS" "Git repository detected"
        
        # Test environment detection script loading
        if source "${SCRIPT_DIR}/detect_env.sh" >/dev/null 2>&1; then
            record_test_result "detect_env_loading" "PASS" "Environment detection script loaded"
            
            # Test functions exist
            if declare -f detect_git_environment >/dev/null; then
                record_test_result "detect_env_functions" "PASS" "Environment detection functions available"
            else
                record_test_result "detect_env_functions" "FAIL" "Environment detection functions not found"
            fi
        else
            record_test_result "detect_env_loading" "FAIL" "Failed to load environment detection script"
        fi
    else
        record_test_result "git_repo_detection" "SKIP" "Not in a Git repository - skipping Git-related tests"
    fi
}

# Test 4: Version management
test_version_management() {
    log_info "Testing version management..."
    
    # Test version script loading
    if source "${SCRIPT_DIR}/version.sh" >/dev/null 2>&1; then
        record_test_result "version_script_loading" "PASS" "Version script loaded successfully"
    else
        record_test_result "version_script_loading" "FAIL" "Failed to load version script"
        return 1
    fi
    
    # Test version parsing
    local test_version="v1.2.3"
    if parse_version "$test_version" >/dev/null 2>&1; then
        if [[ "$VERSION_MAJOR" == "1" && "$VERSION_MINOR" == "2" && "$VERSION_PATCH" == "3" ]]; then
            record_test_result "version_parsing" "PASS" "Version parsing works correctly"
        else
            record_test_result "version_parsing" "FAIL" "Version parsing returned wrong values: $VERSION_MAJOR.$VERSION_MINOR.$VERSION_PATCH"
        fi
    else
        record_test_result "version_parsing" "FAIL" "Version parsing failed"
    fi
    
    # Test filename generation
    local test_filename
    test_filename=$(generate_filename "linux/amd64" "all" "release" "1.2.3")
    if [[ -n "$test_filename" ]]; then
        record_test_result "filename_generation" "PASS" "Filename generation works: $test_filename"
    else
        record_test_result "filename_generation" "FAIL" "Filename generation failed"
    fi
}

# Test 5: Cross-compilation script validation
test_cross_compilation() {
    log_info "Testing cross-compilation setup..."
    
    # Test cross-compilation script loading
    if source "${SCRIPT_DIR}/cross_compile.sh" >/dev/null 2>&1; then
        record_test_result "cross_compile_loading" "PASS" "Cross-compilation script loaded"
    else
        record_test_result "cross_compile_loading" "FAIL" "Failed to load cross-compilation script"
        return 1
    fi
    
    # Test platform support checking
    if is_platform_supported "linux/amd64"; then
        record_test_result "platform_support_check" "PASS" "Platform support checking works"
    else
        record_test_result "platform_support_check" "FAIL" "Platform support checking failed"
    fi
    
    # Test Go availability
    if command_exists "go"; then
        local go_version
        go_version=$(go version 2>/dev/null | head -n1)
        record_test_result "go_availability" "PASS" "Go is available: $go_version"
    else
        record_test_result "go_availability" "SKIP" "Go not available - cross-compilation tests will be limited"
    fi
}

# Test 6: Packaging functionality
test_packaging() {
    log_info "Testing packaging functionality..."
    
    # Test packaging script loading
    if source "${SCRIPT_DIR}/package.sh" >/dev/null 2>&1; then
        record_test_result "package_script_loading" "PASS" "Packaging script loaded"
    else
        record_test_result "package_script_loading" "FAIL" "Failed to load packaging script"
        return 1
    fi
    
    # Test archive creation with mock data
    local temp_dir="/tmp/test_package_$$"
    local temp_content_dir="${temp_dir}/test_content"
    local temp_file="${temp_content_dir}/test_file.txt"
    local temp_archive="${temp_dir}/test.tar.gz"
    
    mkdir -p "$temp_content_dir"
    echo "test content" > "$temp_file"
    
    if create_archive "$temp_content_dir" "$temp_archive" "tar.gz" >/dev/null 2>&1; then
        if [[ -f "$temp_archive" ]]; then
            record_test_result "archive_creation" "PASS" "Archive creation works"
        else
            record_test_result "archive_creation" "FAIL" "Archive was not created"
        fi
    else
        record_test_result "archive_creation" "FAIL" "Archive creation failed"
    fi
    
    rm -rf "$temp_dir"
}

# Test 7: Upload functionality
test_upload() {
    log_info "Testing upload functionality..."
    
    # Test upload script loading
    if source "${SCRIPT_DIR}/upload.sh" >/dev/null 2>&1; then
        record_test_result "upload_script_loading" "PASS" "Upload script loaded"
    else
        record_test_result "upload_script_loading" "FAIL" "Failed to load upload script"
        return 1
    fi
    
    # Test upload path generation
    local test_upload_path
    test_upload_path=$(generate_upload_path "release" "all" "1.2.3" "qoder-test.tar.gz")
    
    if [[ -n "$test_upload_path" ]]; then
        record_test_result "upload_path_generation" "PASS" "Upload path generation works: $test_upload_path"
    else
        record_test_result "upload_path_generation" "FAIL" "Upload path generation failed"
    fi
}

# Test 8: Main build script validation
test_main_build_script() {
    log_info "Testing main build script..."
    
    # Test main build script loading
    if [[ -f "${SCRIPT_DIR}/build.sh" ]]; then
        record_test_result "main_script_exists" "PASS" "Main build script exists"
    else
        record_test_result "main_script_exists" "FAIL" "Main build script not found"
        return 1
    fi
    
    # Test help output
    local help_output
    help_output=$("${SCRIPT_DIR}/build.sh" --help 2>&1)
    local help_exit_code=$?
    
    if [[ $help_exit_code -eq 0 && "$help_output" =~ "Usage:" ]]; then
        record_test_result "main_script_help" "PASS" "Main script help works"
    else
        record_test_result "main_script_help" "FAIL" "Main script help failed (exit code: $help_exit_code)"
    fi
}

# Test 9: External script placeholders
test_external_scripts() {
    log_info "Testing external script placeholders..."
    
    local external_scripts=(
        "download_deps.sh"
        "build_binary.sh"
        "sign_binary.sh"
    )
    
    for script in "${external_scripts[@]}"; do
        local script_path="${SCRIPT_DIR}/${script}"
        
        if [[ -f "$script_path" ]]; then
            record_test_result "external_script_$script" "PASS" "$script exists"
            
            # Test if script is executable
            if [[ -x "$script_path" ]]; then
                record_test_result "external_script_exec_$script" "PASS" "$script is executable"
            else
                record_test_result "external_script_exec_$script" "FAIL" "$script is not executable"
            fi
        else
            record_test_result "external_script_$script" "FAIL" "$script not found"
        fi
    done
}

# Test 10: Integration test (dry run)
test_integration_dry_run() {
    log_info "Testing integration (dry run)..."
    
    # This would test the entire build process without actually building
    # For now, we'll just test that all required scripts can be loaded together
    
    local all_scripts=(
        "config.sh"
        "utils.sh"
        "detect_env.sh"
        "version.sh"
        "cross_compile.sh"
        "package.sh"
        "upload.sh"
    )
    
    local integration_success=true
    for script in "${all_scripts[@]}"; do
        if ! source "${SCRIPT_DIR}/${script}" >/dev/null 2>&1; then
            integration_success=false
            break
        fi
    done
    
    if [[ "$integration_success" == true ]]; then
        record_test_result "integration_script_loading" "PASS" "All scripts load together successfully"
    else
        record_test_result "integration_script_loading" "FAIL" "Script integration failed"
    fi
}

# Generate test report
generate_test_report() {
    local test_end_time=$(date +%s)
    local test_duration=$((test_end_time - TEST_START_TIME))
    local total_tests=$((TESTS_PASSED + TESTS_FAILED + TESTS_SKIPPED))
    
    echo ""
    echo "================================================="
    echo "Build System Test Report"
    echo "================================================="
    echo "Test Date: $(date)"
    echo "Test Duration: ${test_duration}s"
    echo "Total Tests: $total_tests"
    echo "Passed: $TESTS_PASSED"
    echo "Failed: $TESTS_FAILED" 
    echo "Skipped: $TESTS_SKIPPED"
    echo ""
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo "Overall Result: ✓ ALL TESTS PASSED"
    else
        echo "Overall Result: ✗ SOME TESTS FAILED"
    fi
    
    echo ""
    echo "Detailed Results:"
    echo "-----------------"
    for result in "${TEST_RESULTS[@]}"; do
        echo "$result"
    done
    echo "================================================="
    
    # Write report to file
    local report_file="${LOG_DIR}/test_report_$(date +%Y%m%d_%H%M%S).txt"
    mkdir -p "$LOG_DIR"
    
    {
        echo "Build System Test Report"
        echo "========================"
        echo "Test Date: $(date)"
        echo "Test Duration: ${test_duration}s"
        echo "Total Tests: $total_tests"
        echo "Passed: $TESTS_PASSED"
        echo "Failed: $TESTS_FAILED"
        echo "Skipped: $TESTS_SKIPPED"
        echo ""
        echo "Detailed Results:"
        for result in "${TEST_RESULTS[@]}"; do
            echo "$result"
        done
    } > "$report_file"
    
    log_info "Test report saved to: $report_file"
}

# Main test function
main() {
    local test_pattern="${1:-.*}"  # Test pattern (regex)
    
    log_info "Starting build system tests..."
    log_info "Test pattern: $test_pattern"
    
    # Run all tests
    local test_functions=(
        "test_configuration"
        "test_utilities"
        "test_environment_detection"
        "test_version_management" 
        "test_cross_compilation"
        "test_packaging"
        "test_upload"
        "test_main_build_script"
        "test_external_scripts"
        "test_integration_dry_run"
    )
    
    for test_func in "${test_functions[@]}"; do
        if [[ "$test_func" =~ $test_pattern ]]; then
            log_info "Running $test_func..."
            $test_func
            echo ""
        fi
    done
    
    # Generate test report
    generate_test_report
    
    # Return appropriate exit code
    if [[ $TESTS_FAILED -eq 0 ]]; then
        log_success "All tests passed!"
        return 0
    else
        log_error "Some tests failed!"
        return 1
    fi
}

# Test specific build output directory
test_build_output() {
    local build_output_dir="$1"
    
    if [[ -z "$build_output_dir" ]]; then
        log_error "Usage: $0 test_output <build_output_directory>"
        return 1
    fi
    
    log_info "Testing build output in: $build_output_dir"
    
    # Test if build output directory exists
    if [[ -d "$build_output_dir" ]]; then
        record_test_result "build_output_exists" "PASS" "Build output directory exists"
    else
        record_test_result "build_output_exists" "FAIL" "Build output directory does not exist"
        generate_test_report
        return 1
    fi
    
    # Test for expected files
    local expected_files=("build_manifest.json")
    for file in "${expected_files[@]}"; do
        local file_path="${build_output_dir}/${file}"
        if [[ -f "$file_path" ]]; then
            record_test_result "expected_file_$file" "PASS" "$file exists"
        else
            record_test_result "expected_file_$file" "FAIL" "$file not found"
        fi
    done
    
    # Test for package files
    local package_count=0
    for package in "$build_output_dir"/*.{tar.gz,zip}; do
        if [[ -f "$package" ]]; then
            ((package_count++))
            local package_name=$(basename "$package")
            record_test_result "package_$package_name" "PASS" "Package exists: $package_name"
        fi
    done
    
    if [[ $package_count -gt 0 ]]; then
        record_test_result "package_generation" "PASS" "$package_count packages found"
    else
        record_test_result "package_generation" "FAIL" "No packages found"
    fi
    
    generate_test_report
    return $([[ $TESTS_FAILED -eq 0 ]] && echo 0 || echo 1)
}

# Handle different modes
case "${1:-test}" in
    "test")
        shift
        main "$@"
        ;;
    "test_output")
        shift
        test_build_output "$@"
        ;;
    *)
        echo "Usage: $0 [test|test_output] [options]"
        echo "  test [pattern]           Run all tests (optionally filter by pattern)"
        echo "  test_output <dir>        Test build output in specific directory"
        exit 1
        ;;
esac