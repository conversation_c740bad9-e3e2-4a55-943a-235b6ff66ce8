#!/bin/bash

# Build Configuration File
# This file contains all the configuration constants used across the build system

# Project Information
PROJECT_NAME="qoder"
BUILD_DIR="build"
DIST_DIR="dist"
ASSETS_DIR="assets"
BUILD_TARGET_PRODUCT="qoder"
BUILD_TARGET_FORM="ide"

# Version Configuration
VERSION_FILE="VERSION"
DEFAULT_VERSION="1.0.0"

# Platform Configuration
SUPPORTED_PLATFORMS=(
    "linux/amd64"
    "linux/arm64" 
    "darwin/amd64"
    "darwin/arm64"
    "windows/amd64"
    "windows/arm64"
)

# Qoder-specific platform mappings
QODER_PLATFORM_MAPPINGS=(
    "linux/amd64:x86_64_linux"
    "linux/arm64:aarch64_linux"
    "darwin/amd64:x86_64_darwin"
    "darwin/arm64:aarch64_darwin"
    "windows/amd64:x86_64_windows"
    "windows/arm64:aarch64_windows"
)

# Platform-specific binary names (function-based for compatibility)
get_binary_name() {
    local os="$1"
    case "$os" in
        "linux"|"darwin")
            echo "Qoder"
            ;;
        "windows")
            echo "Qoder.exe"
            ;;
        *)
            echo "Qoder"
            ;;
    esac
}

# Get qoder platform directory name
get_qoder_platform_dir() {
    local platform="$1"
    for mapping in "${QODER_PLATFORM_MAPPINGS[@]}"; do
        if [[ "$mapping" == "$platform:"* ]]; then
            echo "${mapping#*:}"
            return 0
        fi
    done
    # Fallback to standard format
    echo "${platform//\//_}"
}

# Archive formats for different platforms (function-based for compatibility)
get_archive_format() {
    # 统一使用 zip 格式
    echo "zip"
}

# Build Types
BUILD_TYPE_RELEASE="release"
BUILD_TYPE_DEV="dev"

# Build Tags Configuration
DEFAULT_BUILD_TAGS="ne,prod,qoder"
DEV_BUILD_TAGS="ne,dev,qoder"

# Qoder Build Configuration
QODER_CONFIG=(
    "SERVER_URL"
    "SERVER_HOST"
    "SERVER_PROXY"
    "COSY_VERSION"
    "COSY_BUILDING_TAGS"
    "COSY_ASSET_VERSION"
    "COSY_FEATURE_CONFIG"
    "COSY_TARGET_FORM"
)

# Cross-compilation settings for qoder
QODER_CROSS_COMPILE_SETTINGS=(
    "linux/amd64:CGO_ENABLED=1:CC=x86_64-linux-musl-gcc:CXX=x86_64-linux-musl-g++:LDFLAGS=-linkmode external -extldflags -static"
    "linux/arm64:CGO_ENABLED=1:CC=aarch64-linux-musl-gcc:CXX=aarch64-linux-musl-g++:LDFLAGS=-linkmode external -extldflags -static"
    "darwin/amd64:CGO_ENABLED=1:BUILDMODE=pie"
    "darwin/arm64:CGO_ENABLED=1"
    "windows/amd64:CGO_ENABLED=1:CC=zig cc -target x86_64-windows -Wno-dll-attribute-on-redeclaration:CXX=zig c++ -target x86_64-windows -Wno-dll-attribute-on-redeclaration:CGO_LDFLAGS=-static:BUILDMODE=pie"
    "windows/arm64:CGO_ENABLED=1:CC=zig cc -target aarch64-windows -Wno-dll-attribute-on-redeclaration:CXX=zig c++ -target aarch64-windows -Wno-dll-attribute-on-redeclaration:CGO_LDFLAGS=-static:BUILDMODE=pie"
)

# Edition Types
EDITION_CN="cn"
EDITION_INTL="intl"
EDITION_ALL="all"

# Git Configuration
GIT_TAG_PREFIX="v"

# Upload Configuration
# Default bucket can be overridden by OSS_BUCKET environment variable
OSS_BUCKET="${OSS_BUCKET:-qbuilder}"
OSS_BASE_PATH="oss://${OSS_BUCKET}/qoder"
UPLOAD_TIMEOUT="300"  # 5 minutes

# File Structure Templates
RELEASE_NAME_TEMPLATE="%s/[edition]/%s.%s/%s"  # release/[edition]/x.y.z/qoder.zip
DEV_NAME_TEMPLATE="%s/[edition]/%s.%s/qoder.%s"  # bugfix/[edition]/x.y.z/qoder.zip

# Compression Configuration  
COMPRESSION_LEVEL="9"
TAR_OPTIONS="-czf"
ZIP_OPTIONS="-9 -r"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Log Configuration
LOG_DIR="logs"
LOG_FILE="build.log"
ERROR_LOG_FILE="build_error.log"

# External Script Names (placeholders)
DOWNLOAD_DEPS_SCRIPT="download_deps.sh"
BUILD_BINARY_SCRIPT="build_binary.sh" 
SIGN_SCRIPT="sign_binary.sh"
NOTARIZE_SCRIPT="notarize_binary.sh"

# Environment Variables
export GOOS=""
export GOARCH=""
export CGO_ENABLED=0

# Qoder-specific environment variables
export BUILD_TARGET_PRODUCT="${BUILD_TARGET_PRODUCT}"
export BUILD_TARGET_FORM="${BUILD_TARGET_FORM}"

# Get cross-compilation settings for a platform
get_qoder_cross_compile_settings() {
    local platform="$1"
    for setting in "${QODER_CROSS_COMPILE_SETTINGS[@]}"; do
        if [[ "$setting" == "$platform:"* ]]; then
            echo "${setting#*:}"
            return 0
        fi
    done
    echo "CGO_ENABLED=0"  # Default fallback
}

# Parse qoder build configuration from environment
parse_qoder_config() {
    local config_str=""
    
    # Build ldflags from environment variables
    if [[ -n "$SERVER_URL" ]]; then
        config_str="$config_str -X main.serverUrl=$SERVER_URL"
    fi
    
    if [[ -n "$SERVER_HOST" ]]; then
        config_str="$config_str -X main.serverHost=$SERVER_HOST"
    fi
    
    if [[ -n "$SERVER_PROXY" ]]; then
        config_str="$config_str -X main.serverProxy=$SERVER_PROXY"
    fi
    
    if [[ -n "$COSY_ASSET_VERSION" ]]; then
        config_str="$config_str -X main.assertVersion=$COSY_ASSET_VERSION"
    fi
    
    if [[ -n "$COSY_FEATURE_CONFIG" ]]; then
        config_str="$config_str -X main.featureSwitchConfig=$COSY_FEATURE_CONFIG"
    fi
    
    config_str="$config_str -X main.productType=$BUILD_TARGET_PRODUCT"
    config_str="$config_str -X main.buildFormType=$BUILD_TARGET_FORM"
    
    echo "$config_str"
}

# Validation function for configuration
validate_config() {
    local errors=0
    
    # Check required directories
    if [[ ! -d "$BUILD_DIR" ]]; then
        mkdir -p "$BUILD_DIR"
    fi
    
    if [[ ! -d "$DIST_DIR" ]]; then
        mkdir -p "$DIST_DIR"
    fi
    
    if [[ ! -d "$LOG_DIR" ]]; then
        mkdir -p "$LOG_DIR"
    fi
    
    # Check for required external scripts (just warn, don't fail)
    local external_scripts=("$DOWNLOAD_DEPS_SCRIPT" "$BUILD_BINARY_SCRIPT" "$SIGN_SCRIPT")
    for script in "${external_scripts[@]}"; do
        if [[ ! -f "$script" ]]; then
            echo -e "${YELLOW}Warning: External script '$script' not found${NC}" >&2
        fi
    done
    
    # Check for cross-compilation tools
    check_cross_compile_tools
    
    return $errors
}

# Check cross-compilation tools availability
check_cross_compile_tools() {
    local tools_missing=0
    
    # Check for musl cross-compilers
    if ! command -v x86_64-linux-musl-gcc >/dev/null 2>&1; then
        echo -e "${YELLOW}Warning: x86_64-linux-musl-gcc not found${NC}" >&2
        tools_missing=1
    fi
    
    if ! command -v aarch64-linux-musl-gcc >/dev/null 2>&1; then
        echo -e "${YELLOW}Warning: aarch64-linux-musl-gcc not found${NC}" >&2
        tools_missing=1
    fi
    
    # Check for zig (for Windows cross-compilation)
    if ! command -v zig >/dev/null 2>&1; then
        echo -e "${YELLOW}Warning: zig compiler not found (needed for Windows cross-compilation)${NC}" >&2
        tools_missing=1
    fi
    
    if [[ $tools_missing -eq 1 ]]; then
        echo -e "${YELLOW}Note: Install missing tools with: brew install FiloSottile/musl-cross/musl-cross zig${NC}" >&2
    fi
    
    return 0
}