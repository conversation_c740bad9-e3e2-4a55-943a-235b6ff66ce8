#!/bin/bash

# Upload Script V2
# 上传统一打包格式的文件到 OSS

# Source utilities and version management
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/utils.sh"
source "${SCRIPT_DIR}/version.sh"
source "${SCRIPT_DIR}/config.sh"

# Upload unified package
upload_unified_package() {
    local edition="$1"
    local package_dir="$2"
    local build_type="$3"
    local version="$4"
    
    log_info "Starting unified package upload"
    log_info "Edition: $edition"
    log_info "Package directory: $package_dir"
    log_info "Build type: $build_type"
    log_info "Version: $version"
    
    # 规范化目录路径
    package_dir="${package_dir%/}"
    
    # 查找统一包文件
    local package_filename="qoder_${edition}_${version}.zip"
    local package_path="${package_dir}/${package_filename}"
    
    if [[ ! -f "$package_path" ]]; then
        log_error "Unified package not found: $package_path"
        log_info "Looking for package files in: $package_dir"
        ls -la "$package_dir"/*.zip 2>/dev/null || log_warning "No zip files found in $package_dir"
        return 1
    fi
    
    # 计算文件信息
    local file_size
    file_size=$(stat -c%s "$package_path" 2>/dev/null || stat -f%z "$package_path" 2>/dev/null)
    local file_hash
    file_hash=$(calculate_hash "$package_path" "sha256")
    
    log_info "Found package: $package_filename"
    log_info "  Size: $(numfmt --to=iec-i --suffix=B $file_size 2>/dev/null || echo "$file_size bytes")"
    log_info "  SHA256: $file_hash"
    
    # 生成 OSS 上传路径
    local remote_path
    remote_path=$(generate_oss_path "$build_type" "$edition" "$version" "$package_filename")
    
    log_info "Upload path: $remote_path"
    
    # 执行上传
    if upload_file_with_retry "$package_path" "$remote_path"; then
        log_success "Unified package uploaded successfully"
        
        # 创建并上传元数据
        create_and_upload_metadata "$package_dir" "$edition" "$build_type" "$version" "$package_filename" "$file_hash" "$file_size"
        
        return 0
    else
        log_error "Failed to upload unified package"
        return 1
    fi
}

# 生成 OSS 路径
generate_oss_path() {
    local build_type="$1"
    local edition="$2"
    local version="$3"
    local filename="$4"
    
    # 移除版本号前缀 'v'
    local version_num="${version#v}"
    
    # 构建基础路径
    local base_path="${OSS_BASE_PATH}"
    
    # 根据构建类型选择路径
    case "$build_type" in
        "release")
            echo "${base_path}/release/${edition}/${version_num}/${filename}"
            ;;
        "dev")
            echo "${base_path}/dev/${edition}/${version_num}/${filename}"
            ;;
        *)
            echo "${base_path}/unknown/${edition}/${version_num}/${filename}"
            ;;
    esac
}

# 带重试的文件上传
upload_file_with_retry() {
    local local_path="$1"
    local remote_path="$2"
    local retry_count="${3:-3}"
    
    local attempt=1
    while [[ $attempt -le $retry_count ]]; do
        log_info "Upload attempt $attempt of $retry_count"
        
        if upload_to_oss "$local_path" "$remote_path"; then
            log_success "Upload completed successfully on attempt $attempt"
            
            # 验证上传
            if verify_oss_file "$remote_path"; then
                log_success "Upload verification successful"
                return 0
            else
                log_warning "Upload verification failed, but upload appeared successful"
                return 0  # 继续，因为上传本身成功了
            fi
        else
            log_warning "Upload attempt $attempt failed"
            if [[ $attempt -lt $retry_count ]]; then
                local wait_time=$((attempt * 5))
                log_info "Waiting ${wait_time}s before retry..."
                sleep $wait_time
            fi
        fi
        
        ((attempt++))
    done
    
    log_error "Upload failed after $retry_count attempts"
    return 1
}

# 上传到 OSS
upload_to_oss() {
    local local_path="$1"
    local remote_path="$2"
    
    # 检查 ossutil
    if ! command -v ossutil &> /dev/null; then
        log_error "ossutil command not found, please install ossutil first"
        return 1
    fi
    
    log_info "Uploading to: $remote_path"
    
    # 构建 ossutil 命令
    local ossutil_cmd="ossutil cp -f"
    
    if [[ -n "$OSS_ACCESS_KEY_ID" && -n "$OSS_ACCESS_KEY_SECRET" ]]; then
        ossutil_cmd="$ossutil_cmd -i $OSS_ACCESS_KEY_ID -k $OSS_ACCESS_KEY_SECRET"
        log_info "Using provided access credentials for upload"
    else
        log_info "Using default/configured credentials for upload"
    fi
    
    # 执行上传
    if eval "$ossutil_cmd \"$local_path\" \"$remote_path\""; then
        log_info "Upload successful"
        return 0
    else
        log_error "Upload failed"
        return 1
    fi
}

# 验证 OSS 文件
verify_oss_file() {
    local remote_path="$1"
    
    log_info "Verifying remote file: $remote_path"
    
    if command -v ossutil &> /dev/null; then
        local ossutil_cmd="ossutil stat"
        
        if [[ -n "$OSS_ACCESS_KEY_ID" && -n "$OSS_ACCESS_KEY_SECRET" ]]; then
            ossutil_cmd="$ossutil_cmd -i $OSS_ACCESS_KEY_ID -k $OSS_ACCESS_KEY_SECRET"
        fi
        
        if eval "$ossutil_cmd \"$remote_path\"" > /dev/null 2>&1; then
            log_info "Remote file exists and is accessible"
            return 0
        else
            log_warning "Remote file verification failed or file not accessible"
            return 1
        fi
    else
        log_warning "ossutil not available, skipping remote verification"
        return 0
    fi
}

# 创建并上传元数据
create_and_upload_metadata() {
    local package_dir="$1"
    local edition="$2"
    local build_type="$3"
    local version="$4"
    local package_filename="$5"
    local file_hash="$6"
    local file_size="$7"
    
    log_info "Creating and uploading metadata..."
    
    # 创建 metadata.json
    local metadata_file="${package_dir}/metadata.json"
    cat > "$metadata_file" << EOF
{
  "package": {
    "filename": "${package_filename}",
    "sha256": "${file_hash}",
    "size": ${file_size},
    "edition": "${edition}",
    "version": "${version}",
    "build_type": "${build_type}",
    "upload_time": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
  },
  "platforms": [
    "darwin/amd64",
    "darwin/arm64",
    "linux/amd64",
    "linux/arm64",
    "windows/amd64",
    "windows/arm64"
  ],
  "download_url": "$(generate_oss_path "$build_type" "$edition" "$version" "$package_filename")"
}
EOF
    
    # 上传 metadata.json
    local metadata_remote_path
    metadata_remote_path=$(generate_oss_path "$build_type" "$edition" "$version" "metadata.json")
    
    if upload_to_oss "$metadata_file" "$metadata_remote_path"; then
        log_success "Metadata uploaded successfully"
    else
        log_warning "Failed to upload metadata (non-critical)"
    fi
    
    # 创建 latest 链接（仅用于 release）
    if [[ "$build_type" == "release" ]]; then
        create_latest_link "$edition" "$version" "$package_filename"
    fi
}

# 创建 latest 链接
create_latest_link() {
    local edition="$1"
    local version="$2"
    local package_filename="$3"
    
    log_info "Creating latest link for release..."
    
    # 创建 latest.json
    local latest_file="/tmp/latest_${edition}.json"
    cat > "$latest_file" << EOF
{
  "version": "${version}",
  "filename": "${package_filename}",
  "updated": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF
    
    # 上传到 latest 路径
    local latest_path="${OSS_BASE_PATH}/release/${edition}/latest.json"
    
    if upload_to_oss "$latest_file" "$latest_path"; then
        log_success "Latest link created successfully"
    else
        log_warning "Failed to create latest link (non-critical)"
    fi
    
    rm -f "$latest_file"
}

# 主函数
main() {
    local edition="${1:-all}"
    local package_dir="${2:-dist}"
    local build_type="${3:-dev}"
    local version="${4:-v0.0.0}"
    
    log_info "Starting unified package upload process"
    
    # 验证参数
    if [[ -z "$edition" || -z "$package_dir" || -z "$build_type" || -z "$version" ]]; then
        log_error "Usage: $0 <edition> <package_dir> <build_type> <version>"
        return 1
    fi
    
    if [[ ! -d "$package_dir" ]]; then
        log_error "Package directory does not exist: $package_dir"
        return 1
    fi
    
    # 执行上传
    if upload_unified_package "$edition" "$package_dir" "$build_type" "$version"; then
        log_success "Upload process completed successfully"
        
        # 输出上传摘要
        log_info "=========================================="
        log_info "Upload Summary:"
        log_info "  Edition: $edition"
        log_info "  Version: $version"
        log_info "  Build Type: $build_type"
        log_info "  Package: qoder_${edition}_${version}.zip"
        
        local oss_path
        oss_path=$(generate_oss_path "$build_type" "$edition" "$version" "qoder_${edition}_${version}.zip")
        log_info "  OSS Path: $oss_path"
        log_info "=========================================="
        
        return 0
    else
        log_error "Upload process failed"
        return 1
    fi
}

# 只在直接执行时运行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi