#!/bin/bash

# Version Management Script
# This script handles version naming for different build types and generates appropriate file names
# Based on XMind structure: release/[edition]/x.y.z and bugfix/[edition]/x.y.z

# Source utilities
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/utils.sh"

# Get version from file or environment
get_version() {
    local version=""
    
    # First check if COSY_VERSION is set
    if [[ -n "$COSY_VERSION" ]]; then
        version="$COSY_VERSION"
        echo "$version"
        return 0
    fi
    
    # Check for DEV_VERSION file first
    if [[ -f "DEV_VERSION" ]]; then
        version=$(cat DEV_VERSION | tr -d '\n\r')
        if [[ -n "$version" ]]; then
            echo "$version"
            return 0
        fi
    fi
    
    # Fall back to VERSION file
    if [[ -f "VERSION" ]]; then
        version=$(cat VERSION | tr -d '\n\r')
        if [[ -n "$version" ]]; then
            echo "$version"
            return 0
        fi
    fi
    
    # If no version file found, try to get from git
    if command -v git >/dev/null 2>&1 && git rev-parse --git-dir >/dev/null 2>&1; then
        # Try to get version from git tag
        version=$(git describe --tags --exact-match 2>/dev/null || echo "")
        if [[ -n "$version" ]]; then
            echo "$version"
            return 0
        fi
        
        # If no exact tag, create a dev version
        local latest_tag
        latest_tag=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0")
        local commit_count
        commit_count=$(git rev-list --count HEAD 2>/dev/null || echo "0")
        local commit_hash
        commit_hash=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
        
        version="${latest_tag}-dev.${commit_count}.${commit_hash}"
        echo "$version"
        return 0
    fi
    
    # Default fallback version
    echo "${DEFAULT_VERSION:-1.0.0}"
    return 0
}

# Parse version string into components
parse_version() {
    local version="$1"
    local clean_version
    
    # Remove 'v' prefix if present
    clean_version=$(echo "$version" | sed 's/^v//')
    
    # Extract major.minor.patch using regex
    if [[ "$clean_version" =~ ^([0-9]+)\.([0-9]+)\.([0-9]+)(.*)$ ]]; then
        export VERSION_MAJOR="${BASH_REMATCH[1]}"
        export VERSION_MINOR="${BASH_REMATCH[2]}"
        export VERSION_PATCH="${BASH_REMATCH[3]}"
        export VERSION_SUFFIX="${BASH_REMATCH[4]}"
        export VERSION_CLEAN="$clean_version"
        
        log_info "Version parsed: ${VERSION_MAJOR}.${VERSION_MINOR}.${VERSION_PATCH}${VERSION_SUFFIX}"
        return 0
    else
        log_error "Invalid version format: $version"
        return 1
    fi
}

# Generate version info based on build type
generate_version_info() {
    local build_type="$1"
    local base_version="$2"
    
    case "$build_type" in
        "$BUILD_TYPE_RELEASE")
            generate_release_version "$base_version"
            ;;
        "$BUILD_TYPE_DEV")
            generate_dev_version "$base_version"
            ;;
        *)
            log_error "Unknown build type: $build_type"
            return 1
            ;;
    esac
}

# Generate release version information
generate_release_version() {
    local tag_version="$1"
    
    log_info "Generating release version information for: $tag_version"
    
    if ! parse_version "$tag_version"; then
        return 1
    fi
    
    # For release builds, use the tag version as-is
    export BUILD_VERSION="$VERSION_CLEAN"
    export BUILD_VERSION_FULL="$tag_version"
    export IS_RELEASE=true
    export IS_PRERELEASE=false
    
    # Check if this is a prerelease (contains alpha, beta, rc, etc.)
    if [[ "$VERSION_SUFFIX" =~ -(alpha|beta|rc|pre) ]]; then
        export IS_PRERELEASE=true
        log_info "Detected prerelease version: $BUILD_VERSION"
    fi
    
    log_success "Release version: $BUILD_VERSION"
    return 0
}

# Generate development version information
generate_dev_version() {
    local dev_version="$1"
    
    log_info "Generating development version information for: $dev_version"
    
    # Dev version format: v1.2.3-dev.commits.hash (hash can be 'unknown')
    if [[ "$dev_version" =~ ^(.+)-dev\.([0-9]+)\.([a-f0-9]+|unknown)$ ]]; then
        local base_version="${BASH_REMATCH[1]}"
        local commit_count="${BASH_REMATCH[2]}"
        local commit_hash="${BASH_REMATCH[3]}"
        
        if ! parse_version "$base_version"; then
            return 1
        fi
        
        export BUILD_VERSION="${VERSION_CLEAN}-dev.${commit_count}.${commit_hash}"
        export BUILD_VERSION_FULL="$dev_version"
        export DEV_COMMIT_COUNT="$commit_count"
        export DEV_COMMIT_HASH="$commit_hash"
        export IS_RELEASE=false
        export IS_PRERELEASE=false
        
        log_success "Development version: $BUILD_VERSION"
        return 0
    else
        log_error "Invalid development version format: $dev_version"
        log_info "Expected format: vX.Y.Z-dev.N.HASH (where HASH can be 'unknown')"
        return 1
    fi
}

# Generate filename based on platform and build type
generate_filename() {
    local platform="$1"
    local edition="$2"
    local build_type="${3:-$BUILD_TYPE}"
    local version="${4:-$BUILD_VERSION}"
    
    local os arch
    read -r os arch <<< "$(extract_platform "$platform")"
    
    local archive_format
    archive_format=$(get_archive_format "$os")
    
    # Base filename
    local base_name="qoder"
    
    # Add platform suffix for specific builds
    if [[ "$platform" != "all" ]]; then
        base_name="${base_name}-${os}-${arch}"
    fi
    
    # Add edition suffix if not 'all'
    if [[ "$edition" != "$EDITION_ALL" ]]; then
        base_name="${base_name}-${edition}"
    fi
    
    # Add version
    base_name="${base_name}-${version}"
    
    # Add file extension
    local filename="${base_name}.${archive_format}"
    
    echo "$filename"
}

# Generate upload path based on build type and edition
generate_upload_path() {
    local build_type="$1"
    local edition="$2"
    local version="$3"
    local filename="$4"
    
    local base_path
    case "$build_type" in
        "$BUILD_TYPE_RELEASE")
            base_path="release"
            ;;
        "$BUILD_TYPE_DEV")
            base_path="dev"  # 使用dev路径而不是bugfix
            ;;
        *)
            log_error "Unknown build type for upload path: $build_type"
            return 1
            ;;
    esac
    
    # Clean version for path
    local clean_version
    clean_version=$(echo "$version" | sed 's/^v//')
    
    # Generate path: base_path/edition/version/filename
    local upload_path="${OSS_BASE_PATH}/${base_path}/${edition}/${clean_version}/${filename}"
    
    echo "$upload_path"
}

# Generate build manifest with version information
generate_build_manifest() {
    local output_dir="$1"
    local manifest_file="${output_dir}/build_manifest.json"
    
    log_info "Generating build manifest: $manifest_file"
    
    # Create manifest directory if it doesn't exist
    mkdir -p "$output_dir"
    
    # Generate manifest content
    cat > "$manifest_file" << EOF
{
  "build_info": {
    "build_type": "${BUILD_TYPE}",
    "build_version": "${BUILD_VERSION}",
    "build_version_full": "${BUILD_VERSION_FULL}",
    "edition": "${EDITION}",
    "is_release": ${IS_RELEASE:-false},
    "is_prerelease": ${IS_PRERELEASE:-false},
    "build_timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
  },
  "version_info": {
    "major": "${VERSION_MAJOR}",
    "minor": "${VERSION_MINOR}",
    "patch": "${VERSION_PATCH}",
    "suffix": "${VERSION_SUFFIX}",
    "clean": "${VERSION_CLEAN}"
  },
  "git_info": {
    "branch": "${GIT_BRANCH}",
    "tag": "${GIT_TAG}",
    "commit": "${GIT_COMMIT}",
    "commit_short": "${GIT_SHORT_COMMIT}"
  },
  "dev_info": {
    "commit_count": "${DEV_COMMIT_COUNT:-0}",
    "commit_hash": "${DEV_COMMIT_HASH:-""}"
  }
}
EOF
    
    if [[ -f "$manifest_file" ]]; then
        log_success "Build manifest created: $manifest_file"
        return 0
    else
        log_error "Failed to create build manifest"
        return 1
    fi
}

# Generate checksums for all files in directory
generate_checksums() {
    local target_dir="$1"
    local checksum_file="${target_dir}/checksums.txt"
    
    log_info "Generating checksums for files in: $target_dir"
    
    if [[ ! -d "$target_dir" ]]; then
        log_error "Target directory does not exist: $target_dir"
        return 1
    fi
    
    # Generate checksums for all archive files
    local files_found=false
    
    > "$checksum_file"  # Clear the file
    
    for file in "$target_dir"/*.{tar.gz,zip}; do
        if [[ -f "$file" ]]; then
            local hash
            hash=$(calculate_hash "$file" "sha256")
            if [[ -n "$hash" ]]; then
                echo "$hash  $(basename "$file")" >> "$checksum_file"
                log_info "Checksum for $(basename "$file"): $hash"
                files_found=true
            fi
        fi
    done
    
    if [[ "$files_found" == true ]]; then
        log_success "Checksums generated: $checksum_file"
        return 0
    else
        log_warning "No archive files found for checksum generation"
        rm -f "$checksum_file"
        return 1
    fi
}

# Print version summary
print_version_summary() {
    echo "======================================"
    echo "Version Information Summary"
    echo "======================================"
    echo "Build Type:        ${BUILD_TYPE:-not set}"
    echo "Build Version:     ${BUILD_VERSION:-not set}"
    echo "Full Version:      ${BUILD_VERSION_FULL:-not set}"
    echo "Edition:           ${EDITION:-not set}"
    echo "Is Release:        ${IS_RELEASE:-false}"
    echo "Is Prerelease:     ${IS_PRERELEASE:-false}"
    echo ""
    echo "Version Components:"
    echo "  Major:           ${VERSION_MAJOR:-not set}"
    echo "  Minor:           ${VERSION_MINOR:-not set}"
    echo "  Patch:           ${VERSION_PATCH:-not set}"
    echo "  Suffix:          ${VERSION_SUFFIX:-none}"
    echo "  Clean:           ${VERSION_CLEAN:-not set}"
    echo ""
    if [[ "${BUILD_TYPE}" == "${BUILD_TYPE_DEV}" ]]; then
        echo "Development Info:"
        echo "  Commit Count:    ${DEV_COMMIT_COUNT:-0}"
        echo "  Commit Hash:     ${DEV_COMMIT_HASH:-not set}"
    fi
    echo "======================================"
}

# Main version management function
main() {
    local version_input="$1"
    local build_type_input="$2"
    
    if [[ -z "$version_input" ]]; then
        log_error "Usage: $0 <version> [build_type]"
        return 1
    fi
    
    # Use provided build type or fall back to environment variable
    local build_type="${build_type_input:-$BUILD_TYPE}"
    if [[ -z "$build_type" ]]; then
        log_error "Build type not specified and BUILD_TYPE not set"
        return 1
    fi
    
    # Generate version information
    if ! generate_version_info "$build_type" "$version_input"; then
        return 1
    fi
    
    log_success "Version management completed successfully"
    return 0
}

# Only run main if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
    print_version_summary
fi