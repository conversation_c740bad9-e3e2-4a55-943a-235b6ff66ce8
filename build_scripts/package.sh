#!/bin/bash

# Packaging Script  
# This script handles file compression, content verification, signing verification, and packaging
# Based on XMind flow: 文件检查 -> 内容检查 -> 签名检查 -> 打包压缩

# Source utilities and version management
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/utils.sh"
source "${SCRIPT_DIR}/version.sh"

# Windows DLL 下载函数
download_windows_dlls() {
    local target_dir="$1"
    local platform="$2"
    
    local os_type="${platform%%/*}"
    local arch_type="${platform##*/}"
    
    # 只处理 Windows 平台
    if [[ "$os_type" != "windows" ]]; then
        return 0
    fi
    
    log_info "📥 Downloading Windows DLL dependencies for $platform"
    
    # 检查是否为 ARM 架构
    if [[ "$arch_type" == "arm64" ]]; then
        log_info "🚧 Windows ARM64 DLL download skipped - ARM DLLs not yet available"
        log_info "    TODO: Add ARM64 DLL support when ARM DLLs are available in OSS"
        return 0
    fi
    
    # 只处理 AMD64/x86_64 架构
    if [[ "$arch_type" != "amd64" ]]; then
        log_warning "Unsupported Windows architecture for DLL download: $arch_type"
        return 0
    fi
    
    # OSS DLL 路径
    local oss_dll_path="oss://qbuilder/qoder/shared/winlib/"
    
    # 检查 ossutil
    if ! command -v ossutil &> /dev/null; then
        log_error "ossutil command not found, cannot download Windows DLLs"
        return 1
    fi
    
    # 构建 ossutil 命令
    local ossutil_cmd="ossutil cp -r -f"
    
    if [[ -n "$OSS_ACCESS_KEY_ID" && -n "$OSS_ACCESS_KEY_SECRET" ]]; then
        ossutil_cmd="$ossutil_cmd -i $OSS_ACCESS_KEY_ID -k $OSS_ACCESS_KEY_SECRET"
        log_info "Using provided access credentials for DLL download"
    else
        log_info "Using default/configured credentials for DLL download"
    fi
    
    # 下载 DLL 文件到目标目录
    log_info "Downloading DLLs from: $oss_dll_path"
    log_info "Target directory: $target_dir"
    
    if eval "$ossutil_cmd \"$oss_dll_path\" \"$target_dir/\""; then
        # 统计下载的文件
        local dll_count
        dll_count=$(find "$target_dir" -name "*.dll" | wc -l)
        log_success "✅ Downloaded $dll_count DLL files successfully"
        
        # 验证下载文件的完整性
        log_info "🔍 Verifying downloaded DLL file integrity..."
        local verification_failed=false
        local verified_count=0
        local corrupted_files=()
        
        # 列出并验证下载的 DLL 文件
        if [[ $dll_count -gt 0 ]]; then
            log_info "Verifying DLL files:"
            while IFS= read -r dll_file; do
                local dll_name
                dll_name=$(basename "$dll_file")
                log_info "  - Checking $dll_name..."
                
                # 检查文件是否存在且不为空
                if [[ ! -f "$dll_file" ]]; then
                    log_error "    File not found: $dll_file"
                    corrupted_files+=("$dll_name")
                    verification_failed=true
                    continue
                fi
                
                local file_size
                file_size=$(stat -c%s "$dll_file" 2>/dev/null || stat -f%z "$dll_file" 2>/dev/null)
                if [[ "$file_size" -eq 0 ]]; then
                    log_error "    File is empty: $dll_name"
                    corrupted_files+=("$dll_name")
                    verification_failed=true
                    continue
                fi
                
                # 计算文件哈希值用于完整性检查
                local file_hash
                if file_hash=$(calculate_hash "$dll_file" "sha256"); then
                    if [[ -n "$file_hash" ]]; then
                        log_info "    ✓ $dll_name (${file_size} bytes, SHA256: ${file_hash:0:16}...)"
                        ((verified_count++))
                    else
                        log_warning "    ? $dll_name (${file_size} bytes, hash calculation failed)"
                        ((verified_count++))  # 仍计为验证通过，只是没有哈希值
                    fi
                else
                    log_error "    ✗ $dll_name - hash calculation failed, possible corruption"
                    corrupted_files+=("$dll_name")
                    verification_failed=true
                fi
            done < <(find "$target_dir" -name "*.dll" | sort)
            
            # 验证结果总结
            if [[ "$verification_failed" == true ]]; then
                log_error "❌ File integrity verification failed for ${#corrupted_files[@]} files:"
                for file in "${corrupted_files[@]}"; do
                    log_error "  - $file"
                done
                log_warning "Continuing with packaging, but some DLL files may be corrupted"
                log_warning "Consider re-running the build to re-download the files"
            else
                log_success "✅ All $verified_count DLL files passed integrity verification"
            fi
        fi
        
        return 0
    else
        log_error "Failed to download Windows DLL dependencies"
        return 1
    fi
}

# Package single platform
package_platform() {
    local platform="$1"
    local edition="$2"
    local binary_dir="$3"
    local output_dir="$4"
    local build_version="$5"
    
    log_info "Starting packaging for platform: $platform, edition: $edition"
    
    # Extract OS and architecture
    local os arch
    read -r os arch <<< "$(extract_platform "$platform")"
    
    # Get platform-specific paths and names
    # Convert platform format: darwin/amd64 -> x86_64_darwin (to match build output)
    local platform_arch_name
    platform_arch_name=$(get_platform_arch_name "$platform")
    
    # Build output includes version directory
    local platform_binary_dir
    if [[ -n "$build_version" ]]; then
        platform_binary_dir="${binary_dir}/${build_version}/${platform_arch_name}"
    else
        # Fallback to old format for compatibility
        platform_binary_dir="${binary_dir}/${os}-${arch}"
    fi
    
    local binary_name
    binary_name=$(get_binary_name "$os")
    local binary_path="${platform_binary_dir}/${binary_name}"
    
    # Verify binary exists
    if [[ ! -f "$binary_path" ]]; then
        log_error "Binary not found: $binary_path"
        return 1
    fi
    
    # Step 1: File Verification (文件检查)
    if ! verify_files "$platform_binary_dir" "$binary_name"; then
        log_error "File verification failed for $platform"
        return 1
    fi
    
    # Step 2: Content Verification (内容检查)
    if ! verify_content "$binary_path" "$os"; then
        log_error "Content verification failed for $platform"
        return 1
    fi
    
    # Step 3: Signature Verification (签名检查) - 仅在签名启用时执行
    local enable_signing="${ENABLE_SIGNING:-false}"
    if [[ "$enable_signing" == "true" ]]; then
        if ! verify_signature "$binary_path" "$os"; then
            log_error "Signature verification failed for $platform"
            return 1
        fi
    else
        log_info "💨 Signature verification skipped (ENABLE_SIGNING=$enable_signing)"
    fi
    
    # Step 4: Create Package (打包压缩)
    if ! create_platform_package "$platform" "$edition" "$platform_binary_dir" "$output_dir" "$build_version"; then
        log_error "Package creation failed for $platform"
        return 1
    fi
    
    log_success "Platform packaging completed: $platform"
    return 0
}

# Verify files exist and have correct permissions
verify_files() {
    local binary_dir="$1"
    local binary_name="$2"
    
    log_info "Performing file verification..."
    
    local binary_path="${binary_dir}/${binary_name}"
    
    # Check if binary exists
    if [[ ! -f "$binary_path" ]]; then
        log_error "Binary file not found: $binary_path"
        return 1
    fi
    
    # Check file size (should not be empty)
    local file_size
    file_size=$(stat -c%s "$binary_path" 2>/dev/null || stat -f%z "$binary_path" 2>/dev/null)
    if [[ $file_size -eq 0 ]]; then
        log_error "Binary file is empty: $binary_path"
        return 1
    fi
    
    # Check if file is executable (on Unix systems)
    if [[ "$binary_name" != *.exe ]] && [[ ! -x "$binary_path" ]]; then
        log_warning "Binary is not executable, making it executable: $binary_path"
        chmod +x "$binary_path"
    fi
    
    log_success "File verification passed: $binary_path ($file_size bytes)"
    return 0
}

# Verify binary content and structure
verify_content() {
    local binary_path="$1"
    local os="$2"
    
    log_info "Performing content verification..."
    
    # Basic file type verification
    case "$os" in
        "linux"|"darwin")
            # Check if it's a valid ELF/Mach-O binary
            if command_exists "file"; then
                local file_type
                file_type=$(file "$binary_path")
                log_info "File type: $file_type"
                
                case "$os" in
                    "linux")
                        if [[ ! "$file_type" =~ ELF ]]; then
                            log_warning "Binary may not be a valid Linux ELF file"
                        fi
                        ;;
                    "darwin")
                        if [[ ! "$file_type" =~ "Mach-O" ]]; then
                            log_warning "Binary may not be a valid macOS Mach-O file"
                        fi
                        ;;
                esac
            fi
            ;;
        "windows")
            # Check if it's a valid PE file
            if command_exists "file"; then
                local file_type
                file_type=$(file "$binary_path")
                log_info "File type: $file_type"
                
                if [[ ! "$file_type" =~ "PE32" ]]; then
                    log_warning "Binary may not be a valid Windows PE file"
                fi
            fi
            ;;
    esac
    
    # Check for stripped symbols (optional verification)
    if command_exists "objdump" || command_exists "nm"; then
        log_info "Checking binary symbols..."
        # Additional symbol verification can be added here
    fi
    
    # Verify version information is embedded (if applicable)
    log_info "Verifying version information..."
    # This could check for embedded version strings in the binary
    
    log_success "Content verification completed"
    return 0
}

# Verify binary signature (for signed binaries)
verify_signature() {
    local binary_path="$1"
    local os="$2"
    
    log_info "Performing signature verification..."
    
    case "$os" in
        "darwin")
            if command_exists "codesign"; then
                log_info "Verifying macOS code signature..."
                if codesign -v "$binary_path" 2>/dev/null; then
                    log_success "macOS code signature is valid"
                else
                    log_warning "macOS binary is not signed or signature is invalid"
                    # For now, we'll continue even without signature
                fi
            else
                log_info "codesign not available, skipping macOS signature verification"
            fi
            ;;
        "windows")
            # Windows signature verification would go here
            # This would typically use signtool or similar
            log_info "Windows signature verification not implemented (placeholder)"
            ;;
        "linux")
            # Linux binaries typically don't have code signatures
            log_info "Linux binaries don't require signature verification"
            ;;
    esac
    
    log_success "Signature verification completed"
    return 0
}

# Create package for platform
create_platform_package() {
    local platform="$1"
    local edition="$2"
    local binary_dir="$3"
    local output_dir="$4"
    local build_version="$5"
    
    log_info "Creating package for platform: $platform"
    
    # Extract OS and architecture
    local os arch
    read -r os arch <<< "$(extract_platform "$platform")"
    
    # Generate filename
    local filename
    filename=$(generate_filename "$platform" "$edition" "$BUILD_TYPE" "$build_version")
    
    # Create temporary package directory
    local temp_dir="${BUILD_DIR}/tmp_package_${os}_${arch}_$$"
    local package_content_dir="${temp_dir}/qoder"
    
    mkdir -p "$package_content_dir"
    
    # Copy binary
    local binary_name
    binary_name=$(get_binary_name "$os")
    local binary_path="${binary_dir}/${binary_name}"
    
    cp "$binary_path" "$package_content_dir/"
    if [[ $? -ne 0 ]]; then
        log_error "Failed to copy binary to package directory"
        rm -rf "$temp_dir"
        return 1
    fi
    
    # 下载 Windows DLL 依赖文件
    if [[ "$os" == "windows" ]]; then
        if ! download_windows_dlls "$package_content_dir" "$platform"; then
            log_warning "Failed to download Windows DLL dependencies for $platform"
            # 不将此视为致命错误，继续打包过程
            log_warning "Continuing packaging without DLL dependencies"
        fi
    fi
    
    # Add additional files to package
    add_package_content "$package_content_dir" "$os" "$edition" "$build_version"
    
    # Create the archive
    local archive_format
    archive_format=$(get_archive_format "$os")
    
    # Normalize output path to prevent double slashes
    local normalized_output_dir="${output_dir%/}"  # Remove trailing slash if any
    local full_output_path="${normalized_output_dir}/${filename}"
    
    # Ensure output directory exists
    mkdir -p "$output_dir"
    
    # Create archive
    if ! create_archive "$package_content_dir" "$full_output_path" "$archive_format"; then
        log_error "Failed to create archive for $platform"
        rm -rf "$temp_dir"
        return 1
    fi
    
    # Cleanup temporary directory
    rm -rf "$temp_dir"
    
    # Verify created package
    if [[ -f "$full_output_path" ]]; then
        local package_size
        package_size=$(stat -c%s "$full_output_path" 2>/dev/null || stat -f%z "$full_output_path" 2>/dev/null)
        local package_hash
        package_hash=$(calculate_hash "$full_output_path" "sha256")
        
        log_success "Package created: $filename ($package_size bytes)"
        log_info "Package path: $full_output_path"
        log_info "Package SHA256: $package_hash"
        
        # Store package info for later use
        export "PACKAGE_${os}_${arch}=$full_output_path"
        export "PACKAGE_HASH_${os}_${arch}=$package_hash"
        
        return 0
    else
        log_error "Package file not found after creation: $full_output_path"
        return 1
    fi
}

# Add additional content to package
add_package_content() {
    local package_dir="$1"
    local os="$2"
    local edition="$3"
    local build_version="$4"
    
    log_info "Adding additional content to package..."
    
    # Create README file
    create_package_readme "$package_dir" "$os" "$edition" "$build_version"
    
    # Create VERSION file
    echo "$build_version" > "$package_dir/VERSION"
    
    # Add LICENSE file if it exists
    if [[ -f "LICENSE" ]]; then
        cp "LICENSE" "$package_dir/"
        log_info "Added LICENSE file to package"
    elif [[ -f "LICENSE.txt" ]]; then
        cp "LICENSE.txt" "$package_dir/"
        log_info "Added LICENSE.txt file to package"
    fi
    
    # Add CHANGELOG if it exists
    if [[ -f "CHANGELOG.md" ]]; then
        cp "CHANGELOG.md" "$package_dir/"
        log_info "Added CHANGELOG.md to package"
    fi
    
    # Add any additional platform-specific files
    case "$os" in
        "windows")
            # Add batch files or Windows-specific documentation
            create_windows_scripts "$package_dir"
            ;;
        "darwin")
            # Add macOS-specific files
            create_macos_files "$package_dir"
            ;;
        "linux")
            # Add Linux-specific files
            create_linux_files "$package_dir"
            ;;
    esac
    
    log_success "Additional content added to package"
}

# Create README file for package
create_package_readme() {
    local package_dir="$1"
    local os="$2"
    local edition="$3"
    local build_version="$4"
    
    local readme_file="${package_dir}/README.md"
    
    cat > "$readme_file" << EOF
# Qoder ${build_version}

## Package Information

- **Version**: ${build_version}
- **Edition**: ${edition}
- **Platform**: ${os}
- **Build Type**: ${BUILD_TYPE}
- **Build Date**: $(date -u +%Y-%m-%dT%H:%M:%SZ)

## Installation

### $(echo ${os} | sed 's/^./\U&/') Installation

EOF

    case "$os" in
        "windows")
            cat >> "$readme_file" << EOF
1. Extract the archive to your desired location
2. Run \`qoder.exe\` from the command line or double-click to start
3. Optionally, add the directory to your PATH environment variable

EOF
            ;;
        "darwin")
            cat >> "$readme_file" << EOF
1. Extract the archive to your desired location (e.g., /usr/local/bin)
2. Make the binary executable: \`chmod +x qoder\`
3. Run \`./qoder\` or add to PATH for global access

**Note**: On macOS, you may need to allow the app in Security & Privacy settings.

EOF
            ;;
        "linux")
            cat >> "$readme_file" << EOF
1. Extract the archive to your desired location (e.g., /usr/local/bin)
2. Make the binary executable: \`chmod +x qoder\`
3. Run \`./qoder\` or add to PATH for global access

EOF
            ;;
    esac
    
    cat >> "$readme_file" << EOF
## Usage

Run \`qoder --help\` to see available commands and options.

## Support

For support and documentation, please visit: [Project Repository](https://your-repo-url)

EOF
    
    log_info "Created README.md for package"
}

# Create Windows-specific scripts
create_windows_scripts() {
    local package_dir="$1"
    
    # Create a simple batch file for Windows users
    cat > "${package_dir}/run.bat" << 'EOF'
@echo off
REM Qoder Windows Launcher
echo Starting Qoder...
qoder.exe %*
EOF
    
    log_info "Created Windows batch script"
}

# Create macOS-specific files
create_macos_files() {
    local package_dir="$1"
    
    # Create a shell script for macOS
    cat > "${package_dir}/run.sh" << 'EOF'
#!/bin/bash
# Qoder macOS Launcher
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
exec "${DIR}/qoder" "$@"
EOF
    
    chmod +x "${package_dir}/run.sh"
    log_info "Created macOS launcher script"
}

# Create Linux-specific files
create_linux_files() {
    local package_dir="$1"
    
    # Create a shell script for Linux
    cat > "${package_dir}/run.sh" << 'EOF'
#!/bin/bash
# Qoder Linux Launcher
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
exec "${DIR}/qoder" "$@"
EOF
    
    chmod +x "${package_dir}/run.sh"
    log_info "Created Linux launcher script"
}

# Package all platforms
package_all_platforms() {
    local edition="$1"
    local binary_dir="$2"
    local output_dir="$3"
    local build_version="$4"
    local platforms_to_package=("${@:5}")
    
    # If no specific platforms provided, detect available binaries
    if [[ ${#platforms_to_package[@]} -eq 0 ]]; then
        platforms_to_package=()
        for platform in "${SUPPORTED_PLATFORMS[@]}"; do
            local os arch
            read -r os arch <<< "$(extract_platform "$platform")"
            local binary_name
            binary_name=$(get_binary_name "$os")
            # Use the same logic as in package_platform function
            local platform_arch_name
            platform_arch_name=$(get_platform_arch_name "$platform")
            local binary_path
            if [[ -n "$build_version" ]]; then
                binary_path="${binary_dir}/${build_version}/${platform_arch_name}/${binary_name}"
            else
                binary_path="${binary_dir}/${os}-${arch}/${binary_name}"
            fi
            
            if [[ -f "$binary_path" ]]; then
                platforms_to_package+=("$platform")
            fi
        done
    fi
    
    log_info "Starting packaging for ${#platforms_to_package[@]} platforms"
    log_info "Platforms: ${platforms_to_package[*]}"
    
    local failed_platforms=()
    local successful_platforms=()
    
    # Create output directory
    mkdir -p "$output_dir"
    
    # Package each platform
    for platform in "${platforms_to_package[@]}"; do
        log_info "Packaging platform: $platform"
        
        if package_platform "$platform" "$edition" "$binary_dir" "$output_dir" "$build_version"; then
            successful_platforms+=("$platform")
            log_success "✓ $platform packaging successful"
        else
            failed_platforms+=("$platform")
            log_error "✗ $platform packaging failed"
        fi
        
        echo "----------------------------------------"
    done
    
    # Generate checksums for all packages
    if [[ ${#successful_platforms[@]} -gt 0 ]]; then
        log_info "Generating checksums for packages..."
        generate_checksums "$output_dir"
    fi
    
    # Report packaging results
    log_info "Packaging completed"
    log_info "Successful platforms (${#successful_platforms[@]}): ${successful_platforms[*]}"
    
    if [[ ${#failed_platforms[@]} -gt 0 ]]; then
        log_warning "Failed platforms (${#failed_platforms[@]}): ${failed_platforms[*]}"
        return 1
    else
        log_success "All platforms packaged successfully"
        return 0
    fi
}

# Main packaging function
main() {
    local edition="${1:-$EDITION_ALL}"
    local binary_dir="${2:-$BUILD_DIR/binaries}"
    local output_dir="${3:-$BUILD_OUTPUT_DIR}"
    local build_version="${4:-$BUILD_VERSION}"
    shift 4
    local platforms=("$@")
    
    log_info "Starting packaging process"
    log_info "Edition: $edition"
    log_info "Binary directory: $binary_dir"
    log_info "Output directory: $output_dir"
    
    # Validate inputs
    if [[ ! -d "$binary_dir" ]]; then
        log_error "Binary directory does not exist: $binary_dir"
        return 1
    fi
    
    # Package platforms
    if package_all_platforms "$edition" "$binary_dir" "$output_dir" "$build_version" "${platforms[@]}"; then
        log_success "Packaging process completed successfully"
        return 0
    else
        log_error "Packaging process completed with errors"
        return 1
    fi
}

# Only run main if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi