#!/bin/bash

# Version Management Script V2
# Strict version calculation for release and development builds
# Release: Git tags with format qoder/vX.Y.Z
# Development: Branches with format qoder/bugfix/x.y.z or qoder/release/x.y.z

# Source utilities
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/utils.sh"

# Validate version format (x.y.z where x,y,z are numbers)
validate_version_format() {
    local version="$1"
    
    # Must be in format x.y.z where x, y, z are numbers
    if [[ "$version" =~ ^([0-9]+)\.([0-9]+)\.([0-9]+)$ ]]; then
        local major="${BASH_REMATCH[1]}"
        local minor="${BASH_REMATCH[2]}"
        local patch="${BASH_REMATCH[3]}"
        
        # Validate they are valid numbers (no leading zeros except for 0 itself)
        if [[ "$major" =~ ^(0|[1-9][0-9]*)$ ]] && \
           [[ "$minor" =~ ^(0|[1-9][0-9]*)$ ]] && \
           [[ "$patch" =~ ^(0|[1-9][0-9]*)$ ]]; then
            return 0
        fi
    fi
    
    return 1
}

# Extract version from tag (qoder/vX.Y.Z)
extract_version_from_tag() {
    local tag="$1"
    
    # Check if tag matches qoder/vX.Y.Z
    if [[ "$tag" =~ ^qoder/v([0-9]+\.[0-9]+\.[0-9]+)$ ]]; then
        local version="${BASH_REMATCH[1]}"
        if validate_version_format "$version"; then
            echo "$version"
            return 0
        fi
    fi
    
    return 1
}

# Extract version from branch (qoder/bugfix/x.y.z or qoder/release/x.y.z)
extract_version_from_branch() {
    local branch="$1"
    
    # Check if branch matches qoder/bugfix/x.y.z or qoder/release/x.y.z
    if [[ "$branch" =~ ^qoder/(bugfix|release)/([0-9]+\.[0-9]+\.[0-9]+)$ ]]; then
        local branch_type="${BASH_REMATCH[1]}"
        local version="${BASH_REMATCH[2]}"
        
        if validate_version_format "$version"; then
            echo "$version"
            return 0
        fi
    fi
    
    return 1
}

# Get current Git context (tag or branch)
get_git_context() {
    if ! command -v git >/dev/null 2>&1 || ! git rev-parse --git-dir >/dev/null 2>&1; then
        log_error "Not in a Git repository or Git not available"
        return 1
    fi
    
    # Check if we're on an exact tag
    local current_tag
    current_tag=$(git describe --tags --exact-match 2>/dev/null || echo "")
    
    if [[ -n "$current_tag" ]]; then
        echo "tag:$current_tag"
        return 0
    fi
    
    # Get current branch
    local current_branch
    current_branch=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "")
    
    if [[ -n "$current_branch" && "$current_branch" != "HEAD" ]]; then
        echo "branch:$current_branch"
        return 0
    fi
    
    # Detached HEAD or other state
    echo "unknown:"
    return 1
}

# Calculate version based on Git context
calculate_version() {
    local context
    context=$(get_git_context)
    
    if [[ -z "$context" ]]; then
        log_error "Failed to determine Git context"
        return 1
    fi
    
    local context_type="${context%%:*}"
    local context_value="${context#*:}"
    
    case "$context_type" in
        "tag")
            # Release version from tag
            local version
            if version=$(extract_version_from_tag "$context_value"); then
                export BUILD_TYPE="release"
                export BUILD_VERSION="${version}"  # Remove v prefix for release
                export VERSION_MAJOR="${version%%.*}"
                export VERSION_MINOR="${version#*.}"
                VERSION_MINOR="${VERSION_MINOR%%.*}"
                export VERSION_PATCH="${version##*.}"
                export IS_RELEASE=true
                export IS_DEV=false
                
                log_success "🏷️ Release version from tag '$context_value': ${version}"
                return 0
            else
                log_error "❌ Tag '$context_value' does not match required format"
                log_error ""
                log_error "📋 RELEASE TAG FORMAT REQUIREMENTS:"
                log_error "   Format: qoder/vX.Y.Z (where X.Y.Z are numbers without leading zeros)"
                log_error "   Valid examples: qoder/v1.0.0, qoder/v2.1.3, qoder/v0.1.0, qoder/v10.20.30"
                log_error "   Invalid examples: v1.0.0, qoder/v1.0, qoder/v01.0.0, qoder/v1.02.0"
                log_error ""
                log_error "🚫 BUILD ABORTED: Invalid release tag format"
                return 1
            fi
            ;;
            
        "branch")
            # Development version from branch
            local version
            if version=$(extract_version_from_branch "$context_value"); then
                # Get commit hash (required for dev version)
                local commit_hash
                commit_hash=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
                
                if [[ "$commit_hash" == "unknown" ]]; then
                    log_error "❌ Cannot determine commit hash for development build"
                    log_error "   Development builds require a valid Git commit hash"
                    log_error ""
                    log_error "🚫 BUILD ABORTED: No commit hash available for development version"
                    return 1
                fi
                
                # Get pipeline instance number for dev version (required for CI builds)
                local pipeline_inst_number
                
                # Try to get pipeline instance number from various sources
                if [[ -n "${PIPELINE_INST_NUMBER:-}" ]]; then
                    # Direct environment variable
                    pipeline_inst_number="$PIPELINE_INST_NUMBER"
                elif [[ -n "${CI_PIPELINE_IID:-}" ]]; then
                    # GitLab CI pipeline instance ID (internal ID)
                    pipeline_inst_number="$CI_PIPELINE_IID"
                elif [[ -n "${BUILD_NUMBER:-}" ]]; then
                    # Jenkins build number
                    pipeline_inst_number="$BUILD_NUMBER"
                elif [[ -n "${GITHUB_RUN_NUMBER:-}" ]]; then
                    # GitHub Actions run number (not run ID)
                    pipeline_inst_number="$GITHUB_RUN_NUMBER"
                else
                    # No pipeline instance number found - generate a default for local/test builds
                    log_warning "⚠️ No pipeline instance number found, generating default for local/test build"
                    
                    # For local builds, use timestamp or a default number
                    if [[ "${BUILD_ENVIRONMENT:-}" == "test" ]] || [[ "${CI:-}" != "true" ]]; then
                        # Use current timestamp (last 4 digits) as pipeline number for local builds
                        pipeline_inst_number=$(date +%s | tail -c 5)
                        log_info "🔧 Using timestamp-based pipeline number for local build: $pipeline_inst_number"
                    else
                        # In CI environment but no pipeline number - this is still an error
                        log_error "❌ Pipeline instance number not found for CI development build"
                        log_error ""
                        log_error "📋 DEVELOPMENT BUILD REQUIREMENTS:"
                        log_error "   Development builds in CI require a pipeline instance number to generate version numbers"
                        log_error "   Expected environment variables (in order of preference):"
                        log_error "     - PIPELINE_INST_NUMBER"
                        log_error "     - CI_PIPELINE_IID (GitLab CI pipeline internal ID)"
                        log_error "     - BUILD_NUMBER (Jenkins)"
                        log_error "     - GITHUB_RUN_NUMBER (GitHub Actions)"
                        log_error ""
                        log_error "💡 For local/test builds, set BUILD_ENVIRONMENT=test to use timestamp-based numbering"
                        log_error ""
                        log_error "🚫 BUILD ABORTED: No pipeline instance number available for CI development version"
                        return 1
                    fi
                fi
                
                # Validate pipeline instance number is numeric
                if [[ ! "$pipeline_inst_number" =~ ^[0-9]+$ ]]; then
                    log_error "❌ Invalid pipeline instance number format: '$pipeline_inst_number'"
                    log_error "   Pipeline instance number must be numeric"
                    log_error ""
                    log_error "🚫 BUILD ABORTED: Invalid pipeline instance number"
                    return 1
                fi
                
                export BUILD_TYPE="dev"
                export BUILD_VERSION="${version}.${commit_hash}_${pipeline_inst_number}"  # Format: x.y.z.$commitId_buildId
                export VERSION_MAJOR="${version%%.*}"
                export VERSION_MINOR="${version#*.}"
                VERSION_MINOR="${VERSION_MINOR%%.*}"
                export VERSION_PATCH="${version##*.}"
                export IS_RELEASE=false
                export IS_DEV=true
                export DEV_COMMIT_HASH="$commit_hash"
                export DEV_PIPELINE_INST_NUMBER="$pipeline_inst_number"
                
                log_success "🔧 Development version from branch '$context_value': ${version}.${commit_hash}_${pipeline_inst_number}"
                log_info "   Commit hash: $commit_hash"
                log_info "   Pipeline instance number: $pipeline_inst_number"
                return 0
            else
                log_error "❌ Branch '$context_value' does not match required format"
                log_error ""
                log_error "📋 DEVELOPMENT BRANCH FORMAT REQUIREMENTS:"
                log_error "   Format: qoder/bugfix/x.y.z or qoder/release/x.y.z (where x.y.z are numbers without leading zeros)"
                log_error "   Valid examples: qoder/bugfix/1.2.3, qoder/release/2.0.0, qoder/bugfix/0.1.5, qoder/release/10.20.30"
                log_error "   Invalid examples: bugfix/1.2.3, qoder/feature/1.2.3, qoder/bugfix-1.2.3, qoder/bugfix/1.2, main"
                log_error ""
                log_error "🚫 BUILD ABORTED: Invalid development branch format"
                return 1
            fi
            ;;
            
        *)
            log_error "❌ Cannot determine version: not on a valid tag or branch"
            log_error ""
            log_error "📋 VERSION FORMAT REQUIREMENTS:"
            log_error ""
            log_error "🏷️ For RELEASE builds - use Git tags:"
            log_error "   Format: qoder/vX.Y.Z (where X.Y.Z are numbers)"
            log_error "   Examples:"
            log_error "     git tag qoder/v1.0.0      # Creates release version 1.0.0"
            log_error "     git tag qoder/v2.1.3      # Creates release version 2.1.3"
            log_error "     git tag qoder/v0.1.0      # Creates release version 0.1.0"
            log_error ""
            log_error "🌿 For DEVELOPMENT builds - use Git branches:"
            log_error "   Format: qoder/bugfix/x.y.z or qoder/release/x.y.z (where x.y.z are numbers)"
            log_error "   Examples:"
            log_error "     git checkout -b qoder/bugfix/1.2.3    # Creates dev version like 1.2.3.a19e7c4a_89"
            log_error "     git checkout -b qoder/release/2.0.0   # Creates dev version like 2.0.0.b2f8d1c6_145"
            log_error "     git checkout -b qoder/bugfix/0.1.5    # Creates dev version like 0.1.5.c3a9e2b1_67"
            log_error ""
            log_error "📍 Current Git state:"
            log_error "   Branch: $(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')"
            log_error "   Tag: $(git describe --tags --exact-match 2>/dev/null || echo 'none')"
            log_error ""
            log_error "🚫 BUILD ABORTED: Invalid version environment"
            return 1
            ;;
    esac
}

# Get version (main entry point)
get_version() {
    # Allow override from environment
    if [[ -n "$COSY_VERSION" ]]; then
        log_info "Using version from COSY_VERSION environment variable: $COSY_VERSION"
        # Check if COSY_VERSION already has a prefix or is already clean
        local clean_version="$COSY_VERSION"
        if [[ "$COSY_VERSION" =~ ^qoder/v(.*)$ ]]; then
            # New qoder/v prefix format
            clean_version="${BASH_REMATCH[1]}"
            log_info "Removed qoder/v prefix from COSY_VERSION: $COSY_VERSION -> $clean_version"
        elif [[ "$COSY_VERSION" =~ ^[vq] ]]; then
            # Legacy prefix format
            clean_version=$(echo "$clean_version" | sed 's/^[vq]//')
            log_info "Removed legacy prefix from COSY_VERSION: $COSY_VERSION -> $clean_version"
        else
            # Already clean version
            log_info "COSY_VERSION is already clean: $clean_version"
        fi
        echo "$clean_version"
        return 0
    fi
    
    # Calculate version from Git
    if calculate_version; then
        echo "$BUILD_VERSION"
        return 0
    fi
    
    # No valid version found
    return 1
}

# Parse version string into components
parse_version() {
    local version="$1"
    local clean_version
    
    # Remove 'qoder/v' prefix if present, or legacy 'v'/'q' prefix for backward compatibility
    if [[ "$version" =~ ^qoder/v(.*)$ ]]; then
        clean_version="${BASH_REMATCH[1]}"
    else
        # Legacy format support
        clean_version=$(echo "$version" | sed 's/^[vq]//')
    fi
    
    # Extract major.minor.patch and suffix
    if [[ "$clean_version" =~ ^([0-9]+)\.([0-9]+)\.([0-9]+)(.*)$ ]]; then
        export VERSION_MAJOR="${BASH_REMATCH[1]}"
        export VERSION_MINOR="${BASH_REMATCH[2]}"
        export VERSION_PATCH="${BASH_REMATCH[3]}"
        export VERSION_SUFFIX="${BASH_REMATCH[4]}"
        export VERSION_CLEAN="${VERSION_MAJOR}.${VERSION_MINOR}.${VERSION_PATCH}"
        
        log_info "Version parsed: ${VERSION_MAJOR}.${VERSION_MINOR}.${VERSION_PATCH}${VERSION_SUFFIX}"
        return 0
    else
        log_error "Invalid version format: $version"
        return 1
    fi
}

# Generate filename based on platform and build type
generate_filename() {
    local platform="$1"
    local edition="$2"
    local build_type="${3:-$BUILD_TYPE}"
    local version="${4:-$BUILD_VERSION}"
    
    # Version is already clean (no v prefix)
    local clean_version="$version"
    
    local os arch
    read -r os arch <<< "$(extract_platform "$platform")"
    
    local archive_format
    archive_format=$(get_archive_format "$os")
    
    # Base filename
    local base_name="qoder"
    
    # Add platform suffix for specific builds
    if [[ "$platform" != "all" ]]; then
        base_name="${base_name}-${os}-${arch}"
    fi
    
    # Add edition suffix if not 'all'
    if [[ "$edition" != "$EDITION_ALL" ]]; then
        base_name="${base_name}-${edition}"
    fi
    
    # Add version
    base_name="${base_name}-${clean_version}"
    
    # Add file extension
    local filename="${base_name}.${archive_format}"
    
    echo "$filename"
}

# Generate upload path based on build type and edition
generate_upload_path() {
    local build_type="$1"
    local edition="$2"
    local version="$3"
    local filename="$4"
    
    # Version is already clean (no v prefix)
    local clean_version="$version"
    
    local base_path
    case "$build_type" in
        "release")
            base_path="release"
            ;;
        "dev")
            # Determine if it's bugfix or release from branch name
            local branch
            branch=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "")
            if [[ "$branch" =~ ^qoder/bugfix/ ]]; then
                base_path="bugfix"
            elif [[ "$branch" =~ ^qoder/release/ ]]; then
                base_path="release"
            else
                base_path="dev"
            fi
            ;;
        *)
            log_error "Unknown build type for upload path: $build_type"
            return 1
            ;;
    esac
    
    # Generate path: base_path/edition/version/filename
    local upload_path="${OSS_BASE_PATH}/${base_path}/${edition}/${clean_version}/${filename}"
    
    echo "$upload_path"
}

# Validate build environment for version requirements
validate_version_environment() {
    log_info "🔍 Validating version environment..."
    
    local context
    context=$(get_git_context)
    
    if [[ -z "$context" ]]; then
        log_error "❌ Version validation failed: Cannot determine Git context"
        print_version_requirements
        return 1
    fi
    
    local context_type="${context%%:*}"
    local context_value="${context#*:}"
    
    case "$context_type" in
        "tag")
            if extract_version_from_tag "$context_value" >/dev/null; then
                log_success "✅ Valid release tag: $context_value"
                return 0
            else
                log_error "❌ Invalid release tag format: $context_value"
                print_release_tag_requirements
                return 1
            fi
            ;;
            
        "branch")
            if extract_version_from_branch "$context_value" >/dev/null; then
                log_success "✅ Valid development branch: $context_value"
                return 0
            else
                log_error "❌ Invalid development branch format: $context_value"
                print_dev_branch_requirements
                return 1
            fi
            ;;
            
        *)
            log_error "❌ Not on a valid tag or branch for versioning"
            print_version_requirements
            return 1
            ;;
    esac
}

# Print comprehensive version requirements
print_version_requirements() {
    log_error ""
    log_error "📋 VERSION FORMAT REQUIREMENTS:"
    log_error ""
    log_error "🏷️ For RELEASE builds - use Git tags:"
    log_error "   Format: qoder/vX.Y.Z (where X.Y.Z are numbers without leading zeros)"
    log_error "   Examples:"
    log_error "     git tag qoder/v1.0.0      # Creates release version 1.0.0"
    log_error "     git tag qoder/v2.1.3      # Creates release version 2.1.3"
    log_error "     git tag qoder/v0.1.0      # Creates release version 0.1.0"
    log_error ""
    log_error "🌿 For DEVELOPMENT builds - use Git branches:"
    log_error "   Format: qoder/bugfix/x.y.z or qoder/release/x.y.z (where x.y.z are numbers without leading zeros)"
    log_error "   Examples:"
    log_error "     git checkout -b qoder/bugfix/1.2.3    # Creates dev version like 1.2.3.a19e7c4a_89"
    log_error "     git checkout -b qoder/release/2.0.0   # Creates dev version like 2.0.0.b2f8d1c6_145"
    log_error "     git checkout -b qoder/bugfix/0.1.5    # Creates dev version like 0.1.5.c3a9e2b1_67"
    log_error ""
    log_error "📍 Current Git state:"
    log_error "   Branch: $(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')"
    log_error "   Tag: $(git describe --tags --exact-match 2>/dev/null || echo 'none')"
    log_error ""
    log_error "🚫 BUILD ABORTED: Invalid version environment"
}

# Print release tag requirements
print_release_tag_requirements() {
    log_error ""
    log_error "📋 RELEASE TAG FORMAT REQUIREMENTS:"
    log_error "   Format: qoder/vX.Y.Z (where X.Y.Z are numbers without leading zeros)"
    log_error "   Valid examples: qoder/v1.0.0, qoder/v2.1.3, qoder/v0.1.0, qoder/v10.20.30"
    log_error "   Invalid examples: v1.0.0, qoder/v1.0, qoder/v01.0.0, qoder/v1.02.0"
    log_error ""
    log_error "📍 Current tag: $(git describe --tags --exact-match 2>/dev/null || echo 'none')"
    log_error ""
    log_error "🚫 BUILD ABORTED: Invalid release tag format"
}

# Print development branch requirements  
print_dev_branch_requirements() {
    log_error ""
    log_error "📋 DEVELOPMENT BRANCH FORMAT REQUIREMENTS:"
    log_error "   Format: qoder/bugfix/x.y.z or qoder/release/x.y.z (where x.y.z are numbers without leading zeros)"
    log_error "   Valid examples: qoder/bugfix/1.2.3, qoder/release/2.0.0, qoder/bugfix/0.1.5, qoder/release/10.20.30"
    log_error "   Invalid examples: bugfix/1.2.3, qoder/feature/1.2.3, qoder/bugfix-1.2.3, qoder/bugfix/1.2, main"
    log_error ""
    log_error "📍 Current branch: $(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')"
    log_error ""
    log_error "🚫 BUILD ABORTED: Invalid development branch format"
}

# Export functions
export -f get_version
export -f calculate_version
export -f parse_version
export -f generate_filename
export -f generate_upload_path
export -f validate_version_environment
export -f validate_version_format