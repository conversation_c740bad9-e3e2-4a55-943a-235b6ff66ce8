#!/bin/bash

# Upload Script
# This script handles uploading packages to OSS/assets storage  
# Based on XMind structure: oss://assets_name/qoder/[release|bugfix]/[edition]/x.y.z/qoder.zip

# Source utilities and version management
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/utils.sh"
source "${SCRIPT_DIR}/version.sh"

# Upload single file to OSS
upload_file() {
    local local_path="$1"
    local remote_path="$2"
    local retry_count="${3:-3}"
    
    log_info "Uploading file: $(basename "$local_path") -> $remote_path"
    
    # Verify local file exists
    if [[ ! -f "$local_path" ]]; then
        log_error "Local file does not exist: $local_path"
        return 1
    fi
    
    # Get file size for logging
    local file_size
    file_size=$(stat -c%s "$local_path" 2>/dev/null || stat -f%z "$local_path" 2>/dev/null)
    log_info "File size: $file_size bytes"
    
    # Calculate file hash for verification
    local file_hash
    file_hash=$(calculate_hash "$local_path" "sha256")
    log_info "File SHA256: $file_hash"
    
    # Attempt upload with retries
    local attempt=1
    while [[ $attempt -le $retry_count ]]; do
        log_info "Upload attempt $attempt of $retry_count"
        
        # Upload using ossutil
        if real_oss_upload "$local_path" "$remote_path" "$OSS_ACCESS_KEY_ID" "$OSS_ACCESS_KEY_SECRET"; then
            log_success "Upload completed successfully on attempt $attempt"
            
            # Verify upload by checking remote file (if possible)
            if verify_remote_file "$remote_path" "$file_hash" "$OSS_ACCESS_KEY_ID" "$OSS_ACCESS_KEY_SECRET"; then
                log_success "Upload verification successful"
                return 0
            else
                log_warning "Upload verification failed, but upload appeared successful"
                return 0  # Continue anyway for now
            fi
        else
            log_warning "Upload attempt $attempt failed"
            if [[ $attempt -lt $retry_count ]]; then
                local wait_time=$((attempt * 5))
                log_info "Waiting ${wait_time}s before retry..."
                sleep $wait_time
            fi
        fi
        
        ((attempt++))
    done
    
    log_error "Upload failed after $retry_count attempts"
    return 1
}

# Upload file to OSS using ossutil
real_oss_upload() {
    local local_path="$1"
    local remote_path="$2"
    local access_key_id="$3"
    local access_key_secret="$4"
    
    # Check if ossutil is available
    if ! command -v ossutil &> /dev/null; then
        log_error "ossutil command not found, please install ossutil first"
        return 1
    fi
    
    log_info "Uploading to: $remote_path"
    
    # Build ossutil command with credentials if provided
    local ossutil_cmd="ossutil cp -f"
    
    if [[ -n "$access_key_id" && -n "$access_key_secret" ]]; then
        ossutil_cmd="$ossutil_cmd -i $access_key_id -k $access_key_secret"
        log_info "Using provided access credentials for upload"
    else
        log_info "Using default/configured credentials for upload"
    fi
    
    # Execute upload command
    if eval "$ossutil_cmd \"$local_path\" \"$remote_path\""; then
        log_info "Upload successful"
        return 0
    else
        log_error "Upload failed"
        return 1
    fi
}

# Verify remote file exists and matches hash
verify_remote_file() {
    local remote_path="$1"
    local expected_hash="$2"
    local access_key_id="$3"
    local access_key_secret="$4"
    
    log_info "Verifying remote file: $remote_path"
    
    # Use ossutil stat to check if file exists
    if command -v ossutil &> /dev/null; then
        local ossutil_cmd="ossutil stat"
        
        if [[ -n "$access_key_id" && -n "$access_key_secret" ]]; then
            ossutil_cmd="$ossutil_cmd -i $access_key_id -k $access_key_secret"
        fi
        
        if eval "$ossutil_cmd \"$remote_path\"" > /dev/null 2>&1; then
            log_info "Remote file exists and is accessible"
            return 0
        else
            log_warning "Remote file verification failed or file not accessible"
            return 1
        fi
    else
        log_warning "ossutil not available, skipping remote verification"
        return 0
    fi
}

# Upload package for single platform
upload_platform_package() {
    local platform="$1"
    local edition="$2"
    local package_dir="$3"
    local build_type="$4"
    local version="$5"
    
    log_info "Uploading package for platform: $platform"
    
    # Extract OS and architecture
    local os arch
    read -r os arch <<< "$(extract_platform "$platform")"
    
    # Generate filename
    local filename
    filename=$(generate_filename "$platform" "$edition" "$build_type" "$version")
    
    # Find package file
    local package_path="${package_dir}/${filename}"
    if [[ ! -f "$package_path" ]]; then
        log_error "Package file not found: $package_path"
        return 1
    fi
    
    # Generate upload path
    local upload_path
    upload_path=$(generate_upload_path "$build_type" "$edition" "$version" "$filename")
    if [[ -z "$upload_path" ]]; then
        log_error "Failed to generate upload path for $platform"
        return 1
    fi
    
    # Upload the package
    if upload_file "$package_path" "$upload_path"; then
        log_success "Package uploaded successfully: $platform"
        
        # Store upload info
        export "UPLOADED_${os}_${arch}=$upload_path"
        
        return 0
    else
        log_error "Failed to upload package for $platform"
        return 1
    fi
}

# Upload all packages in directory
upload_all_packages() {
    local edition="$1"
    local package_dir="$2"
    local build_type="$3"
    local version="$4"
    local platforms_to_upload=("${@:5}")
    
    log_info "Starting upload process for all packages"
    log_info "Edition: $edition"
    log_info "Package directory: $package_dir"
    log_info "Build type: $build_type"
    log_info "Version: $version"
    
    # Verify package directory exists
    if [[ ! -d "$package_dir" ]]; then
        log_error "Package directory does not exist: $package_dir"
        return 1
    fi
    
    # If no platforms specified, detect available packages
    if [[ ${#platforms_to_upload[@]} -eq 0 ]]; then
        platforms_to_upload=()
        for platform in "${SUPPORTED_PLATFORMS[@]}"; do
            local filename
            filename=$(generate_filename "$platform" "$edition" "$build_type" "$version")
            local package_path="${package_dir}/${filename}"
            
            if [[ -f "$package_path" ]]; then
                platforms_to_upload+=("$platform")
            fi
        done
    fi
    
    if [[ ${#platforms_to_upload[@]} -eq 0 ]]; then
        log_error "No packages found to upload in: $package_dir"
        return 1
    fi
    
    log_info "Found ${#platforms_to_upload[@]} packages to upload"
    
    # Upload metadata files first
    upload_metadata_files "$edition" "$package_dir" "$build_type" "$version"
    
    local failed_uploads=()
    local successful_uploads=()
    
    # Upload each platform package
    for platform in "${platforms_to_upload[@]}"; do
        log_info "Processing upload for platform: $platform"
        
        if upload_platform_package "$platform" "$edition" "$package_dir" "$build_type" "$version"; then
            successful_uploads+=("$platform")
            log_success "✓ $platform upload successful"
        else
            failed_uploads+=("$platform")
            log_error "✗ $platform upload failed"
        fi
        
        echo "----------------------------------------"
    done
    
    # Report upload results
    log_info "Upload process completed"
    log_info "Successful uploads (${#successful_uploads[@]}): ${successful_uploads[*]}"
    
    if [[ ${#failed_uploads[@]} -gt 0 ]]; then
        log_warning "Failed uploads (${#failed_uploads[@]}): ${failed_uploads[*]}"
        return 1
    else
        log_success "All packages uploaded successfully"
        return 0
    fi
}

# Upload metadata files (checksums, manifests, etc.)
upload_metadata_files() {
    local edition="$1"
    local package_dir="$2"
    local build_type="$3"
    local version="$4"
    
    log_info "Uploading metadata files..."
    
    # Upload checksums file
    local checksums_file="${package_dir}/checksums.txt"
    if [[ -f "$checksums_file" ]]; then
        local checksums_upload_path
        checksums_upload_path=$(generate_upload_path "$build_type" "$edition" "$version" "checksums.txt")
        
        if upload_file "$checksums_file" "$checksums_upload_path"; then
            log_success "Checksums file uploaded successfully"
        else
            log_warning "Failed to upload checksums file"
        fi
    fi
    
    # Upload build manifest if it exists
    local manifest_file="${package_dir}/build_manifest.json"
    if [[ -f "$manifest_file" ]]; then
        local manifest_upload_path
        manifest_upload_path=$(generate_upload_path "$build_type" "$edition" "$version" "build_manifest.json")
        
        if upload_file "$manifest_file" "$manifest_upload_path"; then
            log_success "Build manifest uploaded successfully"
        else
            log_warning "Failed to upload build manifest"
        fi
    fi
    
    # Create and upload index file
    create_and_upload_index "$edition" "$package_dir" "$build_type" "$version"
}

# Create and upload index file
create_and_upload_index() {
    local edition="$1"
    local package_dir="$2"
    local build_type="$3"
    local version="$4"
    
    local index_file="${package_dir}/index.json"
    
    log_info "Creating index file: $index_file"
    
    # Start JSON structure
    {
        echo "{"
        echo "  \"release_info\": {"
        echo "    \"version\": \"$version\","
        echo "    \"build_type\": \"$build_type\","
        echo "    \"edition\": \"$edition\","
        echo "    \"build_timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\","
        echo "    \"git_commit\": \"${GIT_COMMIT:-unknown}\","
        echo "    \"git_branch\": \"${GIT_BRANCH:-unknown}\""
        echo "  },"
        echo "  \"packages\": ["
        
        # Add package entries
        local first_entry=true
        for platform in "${SUPPORTED_PLATFORMS[@]}"; do
            local filename
            filename=$(generate_filename "$platform" "$edition" "$build_type" "$version")
            local package_path="${package_dir}/${filename}"
            
            if [[ -f "$package_path" ]]; then
                local os arch
                read -r os arch <<< "$(extract_platform "$platform")"
                
                local file_size
                file_size=$(stat -c%s "$package_path" 2>/dev/null || stat -f%z "$package_path" 2>/dev/null)
                local file_hash
                file_hash=$(calculate_hash "$package_path" "sha256")
                
                if [[ "$first_entry" != true ]]; then
                    echo ","
                fi
                
                echo "    {"
                echo "      \"platform\": \"$platform\","
                echo "      \"os\": \"$os\","
                echo "      \"arch\": \"$arch\","
                echo "      \"filename\": \"$filename\","
                echo "      \"size\": $file_size,"
                echo "      \"sha256\": \"$file_hash\""
                echo -n "    }"
                
                first_entry=false
            fi
        done
        
        echo ""
        echo "  ]"
        echo "}"
        
    } > "$index_file"
    
    if [[ -f "$index_file" ]]; then
        log_success "Index file created successfully"
        
        # Upload index file
        local index_upload_path
        index_upload_path=$(generate_upload_path "$build_type" "$edition" "$version" "index.json")
        
        if upload_file "$index_file" "$index_upload_path"; then
            log_success "Index file uploaded successfully"
            return 0
        else
            log_error "Failed to upload index file"
            return 1
        fi
    else
        log_error "Failed to create index file"
        return 1
    fi
}

# Generate upload summary
generate_upload_summary() {
    local package_dir="$1"
    local summary_file="${package_dir}/upload_summary.txt"
    
    log_info "Generating upload summary: $summary_file"
    
    {
        echo "Upload Summary"
        echo "=============="
        echo "Date: $(date)"
        echo "Build Type: ${BUILD_TYPE}"
        echo "Version: ${BUILD_VERSION}"
        echo "Edition: ${EDITION}"
        echo ""
        echo "Uploaded Files:"
        echo "---------------"
        
        # List uploaded packages
        for platform in "${SUPPORTED_PLATFORMS[@]}"; do
            local os arch
            read -r os arch <<< "$(extract_platform "$platform")"
            local upload_var="UPLOADED_${os}_${arch}"
            
            if [[ -n "${!upload_var}" ]]; then
                echo "✓ ${platform}: ${!upload_var}"
            fi
        done
        
        echo ""
        echo "Upload completed at: $(date)"
        
    } > "$summary_file"
    
    if [[ -f "$summary_file" ]]; then
        log_success "Upload summary created: $summary_file"
        
        # Display summary
        echo ""
        cat "$summary_file"
        
        return 0
    else
        log_error "Failed to create upload summary"
        return 1
    fi
}

# Main upload function
main() {
    local edition="${1:-$EDITION}"
    local package_dir="${2:-$BUILD_OUTPUT_DIR}"
    local build_type="${3:-$BUILD_TYPE}"
    local version="${4:-$BUILD_VERSION}"
    shift 4
    local platforms=("$@")
    
    log_info "Starting upload process"
    log_info "Edition: $edition"
    log_info "Package directory: $package_dir"
    log_info "Build type: $build_type"
    log_info "Version: $version"
    
    # Validate inputs
    if [[ -z "$edition" || -z "$package_dir" || -z "$build_type" || -z "$version" ]]; then
        log_error "Usage: $0 <edition> <package_dir> <build_type> <version> [platforms...]"
        log_error "Missing required parameters"
        return 1
    fi
    
    if [[ ! -d "$package_dir" ]]; then
        log_error "Package directory does not exist: $package_dir"
        return 1
    fi
    
    # Check for required tools (this would check for OSS CLI tools)
    # if ! command_exists "ossutil"; then
    #     log_error "OSS CLI tool not found. Please install ossutil."
    #     return 1
    # fi
    
    # Upload all packages
    local upload_result
    upload_all_packages "$edition" "$package_dir" "$build_type" "$version" "${platforms[@]}"
    upload_result=$?
    
    # Generate upload summary
    generate_upload_summary "$package_dir"
    
    if [[ $upload_result -eq 0 ]]; then
        log_success "Upload process completed successfully"
    else
        log_error "Upload process completed with errors"
    fi
    
    return $upload_result
}

# Only run main if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi