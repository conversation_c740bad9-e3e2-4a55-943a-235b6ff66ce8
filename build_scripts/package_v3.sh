#!/bin/bash

# Packaging Script V3
# 支持生成两种类型的压缩包：
# 1. 全平台包 - 包含所有平台的文件，保持目录结构，配置文件保持
# 2. 单平台包 - 每个操作系统单独的包，不包含其他平台文件，保持目录结构和配置文件
# 
# 压缩包命名：
# - 正式版：qoder-x.y.z.zip
# - 测试版：qoder-x.y.z.$commitId.zip

# Source utilities and version management
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/utils.sh"
source "${SCRIPT_DIR}/version.sh"
source "${SCRIPT_DIR}/config.sh"

# Windows DLL 下载函数
download_windows_dlls() {
    local target_dir="$1"
    local platform="$2"
    
    local os_type="${platform%%/*}"
    local arch_type="${platform##*/}"
    
    # 只处理 Windows 平台
    if [[ "$os_type" != "windows" ]]; then
        return 0
    fi
    
    log_info "📥 Downloading Windows DLL dependencies for $platform"
    
    # 检查是否为 ARM 架构
    if [[ "$arch_type" == "arm64" ]]; then
        log_info "🚧 Windows ARM64 DLL download skipped - ARM DLLs not yet available"
        log_info "    TODO: Add ARM64 DLL support when ARM DLLs are available in OSS"
        return 0
    fi
    
    # 只处理 AMD64/x86_64 架构
    if [[ "$arch_type" != "amd64" ]]; then
        log_warning "Unsupported Windows architecture for DLL download: $arch_type"
        return 0
    fi
    
    # OSS DLL 路径
    local oss_dll_path="oss://qbuilder/qoder/shared/winlib/"
    
    # 检查 ossutil
    if ! command -v ossutil &> /dev/null; then
        log_error "ossutil command not found, cannot download Windows DLLs"
        return 1
    fi
    
    # 构建 ossutil 命令
    local ossutil_cmd="ossutil cp -r -f"
    
    if [[ -n "$OSS_ACCESS_KEY_ID" && -n "$OSS_ACCESS_KEY_SECRET" ]]; then
        ossutil_cmd="$ossutil_cmd -i $OSS_ACCESS_KEY_ID -k $OSS_ACCESS_KEY_SECRET"
        log_info "Using provided access credentials for DLL download"
    else
        log_info "Using default/configured credentials for DLL download"
    fi
    
    # 下载 DLL 文件到目标目录
    log_info "Downloading DLLs from: $oss_dll_path"
    log_info "Target directory: $target_dir"
    
    if eval "$ossutil_cmd \"$oss_dll_path\" \"$target_dir/\""; then
        # 统计下载的文件
        local dll_count
        dll_count=$(find "$target_dir" -name "*.dll" | wc -l)
        log_success "✅ Downloaded $dll_count DLL files successfully"
        return 0
    else
        log_error "Failed to download Windows DLL dependencies"
        return 1
    fi
}

# 生成全平台压缩包
create_all_platforms_package() {
    local edition="$1"
    local binary_dir="$2"
    local output_dir="$3"
    local build_version="$4"
    shift 4
    local platforms=("$@")
    
    log_info "📦 Creating all-platforms package"
    log_info "Edition: $edition"
    log_info "Version: $build_version"
    log_info "Platforms: ${platforms[*]}"
    
    # 创建临时打包目录
    local temp_dir="${BUILD_DIR}/all_platforms_package_$$"
    local package_root="${temp_dir}/qoder"
    local version_dir="${package_root}/${build_version}"
    
    mkdir -p "$version_dir"
    
    # 复制各平台二进制文件
    local failed_platforms=()
    local successful_platforms=()
    
    for platform in "${platforms[@]}"; do
        log_info "⚙️ Processing platform: $platform"
        
        local platform_arch_name
        platform_arch_name=$(get_platform_arch_name "$platform")
        
        # 源二进制路径
        local source_binary_dir="${binary_dir}/${build_version}/${platform_arch_name}"
        
        # 如果版本目录不存在，尝试旧格式
        if [[ ! -d "$source_binary_dir" ]]; then
            local os arch
            read -r os arch <<< "$(extract_platform "$platform")"
            source_binary_dir="${binary_dir}/${os}-${arch}"
        fi
        
        local binary_name
        binary_name=$(get_binary_name "$(echo "$platform" | cut -d'/' -f1)")
        local binary_path="${source_binary_dir}/${binary_name}"
        
        if [[ ! -f "$binary_path" ]]; then
            log_warning "Binary not found for $platform at $binary_path"
            failed_platforms+=("$platform")
            continue
        fi
        
        # 创建平台目录并复制二进制文件
        local target_dir="${version_dir}/${platform_arch_name}"
        mkdir -p "$target_dir"
        
        cp "$binary_path" "$target_dir/"
        if [[ $? -ne 0 ]]; then
            log_error "Failed to copy binary for $platform"
            failed_platforms+=("$platform")
            continue
        fi
        
        # 下载 Windows DLL 依赖文件
        local os_type="${platform%%/*}"
        if [[ "$os_type" == "windows" ]]; then
            if ! download_windows_dlls "$target_dir" "$platform"; then
                log_warning "Failed to download Windows DLL dependencies for $platform"
                log_warning "Continuing packaging without DLL dependencies"
            fi
        fi
        
        successful_platforms+=("$platform")
        log_success "✅ Processed $platform"
    done
    
    if [[ ${#successful_platforms[@]} -eq 0 ]]; then
        log_error "No platforms were successfully processed"
        rm -rf "$temp_dir"
        return 1
    fi
    
    # 创建 extension 目录（占位）
    mkdir -p "${version_dir}/extension"
    echo "Extension files would go here" > "${version_dir}/extension/README.txt"
    
    # 创建 config.json
    create_config_json "$package_root" "$edition" "$build_version"
    
    # 创建 env.json
    create_env_json "$package_root" "$build_version" "$edition"
    
    # 生成打包文件名
    local package_filename
    package_filename=$(generate_all_platforms_filename "$edition" "$build_version")
    
    # output_dir 在 main 函数中已经转换为绝对路径
    mkdir -p "$output_dir"
    local package_path="${output_dir}/${package_filename}"
    
    # 创建 zip 包
    log_info "🗜️ Creating all-platforms zip archive: $package_filename"
    
    (cd "$temp_dir" && zip -r "$package_path" "qoder" -q)
    
    if [[ $? -ne 0 ]]; then
        log_error "Failed to create all-platforms zip archive"
        rm -rf "$temp_dir"
        return 1
    fi
    
    # 清理临时目录
    rm -rf "$temp_dir"
    
    # 验证生成的包
    if [[ -f "$package_path" ]]; then
        local package_size
        package_size=$(stat -c%s "$package_path" 2>/dev/null || stat -f%z "$package_path" 2>/dev/null)
        local package_hash
        package_hash=$(calculate_hash "$package_path" "sha256")
        
        log_success "🎉 All-platforms package created successfully"
        log_info "  File: $package_filename"
        log_info "  Size: $(numfmt --to=iec-i --suffix=B $package_size 2>/dev/null || echo "$package_size bytes")"
        log_info "  SHA256: $package_hash"
        log_info "  Platforms: ${successful_platforms[*]}"
        
        # 导出包信息
        export ALL_PLATFORMS_PACKAGE_PATH="$package_path"
        export ALL_PLATFORMS_PACKAGE_HASH="$package_hash"
        
        # 报告失败的平台
        if [[ ${#failed_platforms[@]} -gt 0 ]]; then
            log_warning "Failed platforms: ${failed_platforms[*]}"
        fi
        
        return 0
    else
        log_error "All-platforms package file not found after creation"
        return 1
    fi
}

# 生成单平台压缩包 - 每个操作系统+架构组合独立包
create_single_platform_packages() {
    local edition="$1"
    local binary_dir="$2"
    local output_dir="$3"
    local build_version="$4"
    shift 4
    local platforms=("$@")
    
    log_info "📦 Creating single-platform packages (one per OS+arch combination)"
    log_info "Edition: $edition"
    log_info "Version: $build_version"
    log_info "Platforms: ${platforms[*]}"
    
    local failed_platforms=()
    local successful_platforms=()
    local package_paths=()
    
    for platform in "${platforms[@]}"; do
        log_info "⚙️ Creating package for platform: $platform"
        
        local platform_arch_name
        platform_arch_name=$(get_platform_arch_name "$platform")
        
        # 源二进制路径
        local source_binary_dir="${binary_dir}/${build_version}/${platform_arch_name}"
        
        # 如果版本目录不存在，尝试旧格式
        if [[ ! -d "$source_binary_dir" ]]; then
            local os arch
            read -r os arch <<< "$(extract_platform "$platform")"
            source_binary_dir="${binary_dir}/${os}-${arch}"
        fi
        
        local os_type="${platform%%/*}"
        local arch_type="${platform##*/}"
        local binary_name
        binary_name=$(get_binary_name "$os_type")
        local binary_path="${source_binary_dir}/${binary_name}"
        
        if [[ ! -f "$binary_path" ]]; then
            log_warning "Binary not found for $platform at $binary_path"
            failed_platforms+=("$platform")
            continue
        fi
        
        # 创建临时打包目录 - 使用平台+架构标识符
        local temp_dir="${BUILD_DIR}/single_platform_${os_type}_${arch_type}_$$"
        local package_root="${temp_dir}/qoder"
        local version_dir="${package_root}/${build_version}"
        local target_dir="${version_dir}/${platform_arch_name}"
        
        mkdir -p "$target_dir"
        
        # 复制二进制文件
        cp "$binary_path" "$target_dir/"
        if [[ $? -ne 0 ]]; then
            log_error "Failed to copy binary for $platform"
            failed_platforms+=("$platform")
            rm -rf "$temp_dir"
            continue
        fi
        
        # 下载 Windows DLL 依赖文件
        if [[ "$os_type" == "windows" ]]; then
            if ! download_windows_dlls "$target_dir" "$platform"; then
                log_warning "Failed to download Windows DLL dependencies for $platform"
                log_warning "Continuing packaging without DLL dependencies"
            fi
        fi
        
        # 创建 extension 目录（占位）
        mkdir -p "${version_dir}/extension"
        echo "Extension files would go here" > "${version_dir}/extension/README.txt"
        
        # 创建 config.json
        create_config_json "$package_root" "$edition" "$build_version"
        
        # 创建 env.json
        create_env_json "$package_root" "$build_version" "$edition"
        
        # 生成打包文件名
        local package_filename
        package_filename=$(generate_single_platform_filename "$platform" "$edition" "$build_version")
        
        # output_dir 在 main 函数中已经转换为绝对路径
        mkdir -p "$output_dir"
        local package_path="${output_dir}/${package_filename}"
        
        # 创建 zip 包
        log_info "🗜️ Creating single-platform zip archive: $package_filename"
        
        (cd "$temp_dir" && zip -r "$package_path" "qoder" -q)
        
        if [[ $? -ne 0 ]]; then
            log_error "Failed to create single-platform zip archive for $platform"
            failed_platforms+=("$platform")
            rm -rf "$temp_dir"
            continue
        fi
        
        # 清理临时目录
        rm -rf "$temp_dir"
        
        # 验证生成的包
        if [[ -f "$package_path" ]]; then
            local package_size
            package_size=$(stat -c%s "$package_path" 2>/dev/null || stat -f%z "$package_path" 2>/dev/null)
            local package_hash
            package_hash=$(calculate_hash "$package_path" "sha256")
            
            log_success "✅ Single-platform package created for $platform"
            log_info "  File: $package_filename"
            log_info "  Size: $(numfmt --to=iec-i --suffix=B $package_size 2>/dev/null || echo "$package_size bytes")"
            log_info "  SHA256: $package_hash"
            
            successful_platforms+=("$platform")
            package_paths+=("$package_path")
            
            # 导出包信息 - 使用完整平台标识符
            local platform_var_name="${os_type}_${arch_type}"
            export "SINGLE_PLATFORM_PACKAGE_${platform_var_name}_PATH=$package_path"
            export "SINGLE_PLATFORM_PACKAGE_${platform_var_name}_HASH=$package_hash"
        else
            log_error "Single-platform package file not found after creation for $platform"
            failed_platforms+=("$platform")
        fi
    done
    
    # 汇总结果
    if [[ ${#successful_platforms[@]} -gt 0 ]]; then
        log_success "🎉 Single-platform packages created successfully"
        log_info "  Successful platforms: ${successful_platforms[*]}"
        log_info "  Created ${#successful_platforms[@]} packages"
        
        # 报告失败的平台
        if [[ ${#failed_platforms[@]} -gt 0 ]]; then
            log_warning "Failed platforms: ${failed_platforms[*]}"
            return 1
        fi
        
        return 0
    else
        log_error "No single-platform packages were created successfully"
        return 1
    fi
}

# 创建 config.json
create_config_json() {
    local package_dir="$1"
    local edition="$2"
    local version="$3"
    
    # 使用原有的简单格式，与历史脚本保持一致
    printf '{\n    "cosy.core.version": "%s"\n}' "$version" > "${package_dir}/config.json"
    
    # 验证生成的 config.json
    if ! validate_json "${package_dir}/config.json" "generated config.json"; then
        log_error "Failed to validate generated config.json"
        log_error "Build aborted due to invalid JSON generation"
        return 1
    fi
    
    log_info "Created config.json"
}

# 创建 env.json（从 conf 目录按版本选择）
create_env_json() {
    local package_dir="$1"
    local version="$2"
    local edition="${3:-all}"
    
    # 获取脚本目录
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local conf_dir="${script_dir}/conf"
    
    # 按版本选择 env.json，优先级：版本特定 -> 版本类型特定 -> 默认
    local env_source=""
    
    # 1. 先尝试版本特定的配置（如 conf/intl/env.json）
    if [[ "$edition" != "all" && -f "${conf_dir}/${edition}/env.json" ]]; then
        env_source="${conf_dir}/${edition}/env.json"
        log_info "Using edition-specific env.json: ${edition}"
    # 2. 使用默认配置
    elif [[ -f "${conf_dir}/env.json" ]]; then
        env_source="${conf_dir}/env.json"
        log_info "Using default env.json"
    else
        log_error "No env.json configuration found in ${conf_dir}"
        return 1
    fi
    
    # 验证选择的 JSON 配置文件
    if ! validate_json "$env_source" "env.json configuration"; then
        log_error "Failed to validate env.json: $env_source"
        log_error "Build aborted due to invalid JSON configuration"
        return 1
    fi
    
    # 复制选择的配置文件
    cp "$env_source" "${package_dir}/env.json"
    
    if [[ $? -eq 0 ]]; then
        log_info "Created env.json from: $env_source"
    else
        log_error "Failed to copy env.json from: $env_source"
        return 1
    fi
}

# 生成全平台包文件名
generate_all_platforms_filename() {
    local edition="$1"
    local version="$2"
    
    # 根据版本类型生成文件名
    if [[ "$BUILD_TYPE" == "release" ]]; then
        # 正式版：qoder-x.y.z.zip
        echo "qoder-${version}.zip"
    else
        # 测试版：qoder-x.y.z.$commitId.zip
        # 从 x.y.z.commitId_buildId 格式中提取 commitId
        if [[ "$version" =~ ^([0-9]+\.[0-9]+\.[0-9]+)\.([^_]+)_.*$ ]]; then
            local base_version="${BASH_REMATCH[1]}"
            local commit_id="${BASH_REMATCH[2]}"
            echo "qoder-${base_version}.${commit_id}.zip"
        else
            # 兜底方案，使用完整版本
            echo "qoder-${version}.zip"
        fi
    fi
}

# 生成单平台包文件名 - 使用完整版本号和下划线分隔符
generate_single_platform_filename() {
    local platform="$1"
    local edition="$2"
    local version="$3"
    
    local os_type="${platform%%/*}"
    local arch_type="${platform##*/}"
    
    # 根据版本类型生成文件名
    if [[ "$BUILD_TYPE" == "release" ]]; then
        # 正式版：qoder-x.y.z_os_arch.zip
        echo "qoder-${version}_${os_type}_${arch_type}.zip"
    else
        # 测试版：qoder-x.y.z.$commitId_buildId_os_arch.zip
        # 使用完整版本号，包含 commitId 和 buildId
        echo "qoder-${version}_${os_type}_${arch_type}.zip"
    fi
}

# 主函数
main() {
    local edition="${1:-all}"
    local binary_dir="${2:-dist}"
    local output_dir="${3:-dist}"
    local build_version="${4:-v0.0.0}"
    shift 4
    local platforms=("$@")
    
    log_info "🎯 Starting V3 packaging process"
    log_info "Edition: $edition"
    log_info "Binary directory: $binary_dir"
    log_info "Output directory: $output_dir"
    log_info "Build version: $build_version"
    
    # 转换为绝对路径
    binary_dir="$(cd "$binary_dir" 2>/dev/null && pwd || echo "$binary_dir")"
    output_dir="$(mkdir -p "$output_dir" && cd "$output_dir" && pwd)"
    
    # 验证输入
    if [[ ! -d "$binary_dir" ]]; then
        log_error "Binary directory does not exist: $binary_dir"
        return 1
    fi
    
    # 检测实际可用的平台
    local requested_platforms=("${platforms[@]}")
    platforms=()
    
    # 如果没有指定平台，检测所有支持的平台
    if [[ ${#requested_platforms[@]} -eq 0 ]]; then
        requested_platforms=("${SUPPORTED_PLATFORMS[@]}")
    fi
    
    log_info "🔍 Checking for available binaries among requested platforms: ${requested_platforms[*]}"
    
    for platform in "${requested_platforms[@]}"; do
        local platform_arch_name
        platform_arch_name=$(get_platform_arch_name "$platform")
        
        # 检查两种可能的路径格式
        local check_path1="${binary_dir}/${build_version}/${platform_arch_name}"
        local os arch
        read -r os arch <<< "$(extract_platform "$platform")"
        local check_path2="${binary_dir}/${os}-${arch}"
        
        local binary_name
        binary_name=$(get_binary_name "$os")
        
        if [[ -f "${check_path1}/${binary_name}" ]] || [[ -f "${check_path2}/${binary_name}" ]]; then
            platforms+=("$platform")
            log_info "✅ Found binary for $platform"
        else
            log_warning "✗ No binary found for $platform (skipping)"
        fi
    done
    
    if [[ ${#platforms[@]} -eq 0 ]]; then
        log_error "No platform binaries found"
        return 1
    fi
    
    log_info "📦 Will package ${#platforms[@]} platforms: ${platforms[*]}"
    
    local success=0
    local all_platforms_success=0
    local single_platforms_success=0
    
    # 创建全平台包
    log_info "========================================="
    log_info "Creating All-Platforms Package"
    log_info "========================================="
    
    if create_all_platforms_package "$edition" "$binary_dir" "$output_dir" "$build_version" "${platforms[@]}"; then
        log_success "✅ All-platforms package created successfully"
        all_platforms_success=1
    else
        log_error "❌ All-platforms package creation failed"
    fi
    
    # 创建单平台包
    log_info "========================================="
    log_info "Creating Single-Platform Packages"
    log_info "========================================="
    
    if create_single_platform_packages "$edition" "$binary_dir" "$output_dir" "$build_version" "${platforms[@]}"; then
        log_success "✅ Single-platform packages created successfully"
        single_platforms_success=1
    else
        log_error "❌ Single-platform packages creation failed"
    fi
    
    # 最终结果
    log_info "========================================="
    log_info "Packaging Summary"
    log_info "========================================="
    
    if [[ $all_platforms_success -eq 1 && $single_platforms_success -eq 1 ]]; then
        log_success "🎆 V3 packaging completed successfully"
        log_info "✅ All-platforms package: Created"
        log_info "✅ Single-platform packages: Created"
        return 0
    elif [[ $all_platforms_success -eq 1 || $single_platforms_success -eq 1 ]]; then
        log_warning "⚠️ V3 packaging completed with partial success"
        log_info "$([[ $all_platforms_success -eq 1 ]] && echo "✅" || echo "❌") All-platforms package: $([[ $all_platforms_success -eq 1 ]] && echo "Created" || echo "Failed")"
        log_info "$([[ $single_platforms_success -eq 1 ]] && echo "✅" || echo "❌") Single-platform packages: $([[ $single_platforms_success -eq 1 ]] && echo "Created" || echo "Failed")"
        return 1
    else
        log_error "❌ V3 packaging failed completely"
        log_error "❌ All-platforms package: Failed"
        log_error "❌ Single-platform packages: Failed"
        return 1
    fi
}

# 只在直接执行时运行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi