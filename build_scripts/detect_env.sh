#!/bin/bash

# Environment Detection Script
# This script implements the core logic to distinguish between release and development builds
# Based on the XMind analysis: release builds use tags, dev builds use bugfix branches

# Source utilities
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/utils.sh"

# Detect current Git environment
detect_git_environment() {
    local current_branch
    local current_tag
    local build_type
    local version_info
    
    log_info "Detecting Git environment..."
    
    # Check if we're in a Git repository
    if ! git rev-parse --git-dir >/dev/null 2>&1; then
        log_error "Not in a Git repository"
        return 1
    fi
    
    # Get current branch
    current_branch=$(git rev-parse --abbrev-ref HEAD 2>/dev/null)
    log_info "Current branch: $current_branch"
    
    # Check if we're on a tag
    current_tag=$(git describe --exact-match --tags HEAD 2>/dev/null)
    
    if [[ -n "$current_tag" ]]; then
        # We're on a tag - this is a release build
        log_info "Current tag: $current_tag"
        build_type="$BUILD_TYPE_RELEASE"
        version_info="$current_tag"
        
        # Validate tag format (should start with 'v' followed by version)
        if [[ "$current_tag" =~ ^v[0-9]+\.[0-9]+\.[0-9]+ ]]; then
            log_success "Valid release tag detected: $current_tag"
        else
            log_warning "Tag format may not be standard: $current_tag"
        fi
        
    else
        # We're not on a tag - this is a development build
        build_type="$BUILD_TYPE_DEV"
        
        # Get version from latest tag + commit info
        local latest_tag
        latest_tag=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0")
        local commit_hash
        commit_hash=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
        local commit_count
        commit_count=$(git rev-list --count HEAD 2>/dev/null || echo "1")
        
        # Ensure we have valid values
        if [[ "$commit_hash" == "unknown" || -z "$commit_hash" ]]; then
            commit_hash="unknown"
        fi
        
        if [[ "$commit_count" == "0" || -z "$commit_count" ]]; then
            commit_count="1"
        fi
        
        version_info="${latest_tag}-dev.${commit_count}.${commit_hash}"
        
        log_info "Development build detected"
        log_info "Latest tag: $latest_tag"
        log_info "Commits since tag: $commit_count"
        log_info "Current commit: $commit_hash"
    fi
    
    # Export environment variables
    export BUILD_TYPE="$build_type"
    export VERSION_INFO="$version_info"
    export GIT_BRANCH="$current_branch"
    export GIT_TAG="$current_tag"
    export GIT_COMMIT=$(git rev-parse HEAD 2>/dev/null || echo "unknown")
    export GIT_SHORT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    
    log_success "Environment detection completed:"
    log_info "  Build Type: $BUILD_TYPE"
    log_info "  Version: $VERSION_INFO"
    log_info "  Branch: $GIT_BRANCH"
    log_info "  Tag: ${GIT_TAG:-none}"
    log_info "  Commit: $GIT_SHORT_COMMIT"
    
    return 0
}

# Determine edition based on parameters or environment
detect_edition() {
    local edition_param="$1"
    local detected_edition
    
    if [[ -n "$edition_param" ]]; then
        case "$edition_param" in
            "cn"|"intl"|"all")
                detected_edition="$edition_param"
                ;;
            *)
                log_warning "Invalid edition parameter: $edition_param, defaulting to 'all'"
                detected_edition="$EDITION_ALL"
                ;;
        esac
    else
        # Default edition detection logic
        detected_edition="$EDITION_ALL"
        
        # You can add more sophisticated edition detection here
        # For example, based on branch name, environment variables, etc.
        
        log_info "No edition specified, defaulting to: $detected_edition"
    fi
    
    export EDITION="$detected_edition"
    log_info "Edition set to: $EDITION"
    
    return 0
}

# Validate build environment
validate_environment() {
    log_info "Validating build environment..."
    
    # Check Git status
    local git_status
    git_status=$(git status --porcelain)
    if [[ -n "$git_status" ]]; then
        log_warning "Working directory has uncommitted changes:"
        echo "$git_status"
        
        if [[ "$BUILD_TYPE" == "$BUILD_TYPE_RELEASE" ]]; then
            log_error "Release builds should not have uncommitted changes"
            return 1
        else
            log_warning "Development build with uncommitted changes - proceeding"
        fi
    else
        log_success "Working directory is clean"
    fi
    
    # Check required tools
    if ! check_dependencies; then
        return 1
    fi
    
    # Validate configuration
    if ! validate_config; then
        return 1
    fi
    
    log_success "Environment validation completed"
    return 0
}

# Get build output directory based on type and version
get_build_output_dir() {
    local build_type="$1"
    local version="$2"
    local edition="$3"
    
    local base_dir
    if [[ "$build_type" == "$BUILD_TYPE_RELEASE" ]]; then
        base_dir="release"
    else
        base_dir="dev"  # 使用dev路径而不是bugfix
    fi
    
    # Clean version (remove 'v' prefix if present)
    local clean_version
    clean_version=$(echo "$version" | sed 's/^v//')
    
    echo "${DIST_DIR}/${base_dir}/${edition}/${clean_version}"
}

# Main detection function
main() {
    local edition_param="$1"
    
    log_info "Starting environment detection..."
    
    # Detect Git environment
    if ! detect_git_environment; then
        return 1
    fi
    
    # Detect edition
    if ! detect_edition "$edition_param"; then
        return 1
    fi
    
    # Validate environment
    if ! validate_environment; then
        return 1
    fi
    
    # Set output directory
    local output_dir
    output_dir=$(get_build_output_dir "$BUILD_TYPE" "$VERSION_INFO" "$EDITION")
    export BUILD_OUTPUT_DIR="$output_dir"
    
    log_success "Build output directory: $BUILD_OUTPUT_DIR"
    
    # Create output directory
    mkdir -p "$BUILD_OUTPUT_DIR"
    
    log_success "Environment detection and setup completed successfully"
    return 0
}

# Function to print environment summary
print_environment_summary() {
    echo "======================================"
    echo "Build Environment Summary"
    echo "======================================"
    echo "Build Type:    ${BUILD_TYPE:-not set}"
    echo "Version:       ${VERSION_INFO:-not set}"
    echo "Edition:       ${EDITION:-not set}"
    echo "Git Branch:    ${GIT_BRANCH:-not set}"
    echo "Git Tag:       ${GIT_TAG:-none}"
    echo "Git Commit:    ${GIT_SHORT_COMMIT:-not set}"
    echo "Output Dir:    ${BUILD_OUTPUT_DIR:-not set}"
    echo "======================================"
}

# Only run main if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
    print_environment_summary
fi