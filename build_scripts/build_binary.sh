#!/bin/bash

# Build Binary Script for Qoder
# This script builds the actual qoder binary with proper configuration

# Source utilities and configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/utils.sh"
source "${SCRIPT_DIR}/config.sh"
source "${SCRIPT_DIR}/version.sh"

build_qoder_binary() {
    local target_os="$1"
    local target_arch="$2"
    local output_path="$3"
    local edition="${4:-all}"
    
    log_info "🔨 Building Qoder binary for ${target_os}/${target_arch} (edition: ${edition})"
    
    # Get version from VERSION or DEV_VERSION file
    local cosy_version
    # Capture only the version output, redirect logs to stderr to avoid pollution
    cosy_version=$(get_version 2>&1 | tail -1)
    if [[ -z "$cosy_version" ]]; then
        log_error "Could not determine version"
        return 1
    fi
    
    log_info "🏷️ Building version: $cosy_version"
    
    # Set cross-compilation environment
    setup_cross_compile_env "$target_os" "$target_arch"
    
    # Get binary name for platform
    local binary_name
    binary_name=$(get_binary_name "$target_os")
    
    # Prepare build tags based on build type
    local build_tags
    if [[ -n "$COSY_BUILDING_TAGS" ]]; then
        # Use explicitly set tags
        build_tags="$COSY_BUILDING_TAGS"
        log_info "Using explicitly set build tags: $build_tags"
    else
        # Select tags based on build type
        if [[ "$BUILD_TYPE" == "release" ]]; then
            build_tags="$DEFAULT_BUILD_TAGS"
            log_info "Release build detected, using production tags: $build_tags"
        else
            build_tags="$DEV_BUILD_TAGS"
            log_info "Development build detected, using dev tags: $build_tags"
        fi
    fi
    
    # Prepare ldflags
    local ldflags="-s -w"
    ldflags="$ldflags -X cosy/global.CosyVersion=$cosy_version"
    
    # Add qoder-specific configuration
    local qoder_config
    qoder_config=$(parse_qoder_config)
    if [[ -n "$qoder_config" ]]; then
        ldflags="$ldflags $qoder_config"
    fi
    
    log_info "LDFLAGS: $ldflags"
    
    # Prepare build mode and additional flags
    local build_args=()
    build_args+=("-mod=readonly")
    build_args+=("-tags" "$build_tags")
    build_args+=("-trimpath")
    build_args+=("-ldflags" "$ldflags")
    
    # Add build mode for specific platforms
    case "$target_os" in
        "darwin")
            if [[ "$target_arch" == "amd64" ]]; then
                build_args+=("-buildmode=pie")
            fi
            ;;
        "windows")
            build_args+=("-buildmode=pie")
            ;;
    esac
    
    # Create output directory
    mkdir -p "$output_path"
    
    # Build the binary
    log_info "🚀 Executing: go build ${build_args[*]} -o $output_path/$binary_name"
    
    if go build "${build_args[@]}" -o "$output_path/$binary_name"; then
        log_success "🎉 Binary built successfully: $output_path/$binary_name"
        
        # Verify binary was created and has proper size
        if [[ -f "$output_path/$binary_name" ]]; then
            local file_size
            file_size=$(stat -c%s "$output_path/$binary_name" 2>/dev/null || stat -f%z "$output_path/$binary_name" 2>/dev/null || echo "unknown")
            log_info "Binary size: $file_size bytes"
            
            # Make sure binary is executable
            chmod +x "$output_path/$binary_name"
            
            return 0
        else
            log_error "Binary file not found after build"
            return 1
        fi
    else
        log_error "Failed to build binary for ${target_os}/${target_arch}"
        return 1
    fi
}

# Set up cross-compilation environment for specific platform
setup_cross_compile_env() {
    local target_os="$1"
    local target_arch="$2"
    local platform="$target_os/$target_arch"
    
    # Set basic Go environment
    export GOOS="$target_os"
    export GOARCH="$target_arch"
    
    # Get platform-specific settings
    local cross_settings
    cross_settings=$(get_qoder_cross_compile_settings "$platform")
    
    log_info "Cross-compile settings for $platform: $cross_settings"
    
    # Parse and apply settings
    IFS=':' read -ra SETTINGS <<< "$cross_settings"
    for setting in "${SETTINGS[@]}"; do
        if [[ "$setting" == *"="* ]]; then
            local key="${setting%%=*}"
            local value="${setting#*=}"
            
            case "$key" in
                "CGO_ENABLED")
                    export CGO_ENABLED="$value"
                    ;;
                "CC")
                    export CC="$value"
                    ;;
                "CXX")
                    export CXX="$value"
                    ;;
                "CGO_LDFLAGS")
                    export CGO_LDFLAGS="$value"
                    ;;
                "LDFLAGS")
                    # This will be used in build args
                    export EXTRA_LDFLAGS="$value"
                    ;;
                "BUILDMODE")
                    export BUILD_MODE="$value"
                    ;;
            esac
        fi
    done
    
    log_info "Environment set: GOOS=$GOOS GOARCH=$GOARCH CGO_ENABLED=$CGO_ENABLED"
    if [[ -n "$CC" ]]; then
        log_info "CC=$CC"
    fi
    if [[ -n "$CXX" ]]; then
        log_info "CXX=$CXX"
    fi
}

build_binary() {
    local target_os="$1"
    local target_arch="$2"
    local output_path="$3"
    local edition="${4:-all}"
    
    # Validate inputs
    if [[ -z "$target_os" || -z "$target_arch" || -z "$output_path" ]]; then
        log_error "Missing required parameters"
        log_error "Usage: build_binary <target_os> <target_arch> <output_path> [edition]"
        return 1
    fi
    
    # Check if we're in the correct directory (should have go.mod)
    if [[ ! -f "go.mod" ]]; then
        log_error "go.mod not found. Please run from the project root directory."
        return 1
    fi
    
    # Call the qoder-specific build function
    build_qoder_binary "$target_os" "$target_arch" "$output_path" "$edition"
}

main() {
    local target_os="$1"
    local target_arch="$2"
    local output_path="$3"
    local edition="${4:-all}"
    
    if [[ -z "$target_os" || -z "$target_arch" || -z "$output_path" ]]; then
        log_error "Usage: $0 <target_os> <target_arch> <output_path> [edition]"
        return 1
    fi
    
    build_binary "$target_os" "$target_arch" "$output_path" "$edition"
}

# Only run main if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi