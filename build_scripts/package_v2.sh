#!/bin/bash

# Packaging Script V2
# 将所有平台打包在一个zip文件中，按照指定的目录结构

# Source utilities and version management
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/utils.sh"
source "${SCRIPT_DIR}/version.sh"
source "${SCRIPT_DIR}/config.sh"

# Windows DLL 下载函数
download_windows_dlls() {
    local target_dir="$1"
    local platform="$2"
    
    local os_type="${platform%%/*}"
    local arch_type="${platform##*/}"
    
    # 只处理 Windows 平台
    if [[ "$os_type" != "windows" ]]; then
        return 0
    fi
    
    log_info "📥 Downloading Windows DLL dependencies for $platform"
    
    # 检查是否为 ARM 架构
    if [[ "$arch_type" == "arm64" ]]; then
        log_info "🚧 Windows ARM64 DLL download skipped - ARM DLLs not yet available"
        log_info "    TODO: Add ARM64 DLL support when ARM DLLs are available in OSS"
        return 0
    fi
    
    # 只处理 AMD64/x86_64 架构
    if [[ "$arch_type" != "amd64" ]]; then
        log_warning "Unsupported Windows architecture for DLL download: $arch_type"
        return 0
    fi
    
    # OSS DLL 路径
    local oss_dll_path="oss://qbuilder/qoder/shared/winlib/"
    
    # 检查 ossutil
    if ! command -v ossutil &> /dev/null; then
        log_error "ossutil command not found, cannot download Windows DLLs"
        return 1
    fi
    
    # 构建 ossutil 命令
    local ossutil_cmd="ossutil cp -r -f"
    
    if [[ -n "$OSS_ACCESS_KEY_ID" && -n "$OSS_ACCESS_KEY_SECRET" ]]; then
        ossutil_cmd="$ossutil_cmd -i $OSS_ACCESS_KEY_ID -k $OSS_ACCESS_KEY_SECRET"
        log_info "Using provided access credentials for DLL download"
    else
        log_info "Using default/configured credentials for DLL download"
    fi
    
    # 下载 DLL 文件到目标目录
    log_info "Downloading DLLs from: $oss_dll_path"
    log_info "Target directory: $target_dir"
    
    if eval "$ossutil_cmd \"$oss_dll_path\" \"$target_dir/\""; then
        # 统计下载的文件
        local dll_count
        dll_count=$(find "$target_dir" -name "*.dll" | wc -l)
        log_success "✅ Downloaded $dll_count DLL files successfully"
        
        # 验证下载文件的完整性
        log_info "🔍 Verifying downloaded DLL file integrity..."
        local verification_failed=false
        local verified_count=0
        local corrupted_files=()
        
        # 列出并验证下载的 DLL 文件
        if [[ $dll_count -gt 0 ]]; then
            log_info "Verifying DLL files:"
            while IFS= read -r dll_file; do
                local dll_name
                dll_name=$(basename "$dll_file")
                log_info "  - Checking $dll_name..."
                
                # 检查文件是否存在且不为空
                if [[ ! -f "$dll_file" ]]; then
                    log_error "    File not found: $dll_file"
                    corrupted_files+=("$dll_name")
                    verification_failed=true
                    continue
                fi
                
                local file_size
                file_size=$(stat -c%s "$dll_file" 2>/dev/null || stat -f%z "$dll_file" 2>/dev/null)
                if [[ "$file_size" -eq 0 ]]; then
                    log_error "    File is empty: $dll_name"
                    corrupted_files+=("$dll_name")
                    verification_failed=true
                    continue
                fi
                
                # 计算文件哈希值用于完整性检查
                local file_hash
                if file_hash=$(calculate_hash "$dll_file" "sha256"); then
                    if [[ -n "$file_hash" ]]; then
                        log_info "    ✓ $dll_name (${file_size} bytes, SHA256: ${file_hash:0:16}...)"
                        ((verified_count++))
                    else
                        log_warning "    ? $dll_name (${file_size} bytes, hash calculation failed)"
                        ((verified_count++))  # 仍计为验证通过，只是没有哈希值
                    fi
                else
                    log_error "    ✗ $dll_name - hash calculation failed, possible corruption"
                    corrupted_files+=("$dll_name")
                    verification_failed=true
                fi
            done < <(find "$target_dir" -name "*.dll" | sort)
            
            # 验证结果总结
            if [[ "$verification_failed" == true ]]; then
                log_error "❌ File integrity verification failed for ${#corrupted_files[@]} files:"
                for file in "${corrupted_files[@]}"; do
                    log_error "  - $file"
                done
                log_warning "Continuing with packaging, but some DLL files may be corrupted"
                log_warning "Consider re-running the build to re-download the files"
            else
                log_success "✅ All $verified_count DLL files passed integrity verification"
            fi
        fi
        
        return 0
    else
        log_error "Failed to download Windows DLL dependencies"
        return 1
    fi
}

# 创建统一的打包目录结构
create_unified_package() {
    local edition="$1"
    local binary_dir="$2"
    local output_dir="$3"
    local build_version="$4"
    shift 4
    local platforms=("$@")
    
    log_info "📦 Creating unified package for all platforms"
    log_info "Edition: $edition"
    log_info "Version: $build_version"
    log_info "Platforms: ${platforms[*]}"
    
    # 创建临时打包目录
    local temp_dir="${BUILD_DIR}/unified_package_$$"
    local package_root="${temp_dir}/qoder"
    local version_dir="${package_root}/${build_version}"
    
    mkdir -p "$version_dir"
    
    # 复制各平台二进制文件
    local failed_platforms=()
    for platform in "${platforms[@]}"; do
        log_info "⚙️ Processing platform: $platform"
        
        local platform_arch_name
        platform_arch_name=$(get_platform_arch_name "$platform")
        
        # 源二进制路径
        local source_binary_dir="${binary_dir}/${build_version}/${platform_arch_name}"
        
        # 如果版本目录不存在，尝试旧格式
        if [[ ! -d "$source_binary_dir" ]]; then
            local os arch
            read -r os arch <<< "$(extract_platform "$platform")"
            source_binary_dir="${binary_dir}/${os}-${arch}"
        fi
        
        local binary_name
        binary_name=$(get_binary_name "$(echo "$platform" | cut -d'/' -f1)")
        local binary_path="${source_binary_dir}/${binary_name}"
        
        if [[ ! -f "$binary_path" ]]; then
            log_warning "Binary not found for $platform at $binary_path"
            failed_platforms+=("$platform")
            continue
        fi
        
        # 创建平台目录并复制二进制文件
        local target_dir="${version_dir}/${platform_arch_name}"
        mkdir -p "$target_dir"
        
        cp "$binary_path" "$target_dir/"
        if [[ $? -ne 0 ]]; then
            log_error "Failed to copy binary for $platform"
            failed_platforms+=("$platform")
            continue
        fi
        
        # 下载 Windows DLL 依赖文件
        local os_type="${platform%%/*}"
        if [[ "$os_type" == "windows" ]]; then
            if ! download_windows_dlls "$target_dir" "$platform"; then
                log_warning "Failed to download Windows DLL dependencies for $platform"
                # 不将此视为致命错误，继续打包过程
                log_warning "Continuing packaging without DLL dependencies"
            fi
        fi
        
        # 验证签名（仅 macOS 和 Windows，且签名启用时）
        local enable_signing="${ENABLE_SIGNING:-false}"
        if [[ "$enable_signing" == "true" ]]; then
            if [[ "$os_type" == "darwin" ]]; then
                if ! verify_signature "$target_dir/$binary_name" "$os_type"; then
                    log_error "macOS binary signature verification failed for $platform"
                    log_error "Build aborted due to missing or invalid macOS signature"
                    return 1
                fi
            elif [[ "$os_type" == "windows" ]]; then
                # TODO: Windows signature verification is not implemented
                # Windows binaries should be signed but verification is complex
                log_info "Windows signature verification skipped (TODO: implement verification)"
                verify_signature "$target_dir/$binary_name" "$os_type"
            fi
        else
            log_info "💨 Signature verification skipped (ENABLE_SIGNING=$enable_signing)"
        fi
        
        log_success "✅ Processed $platform"
    done
    
    # 创建 extension 目录（占位）
    mkdir -p "${version_dir}/extension"
    echo "Extension files would go here" > "${version_dir}/extension/README.txt"
    
    # 创建 config.json
    create_config_json "$package_root" "$edition" "$build_version"
    
    # 创建 env.json
    create_env_json "$package_root" "$build_version" "$edition"
    
    # 生成打包文件名
    local package_filename
    package_filename=$(generate_unified_filename "$edition" "$build_version")
    # 确保没有双斜杠
    output_dir="${output_dir%/}"  # 移除末尾斜杠
    
    # 转换为绝对路径
    if [[ "$output_dir" = /* ]]; then
        # 已经是绝对路径
        local package_path="${output_dir}/${package_filename}"
    else
        # 相对路径，转换为绝对路径
        local abs_output_dir="$(cd "$(dirname "$0")/.." && pwd)/${output_dir}"
        local package_path="${abs_output_dir}/${package_filename}"
    fi
    
    # 创建 zip 包
    log_info "🗜️ Creating zip archive: $package_filename"
    
    # 确保输出目录存在
    local output_parent_dir=$(dirname "$package_path")
    if [[ ! -d "$output_parent_dir" ]]; then
        log_info "Creating output directory: $output_parent_dir"
        mkdir -p "$output_parent_dir"
    fi
    
    (cd "$temp_dir" && zip -r "$package_path" "qoder" -q)
    
    if [[ $? -ne 0 ]]; then
        log_error "Failed to create zip archive"
        rm -rf "$temp_dir"
        return 1
    fi
    
    # 清理临时目录
    rm -rf "$temp_dir"
    
    # 验证生成的包
    if [[ -f "$package_path" ]]; then
        local package_size
        package_size=$(stat -c%s "$package_path" 2>/dev/null || stat -f%z "$package_path" 2>/dev/null)
        local package_hash
        package_hash=$(calculate_hash "$package_path" "sha256")
        
        log_success "🎉 Package created successfully"
        log_info "  File: $package_filename"
        log_info "  Size: $(numfmt --to=iec-i --suffix=B $package_size 2>/dev/null || echo "$package_size bytes")"
        log_info "  SHA256: $package_hash"
        
        # 导出包信息
        export UNIFIED_PACKAGE_PATH="$package_path"
        export UNIFIED_PACKAGE_HASH="$package_hash"
        
        # 报告失败的平台
        if [[ ${#failed_platforms[@]} -gt 0 ]]; then
            log_warning "Failed platforms: ${failed_platforms[*]}"
            return 1
        fi
        
        return 0
    else
        log_error "Package file not found after creation"
        return 1
    fi
}

# 创建 config.json（参考原有格式）
create_config_json() {
    local package_dir="$1"
    local edition="$2"
    local version="$3"
    
    # 使用原有的简单格式，与历史脚本保持一致
    printf '{\n    "cosy.core.version": "%s"\n}' "$version" > "${package_dir}/config.json"
    
    # 验证生成的 config.json
    if ! validate_json "${package_dir}/config.json" "generated config.json"; then
        log_error "Failed to validate generated config.json"
        log_error "Build aborted due to invalid JSON generation"
        return 1
    fi
    
    log_info "Created config.json"
}

# 创建 env.json（从 conf 目录按版本选择）
create_env_json() {
    local package_dir="$1"
    local version="$2"
    local edition="${3:-all}"
    
    # 获取脚本目录
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local conf_dir="${script_dir}/conf"
    
    # 按版本选择 env.json，优先级：版本特定 -> 版本类型特定 -> 默认
    local env_source=""
    
    # 1. 先尝试版本特定的配置（如 conf/intl/env.json）
    if [[ "$edition" != "all" && -f "${conf_dir}/${edition}/env.json" ]]; then
        env_source="${conf_dir}/${edition}/env.json"
        log_info "Using edition-specific env.json: ${edition}"
    # 2. 使用默认配置
    elif [[ -f "${conf_dir}/env.json" ]]; then
        env_source="${conf_dir}/env.json"
        log_info "Using default env.json"
    else
        log_error "No env.json configuration found in ${conf_dir}"
        return 1
    fi
    
    # 验证选择的 JSON 配置文件
    if ! validate_json "$env_source" "env.json configuration"; then
        log_error "Failed to validate env.json: $env_source"
        log_error "Build aborted due to invalid JSON configuration"
        return 1
    fi
    
    # 复制选择的配置文件
    cp "$env_source" "${package_dir}/env.json"
    
    if [[ $? -eq 0 ]]; then
        log_info "Created env.json from: $env_source"
    else
        log_error "Failed to copy env.json from: $env_source"
        return 1
    fi
}

# 生成统一包的文件名
generate_unified_filename() {
    local edition="$1"
    local version="$2"
    
    # 格式: qoder_[edition]_[version].zip
    # 例如: qoder_all_v0.1.0.zip 或 qoder_cn_v0.1.0-dev.1.abc123.zip
    echo "qoder_${edition}_${version}.zip"
}

# 验证签名（复用原有逻辑）
verify_signature() {
    local binary_path="$1"
    local os="$2"
    
    case "$os" in
        "darwin")
            if command -v codesign &>/dev/null; then
                if codesign -v "$binary_path" 2>/dev/null; then
                    log_info "🔒 macOS signature valid: $(basename "$binary_path")"
                    return 0
                else
                    log_warning "⚠ macOS signature invalid or missing: $(basename "$binary_path")"
                    # 提供更有用的调试信息
                    log_info "  Binary path: $binary_path"
                    log_info "  Current working directory: $(pwd)"
                    log_info "  Checking signature with: codesign -v '$binary_path'"
                    # 尝试获取更详细的签名信息
                    codesign -dv "$binary_path" 2>&1 | grep -E "(Signature|Authority|not signed)" | head -3
                    return 1
                fi
            else
                log_warning "codesign not available, skipping macOS signature verification"
                return 0
            fi
            ;;
        "windows")
            log_info "Windows signature verification not implemented"
            return 0
            ;;
        *)
            log_info "Signature verification not required for $os"
            return 0
            ;;
    esac
}

# 主函数
main() {
    local edition="${1:-all}"
    local binary_dir="${2:-dist}"
    local output_dir="${3:-dist}"
    local build_version="${4:-v0.0.0}"
    shift 4
    local platforms=("$@")
    
    log_info "🎯 Starting unified packaging process"
    
    # 验证输入
    if [[ ! -d "$binary_dir" ]]; then
        log_error "Binary directory does not exist: $binary_dir"
        return 1
    fi
    
    # 创建输出目录
    mkdir -p "$output_dir"
    
    # 总是检测实际可用的平台，不依赖传入的平台列表
    # 这确保我们只打包真正存在的二进制文件
    local requested_platforms=("${platforms[@]}")
    platforms=()
    
    # 如果没有指定平台，检测所有支持的平台
    if [[ ${#requested_platforms[@]} -eq 0 ]]; then
        requested_platforms=("${SUPPORTED_PLATFORMS[@]}")
    fi
    
    log_info "🔍 Checking for available binaries among requested platforms: ${requested_platforms[*]}"
    
    for platform in "${requested_platforms[@]}"; do
        local platform_arch_name
        platform_arch_name=$(get_platform_arch_name "$platform")
        
        # 检查两种可能的路径格式
        local check_path1="${binary_dir}/${build_version}/${platform_arch_name}"
        local os arch
        read -r os arch <<< "$(extract_platform "$platform")"
        local check_path2="${binary_dir}/${os}-${arch}"
        
        local binary_name
        binary_name=$(get_binary_name "$os")
        
        if [[ -f "${check_path1}/${binary_name}" ]] || [[ -f "${check_path2}/${binary_name}" ]]; then
            platforms+=("$platform")
            log_info "✅ Found binary for $platform"
        else
            log_warning "✗ No binary found for $platform (skipping)"
        fi
    done
    
    if [[ ${#platforms[@]} -eq 0 ]]; then
        log_error "No platform binaries found"
        return 1
    fi
    
    log_info "📦 Will package ${#platforms[@]} platforms: ${platforms[*]}"
    
    # 创建统一包
    if create_unified_package "$edition" "$binary_dir" "$output_dir" "$build_version" "${platforms[@]}"; then
        log_success "🎆 Unified packaging completed successfully"
        return 0
    else
        log_error "Unified packaging failed"
        return 1
    fi
}

# 只在直接执行时运行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi