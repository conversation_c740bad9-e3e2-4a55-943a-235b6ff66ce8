package deepwiki

import (
	"context"
	chainsCommon "cosy/chat/chains/common"
	wikiChains "cosy/deepwiki/chains"
	"cosy/deepwiki/common"
	"cosy/deepwiki/queue"
	wikiService "cosy/deepwiki/service"
	"cosy/deepwiki/storage"
	"cosy/deepwiki/support"
	"cosy/definition"
	cosyError "cosy/errors"
	"cosy/experiment"
	"cosy/global"
	"cosy/indexing/api"
	"cosy/indexing/chat_indexing/workspace_tree_indexing"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/sls"
	"cosy/tree"
	"cosy/user"
	"cosy/util"
	"cosy/util/git"
	"cosy/util/session"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/go-git/go-git/v5/plumbing/object"
	"github.com/google/uuid"

	"github.com/tmc/langchaingo/chains"
)

const (
	// DefaultMaxWorkspaceCount 默认最大项目数量
	DefaultMaxWorkspaceCount = 5
	//非git工程，检查wiki的更新过期时间
	noneGitRepoCheckExpireTime = 14 * 24 * time.Hour
)

const (
	fullTriggerType        = "full"
	incrementalTriggerType = "incremental"
)

var GlobalWikiService *DeepwikiService
var GlobalKnowledgeService *wikiService.KnowledgeIntegrationService

func NewWikiService(storageService *storage.LingmaWikiStorageService) *DeepwikiService {
	// 创建新的监控组件
	immediateChecker := wikiService.NewImmediateUpdateChecker(storageService)
	backgroundMonitor := wikiService.NewBackgroundMonitorService(storageService)

	// 初始化knowledge集成服务
	if GlobalKnowledgeService == nil {
		GlobalKnowledgeService = wikiService.NewKnowledgeIntegrationService()
	}

	service := &DeepwikiService{
		storageService:    storageService,
		immediateChecker:  immediateChecker,
		backgroundMonitor: backgroundMonitor,
		locks:             sync.Map{},
	}

	// 创建任务队列管理器
	config := queue.DefaultTaskQueueConfig()
	config.WorkerCount = 1 // 默认1个worker
	config.QueueSize = 100 // 队列大小100

	// 创建任务执行器
	executor := queue.NewDeepWikiTaskExecutor(
		service.executeFullGeneration,
		service.executeIncrementalUpdate,
		service.executeSingleItemRegeneration,
	)

	service.TaskQueueManager = queue.NewTaskQueueManager(config, executor)

	// 设置后台监控的回调函数
	service.backgroundMonitor.SetGlobalCallback(func(workspacePath string, diffInfo *definition.CommitDiffInfo) error {
		// 创建包含workspace信息的context，用于后台触发的增量更新
		workspaceFolder := definition.WorkspaceInfo{
			WorkspaceFolders: []definition.WorkspaceFolder{
				{
					Name: util.GetProjectName(workspacePath),
					URI:  workspacePath,
				},
			},
		}
		callbackCtx := context.WithValue(context.Background(), definition.ContextKeyWorkspace, workspaceFolder)

		// 尝试从全局工作区树索引器获取已初始化的fileIndexer
		if fileIndexer, ok := workspace_tree_indexing.WorkspaceTreeFileIndexers.GetFileIndexer(workspacePath); ok {
			callbackCtx = context.WithValue(callbackCtx, definition.ContextKeyFileIndexer, fileIndexer)
			log.Debugf("[BackgroundMonitor] Successfully obtained fileIndexer from global WorkspaceTreeFileIndexers for workspace: %s", workspacePath)
		} else {
			log.Debugf("[BackgroundMonitor] No fileIndexer found in global WorkspaceTreeFileIndexers, performIncrementalUpdate will handle initialization for workspace: %s", workspacePath)
		}

		updateRequest := definition.CreateDeepwikiRequest{
			WorkspacePath:     workspacePath,
			RequestId:         uuid.New().String(),
			PreferredLanguage: definition.GetPreferredLanguageDesc(global.PreferredLanguage),
		}

		// 增量更新
		return service.submitIncrementalUpdateTask(callbackCtx, updateRequest, diffInfo)
	})

	// 启动后台监控服务
	err := service.backgroundMonitor.Start()
	if err != nil {
		log.Errorf("Failed to start background monitor service: %v", err)
	}

	return service
}

type DeepwikiService struct {
	storageService *storage.LingmaWikiStorageService
	// 新的监控组件
	immediateChecker  *wikiService.ImmediateUpdateChecker
	backgroundMonitor *wikiService.BackgroundMonitorService
	TaskQueueManager  *queue.TaskQueueManager
	locks             sync.Map
	// lastIndexCheckTimes 记录每个workspace的最后索引检查时间
	lastIndexCheckTimes sync.Map // map[string]time.Time
}

// TaskQueueAdapter 实现TaskSubmitter接口的适配器
type TaskQueueAdapter struct {
	manager *queue.TaskQueueManager
}

func (t *TaskQueueAdapter) SubmitTask(task *queue.Task) error {
	return t.manager.SubmitTask(task)
}

func (t *TaskQueueAdapter) GetQueueStatus() queue.QueueStatus {
	return t.manager.GetQueueStatus()
}

// GetTaskSubmitter 获取TaskSubmitter接口的实现
func (d *DeepwikiService) GetTaskSubmitter() wikiService.TaskSubmitter {
	return &TaskQueueAdapter{manager: d.TaskQueueManager}
}

// StartQueue 启动任务队列
func (d *DeepwikiService) StartQueue() error {
	if d.TaskQueueManager == nil {
		return fmt.Errorf("task queue manager not initialized")
	}
	err := d.TaskQueueManager.Start()
	if err != nil {
		return err
	}

	return nil
}

// StopQueue 停止任务队列
func (d *DeepwikiService) StopQueue() error {
	if d.TaskQueueManager == nil {
		return nil
	}
	return d.TaskQueueManager.Stop()
}

// GetQueueStatus 获取队列状态
func (d *DeepwikiService) GetQueueStatus() queue.QueueStatus {
	if d.TaskQueueManager == nil {
		return queue.QueueStatus{}
	}
	return d.TaskQueueManager.GetQueueStatus()
}

// executeFullGeneration 执行全量生成（队列调用）
func (d *DeepwikiService) executeFullGeneration(ctx context.Context, request definition.CreateDeepwikiRequest) error {
	repoName := util.GetProjectName(request.WorkspacePath)
	log.Infof("Deepwiki: Project start - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)

	// 调用原有的全量生成逻辑
	d.generateNew(ctx, request)
	return nil
}

// executeIncrementalUpdate 执行增量更新（队列调用）
func (d *DeepwikiService) executeIncrementalUpdate(ctx context.Context, request definition.CreateDeepwikiRequest, diffInfo interface{}) error {
	repoName := util.GetProjectName(request.WorkspacePath)
	log.Infof("Deepwiki: IncrementalUpdate start - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)

	// 类型断言
	commitDiffInfo, ok := diffInfo.(*definition.CommitDiffInfo)
	if !ok {
		log.Infof("Deepwiki: IncrementalUpdate failed - Repo: %s, Workspace: %s, Error: invalid diff info type", repoName, request.WorkspacePath)
		return fmt.Errorf("invalid diff info type")
	}

	// 调用原有的增量更新逻辑
	err := d.performIncrementalUpdate(ctx, request, commitDiffInfo)
	if err != nil {
		log.Infof("Deepwiki: IncrementalUpdate failed - Repo: %s, Workspace: %s, Error: %v", repoName, request.WorkspacePath, err)
		return err
	}

	log.Infof("Deepwiki: IncrementalUpdate end - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)
	return nil
}

// executeSingleItemRegeneration 执行单个wiki item重新生成逻辑
func (d *DeepwikiService) executeSingleItemRegeneration(ctx context.Context, request definition.CreateDeepwikiRequest, catalogID string) error {
	repoName := util.GetProjectName(request.WorkspacePath)
	log.Infof("Deepwiki: SingleItemRegeneration start - Repo: %s, Workspace: %s, Catalog: %s", repoName, request.WorkspacePath, catalogID)

	// 创建单个item重新生成的链
	c, err := newSingleItemRegenerationChains()
	if err != nil {
		log.Errorf("Failed to create single item regeneration chains: %v", err)
		return fmt.Errorf("failed to create single item regeneration chains: %w", err)
	}

	// 准备输入
	inputs := map[string]any{
		chainsCommon.KeyCreateDeepwikiRequest: request,
		chainsCommon.KeyRequestId:             request.RequestId,
		chainsCommon.KeySessionId:             request.SessionId,
		chainsCommon.KeyPreferredLanguage:     request.PreferredLanguage,
		chainsCommon.KeyTargetCatalogue:       catalogID,
	}

	// 执行链
	_, err = chains.Call(ctx, c, inputs)
	if err != nil {
		log.Errorf("Deepwiki: SingleItemRegeneration failed - Repo: %s, Workspace: %s, Catalog: %s, Error: %v", repoName, request.WorkspacePath, catalogID, err)
		return fmt.Errorf("single item regeneration failed: %w", err)
	}

	log.Infof("Deepwiki: SingleItemRegeneration end - Repo: %s, Workspace: %s, Catalog: %s", repoName, request.WorkspacePath, catalogID)
	return nil
}

func (d *DeepwikiService) GenerateUpdate(ctx context.Context, request definition.CreateDeepwikiRequest) {
	workspacePath := request.WorkspacePath
	log.Debugf("generate wiki update. workspacePath: %s", workspacePath)

	// 获取锁，同一个项目，只能同时处理一个
	lockInterface, _ := d.locks.LoadOrStore(request.WorkspacePath, &sync.Mutex{})
	lock := lockInterface.(*sync.Mutex)
	lock.Lock()
	defer lock.Unlock()
	log.Debugf("generate wiki update. workspacePath: %s, get lock", workspacePath)

	// 确保repo存在，如果不存在则创建
	repo, err := d.ensureRepoExistsForTask(request)
	if err != nil {
		log.Errorf("GenerateUpdate failed to ensure repo exists %v", err)
		return
	}

	// 准入校验
	if !d.accessVerify(request, repo) {
		log.Debugf("GenerateUpdate accessVerify fail, request:%+v", request)
		return
	}

	// 初始化
	d.initContext(ctx, request.WorkspacePath)
	request.CurrentWorkspaceCount = CountStartedRepo()

	// 检查当前workspace的failed状态repo并进行恢复
	fullUpdate, err := d.checkFullUpdate(repo)
	if err != nil {
		log.Debugf("GenerateUpdate checkFullUpdate fail, repo:%+v", repo)
		return
	}

	// 全量更新
	if fullUpdate {
		log.Debugf("GenerateUpdate fullUpdate for workspace:%s", workspacePath)
		d.RecoverFailedRepoForWorkspace(request, repo)
		d.submitFullGenerationTask(ctx, request)
		return
	}

	// 检查和修复wiki索引完整性（基于时间间隔和repo修改时间）
	if d.shouldPerformIndexCheck(workspacePath) {
		go d.checkAndFixWikiIndex(ctx, workspacePath)
	}

	// 增量更新
	err = d.immediateChecker.CheckProjectNow(ctx, workspacePath, func(ctx context.Context, request definition.CreateDeepwikiRequest, diffInfo *definition.CommitDiffInfo) error {
		log.Debugf("GenerateUpdate incrementalUpdate for workspace:%s", workspacePath)
		return d.submitIncrementalUpdateTask(ctx, request, diffInfo)
	}, request)
	if err != nil {
		log.Errorf("Failed to perform immediate check for project update: %v", err)
	}
}

func (d *DeepwikiService) checkFullUpdate(repo *definition.AgentWikiRepo) (bool, error) {
	// 获取repo的catalog状态统计
	statusCount, err := d.storageService.GetCatalogStatusByRepoID(repo.ID)
	if err != nil {
		log.Errorf("[checkFullUpdate] Failed to get catalog status for repo %s: %v", repo.ID, err)
		return false, err
	}

	// 检查是否有failed的catalog
	failedCount := statusCount[definition.DeepWikiProgressStatusFailed]
	waitingCount := statusCount[definition.DeepWikiProgressStatusWaiting]
	pendingCount := statusCount[definition.DeepWikiProgressStatusPending]

	// 已完成状态，或者处理中但恢复点已完成全量，且没有待处理的目录，不需要走全量
	if (repo.ProgressStatus == definition.DeepWikiProgressStatusCompleted || (repo.ProgressStatus == definition.DeepWikiProgressStatusProcessing && (repo.RecoveryCheckpoint == "wiki_generation_completed" || repo.RecoveryCheckpoint == "incremental_processing"))) && failedCount == 0 && waitingCount == 0 && pendingCount == 0 {
		log.Debugf("[checkFullUpdate] Repo status is %s, statusCount is %+v, gspace: %s",
			repo.ProgressStatus, statusCount, repo.WorkspacePath)
		return false, nil
	}

	return true, nil
}

// WIKI 生成，上下文补全
func (d *DeepwikiService) initContext(ctx context.Context, workspacePath string) context.Context {
	if ctx.Value(definition.ContextKeyWorkspace) == nil {
		//设置ctx信息
		workspaceFolder := definition.WorkspaceInfo{
			WorkspaceFolders: []definition.WorkspaceFolder{
				{
					Name: util.GetProjectName(workspacePath),
					URI:  workspacePath,
				},
			},
		}
		ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, workspaceFolder)
	}
	if ctx.Value(definition.ContextKeyFileIndexer) == nil {
		fileIndexer, ok := workspace_tree_indexing.WorkspaceTreeFileIndexers.GetFileIndexer(workspacePath)
		if !ok {
			log.Debugf("GenerateUpdate failed to get file indexer. ok: %v", ok)
		} else {
			ctx = context.WithValue(ctx, definition.ContextKeyFileIndexer, fileIndexer)
		}
	}

	return ctx
}

// WIKI 生成准入校验
func (d *DeepwikiService) accessVerify(request definition.CreateDeepwikiRequest, repo *definition.AgentWikiRepo) bool {
	workspacePath := request.WorkspacePath
	// 登录校验
	if user.GetCachedUserInfo() == nil {
		log.Infof("user not login, skip wiki generate.")
		return false
	}

	// 禁止remote agent模式下进行wiki生成
	if !support.IsEnableWiki() {
		log.Infof("Skip wiki generate for workspace: %s", workspacePath)
		return false
	}

	// 太多项目空间拦截，不考虑not_started项目
	// 当前项目已经开始，不做拦截
	if repo.RecoveryCheckpoint == "not_started" {
		startRepoCount := CountStartedRepo()
		maxWorkspaceCount := experiment.ConfigService.GetIntConfigWithEnv(definition.ExperimentWikiWorkspaceMaxCount, experiment.ConfigScopeClient, DefaultMaxWorkspaceCount)
		if startRepoCount >= maxWorkspaceCount {
			log.Debugf("Too many workspace,startRepoCount:%d, maxWorkspaceCount:%d", startRepoCount, maxWorkspaceCount)
			repo.WikiPresentStatus = definition.WikiPresentTooManyWorkspaceFailed
			repo.ProgressStatus = definition.DeepWikiProgressStatusPending
			updateErr := storage.GlobalStorageService.UpdateWikiRepo(repo)
			if updateErr != nil {
				log.Errorf("GenerateUpdate too many workspace, failed to update wiki repo. err: %v", updateErr)
			}
			return false
		} else if repo.WikiPresentStatus == definition.WikiPresentTooManyWorkspaceFailed {
			repo.WikiPresentStatus = definition.WikiPresentStatusWaiting
			repo.ProgressStatus = definition.DeepWikiProgressStatusPending
			updateErr := storage.GlobalStorageService.UpdateWikiRepo(repo)
			if updateErr != nil {
				log.Errorf("GenerateUpdate not too many workspace, failed to update wiki repo. err: %v", updateErr)
				return false
			}
		}
	}

	// 手动暂停
	if IsWikiGeneratePaused(workspacePath) {
		log.Infof("wiki generate paused for workspace: %s", workspacePath)
		return false
	}

	// 判断是否有处理中的任务,有处理中任务直接返回，不允许进行状态恢复
	tasks := d.TaskQueueManager.GetTasksByWorkspace(workspacePath)
	for _, task := range tasks {
		if !task.IsFinished() {
			log.Debugf("GenerateUpdate wiki task is processing, workspacePath:%s", workspacePath)
			return false
		}
	}

	// 必须是git仓库
	isGitRepo, err := support.IsGitRepo(request.WorkspacePath)
	if err != nil {
		log.Errorf("GenerateUpdate check is git repo error. workspace: %s, err: %v", request.WorkspacePath, err)
		return false
	}
	if !isGitRepo {
		log.Infof("GenerateUpdate not git repo, workspace: %s", workspacePath)

		repo.WikiPresentStatus = definition.WikiPresentStatusNotSourceControlFailed
		repo.ProgressStatus = definition.DeepWikiProgressStatusPending
		updateErr := storage.GlobalStorageService.UpdateWikiRepo(repo)
		if updateErr != nil {
			log.Errorf("GenerateUpdate no git repo, failed to update wiki repo. err: %v", updateErr)
		}
		return false
	} else if repo.WikiPresentStatus == definition.WikiPresentStatusNotSourceControlFailed {
		repo.WikiPresentStatus = definition.WikiPresentStatusWaiting
		repo.ProgressStatus = definition.DeepWikiProgressStatusPending
		updateErr := storage.GlobalStorageService.UpdateWikiRepo(repo)
		if updateErr != nil {
			log.Errorf("GenerateUpdate git repo, failed to update wiki repo. err: %v", updateErr)
			return false
		}
	}

	// WIKI更新，必须有提交历史
	currentCommitId, err := support.GetCurrentCommitId(workspacePath)
	if err != nil {
		log.Errorf("GenerateUpdate get current commit id error. workspace: %s, err: %v", request.WorkspacePath, err)
		return false
	}
	if currentCommitId == "" {
		repo.WikiPresentStatus = definition.WikiPresentStatusNoCommitHistoryFailed
		repo.ProgressStatus = definition.DeepWikiProgressStatusPending
		updateErr := storage.GlobalStorageService.UpdateWikiRepo(repo)
		if updateErr != nil {
			log.Errorf("GenerateUpdate no commit history, failed to update wiki repo. err: %v", updateErr)
		}
		return false
	} else if repo.WikiPresentStatus == definition.WikiPresentStatusNoCommitHistoryFailed {
		repo.WikiPresentStatus = definition.WikiPresentStatusWaiting
		repo.ProgressStatus = definition.DeepWikiProgressStatusPending
		repo.LastCommitID = currentCommitId
		updateErr := storage.GlobalStorageService.UpdateWikiRepo(repo)
		if updateErr != nil {
			log.Errorf("GenerateUpdate has commit history,failed to update wiki repo. err: %v", updateErr)
			return false
		}
	}

	// 触发拦截，不允许非版本控制工程，不允许文件数量超过6000
	workspaceTree := tree.NewWorkspaceMerkleTree(workspacePath)
	if workspaceTree.GetLeafNodeCount() > 6000 {
		repo.WikiPresentStatus = definition.WikiPresentStatusTooManyFilesFailed
		repo.ProgressStatus = definition.DeepWikiProgressStatusPending
		updateErr := storage.GlobalStorageService.UpdateWikiRepo(repo)
		if updateErr != nil {
			log.Errorf("GenerateUpdate too many files, failed to update wiki repo. err: %v", updateErr)
		}
		return false
	} else if repo.WikiPresentStatus == definition.WikiPresentStatusTooManyFilesFailed {
		repo.WikiPresentStatus = definition.WikiPresentStatusWaiting
		repo.ProgressStatus = definition.DeepWikiProgressStatusPending
		updateErr := storage.GlobalStorageService.UpdateWikiRepo(repo)
		if updateErr != nil {
			log.Errorf("GenerateUpdate not too many files, failed to update wiki repo. err: %v", updateErr)
			return false
		}
	}

	// 先恢复空文件校验
	if workspaceTree.GetLeafNodeCount() == 0 {
		repo.WikiPresentStatus = definition.WikiPresentStatusNoFileFailed
		repo.ProgressStatus = definition.DeepWikiProgressStatusPending
		updateErr := storage.GlobalStorageService.UpdateWikiRepo(repo)
		if updateErr != nil {
			log.Errorf("GenerateUpdate no file, failed to update wiki repo. err: %v", updateErr)
		}
		return false
	} else if repo.WikiPresentStatus == definition.WikiPresentStatusNoFileFailed {
		repo.WikiPresentStatus = definition.WikiPresentStatusWaiting
		repo.ProgressStatus = definition.DeepWikiProgressStatusPending
		updateErr := storage.GlobalStorageService.UpdateWikiRepo(repo)
		if updateErr != nil {
			log.Errorf("GenerateUpdate has file, failed to update wiki repo. err: %v", updateErr)
			return false
		}
	}

	return true
}

// 计算非not_started项目数量
func CountStartedRepo() int {
	repos, _ := storage.GlobalStorageService.GetAllWikiRepos()
	count := 0
	for _, repo := range repos {
		if repo.RecoveryCheckpoint != "not_started" {
			count++
		}
	}
	return count
}

// submitFullGenerationTask 提交全量生成任务到队列
func (d *DeepwikiService) submitFullGenerationTask(ctx context.Context, request definition.CreateDeepwikiRequest) {
	task := queue.NewFullGenerationTask(ctx, request)
	err := d.TaskQueueManager.SubmitTask(task)
	if err != nil {
		log.Errorf("Failed to submit full generation task for %s: %v", request.WorkspacePath, err)
	}

	log.Infof("Full generation task submitted successfully for workspace: %s, task ID: %s",
		request.WorkspacePath, task.ID)
}

// ensureRepoExistsForTask 确保repo存在，如果不存在则创建
func (d *DeepwikiService) ensureRepoExistsForTask(request definition.CreateDeepwikiRequest) (*definition.AgentWikiRepo, error) {
	// 首先尝试获取现有的repo
	repo, err := d.storageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing repo: %w", err)
	}

	// 如果repo已存在，直接返回
	if repo != nil {
		return repo, nil
	}

	// 如果repo不存在，创建新的repo记录
	log.Debugf("Creating new repo record for task submission: %s", request.WorkspacePath)
	// 获取git信息
	gitSupport, openGitErr := support.NewGitSupport(request.WorkspacePath)
	if openGitErr != nil {
		log.Warnf("Failed to open git support for workspace %s: %v", request.WorkspacePath, openGitErr)
	}

	var latestCommit *object.Commit
	if gitSupport != nil && gitSupport.IsAvailable() {
		latestCommit, err = gitSupport.GetHeadCommit()
		if err != nil {
			log.Warnf("Failed to get head commit for workspace %s: %v", request.WorkspacePath, err)
		}
	}
	if latestCommit == nil {
		latestCommit = support.NewFixedCommit()
	}

	// 创建新的repo记录
	newRepo := &definition.AgentWikiRepo{
		ID:                 uuid.NewString(),
		LastCommitID:       latestCommit.Hash.String(),
		LastCommitUpdate:   latestCommit.Committer.When,
		Name:               util.GetProjectName(request.WorkspacePath),
		ProgressStatus:     definition.DeepWikiProgressStatusPending,
		WikiPresentStatus:  definition.WikiPresentStatusWaiting, // 初始状态为WAITING，稍后会更新为LINING
		RecoveryCheckpoint: "not_started",
		WorkspacePath:      request.WorkspacePath,
		GmtCreate:          time.Now(),
		GmtModified:        time.Now(),
	}

	// 使用安全的创建方法，避免创建重复的repo
	actualRepo, isNewRepo, err := d.storageService.CreateWikiRepoIfNotExists(newRepo)
	if err != nil {
		return nil, fmt.Errorf("failed to create repo: %w", err)
	}

	if isNewRepo {
		log.Infof("Created new repo record for workspace: %s (ID: %s)", request.WorkspacePath, actualRepo.ID)
	} else {
		log.Debugf("Repo already exists for workspace: %s (ID: %s)", request.WorkspacePath, actualRepo.ID)
	}

	return actualRepo, nil
}

// submitIncrementalUpdateTask 提交增量更新任务到队列
func (d *DeepwikiService) submitIncrementalUpdateTask(ctx context.Context, request definition.CreateDeepwikiRequest, diffInfo *definition.CommitDiffInfo) error {
	// 禁止remote agent模式下进行wiki增量更新任务提交
	if !support.IsEnableWiki() {
		log.Infof("Skip wiki generate for workspace: %s", request.WorkspacePath)
		return nil
	}

	// 检查修改行数阈值（250行）
	const changedLinesThreshold = 250
	if diffInfo != nil && diffInfo.TotalChangedLines < changedLinesThreshold {
		log.Debugf("[deepwiki-incremental-update] Commit changes (%d lines) below threshold (%d lines), skipping incremental update for workspace: %s",
			diffInfo.TotalChangedLines, changedLinesThreshold, request.WorkspacePath)
		return nil
	}

	log.Debugf("[deepwiki-incremental-update] Commit changes exceed threshold (%d lines), proceeding with incremental update for workspace: %s",
		changedLinesThreshold, request.WorkspacePath)

	// commit diff检测达到触发增量更新条件时，立即设置progress status为processing
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err != nil {
		return fmt.Errorf("failed to get wiki repo for status update: %w", err)
	}
	if repo == nil {
		return fmt.Errorf("wiki repo not found for workspace: %s", request.WorkspacePath)
	}

	oldProgressStatus := repo.ProgressStatus
	repo.ProgressStatus = definition.DeepWikiProgressStatusProcessing
	if err := storage.GlobalStorageService.UpdateWikiRepo(repo); err != nil {
		return fmt.Errorf("failed to update repo status when triggering incremental update: %w", err)
	}

	log.Infof("[deepwiki-incremental-update] Triggered incremental update - updated repo status: %s -> %s, WikiPresentStatus: %s",
		oldProgressStatus, repo.ProgressStatus, repo.WikiPresentStatus)

	task := queue.NewIncrementalUpdateTask(ctx, request, diffInfo)
	err = d.TaskQueueManager.SubmitTask(task)
	if err != nil {
		log.Errorf("Failed to submit incremental update task for %s: %v", request.WorkspacePath, err)
		// 如果队列提交失败，回退到直接执行
		return err
	}

	log.Infof("Incremental update task submitted successfully for workspace: %s, task ID: %s",
		request.WorkspacePath, task.ID)
	return nil
}

func (d *DeepwikiService) UpdateWithCodeChunks(ctx context.Context, request definition.CreateDeepwikiRequest) {

	//TODO 基于codebase相关工具调用结果进行更新
}

// 更新wiki检查
func (d *DeepwikiService) checkAndUpdate(ctx context.Context, request definition.CreateDeepwikiRequest) {

	// 禁止remote agent模式下进行wiki检查和更新
	if !support.IsEnableWiki() {
		log.Infof("Skip wiki generate for workspace: %s", request.WorkspacePath)
		return
	}
	// 手动暂停
	if IsWikiGeneratePaused(request.WorkspacePath) {
		log.Infof("wiki generate paused for workspace: %s", request.WorkspacePath)
		return
	}

	isNoWiki, err := support.IsNoWikiExist(request.WorkspacePath)
	if err != nil {
		log.Errorf("check is no wiki exist error. workspace: %s, err: %v", request.WorkspacePath, err)
		return
	}
	if isNoWiki {
		d.submitFullGenerationTask(ctx, request)
		return
	} else {
		wikiRepo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
		if err != nil {
			log.Errorf("get wiki repo error. workspace: %s, err: %v", request.WorkspacePath, err)
			return
		}
		isGitRepo, err := support.IsGitRepo(request.WorkspacePath)
		if err != nil {
			log.Errorf("check is git repo error. workspace: %s, err: %v", request.WorkspacePath, err)
			return
		}
		if !isGitRepo {
			//非git工程检查wiki的上次更新时间
			if time.Since(wikiRepo.GmtModified) < noneGitRepoCheckExpireTime {
				return
			} else {
				//全量更新一次
				d.submitFullGenerationTask(ctx, request)
			}
		} else {
			commitDiffInfo, err := support.GetCommitDiff(request.WorkspacePath, wikiRepo.LastCommitID)
			if err != nil {
				log.Errorf("Get commit diff error. workspace: %s, err: %v", request.WorkspacePath, err)
				return
			}
			err = d.submitIncrementalUpdateTask(ctx, request, commitDiffInfo)
			if err != nil {
				return
			}
		}
	}
}

// performIncrementalUpdate 执行增量更新
func (d *DeepwikiService) performIncrementalUpdate(ctx context.Context, request definition.CreateDeepwikiRequest, diffInfo *definition.CommitDiffInfo) error {
	log.Infof("[layer-based-update] Starting incremental update for workspace: %s, commits: %d", request.WorkspacePath, len(diffInfo.Commits))

	// 获取repo信息
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err != nil {
		return fmt.Errorf("failed to get wiki repo: %w", err)
	}
	if repo == nil {
		return fmt.Errorf("wiki repo not found for workspace: %s", request.WorkspacePath)
	}

	// 注意：progress status已经在submitIncrementalUpdateTask中被设置为processing
	log.Debugf("[layer-based-update] Starting layer based analysis for workspace: %s (status already updated to processing)", request.WorkspacePath)
	// 禁止remote agent模式下进行增量wiki更新
	if !support.IsEnableWiki() {
		log.Infof("Skip wiki generate for workspace: %s", request.WorkspacePath)
		return nil
	}
	// 手动暂停
	if IsWikiGeneratePaused(request.WorkspacePath) {
		log.Infof("wiki generate paused for workspace: %s", request.WorkspacePath)
		return nil
	}
	// 创建分层analysis链
	layeredUpdateChains, err := d.newLayeredCommitDiffBasedUpdateChains(ctx, repo.ID, request.WorkspacePath)
	if err != nil {
		return fmt.Errorf("failed to create layered update chains: %w", err)
	}
	eventData := map[string]string{
		"repo_id":                  repo.ID,
		"line_up_id":               request.LineUpId,
		"trigger_type":             incrementalTriggerType,
		"from_commit_id":           diffInfo.FromCommitID,
		"to_commit_id":             diffInfo.ToCommitID,
		"workspace_path":           request.WorkspacePath,
		"total_changed_line_count": strconv.Itoa(diffInfo.TotalChangedLines),
		"total_commit_count":       strconv.Itoa(diffInfo.TotalCommits),
	}
	remoteUrls, err := git.GetRemoteURLs(request.WorkspacePath)
	if err == nil {
		remoteUrlJson, err := json.Marshal(remoteUrls)
		if err == nil {
			eventData["remote_urls"] = string(remoteUrlJson)
		}
	}
	go sls.Report(sls.EventTypeDeepWikiTrigger, request.RequestId, eventData)

	// 创建agent统计信息，与全量生成保持一致
	agentStats := definition.NewDeepWikiAgentStats(request.RequestId, request.WorkspacePath)
	inputs := map[string]any{
		chainsCommon.KeyCreateDeepwikiRequest: request,
		chainsCommon.KeyRequestId:             request.RequestId,
		chainsCommon.KeySessionId:             request.SessionId,
		chainsCommon.KeyWikiCommitDiff:        diffInfo,
		chainsCommon.KeyPreferredLanguage:     request.PreferredLanguage,
		chainsCommon.KeyWikiGenerateStat:      agentStats,
	}

	// 执行分层更新链
	result, err := layeredUpdateChains.Call(ctx, inputs)
	if err != nil {
		// 增量更新失败时，回滚状态到completed
		log.Errorf("[layer-based-update] Incremental update failed for workspace: %s, error: %v", request.WorkspacePath, err)

		// 回滚repo状态并检查失败的catalog
		err2 := d.handleIncrementalUpdateFailure(ctx, repo, request.WorkspacePath)
		if err2 != nil {
			log.Errorf("[layer-based-update] Failed to handle incremental update failure: %v", err2)
		}

		return fmt.Errorf("layered incremental update failed: %w", err)
	}

	// 检查是否有更新结果
	if finalResult, exists := result[chainsCommon.KeyFinalMergedResult]; exists {
		if mergedResult, ok := finalResult.(*wikiChains.FinalMergedResult); ok {
			log.Infof("[layer-based-update] Incremental update completed: %d layers analyzed, %d affected catalogs, %d new catalogs, %d deleted catalogs",
				mergedResult.Summary.TotalLayersAnalyzed,
				mergedResult.Summary.TotalAffectedCatalogs,
				mergedResult.Summary.TotalNewCatalogs,
				mergedResult.Summary.TotalDeleteCatalogs)
		}
	}
	agentStats.EndCall = time.Now()
	agentStats.CalStatSummary()
	go d.reportIncrementalUpdateMetrics(agentStats, diffInfo, request)

	// 增量更新成功完成时，设置状态为completed
	err = d.handleIncrementalUpdateSuccess(ctx, repo, request.WorkspacePath)
	if err != nil {
		log.Errorf("[layer-based-update] Failed to handle incremental update success: %v", err)
		// 不返回错误，因为更新本身是成功的
	}

	log.Infof("[layer-based-update] Incremental update completed successfully for workspace: %s", request.WorkspacePath)

	return nil
}

// newLayeredCommitDiffBasedUpdateChains 创建分层级的commit diff更新链
func (d *DeepwikiService) newLayeredCommitDiffBasedUpdateChains(ctx context.Context, repoID, workspacePath string) (*chains.SequentialChain, error) {
	// 创建核心服务
	catalogDiffService := wikiService.NewCommitDiffService(storage.GlobalStorageService)
	catalogReader := wikiService.NewLayerCatalogReader(storage.GlobalStorageService, repoID, workspacePath)

	// 1. 预检查：确定最大需要分析的层级
	maxLayer, err := catalogReader.GetMaxLayerLevel()
	if err != nil {
		return nil, fmt.Errorf("failed to get max layer level: %w", err)
	}

	log.Infof("[layer-based-update] Repository has %d layers, creating dynamic analysis chains", maxLayer+1)

	chatChains := []chains.Chain{}

	// 2. 动态构建分层分析chains (从Layer 0到最大层级)
	for layer := 0; layer <= maxLayer; layer++ {
		layerChain := wikiChains.NewLayerAnalysisChain(layer, catalogReader, catalogDiffService)
		chatChains = append(chatChains, layerChain)

		log.Debugf("[layer-based-update] Added Layer %d analysis chain", layer)
	}

	// 3. 最终合并和执行
	chatChains = append(chatChains,
		wikiChains.NewLayeredResultProcessingChain(catalogDiffService), // 合并层级结果并准备处理数据
		wikiChains.NewUpdateWikiChain(),                                // 处理update catalogs - 实际执行数据库更新和内容生成
		wikiChains.NewRepoFinalizationChain(catalogDiffService),        // 在所有catalog操作成功后更新CurrentDocumentStructure和commit id
	)

	log.Infof("[layer-based-update] Created %d chains total (%d layer analysis + 3 support chains)",
		len(chatChains), maxLayer+1)

	return chains.NewSequentialChain(chatChains, []string{}, []string{})
}

// handleIncrementalUpdateSuccess 处理增量更新成功完成时的状态管理
func (d *DeepwikiService) handleIncrementalUpdateSuccess(ctx context.Context, repo *definition.AgentWikiRepo, workspacePath string) error {
	log.Infof("[layer-based-update] Handling incremental update success for workspace: %s", workspacePath)

	// 重新获取最新的repo信息
	latestRepo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return fmt.Errorf("failed to get latest repo info: %w", err)
	}
	if latestRepo == nil {
		return fmt.Errorf("repo not found: %s", workspacePath)
	}

	// 设置状态为completed，并恢复checkpoint为wiki_generation_completed
	latestRepo.ProgressStatus = definition.DeepWikiProgressStatusCompleted
	latestRepo.RecoveryCheckpoint = "wiki_generation_completed"
	latestRepo.WikiPresentStatus = definition.WikiPresentStatusCompleted
	latestRepo.GmtModified = time.Now()

	if err := storage.GlobalStorageService.UpdateWikiRepo(latestRepo); err != nil {
		return fmt.Errorf("failed to update repo status to completed: %w", err)
	}

	log.Infof("[layer-based-update] Successfully updated repo status to completed for workspace: %s", workspacePath)
	return nil
}

// handleIncrementalUpdateFailure 处理增量更新失败时的状态管理
func (d *DeepwikiService) handleIncrementalUpdateFailure(ctx context.Context, repo *definition.AgentWikiRepo, workspacePath string) error {
	log.Infof("[layer-based-update] Handling incremental update failure for workspace: %s", workspacePath)

	// 重新获取最新的repo信息
	latestRepo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return fmt.Errorf("failed to get latest repo info: %w", err)
	}
	if latestRepo == nil {
		return fmt.Errorf("repo not found: %s", workspacePath)
	}

	// 检查失败的catalog并恢复状态
	err = d.recoverFailedCatalogsToCompleted(ctx, latestRepo.ID, workspacePath)
	if err != nil {
		log.Errorf("[layer-based-update] Failed to recover failed catalogs: %v", err)
		// 不返回错误，继续处理repo状态
	}

	// 回滚repo状态到completed（因为增量更新失败不应该影响原有的完成状态）
	latestRepo.ProgressStatus = definition.DeepWikiProgressStatusCompleted
	latestRepo.RecoveryCheckpoint = "wiki_generation_completed"
	latestRepo.WikiPresentStatus = definition.WikiPresentStatusCompleted
	latestRepo.GmtModified = time.Now()

	if err := storage.GlobalStorageService.UpdateWikiRepo(latestRepo); err != nil {
		return fmt.Errorf("failed to rollback repo status to completed: %w", err)
	}

	log.Infof("[layer-based-update] Successfully rolled back repo status to completed for workspace: %s", workspacePath)
	return nil
}

// recoverFailedCatalogsToCompleted 恢复失败的catalog状态
// 根据用户要求：一个repo下的catalog要是failed状态，且拥有对应的wiki item，则说明是在增量中失败导致的，应该把他们的状态重新设置为completed
func (d *DeepwikiService) recoverFailedCatalogsToCompleted(ctx context.Context, repoID, workspacePath string) error {
	log.Infof("[layer-based-update] Starting failed catalogs recovery for repo: %s", repoID)

	// 1. 获取该repo下所有failed状态的catalog
	failedCatalogs, err := storage.GlobalStorageService.GetCatalogsByRepoIDAndStatus(repoID, definition.DeepWikiProgressStatusFailed)
	if err != nil {
		return fmt.Errorf("failed to get failed catalogs: %w", err)
	}

	if len(failedCatalogs) == 0 {
		log.Debugf("[layer-based-update] No failed catalogs found for repo: %s", repoID)
		return nil
	}

	log.Infof("[layer-based-update] Found %d failed catalogs for recovery", len(failedCatalogs))

	recoveredCount := 0
	var recoveryErrors []error

	// 2. 对每个failed catalog检查是否有对应的wiki item
	for _, catalog := range failedCatalogs {
		// 检查是否有对应的wiki item
		wikiItem, err := storage.GlobalStorageService.GetWikiItemByCatalogID(catalog.ID)
		if err != nil {
			log.Errorf("[layer-based-update] Failed to check wiki item for catalog %s: %v", catalog.ID, err)
			recoveryErrors = append(recoveryErrors, fmt.Errorf("failed to check wiki item for catalog %s: %w", catalog.ID, err))
			continue
		}

		if wikiItem != nil {
			// 有对应的wiki item，说明是在增量更新中失败导致的，恢复状态为completed
			log.Infof("[layer-based-update] Recovering catalog %s (%s) from failed to completed (has wiki item: %s)",
				catalog.ID, catalog.Name, wikiItem.ID)

			// 更新catalog状态为completed
			catalog.ProgressStatus = definition.DeepWikiProgressStatusCompleted
			catalog.GmtModified = time.Now()

			err = storage.GlobalStorageService.UpdateCatalog(catalog)
			if err != nil {
				log.Errorf("[layer-based-update] Failed to update catalog %s to completed: %v", catalog.ID, err)
				recoveryErrors = append(recoveryErrors, fmt.Errorf("failed to update catalog %s to completed: %w", catalog.ID, err))
				continue
			}

			// 如果wiki item的状态是failed，也要恢复
			if wikiItem.ProgressStatus == definition.DeepWikiProgressStatusFailed {
				log.Infof("[layer-based-update] Also recovering wiki item %s from failed to completed", wikiItem.ID)

				wikiItem.ProgressStatus = definition.DeepWikiProgressStatusCompleted
				wikiItem.GmtModified = time.Now()

				err = storage.GlobalStorageService.UpdateWikiItem(wikiItem)
				if err != nil {
					log.Errorf("[layer-based-update] Failed to update wiki item %s to completed: %v", wikiItem.ID, err)
					recoveryErrors = append(recoveryErrors, fmt.Errorf("failed to update wiki item %s to completed: %w", wikiItem.ID, err))
					continue
				}
			}

			recoveredCount++
		} else {
			// 没有对应的wiki item，这是真正的失败，不需要恢复
			log.Debugf("[layer-based-update] Catalog %s (%s) is failed but has no wiki item, not recovering (genuine failure)",
				catalog.ID, catalog.Name)
		}
	}

	if len(recoveryErrors) > 0 {
		log.Errorf("[layer-based-update] Catalog recovery completed with %d errors out of %d failed catalogs. Recovered: %d",
			len(recoveryErrors), len(failedCatalogs), recoveredCount)
		// 返回第一个错误，但不阻断整个流程
		return recoveryErrors[0]
	}

	log.Infof("[layer-based-update] Successfully recovered %d failed catalogs to completed state for repo: %s",
		recoveredCount, repoID)

	return nil
}

// StopMonitoring: 停止监控服务
func (d *DeepwikiService) StopMonitoring() {
	// 停止新的后台监控服务
	if d.backgroundMonitor != nil {
		d.backgroundMonitor.Stop()
	}

	// 同时停止任务队列
	if d.TaskQueueManager != nil {
		err := d.TaskQueueManager.Stop()
		if err != nil {
			log.Errorf("Failed to stop task queue manager: %v", err)
		} else {
			log.Debugf("Task queue manager stopped successfully")
		}
	}
}

// RemoveWorkspaceFromMonitoring 从监控中移除指定的workspace
func (d *DeepwikiService) RemoveWorkspaceFromMonitoring(workspacePath string) {
	// 从新的后台监控服务移除
	if d.backgroundMonitor != nil {
		d.backgroundMonitor.UnregisterProject(workspacePath)
		log.Debugf("Removed workspace from background monitoring: %s", workspacePath)
	}
}

// setupIncrementalMonitoring 智能设置增量监控，处理重复注册情况
// DEPRECATED: This method is kept for backward compatibility, but the new architecture handles monitoring automatically
func (d *DeepwikiService) setupIncrementalMonitoring(workspacePath string, preferredLanguage string) error {
	log.Debugf("[setupIncrementalMonitoring] DEPRECATED: This method is now handled automatically by the new architecture for workspace: %s", workspacePath)

	// 在新架构中，监控注册已经在 GenerateUpdate 方法中自动处理
	// 这里只是为了向后兼容性而保留

	// 确保后台监控服务正在运行
	if d.backgroundMonitor != nil && !d.backgroundMonitor.IsRunning() {
		err := d.backgroundMonitor.Start()
		if err != nil {
			log.Errorf("Failed to start background monitor service: %v", err)
		}
	}

	return nil
}

// GetBackgroundMonitorStatus 获取后台监控服务状态
func (d *DeepwikiService) GetBackgroundMonitorStatus() map[string]interface{} {
	if d.backgroundMonitor != nil {
		return d.backgroundMonitor.GetServiceStatus()
	}
	return map[string]interface{}{
		"running":      false,
		"projectCount": 0,
		"error":        "background monitor service not initialized",
	}
}

// GetMonitoredProjects 获取所有被监控的项目列表
func (d *DeepwikiService) GetMonitoredProjects() []string {
	if d.backgroundMonitor != nil {
		return d.backgroundMonitor.GetMonitoredProjects()
	}
	return []string{}
}

// GetProjectMonitorStatus 获取特定项目的监控状态
func (d *DeepwikiService) GetProjectMonitorStatus(workspacePath string) *wikiService.MonitoredProject {
	if d.backgroundMonitor != nil {
		return d.backgroundMonitor.GetProjectStatus(workspacePath)
	}
	return nil
}

// StopWorkspaceTasks 停止指定workspace的所有wiki生成任务和监控
func (d *DeepwikiService) StopWorkspaceTasks(workspacePath string) int {
	var totalStopped int

	// 1. 取消任务队列中该workspace的所有未完成任务
	if d.TaskQueueManager != nil {
		cancelled := d.TaskQueueManager.CancelTasksForWorkspace(workspacePath)
		totalStopped += cancelled
		log.Debugf("Cancelled %d queue tasks for workspace: %s", cancelled, workspacePath)
	}

	// 2. 从监控服务中移除该workspace
	d.RemoveWorkspaceFromMonitoring(workspacePath)

	// 3. 清理workspace锁
	if _, exists := d.locks.Load(workspacePath); exists {
		d.locks.Delete(workspacePath)
		log.Debugf("Removed workspace lock for: %s", workspacePath)
	}

	if totalStopped > 0 {
		log.Infof("Successfully stopped %d wiki generation tasks for workspace: %s", totalStopped, workspacePath)
	} else {
		log.Debugf("No active wiki generation tasks found for workspace: %s", workspacePath)
	}

	return totalStopped
}

// 全量构建
func (d *DeepwikiService) generateNew(ctx context.Context, request definition.CreateDeepwikiRequest) {
	// 禁止remote agent模式下进行全量wiki生成
	if !support.IsEnableWiki() {
		log.Infof("Skip wiki generate for workspace: %s", request.WorkspacePath)
		return
	}
	// 手动暂停
	if IsWikiGeneratePaused(request.WorkspacePath) {
		log.Infof("wiki generate paused for workspace: %s", request.WorkspacePath)
		return
	}

	// 获取已存在的repo（应该在任务提交时已创建）
	actualRepo, err := d.storageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err != nil {
		log.Errorf("Failed to get existing repo for workspace: %s, err: %v", request.WorkspacePath, err)
		return
	}

	// 检查repo状态，确定是否继续处理
	if actualRepo.ProgressStatus == definition.DeepWikiProgressStatusCompleted {
		log.Debugf("Wiki repo already completed, skipping generation for workspace: %s", request.WorkspacePath)
		return
	}
	if actualRepo.ProgressStatus == definition.DeepWikiProgressStatusProcessing {
		log.Debugf("Wiki repo is currently processing, skipping generation for workspace: %s", request.WorkspacePath)
		return
	}

	// 如果状态是失败，先更新repo状态为pending
	if actualRepo.ProgressStatus == definition.DeepWikiProgressStatusFailed {
		actualRepo.ProgressStatus = definition.DeepWikiProgressStatusPending
		// 失败重试时，设置为LINING状态表示重新排队
		actualRepo.WikiPresentStatus = definition.WikiPresentStatusLining
		actualRepo.GmtModified = time.Now()
		if updateErr := d.storageService.UpdateWikiRepo(actualRepo); updateErr != nil {
			log.Errorf("Failed to update failed repo status to pending: %v", updateErr)
			return
		}
		log.Debugf("Updated failed repo status to pending with WikiPresentStatus LINING for workspace: %s", request.WorkspacePath)
	}

	// Pending状态允许继续处理（恢复场景和失败重试场景）
	if actualRepo.ProgressStatus == definition.DeepWikiProgressStatusPending {
		log.Debugf("Wiki repo is pending (recovery/retry scenario), continuing with generation for workspace: %s", request.WorkspacePath)
		// 继续执行，不return
	}
	eventData := map[string]string{
		"repo_id":        actualRepo.ID,
		"line_up_id":     request.LineUpId,
		"trigger_type":   fullTriggerType,
		"commit_id":      actualRepo.LastCommitID,
		"workspace_path": request.WorkspacePath,
	}
	remoteUrls, err := git.GetRemoteURLs(request.WorkspacePath)
	if err == nil {
		remoteUrlJson, err := json.Marshal(remoteUrls)
		if err == nil {
			eventData["remote_urls"] = string(remoteUrlJson)
		}
	}
	go sls.Report(sls.EventTypeDeepWikiTrigger, request.RequestId, eventData)

	c, err := newDeepWikiCreateChains()
	if err != nil {
		log.Errorf("create chains error: %v", err)
		// todo 创建失败状态链来处理错误
		failChain := wikiChains.NewDeepWikiStatusChain("fail")
		inputs := map[string]any{
			chainsCommon.KeyCreateDeepwikiRequest: request,
		}
		failChain.Call(ctx, inputs)
		return
	}

	agentStats := definition.NewDeepWikiAgentStats(request.RequestId, request.WorkspacePath)
	inputs := map[string]any{
		chainsCommon.KeyCreateDeepwikiRequest: request,
		chainsCommon.KeyRequestId:             request.RequestId,
		chainsCommon.KeySessionId:             request.SessionId,
		chainsCommon.KeyPreferredLanguage:     request.PreferredLanguage,
		chainsCommon.KeyWikiGenerateStat:      agentStats,
	}

	_, err = chains.Call(ctx, c, inputs)
	if err != nil {
		log.Errorf("call chains error: %v", err)
		// 创建失败状态链来处理错误
		failChain := wikiChains.NewDeepWikiStatusChain("fail")
		failInputs := map[string]any{
			chainsCommon.KeyCreateDeepwikiRequest: request,
			chainsCommon.KeyWikiGenerateStat:      agentStats,
			common.KeyWikiExecuteError:            err,
		}
		failChain.Call(ctx, failInputs)
	}
	// 上报deepwiki全量生成埋点
	agentStats.EndCall = time.Now()
	agentStats.CalStatSummary()
	go d.reportFullGenerationMetrics(agentStats, request)

	err = support.SaveWikiGenerateResponseToFile(util.ToJsonStr(agentStats), "full-generate-stat")
	if err != nil {
		log.Errorf("Failed to save wiki generate response to file: %v", err)
	}

	repoName := util.GetProjectName(request.WorkspacePath)
	log.Infof("Deepwiki: full-generation of project wiki finished - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)
	return
}

// 创建项目wiki全量创建的流程
func newDeepWikiCreateChains() (*chains.SequentialChain, error) {
	// 创建服务实例
	catalogueFilterService := wikiService.NewCatalogueFilterService()
	catalogueService := wikiService.NewGenerateCatalogueService()

	chatChains := []chains.Chain{
		wikiChains.NewRecoveryCheckChain(),         // 恢复检查（新增）
		wikiChains.NewDeepWikiStatusChain("start"), // 开始状态管理
		&wikiChains.GenerateReadmeChain{},
		&wikiChains.GenerateOverviewChain{},
		wikiChains.NewCatalogueFilterChain(catalogueFilterService),
		wikiChains.NewGenerateCataloguePlanChain(catalogueService),
		wikiChains.NewGenerateCatalogueChain(catalogueService),
		wikiChains.NewGenerateWikiChain(),
		wikiChains.NewDeepWikiStatusChain("complete"), // 完成状态管理
	}
	delegate, err := chains.NewSequentialChain(chatChains, []string{}, []string{})
	if err != nil {
		return nil, err
	}
	return delegate, nil
}

// 创建基于问答过程中的code chunk的wiki增量更新流程
func UpdateChatCode(ctx context.Context, request definition.CreateDeepwikiRequest) {

	// 禁止remote agent模式下进行基于代码块的wiki增量更新
	if !support.IsEnableWiki() {
		log.Infof("Skip wiki generate for workspace: %s", request.WorkspacePath)
		return
	}
	// 手动暂停
	if IsWikiGeneratePaused(request.WorkspacePath) {
		log.Infof("wiki generate paused for workspace: %s", request.WorkspacePath)
		return
	}

	sessionContexts, _ := session.GetSessionContexts(request.SessionId, session.SessionFlowToolCallResultContext)
	if sessionContexts == nil || len(sessionContexts) <= 0 {
		return
	}
	codeChunks := extractRagCodeChunks(sessionContexts)
	if codeChunks == nil || len(codeChunks) <= 0 {
		return
	}
	c, err := newCodeChunkBasedUpdateChains()
	if err != nil {
		log.Errorf("create chains error: %v", err)
		return
	}

	// 1. 设置ctx信息
	workspaceFolder := definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				Name: util.GetProjectName(request.WorkspacePath),
				URI:  request.WorkspacePath,
			},
		},
	}

	ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, workspaceFolder)

	// 创建agent统计信息，与全量生成保持一致
	agentStats := definition.NewDeepWikiAgentStats(request.RequestId, request.WorkspacePath)

	ctx = context.WithValue(ctx, chainsCommon.KeyPreferredLanguage, request.PreferredLanguage)
	inputs := map[string]any{
		chainsCommon.KeyCreateDeepwikiRequest:    request,
		chainsCommon.KeyRequestId:                request.RequestId,
		chainsCommon.KeySessionId:                request.SessionId,
		chainsCommon.KeyWikiConversationRagCodes: codeChunks,
		chainsCommon.KeyPreferredLanguage:        request.PreferredLanguage,
		chainsCommon.KeyWikiGenerateStat:         agentStats,
	}
	newInputs, err := chains.Call(ctx, c, inputs)
	if err != nil {
		log.Errorf("call chains error: %v", err)
		return
	} else {
		//增量更新
		//go api.MemoryIndexAll(ctx, util.GetProjectName(request.WorkspacePath), request.WorkspacePath)
	}

	// 完善agent统计信息，与全量生成保持一致
	agentStats.EndCall = time.Now()
	agentStats.CalStatSummary()

	// 保存基于代码块的增量更新统计信息
	err = support.SaveWikiGenerateResponseToFile(util.ToJsonStr(agentStats), "chat-code-update-stat")
	if err != nil {
		log.Errorf("Failed to save wiki generate response to file: %v", err)
	}

	log.Infof("newInputs: %v", newInputs)

	log.Infof("generate project wiki finished. workspace: %s", request.WorkspacePath)
	return
}

func extractRagCodeChunks(sessionContexts []session.SessionFlowContext) []indexer.CodeChunk {
	if sessionContexts == nil || len(sessionContexts) <= 0 {
		return nil
	}
	var codeChunks []indexer.CodeChunk
	for _, s := range sessionContexts {
		if s.ContextKey == session.SessionContextKeySearchCodebase {
			if s.ContextValue != nil {
				chunks := s.ContextValue.([]indexer.CodeChunk)
				if chunks != nil {
					codeChunks = append(codeChunks, chunks...)
				}
			}

		}
	}
	return codeChunks
}

// 创建基于代码RAG的wiki增量更新流程
func newCodeChunkBasedUpdateChains() (*chains.SequentialChain, error) {
	chatChains := []chains.Chain{
		&wikiChains.CatalogueUpdateWithCodeChain{},
		wikiChains.NewGenerateCatalogueDiffChainDefault(),
		wikiChains.NewUpdateWikiChain(),
	}
	delegate, err := chains.NewSequentialChain(chatChains, []string{}, []string{})
	if err != nil {
		return nil, err
	}
	return delegate, nil
}

// 创建单个wiki item重新生成的流程
func newSingleItemRegenerationChains() (*chains.SequentialChain, error) {
	chatChains := []chains.Chain{
		wikiChains.NewRegenerateSingleItemChain(),
	}
	delegate, err := chains.NewSequentialChain(chatChains, []string{}, []string{})
	if err != nil {
		return nil, err
	}
	return delegate, nil
}

// DeleteWikiIndex 删除指定工程的wiki向量化索引
func (d *DeepwikiService) DeleteWikiIndex(ctx context.Context, workspacePath string) error {
	log.Infof("[DeepwikiService] Deleting wiki index for workspace: %s", workspacePath)

	// 获取workspace信息
	workspaceFolder := definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				Name: util.GetProjectName(workspacePath),
				URI:  workspacePath,
			},
		},
	}
	ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, workspaceFolder)

	// 获取wikiId
	wikiId := util.GetProjectName(workspacePath)

	// 调用内存索引删除函数
	err := api.MemoryDeleteWiki(ctx, wikiId, workspacePath)
	if err != nil {
		log.Errorf("[DeepwikiService] Failed to delete wiki index for workspace %s: %v", workspacePath, err)
		return err
	}

	log.Infof("[DeepwikiService] Successfully deleted wiki index for workspace: %s", workspacePath)
	return nil
}

// RebuildWikiIndex 重建指定工程的wiki向量化索引
func (d *DeepwikiService) RebuildWikiIndex(ctx context.Context, workspacePath string) error {
	log.Infof("[DeepwikiService] Rebuilding wiki index for workspace: %s", workspacePath)

	// 获取workspace信息
	workspaceFolder := definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				Name: util.GetProjectName(workspacePath),
				URI:  workspacePath,
			},
		},
	}
	ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, workspaceFolder)

	// 检查wiki repo是否存在
	wikiRepo, err := d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("[DeepwikiService] Failed to get wiki repo for workspace %s: %v", workspacePath, err)
		return fmt.Errorf("failed to get wiki repo: %w", err)
	}

	if wikiRepo == nil {
		log.Errorf("[DeepwikiService] Wiki repo not found for workspace: %s", workspacePath)
		return fmt.Errorf("wiki repo not found for workspace: %s", workspacePath)
	}

	// 确保wiki已经生成完成
	if wikiRepo.ProgressStatus != definition.DeepWikiProgressStatusCompleted {
		log.Errorf("[DeepwikiService] Wiki is not completed for workspace %s, current status: %s", workspacePath, wikiRepo.ProgressStatus)
		return fmt.Errorf("wiki is not completed, current status: %s", wikiRepo.ProgressStatus)
	}

	// 获取wikiId
	wikiId := util.GetProjectName(workspacePath)

	// 调用内存索引重建函数
	err = api.MemoryRebuildWiki(ctx, wikiId, workspacePath)
	if err != nil {
		log.Errorf("[DeepwikiService] Failed to rebuild wiki index for workspace %s: %v", workspacePath, err)
		return err
	}

	log.Infof("[DeepwikiService] Successfully rebuilt wiki index for workspace: %s", workspacePath)
	return nil
}

// GetKnowledgeService 获取knowledge集成服务
func (d *DeepwikiService) GetKnowledgeService() *wikiService.KnowledgeIntegrationService {
	return GlobalKnowledgeService
}

// SetReposWithFailedCatalogsToFailed 将含有failed状态catalog的repo都设置成failed状态
func (d *DeepwikiService) SetReposWithFailedCatalogsToFailed(ctx context.Context) error {
	log.Infof("[SetReposWithFailedCatalogsToFailed] Starting to check and update repos with failed catalogs...")

	// 获取所有的repo
	repos, err := d.storageService.GetAllWikiRepos()
	if err != nil {
		log.Errorf("[SetReposWithFailedCatalogsToFailed] Failed to get all repos: %v", err)
		return fmt.Errorf("failed to get all repos: %w", err)
	}

	if len(repos) == 0 {
		log.Infof("[SetReposWithFailedCatalogsToFailed] No repos found")
		return nil
	}

	log.Infof("[SetReposWithFailedCatalogsToFailed] Found %d repos to check", len(repos))

	var updatedCount int
	var errors []error

	// 检查每个repo是否含有failed的catalog
	for _, repo := range repos {
		// 获取repo的catalog状态统计
		statusCount, err := d.storageService.GetCatalogStatusByRepoID(repo.ID)
		if err != nil {
			log.Errorf("[SetReposWithFailedCatalogsToFailed] Failed to get catalog status for repo %s: %v", repo.ID, err)
			errors = append(errors, fmt.Errorf("failed to get catalog status for repo %s: %w", repo.ID, err))
			continue
		}

		// 检查是否有failed的catalog
		failedCount := statusCount[definition.DeepWikiProgressStatusFailed]
		if failedCount > 0 {
			// 如果repo状态不是failed，则更新为failed
			if repo.ProgressStatus != definition.DeepWikiProgressStatusFailed {
				oldStatus := repo.ProgressStatus
				repo.ProgressStatus = definition.DeepWikiProgressStatusFailed
				repo.GmtModified = time.Now()

				err = d.storageService.UpdateWikiRepo(repo)
				if err != nil {
					log.Errorf("[SetReposWithFailedCatalogsToFailed] Failed to update repo %s status to failed: %v", repo.ID, err)
					errors = append(errors, fmt.Errorf("failed to update repo %s to failed: %w", repo.ID, err))
					continue
				}

				updatedCount++
				log.Infof("[SetReposWithFailedCatalogsToFailed] Updated repo %s (%s) status: %s -> FAILED (contains %d failed catalogs)",
					repo.ID, repo.Name, oldStatus, failedCount)
			} else {
				log.Debugf("[SetReposWithFailedCatalogsToFailed] Repo %s (%s) already has FAILED status (contains %d failed catalogs)",
					repo.ID, repo.Name, failedCount)
			}
		} else {
			log.Debugf("[SetReposWithFailedCatalogsToFailed] Repo %s (%s) has no failed catalogs, current status: %s",
				repo.ID, repo.Name, repo.ProgressStatus)
		}
	}

	if len(errors) > 0 {
		log.Errorf("[SetReposWithFailedCatalogsToFailed] Completed with %d errors, updated %d repos", len(errors), updatedCount)
		for _, err := range errors {
			log.Errorf("[SetReposWithFailedCatalogsToFailed] Error: %v", err)
		}
		return fmt.Errorf("some repos failed to update: %d errors", len(errors))
	}

	log.Infof("[SetReposWithFailedCatalogsToFailed] Successfully completed: updated %d repos to FAILED status", updatedCount)
	return nil
}

// RecoverFailedRepoForWorkspace 恢复指定workspace的failed状态repo
func (d *DeepwikiService) RecoverFailedRepoForWorkspace(request definition.CreateDeepwikiRequest, repo *definition.AgentWikiRepo) {
	workspacePath := request.WorkspacePath
	log.Debugf("[checkAndRecoverFailedRepoForWorkspace] Checking failed repo for workspace: %s", workspacePath)

	// 创建恢复服务管理器
	recoveryManager := wikiService.NewRecoveryServiceManager(d.storageService)

	// 执行恢复准备
	err := recoveryManager.GetRecoveryService().RecoverRepo(repo)
	if err != nil {
		log.Errorf("[checkAndRecoverFailedRepoForWorkspace] Failed to prepare repo for recovery %s: %v", repo.ID, err)
		return
	}

	log.Infof("[checkAndRecoverFailedRepoForWorkspace] Successfully prepared failed repo for recovery: %s", workspacePath)
}

// shouldPerformIndexCheck 检查是否需要执行索引检查（基于时间间隔和repo修改时间）
func (d *DeepwikiService) shouldPerformIndexCheck(workspacePath string) bool {
	// 获取repo信息以检查修改时间
	repo, err := d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("[shouldPerformIndexCheck] Failed to get repo for %s: %v", workspacePath, err)
		return false
	}
	if repo == nil {
		log.Debugf("[shouldPerformIndexCheck] No repo found for %s", workspacePath)
		return false
	}

	// 检查repo是否在1.5小时内被修改过
	repoModifiedRecently := time.Since(repo.GmtModified) < 90*time.Minute

	// 获取上次检查时间
	if lastCheck, ok := d.lastIndexCheckTimes.Load(workspacePath); ok {
		lastCheckTime := lastCheck.(time.Time)
		timeSinceLastCheck := time.Since(lastCheckTime)

		// 如果repo在1.5小时内被修改过，即使在3小时冷却期内也要检查
		if repoModifiedRecently {
			log.Debugf("[shouldPerformIndexCheck] Repo modified recently (%v ago), performing index check for %s",
				time.Since(repo.GmtModified), workspacePath)
			d.lastIndexCheckTimes.Store(workspacePath, time.Now())
			return true
		}

		// 如果距离上次检查少于3小时且repo未被近期修改，跳过检查
		if timeSinceLastCheck < 3*time.Hour {
			log.Debugf("[shouldPerformIndexCheck] Skipping index check for %s, last check was %v ago, repo modified %v ago",
				workspacePath, timeSinceLastCheck, time.Since(repo.GmtModified))
			return false
		}
	}

	// 更新检查时间
	d.lastIndexCheckTimes.Store(workspacePath, time.Now())
	log.Debugf("[shouldPerformIndexCheck] Performing index check for %s (repo modified %v ago)",
		workspacePath, time.Since(repo.GmtModified))
	return true
}

// checkAndFixWikiIndex 检查并修复wiki索引完整性
func (d *DeepwikiService) checkAndFixWikiIndex(ctx context.Context, workspacePath string) {
	log.Debugf("[checkAndFixWikiIndex] Checking wiki index completeness for workspace: %s", workspacePath)

	// 获取所有wiki item IDs
	wikiIds, err := d.storageService.GetWikiItemIdsByRepoAndWorkspace(workspacePath)
	if err != nil {
		log.Errorf("[checkAndFixWikiIndex] Failed to get wiki item ids for workspace %s: %v", workspacePath, err)
		return
	}

	if len(wikiIds) == 0 {
		log.Debugf("[checkAndFixWikiIndex] No wiki items found for workspace: %s", workspacePath)
		return
	}

	log.Debugf("[checkAndFixWikiIndex] Found %d wiki items for workspace: %s", len(wikiIds), workspacePath)

	// 检查哪些没有被index完成
	failedIds := api.GetBuiltRecords(ctx, workspacePath, wikiIds)

	if len(failedIds) == 0 {
		log.Debugf("[checkAndFixWikiIndex] All wiki items are already indexed for workspace: %s", workspacePath)
		return
	}

	log.Infof("[checkAndFixWikiIndex] Found %d unindexed wiki items for workspace: %s, starting to index them",
		len(failedIds), workspacePath)

	wikiRepo, err := d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil || wikiRepo == nil {
		log.Errorf("Failed to get wiki repo for workspace: %s", workspacePath)
		return
	}
	repoId := wikiRepo.ID // 使用数据库中的唯一ID
	api.MemoryIndexUpdate(ctx, repoId, workspacePath, failedIds, []string{})

	log.Infof("[checkAndFixWikiIndex] Successfully indexed %d wiki items for workspace: %s",
		len(failedIds), workspacePath)
	return
}

// reportFullGenerationMetrics 上报全量生成埋点
func (d *DeepwikiService) reportFullGenerationMetrics(agentStats *definition.DeepWikiAgentStats, request definition.CreateDeepwikiRequest) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Error occurred in reportFullGenerationMetrics: %v", r)
		}
	}()

	// 构建埋点数据
	eventData := map[string]string{
		"generation_type": fullTriggerType,
		"line_up_id":      request.LineUpId,
	}
	agentStats.Export(eventData)
	d.appendCatalogStatus(agentStats.WorkspacePath, eventData)
	mtree := tree.NewWorkspaceMerkleTree(agentStats.WorkspacePath)
	if mtree != nil {
		eventData["file_count"] = strconv.Itoa(mtree.GetLeafNodeCount())
	}

	// 3. 上报埋点
	sls.Report(sls.EventTypeDeepWikiStatistics, agentStats.RequestId, eventData)
}

// reportIncrementalUpdateMetrics 上报增量更新埋点
func (d *DeepwikiService) reportIncrementalUpdateMetrics(agentStats *definition.DeepWikiAgentStats, diffInfo *definition.CommitDiffInfo, request definition.CreateDeepwikiRequest) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Error occurred in reportIncrementalUpdateMetrics: %v", r)
		}
	}()

	// 构建埋点数据
	eventData := map[string]string{
		"generation_type": incrementalTriggerType,
		"line_up_id":      request.LineUpId,
	}
	agentStats.Export(eventData)
	// 获取目录生成状态
	d.appendCatalogStatus(agentStats.WorkspacePath, eventData)
	mtree := tree.NewWorkspaceMerkleTree(agentStats.WorkspacePath)
	if mtree != nil {
		eventData["file_count"] = strconv.Itoa(mtree.GetLeafNodeCount())
	}
	if diffInfo != nil {
		eventData["total_changed_line_count"] = strconv.Itoa(diffInfo.TotalChangedLines)
		eventData["total_commit_count"] = strconv.Itoa(diffInfo.TotalCommits)
	}

	// 3. 上报埋点
	sls.Report(sls.EventTypeDeepWikiStatistics, agentStats.RequestId, eventData)
}

// calculateFailureRates 计算目录生成失败率和wiki内容生成失败率
func (d *DeepwikiService) appendCatalogStatus(workspacePath string, eventData map[string]string) {
	// 1. 获取目录状态统计
	repo, err := d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("Failed to get repo for failure rate calculation: %v", err)
		return
	}

	if repo == nil {
		log.Debugf("No repo found for workspace: %s", workspacePath)
		return
	}
	eventData["repo_id"] = repo.ID
	eventData["repo_process_status"] = repo.ProgressStatus
	eventData["repo_present_status"] = repo.WikiPresentStatus

	// 2. 获取catalog状态统计
	wikiStatusCounts, err := d.storageService.GetCatalogStatusByRepoID(repo.ID)
	if err != nil {
		log.Errorf("Failed to get catalog status for failure rate calculation: %v", err)
	} else {
		for status, count := range wikiStatusCounts {
			eventData[fmt.Sprintf("wiki_status_%s", status)] = strconv.Itoa(count)
		}
	}
}

func PauseGenerate(workspace definition.WorkspaceInfo) error {
	workspacePath, ok := workspace.GetWorkspaceFolder()
	if !ok {
		return cosyError.New(cosyError.InternalError, "Failed to get workspace folder")
	}

	log.Debugf("PauseGenerate start for workspacePath: %s", workspacePath)
	repo, _ := GlobalWikiService.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if repo == nil {
		log.Errorf("[PauseGenerate] Failed to get repo by workspacePath: %s", workspacePath)
		return cosyError.New(cosyError.InternalError, "failed to get repo by workspacePath")
	}

	// 将repo状态设置为paused，中断生成过程
	repo.ProgressStatus = definition.DeepWikiProgressStatusPausing
	err := GlobalWikiService.storageService.UpdateWikiRepoByManual(repo)
	if err != nil {
		log.Errorf("failed to update repo status to paused, repo:%+v err: %v", repo, err)
		return cosyError.New(cosyError.InternalError, "pause wiki generate fail")
	}

	// 判断是否有处理中的任务,取消执行中的任务
	tasks := GlobalWikiService.TaskQueueManager.GetTasksByWorkspace(workspacePath)
	for _, task := range tasks {
		if !task.IsFinished() && task.CancelFunc != nil {
			log.Debugf("[PauseGenerate] task %+v has been cancelled", task)
			task.CancelFunc()
			task.MarkCancelled()
		}
	}

	// 清空缓存
	delete(storage.WikiPauseMap, workspacePath)

	log.Debugf("PauseGenerate end for workspacePath: %s", workspacePath)
	return nil
}

func StartGenerate(ctx context.Context, workspace definition.WorkspaceInfo) error {

	// 设置项目
	workspacePath, ok := workspace.GetWorkspaceFolder()
	if !ok {
		return cosyError.New(cosyError.InternalError, "Failed to get workspace folder")
	}
	ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, workspace)

	// 设置fileIndexer
	if fileIndexer := ctx.Value(definition.ContextKeyFileIndexer); fileIndexer == nil {
		log.Warnf("[deepwiki-StartGenerate] No fileIndexer in context for workspace: %s", workspacePath)
		// 尝试从全局工作区树索引器获取已初始化的fileIndexer
		if fileIndexer, ok := workspace_tree_indexing.WorkspaceTreeFileIndexers.GetFileIndexer(workspacePath); ok {
			ctx = context.WithValue(ctx, definition.ContextKeyFileIndexer, fileIndexer)
			log.Debugf("[deepwiki-StartGenerate] Monitor: Successfully obtained fileIndexer from global WorkspaceTreeFileIndexers for workspace: %s", workspacePath)
		} else {
			log.Debugf("[deepwiki-StartGenerate] Monitor: No fileIndexer found in global WorkspaceTreeFileIndexers, performIncrementalUpdate will handle initialization for workspace: %s", workspacePath)
		}
	} else {
		log.Debugf("[deepwiki-StartGenerate] Using existing fileIndexer from context")
	}

	repo, _ := GlobalWikiService.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if repo == nil {
		log.Errorf("[StartGenerate] Failed to get repo by workspacePath: %s", workspacePath)
		return cosyError.New(cosyError.InternalError, "failed to get repo by workspacePath")
	}

	//判断是否中断中
	if repo.ProgressStatus == definition.DeepWikiProgressStatusPausing {
		log.Debugf("Start wiki generate, status error:%s", repo.ProgressStatus)
		return cosyError.New(cosyError.InternalError, "Start wiki generate, status error")
	}

	// 获取repo的catalog状态统计
	statusCount, err := GlobalWikiService.storageService.GetCatalogStatusByRepoID(repo.ID)
	if err != nil {
		log.Errorf("[StartGenerate] Failed to get catalog status for repo %s: %v", repo.ID, err)
		return cosyError.New(cosyError.InternalError, "Failed to get catalog status")
	}

	// 检查是否有failed的catalog
	failedCount := statusCount[definition.DeepWikiProgressStatusFailed]
	waitingCount := statusCount[definition.DeepWikiProgressStatusWaiting]
	pendingCount := statusCount[definition.DeepWikiProgressStatusPending]
	totalCatalogs := 0
	for _, count := range statusCount {
		totalCatalogs += count
	}

	if totalCatalogs == 0 || (failedCount > 0 || waitingCount > 0 || pendingCount > 0) {
		log.Infof("start generate full wiki for workspace: %s  ", workspacePath)
		repo.ProgressStatus = definition.DeepWikiProgressStatusFailed
	} else {
		log.Infof("start generate incremental wiki for workspace: %s  ", workspacePath)
		repo.ProgressStatus = definition.DeepWikiProgressStatusCompleted
	}

	// 恢复repo状态，触发生成逻辑
	err = GlobalWikiService.storageService.UpdateWikiRepoByManual(repo)
	if err != nil {
		log.Errorf("failed to update repo status to paused, repo:%+v err: %v", repo, err)
		return cosyError.New(cosyError.InternalError, "pause wiki generate fail")
	}

	// 更新 WikiPresentStatus 基于新的 ProgressStatus
	repo.WikiPresentStatus = GlobalWikiService.storageService.DetermineWikiPresentStatusFromRepo(repo, nil)
	err = GlobalWikiService.storageService.UpdateWikiRepo(repo)
	if err != nil {
		log.Errorf("failed to update repo status to paused, repo:%+v err: %v", repo, err)
		return cosyError.New(cosyError.InternalError, "pause wiki generate fail")
	}

	// 清空缓存
	delete(storage.WikiPauseMap, workspacePath)

	go GlobalWikiService.GenerateUpdate(ctx, definition.CreateDeepwikiRequest{
		RequestId:         uuid.NewString(),
		WorkspacePath:     workspacePath,
		PreferredLanguage: definition.GetPreferredLanguageDesc(global.PreferredLanguage),
	})

	return nil
}

func GetCatalogs(workspace definition.WorkspaceInfo) []*definition.Catalog {
	workspacePath, ok := workspace.GetWorkspaceFolder()
	if !ok {
		log.Errorf("[GetCatalogs] Failed to get workspace folder: %s", workspacePath)
		return []*definition.Catalog{}
	}

	repo, _ := GlobalWikiService.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if repo == nil {
		log.Errorf("[GetCatalogs] Failed to get repo for workspace: %s", workspacePath)
		return []*definition.Catalog{}
	}

	catalogs, err := GlobalWikiService.storageService.GetCatalogsByRepoID(repo.ID)
	if err != nil {
		log.Errorf("[GetCatalogs] Failed to get catalogs for repo id: %s", repo.ID)
		return []*definition.Catalog{}
	}

	if catalogs == nil || len(catalogs) == 0 {
		log.Debugf("[GetCatalogs] catalogs is empty, repo id: %s", repo.ID)
		return []*definition.Catalog{}
	}

	return buildCatalogs(catalogs)
}

func buildCatalogs(catalogs []*definition.AgentWikiCatalog) []*definition.Catalog {
	catalogMap := make(map[string]*definition.Catalog)
	var rootCatalogs []*definition.Catalog

	// 第一步：将所有节点放入 map 中
	for _, raw := range catalogs {
		catalog := &definition.Catalog{
			Id:         raw.ID,
			Name:       raw.Name,
			Status:     raw.ProgressStatus,
			SubCatalog: []*definition.Catalog{},
		}
		catalogMap[raw.ID] = catalog
	}

	// 第二步：建立父子关系
	for _, raw := range catalogs {
		if parent, exists := catalogMap[raw.ParentID]; exists && raw.ParentID != "" {
			parent.SubCatalog = append(parent.SubCatalog, catalogMap[raw.ID])
		} else if raw.ParentID == "" || !exists {
			// 如果是根节点或父节点不存在，则加入到根目录
			rootCatalogs = append(rootCatalogs, catalogMap[raw.ID])
		}
	}

	return rootCatalogs
}

func IsWikiGeneratePaused(workspacePath string) bool {
	return GlobalWikiService.storageService.IsWikiGeneratePause(workspacePath)
}

// checkForFailedCatalogs 检查指定workspace是否有failed/waiting/pending状态的catalog
// 返回 true 表示检测到failed/waiting/pending catalogs
func (d *DeepwikiService) checkForFailedCatalogs(workspacePath string) (bool, error) {
	log.Debugf("[checkForFailedCatalogs] Checking catalog status for workspace: %s", workspacePath)

	// 获取当前workspace的repo
	repo, err := d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("[checkForFailedCatalogs] Failed to get repo for workspace %s: %v", workspacePath, err)
		return false, err
	}

	if repo == nil {
		log.Debugf("[checkForFailedCatalogs] No repo found for workspace: %s", workspacePath)
		return false, nil
	}

	// 获取repo的catalog状态统计
	statusCount, err := d.storageService.GetCatalogStatusByRepoID(repo.ID)
	if err != nil {
		log.Errorf("[checkForFailedCatalogs] Failed to get catalog status for repo %s: %v", repo.ID, err)
		return false, err
	}

	// 检查是否有failed/waiting/pending的catalog
	failedCount := statusCount[definition.DeepWikiProgressStatusFailed]
	waitingCount := statusCount[definition.DeepWikiProgressStatusWaiting]
	pendingCount := statusCount[definition.DeepWikiProgressStatusPending]

	hasFailedItems := failedCount > 0 || waitingCount > 0 || pendingCount > 0

	if hasFailedItems {
		log.Debugf("[checkForFailedCatalogs] Found problematic catalogs for workspace %s: %d failed, %d waiting, %d pending",
			workspacePath, failedCount, waitingCount, pendingCount)
	} else {
		log.Debugf("[checkForFailedCatalogs] All catalogs are in good state for workspace: %s", workspacePath)
	}

	return hasFailedItems, nil
}

// checkAllCatalogsCompleted 检查指定workspace的所有catalog是否都是completed状态
// 返回 true 表示所有catalog都是completed状态
func (d *DeepwikiService) checkAllCatalogsCompleted(workspacePath string) (bool, error) {
	log.Debugf("[checkAllCatalogsCompleted] Checking if all catalogs are completed for workspace: %s", workspacePath)

	// 获取当前workspace的repo
	repo, err := d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("[checkAllCatalogsCompleted] Failed to get repo for workspace %s: %v", workspacePath, err)
		return false, err
	}

	if repo == nil {
		log.Debugf("[checkAllCatalogsCompleted] No repo found for workspace: %s", workspacePath)
		return false, nil
	}

	// 获取repo的catalog状态统计
	statusCount, err := d.storageService.GetCatalogStatusByRepoID(repo.ID)
	if err != nil {
		log.Errorf("[checkAllCatalogsCompleted] Failed to get catalog status for repo %s: %v", repo.ID, err)
		return false, err
	}

	// 如果没有任何catalog，认为不满足条件
	totalCatalogs := 0
	for _, count := range statusCount {
		totalCatalogs += count
	}

	if totalCatalogs == 0 {
		log.Debugf("[checkAllCatalogsCompleted] No catalogs found for workspace: %s", workspacePath)
		return false, nil
	}

	// 检查是否所有catalog都是completed状态
	completedCount := statusCount[definition.DeepWikiProgressStatusCompleted]
	allCompleted := completedCount == totalCatalogs && totalCatalogs > 0

	log.Debugf("[checkAllCatalogsCompleted] Catalog status for workspace %s: %d completed out of %d total",
		workspacePath, completedCount, totalCatalogs)

	return allCompleted, nil
}

// TriggerByStatus 根据状态触发wiki功能
func (d *DeepwikiService) TriggerByStatus(ctx context.Context) {
	// 从context中获取workspace信息
	workspaceInfo, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if !ok || len(workspaceInfo.WorkspaceFolders) == 0 {
		log.Errorf("Trigger by status, failed to get workspace info from context")
		return
	}
	workspacePath := workspaceInfo.WorkspaceFolders[0].URI
	repo, err := d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("Trigger by status, failed to get wiki repo: %v", err)
		return
	}
	if repo == nil {
		log.Warnf("Trigger by status, wiki repo not found for workspace: %s", workspacePath)
		return
	}

	// 失败状态恢复
	if repo.WikiPresentStatus == definition.WikiPresentTooManyWorkspaceFailed || repo.WikiPresentStatus == definition.WikiPresentStatusNoCommitHistoryFailed || repo.WikiPresentStatus == definition.WikiPresentStatusNotSourceControlFailed || repo.WikiPresentStatus == definition.WikiPresentStatusTooManyFilesFailed || repo.WikiPresentStatus == definition.WikiPresentStatusNoFileFailed {
		ScheduleWikiGenerateProcessingByWorkspaces(workspacePath)
	}

	// 判断是否中断中,触发中断完成校验
	if repo.ProgressStatus == definition.DeepWikiProgressStatusPausing {
		// 判断是否有处理中的任务
		tasks := d.TaskQueueManager.GetTasksByWorkspace(workspacePath)
		for _, task := range tasks {
			if !task.IsFinished() {
				log.Debugf("Pausing wiki processing, workspacePath:%s, task: %v", workspacePath, task)
				return
			}
		}

		// 将repo状态设置为paused，中断操作完成
		repo.ProgressStatus = definition.DeepWikiProgressStatusPaused
		repo.WikiPresentStatus = definition.WikiPresentStatusPaused
		err := GlobalWikiService.storageService.UpdateWikiRepoByManual(repo)
		if err != nil {
			log.Errorf("failed to update repo status to paused, repo:%+v err: %v", repo, err)
			return
		}
		log.Debugf("Pausing wiki processing success. workspacePath:%s", workspacePath)

		// 清空缓存
		delete(storage.WikiPauseMap, workspacePath)
	}
}
