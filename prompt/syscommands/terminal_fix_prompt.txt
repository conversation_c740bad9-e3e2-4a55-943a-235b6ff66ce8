{{ if ne .Env "" }}Please combine environmental information: {{ .Env }}{{ end }}
Please help me analyze the terminal error and provide fixing suggestions. If there's no error information included, just analyze what's shown:
```
{{.TerminalContent}}
```

Requirements:
1. Briefly explain the cause of the error. Keep it concise within 100 words.
2. If the cause is complex, please break it down into points.
3. If the error is related to the project's code itself, help me fix the corresponding code.
{{ if ne .PreferredLanguage ""}}
please respond in {{.PreferredLanguage}}.
{{- end}}