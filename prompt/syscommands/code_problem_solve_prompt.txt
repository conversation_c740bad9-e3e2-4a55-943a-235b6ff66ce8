<additional_data>
{{- if .FilePath }}
#file:{{ .FilePath }}
{{- end }}
```
{{ .FileCode }}
```
</additional_data>
{{ if ne .PreferredLanguage ""}}
<communication>
The user's preferred language is {{.PreferredLanguage}}， please respond in {{.PreferredLanguage}}.
</communication>
{{- end}}
<user_query>
{{ if eq .Version "2" }}
Code file is:{{ .FilePath }}
Error message list:
{{- if .ErrorMessagesWithCodeLines }}
{{ range $item := .ErrorMessagesWithCodeLines }}
{{ if $item.LineNumber }}Line {{$item.LineNumber}} code:{{ end }}
{{$item.Code}}
{{ if $item.ErrorMessage }}Error message:
{{$item.ErrorMessage}}{{ end }}
{{ end }}
{{ end }}

{{ else }}
Code context:
{{ if .Language }}
{{ .Language }}
{{ end }}
{{ .Code }}
{{ if .ErrorMessages }}
Error messages:
{{ range .ErrorMessages }}
{{ . }}
{{ end }}
{{ end }}
{{- end }}
Fix the above errors.
{{ if .Text }}
Additional requirements: {{ .Text }}
{{- end }}
please respond in {{.PreferredLanguage}}.
</user_query>
