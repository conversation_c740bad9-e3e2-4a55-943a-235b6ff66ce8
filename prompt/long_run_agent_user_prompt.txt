{{- if ne .ReferenceCatalogItemsString "" }}
<project_instructions>
The absolute path of the user's workspace is: {{.WorkspacePath}}
The following is the directory information of the user's workspace. Refer to it if it helps answer the user's query.
{{ .ReferenceCatalogItemsString }}
</project_instructions>
{{- end }}

{{ if ne .PreferredLanguage ""}}
<communication>
The user's preferred language is {{.PreferredLanguage}}， please respond in {{.PreferredLanguage}}.
</communication>
{{- end}}

{{if .IsDesignMode}}
{{if ne .DesignDocContent ""}}
<execution_instruction>
Create an actionable implementation plan with a checklist of coding tasks based on design.
Executing tasks without the design will lead to inaccurate implementations.
</execution_instruction>
<design_doc>
{{ .DesignDocContent }}
</design_doc>
{{end}}
<user_query>
{{ .UserInputQuery }}
</user_query>
{{else}}
<user_query>
{{ .UserInputQuery }}
</user_query>
{{end}}
