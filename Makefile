# Go compiler
GOCMD = go
GOFLAGS =

# Directories and files
LOG_TOOL_DIR = tool/cache_log_reader
LINGMA_DIR = .

LOG_TOOL_MAIN = $(LOG_TOOL_DIR)/main.go
LINGMA_MAIN = $(LINGMA_DIR)/main.go

# Output binaries
LOG_TOOL_BIN = lingma_log_decode
LINGMA_BIN = lingma

# Default target platform (can be overridden by command line)
GOOS ?= darwin
GOARCH ?= arm64

.PHONY: all log_decode clean

all: log_decode

log_decode:
	@echo "Building log_tool for $(GOOS)/$(GOARCH)..."
	GOOS=$(GOOS) GOARCH=$(GOARCH) $(GOCMD) build $(GOFLAGS) -o $(LOG_TOOL_BIN)_$(GOOS)_$(GOARCH) $(LOG_TOOL_MAIN)

clean:
	@echo "Cleaning up..."
	rm -f $(LOG_TOOL_BIN)_* $(LINGMA_BIN)_*