package feedback

import (
	"bytes"
	"context"
	"cosy/client"
	"cosy/components"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/user"
	"cosy/util"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
)

const (
	fileSizeLimit = 1024 * 1024 * 6

	//目前看到diagnosis.bin做zip压缩后会减小到原来的1/5
	diagnosisFileSizeLimit = 1024 * 1024 * 6 * 2
)

type UploadResponse struct {
	Result    UploadResultBody
	Message   string `json:"message"`
	RequestId string `json:"requestId"`
	Success   bool   `json:"success"`
}

type UploadResultBody struct {
	Url string `json:"url"`
}

func errorResult(code, message string) definition.LogFeedbackResult {
	return definition.LogFeedbackResult{
		BaseResult: definition.BaseResult{
			ErrorCode:    code,
			ErrorMessage: message,
		},
	}
}

func ReportDiagnosisLog(ctx context.Context, params definition.LogFeedbackParams) definition.LogFeedbackResult {
	if params.Feedback == "" {
		log.Info("Feedback message is empty")
		return errorResult(definition.ParamErrorCode, "feedback message is empty")
	}
	if !util.FileExists(params.PluginLogFile) {
		log.Warnf("Failed to found log file %s", params.PluginLogFile)
		return errorResult(definition.LogFileNotExistErrorCode, "log file does not exist")
	}
	feedbackId := uuid.NewString()

	var ideInfo definition.IdeConfig
	if ideConfig := ctx.Value(definition.ContextKeyIdeConfig); ideConfig != nil {
		ide, _ := ideConfig.(*definition.IdeConfig)
		ideInfo = *ide
	}

	var imageUrls []string
	if params.ImageUris != nil {
		for _, uri := range params.ImageUris {
			uploadRes, err := components.UploadImage(uri)
			if err != nil {
				log.Warn("Failed to upload feedback image: ", err)
				continue
			}
			log.Debugf("uploaded image: %+v", uploadRes)
			imageUrls = append(imageUrls, uploadRes.Result.Url)
		}
	}

	zipFile, err := collectLogs(params, imageUrls)
	if err != nil {
		log.Errorf("collect diagnosis log error. err: %+v", err)
		return errorResult(definition.LogCollectionErrorCode, "failed to collect diagnosis logs")
	}
	defer os.Remove(zipFile)

	fileName := fmt.Sprintf("%s.zip", feedbackId)
	resp, err := uploadFile(ideInfo, params, feedbackId, zipFile, fileName, imageUrls)
	if err != nil {
		log.Errorf("upload diagnosis log error. err: %+v", err)
		return errorResult(definition.UploadFileErrorCode, "failed to upload diagnosis logs")
	}
	if !resp.Success {
		log.Errorf("failed to upload diagnosis log. err: %s", resp.Message)
		return errorResult(definition.UploadFileErrorCode, resp.Message)
	}
	log.Infof("upload diagnosis log success, feedbackId: %s", feedbackId)
	log.Debugf("> zipFile: %s, Url: %s", zipFile, resp.Result.Url)

	return definition.LogFeedbackResult{
		Result: definition.LogFeedbackBody{
			FeedbackId: feedbackId,
		},
	}
}

func uploadFile(ideInfo definition.IdeConfig, params definition.LogFeedbackParams, feedbackId, zipFile string, fileName string, imgUrls []string) (UploadResponse, error) {
	httpPayload := &bytes.Buffer{}
	writer := multipart.NewWriter(httpPayload)
	defer writer.Close()

	userInfo := user.GetCachedUserInfo()
	if userInfo == nil || userInfo.Uid == "" {
		return UploadResponse{}, errors.New("user not login")
	}

	formFile, err := writer.CreateFormFile("file", fileName)
	if err != nil {
		log.Errorf("when uploading, create form file error: %+v", err)
		return UploadResponse{}, err
	}

	file, fileOpenErr := os.Open(zipFile)
	if fileOpenErr != nil {
		log.Errorf("open zip file error. err: %+v", fileOpenErr)
		return UploadResponse{}, fileOpenErr
	}
	defer file.Close()

	fileBytes, err := io.ReadAll(file)
	if err != nil {
		log.Errorf("read zip file error. err: %+v", err)
		return UploadResponse{}, err
	}
	buf := bytes.Buffer{}
	buf.Write(fileBytes)

	_, err = io.Copy(formFile, &buf)
	if err != nil {
		log.Errorf("when uploading, copy image error: %+v", err)
		return UploadResponse{}, err
	}

	//设置form表单
	writer.WriteField("user_id", userInfo.Uid)
	writer.WriteField("user_name", userInfo.Name)
	writer.WriteField("ide_type", ideInfo.IdePlatform)
	writer.WriteField("ide_version", ideInfo.IdeVersion)
	writer.WriteField("plugin_version", ideInfo.PluginVersion)
	writer.WriteField("description", params.Feedback)
	writer.WriteField("img_urls", strings.Join(imgUrls, ","))

	if err = writer.Close(); err != nil {
		log.Errorf("close multipart writer error: %+v", err)
		return UploadResponse{}, err
	}

	requestUrl := definition.UrlPathUploadFileEndpoint + "?request_id=" + feedbackId
	req, err := remote.BuildBigModelAuthUploadFileRequest(http.MethodPut, requestUrl, httpPayload.Bytes())

	if err != nil {
		log.Errorf("upload image build big model auth request error: %v", err)
		return UploadResponse{}, err
	}
	req.Header.Set("AI-CLIENT-TIMESTAMP", strconv.FormatInt(time.Now().Unix(), 10))
	req.Header.Set("Content-Type", writer.FormDataContentType())

	resp, err := client.GetUploadFileClient().Do(req)
	if err != nil {
		return UploadResponse{}, err
	}
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when uploading file, the err: %v", err)
		return UploadResponse{}, err
	}

	var result UploadResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		log.Errorf("unmarshal response body error when uploading file, the err: %v", err)
		return UploadResponse{}, err
	}
	return result, nil
}

func collectLogs(params definition.LogFeedbackParams, imageUrls []string) (string, error) {
	timestampSec := time.Now().Unix()
	tmpDir, tmpPathErr := getTmpPath()
	filesToZip := []string{}
	if tmpPathErr != nil {
		return "", tmpPathErr
	}

	pluginLogFile := params.PluginLogFile
	truncatedPluginLogFile, truncErr := duplicateFile(tmpDir, pluginLogFile, fileSizeLimit)
	defer os.Remove(truncatedPluginLogFile)
	if truncErr != nil {
		return "", truncErr
	}
	filesToZip = append(filesToZip, truncatedPluginLogFile)

	cacheLogFile := filepath.Join(util.GetCosyHomePath(), "cache", "diagnosis.bin")
	truncatedCacheLogFile, truncErr := duplicateFile(tmpDir, cacheLogFile, diagnosisFileSizeLimit)
	defer os.Remove(truncatedCacheLogFile)
	if truncErr != nil {
		return "", truncErr
	}
	filesToZip = append(filesToZip, truncatedCacheLogFile)

	// 收集IDE日志文件（保持目录结构）
	if params.IdeLogDir != "" {
		ideLogDirs, err := getIdeLogDirs(params.IdeLogDir)
		if err != nil {
			log.Warnf("Failed to get IDE log directories: %v", err)
		} else if len(ideLogDirs) > 0 {
			ideLogFiles, err := collectIdeLogFiles(tmpDir, ideLogDirs)
			if err != nil {
				log.Warnf("Failed to collect IDE log files: %v", err)
			} else if len(ideLogFiles) > 0 {
				// 添加清理延迟函数
				for _, file := range ideLogFiles {
					defer os.RemoveAll(file) // 使用RemoveAll因为可能是目录
				}
				filesToZip = append(filesToZip, ideLogFiles...)
				log.Infof("Added %d IDE log directories to zip package", len(ideLogFiles))
			}
		}
	}

	feedbackFileFile := strings.Join([]string{tmpDir, "feedback.txt"}, string(os.PathSeparator))
	defer os.Remove(feedbackFileFile)
	if err := util.NewFile(feedbackFileFile, params.Feedback); err != nil {
		return "", err
	}
	filesToZip = append(filesToZip, feedbackFileFile)

	imageListFile := strings.Join([]string{tmpDir, "imageList.txt"}, string(os.PathSeparator))
	defer os.Remove(imageListFile)
	if len(imageUrls) > 0 {
		if err := util.NewFile(imageListFile, strings.Join(imageUrls, "\n")); err != nil {
			return "", err
		}
		filesToZip = append(filesToZip, imageListFile)
	}

	zipFile := strings.Join([]string{tmpDir, strconv.FormatInt(timestampSec, 10) + ".zip"}, string(os.PathSeparator))
	err := util.ZipFiles(zipFile, filesToZip)
	if err != nil {
		return "", err
	}
	return zipFile, nil
}

func duplicateFile(tmpDir, file string, sizeLimit int64) (string, error) {
	newTmpFileName := strings.Join([]string{tmpDir, filepath.Base(file)}, string(os.PathSeparator))
	if util.FileExists(newTmpFileName) {
		os.Remove(newTmpFileName)
	}

	tmpFile, createErr := os.Create(newTmpFileName)
	if createErr != nil {
		return "", createErr
	}

	cpErr := util.CopyFile(file, tmpFile.Name())
	if cpErr != nil {
		return "", cpErr
	}

	truncErr := util.TailTruncateFileByLines(tmpFile.Name(), sizeLimit)
	if truncErr != nil {
		return "", truncErr
	}
	return tmpFile.Name(), nil
}

// getIdeLogDirs 根据业务规则筛选IDE日志目录
// 规则：如果有当天日志，记录当天的最新3个目录日志；否则，只记录当前最新的一个目录日志
func getIdeLogDirs(ideLogDir string) ([]string, error) {
	if ideLogDir == "" || !util.DirExists(ideLogDir) {
		return nil, nil
	}

	entries, err := os.ReadDir(ideLogDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read ide log directory: %v", err)
	}

	// 筛选出符合日期格式的目录 (格式: 20250805T204910)
	var logDirs []string
	today := time.Now().Format("20060102")
	var todayDirs []string

	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}

		dirName := entry.Name()
		// 检查目录名是否符合格式 (至少要有日期部分)
		if len(dirName) >= 8 {
			dateStr := dirName[:8]
			if _, err := time.Parse("20060102", dateStr); err == nil {
				logDirs = append(logDirs, dirName)
				// 检查是否是当天的日志
				if dateStr == today {
					todayDirs = append(todayDirs, dirName)
				}
			}
		}
	}

	// 按目录名降序排序（最新的在前面）
	sort.Slice(logDirs, func(i, j int) bool {
		return logDirs[i] > logDirs[j]
	})
	sort.Slice(todayDirs, func(i, j int) bool {
		return todayDirs[i] > todayDirs[j]
	})

	var selectedDirs []string
	if len(todayDirs) > 0 {
		// 如果有当天日志，取最新的3个
		maxCount := 3
		if len(todayDirs) < maxCount {
			maxCount = len(todayDirs)
		}
		selectedDirs = todayDirs[:maxCount]
	} else if len(logDirs) > 0 {
		// 否则只取最新的1个
		selectedDirs = logDirs[:1]
	}

	// 转换为完整路径
	var fullPaths []string
	for _, dir := range selectedDirs {
		fullPaths = append(fullPaths, filepath.Join(ideLogDir, dir))
	}

	return fullPaths, nil
}

// collectIdeLogFiles 收集IDE日志文件，保持目录结构
func collectIdeLogFiles(tmpDir string, ideLogDirs []string) ([]string, error) {
	var copiedDirs []string

	for _, logDir := range ideLogDirs {
		if !util.DirExists(logDir) {
			log.Warnf("IDE log directory does not exist: %s", logDir)
			continue
		}

		// 获取目录的基础名称（例如：20250805T204910）
		dirBaseName := filepath.Base(logDir)
		destDir := filepath.Join(tmpDir, "ide_logs", dirBaseName)

		// 创建目标目录结构
		if err := os.MkdirAll(destDir, 0755); err != nil {
			log.Warnf("Failed to create IDE log destination directory %s: %v", destDir, err)
			continue
		}

		// 复制整个目录结构
		err := filepath.Walk(logDir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				log.Warnf("Error accessing path %s: %v", path, err)
				return nil // 继续处理其他文件
			}

			// 计算相对路径
			relPath, err := filepath.Rel(logDir, path)
			if err != nil {
				log.Warnf("Failed to get relative path for %s: %v", path, err)
				return nil
			}

			destPath := filepath.Join(destDir, relPath)

			if info.IsDir() {
				// 创建目录
				if err := os.MkdirAll(destPath, info.Mode()); err != nil {
					log.Warnf("Failed to create directory %s: %v", destPath, err)
				}
				return nil
			}

			// 检查文件扩展名，只收集日志文件
			ext := strings.ToLower(filepath.Ext(path))
			if ext == ".log" || ext == ".txt" || ext == "" {
				// 创建目标文件的目录
				destFileDir := filepath.Dir(destPath)
				if err := os.MkdirAll(destFileDir, 0755); err != nil {
					log.Warnf("Failed to create directory for file %s: %v", destFileDir, err)
					return nil
				}

				// 复制文件，保持原始文件名和路径
				if err := util.CopyFile(path, destPath); err != nil {
					log.Warnf("Failed to copy IDE log file %s to %s: %v", path, destPath, err)
					return nil
				}

				// 截断文件到合适大小
				if err := util.TailTruncateFileByLines(destPath, fileSizeLimit); err != nil {
					log.Warnf("Failed to truncate IDE log file %s: %v", destPath, err)
					// 不返回错误，继续处理其他文件
				}
			}
			return nil
		})

		if err != nil {
			log.Warnf("Error walking IDE log directory %s: %v", logDir, err)
		} else {
			copiedDirs = append(copiedDirs, destDir)
		}
	}

	if len(copiedDirs) > 0 {
		log.Infof("Successfully copied %d IDE log directories with preserved structure", len(copiedDirs))
		// 返回包含所有IDE日志的根目录
		ideLogsRoot := filepath.Join(tmpDir, "ide_logs")
		return []string{ideLogsRoot}, nil
	}

	return nil, nil
}

func getTmpPath() (string, error) {
	tmpDir := os.TempDir()
	if !util.DirExists(tmpDir) {
		tmpDir = strings.Join([]string{util.GetCosyHomePath(), "tmp"}, string(os.PathSeparator))
		if !util.DirExists(tmpDir) {
			err := os.MkdirAll(tmpDir, 0755)
			if err != nil {
				return "", err
			}
		}
	}
	tmpDir = strings.TrimSuffix(tmpDir, string(os.PathSeparator))
	return tmpDir, nil
}
