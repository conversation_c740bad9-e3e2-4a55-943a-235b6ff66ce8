package recommendation

import (
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/ls"
	"cosy/search"
	"cosy/util"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"sync"
	"unicode"
)

// A<PERSON><PERSON>ocRecommen<PERSON> suggests api doc wrt user context
type ApiDocRecommender struct {
	searcher *search.ApiDocSearcher
}

// NewApiDocRecommender as a Constructor returns a ApiDocRecommender with  an ApiDocSearcher
func NewApiDocRecommender() *ApiDocRecommender {
	return &ApiDocRecommender{
		searcher: search.NewApiDocSearcher(),
	}
}

// Initialize ApiDocRecommender
func (r *ApiDocRecommender) Initialize() error {
	return nil
}

func (r *ApiDocRecommender) Recommend(params definition.RecommendParams) definition.Recommendation {
	log.Info("Start to call ApiDocRecommend.. ")

	// parse context
	currentLine, caller, keyword, err := getCurrentHoverContext(
		params.FileContent, int(params.Position.Line), int(params.Position.Character))

	if err != nil || len(keyword) == 0 {
		return definition.Recommendation{}
	}

	if len(keyword) == 1 {
		if !unicode.IsLetter([]rune(keyword)[0]) && !unicode.IsDigit([]rune(keyword)[0]) {
			log.Info("hover on symbol: ", keyword)
			return definition.Recommendation{}
		}
	}

	// check language type
	fileType := util.ParseFileType(string(params.TextDocument.URI))
	hoverContext := definition.HoverContext{
		Keyword:  keyword,
		Caller:   caller,
		Line:     currentLine,
		FileType: fileType,
	}

	// parse caller
	callerImports := r.ParseCaller(params, hoverContext)
	if callerImports == nil {
		return definition.Recommendation{}
	}

	var searchResults []definition.Documentation
	var layout string

	switch fileType {
	case "JS":
		log.Info("Recommend with JS processor")
		searchResults = r.doSearch(callerImports, hoverContext)
		layout = "javascript_api_doc_hover"
		//slsReport(start, keyword, fileType, layout, callerImports, len(searchResults))
	case "PY":
		log.Info("Recommend with PY processor")
		searchResults = r.doSearch(callerImports, hoverContext)
		layout = "python_api_doc_hover"
		//slsReport(start, keyword, fileType, layout, callerImports, len(searchResults))
	default:
		log.Info("Currently not supported with the given file type..")
		return definition.Recommendation{}
	}

	items := r.filterResult(searchResults, callerImports)
	return definition.Recommendation{Layout: layout, Recommendation: items}
}

// filterResult do exact recommendation first
func (r *ApiDocRecommender) filterResult(
	searchResults []definition.Documentation, callerImports []string) []definition.RecommendationItem {
	log.Info("Search hits: ", len(searchResults))

	if len(searchResults) == 0 {
		return []definition.RecommendationItem{}
	}

	var items []definition.RecommendationItem
	for _, result := range searchResults {
		log.Info("BuildResult ", result.ApiDoc.ApiDocument.ApiFullPath)
		for _, caller := range callerImports {
			if strings.ToLower(result.ApiDoc.ApiDocument.Class) == caller {
				items = append(items, definition.RecommendationItem{
					Type: "apidoc",
					Item: result,
				})
				log.Info(caller, " matched")
				break
			}
		}
	}
	if len(items) > 0 {
		return items
	}
	log.Info("No exact match. Recommend by the most related module.")
	// Recommend one of the most related doc, in terms of module size
	var fuzzyResult definition.Documentation
	minModule := 0
	for _, result := range searchResults {
		if result.ApiDoc.ApiDocument.Module == "" {
			continue
		}
		modules := strings.Split(result.ApiDoc.ApiDocument.Module, ",")
		resModule := len(modules)
		if minModule == 0 || minModule > resModule {
			fuzzyResult = result
			minModule = resModule
		}
	}
	items = append(items, definition.RecommendationItem{
		Type: "apidoc",
		Item: fuzzyResult,
	})
	return items
}

// DoSearch build search queries and call Search do the real job
func (r *ApiDocRecommender) doSearch(callerImports []string, context definition.HoverContext) []definition.Documentation {
	classArrayJson, err := json.Marshal(callerImports)
	if err != nil {
		log.Error("Error for parse imports: ", err.Error())
		return []definition.Documentation{}
	}
	classArrayStr := string(classArrayJson)
	log.Info("Parsed keywords and requires: ", context.Keyword, " & ", callerImports)

	var searchResults []definition.Documentation
	var language = context.FileType

	var queries []definition.SearchQuery
	queries = append(queries, definition.SearchQuery{
		Keyword:  context.Keyword,
		Package:  classArrayStr,
		Language: language,
		FullPath: classArrayStr,
	})

	var wg sync.WaitGroup
	wg.Add(len(queries))
	var mutex sync.Mutex
	for _, query := range queries {
		go func(query definition.SearchQuery) {
			defer wg.Done()
			searchHits := r.searcher.DoSearch(query, "api-doc-search", 100, 0)

			mutex.Lock()
			searchResults = append(searchResults, searchHits...)
			mutex.Unlock()

		}(query)
	}
	wg.Wait()

	return searchResults
}

// ParseCaller return caller related imports, wrt language type
func (r *ApiDocRecommender) ParseCaller(params definition.RecommendParams, context definition.HoverContext) []string {
	var imports []definition.Import
	switch context.FileType {
	case "JS":
		tsService := ls.GetTsService()
		if tsService == nil {
			log.Error("tsService is nil")
			return nil
		}
		// parse source code to get imports
		imports = tsService.ParseImports(params.FileContent)
	case "PY":
		pyService := ls.GetPyService()
		if pyService == nil {
			log.Error("pyService is nil")
			return nil
		}
		// parse source code to get imports
		imports = pyService.ParseImport(string(params.TextDocument.URI), global.ChineseWordRegex.ReplaceAllString(params.FileContent, ""))
	default:
		return nil
	}

	// check hover keyword has params or not
	hasParam := checkParams(context.Line, context.Keyword)

	if context.Caller == "" && !hasParam {
		log.Info("Current hover keyword has no caller or params")
		return nil
	}

	context.Imports = imports
	callerImports := removeDuplicateValues(parseCallerImports(context))

	if context.Caller != "" {
		// append caller itself
		callerImports = append(callerImports, strings.ToLower(context.Caller))
	}
	return callerImports
}

// GetCurrentHoverContext returns: currentLine, caller, hover keyword
func getCurrentHoverContext(content string, lineNum int, colNum int) (string, string, string, error) {
	lines := strings.Split(content, "\n")
	if len(lines) <= lineNum {
		// cannot find valid line
		return "", "", "", errors.New(fmt.Sprintf("invalid line num %d for line count:%d", lineNum, len(lines)))
	}
	currentLine := lines[lineNum]

	if len([]rune(currentLine)) < colNum {
		// cannot find valid word
		return "", "", "", errors.New(fmt.Sprintf("invalid col num %d for col count:%d", colNum, len(currentLine)))
	}

	if colNum <= 0 {
		return currentLine, "", "", nil
	}

	charArr := []rune(currentLine)

	if !unicode.IsLetter(charArr[colNum-1]) && !unicode.IsDigit(charArr[colNum-1]) {
		return currentLine, "", string(charArr[colNum-1]), nil
	}

	log.Info(fmt.Sprintf("currentLine: [%s] lineNum: %d colNum: %d, hoverChar: %s", currentLine, lineNum, colNum, string(charArr[colNum-1])))

	// remove symbol characters
	reg, err := regexp.Compile("[^a-zA-Z0-9_]+")
	if err != nil {
		return "", "", "", errors.New(fmt.Sprintf("failed to compile regexp to remove symbol characters"))
	}
	linePrefix := string([]rune(currentLine)[:colNum])
	lineSuffix := string([]rune(currentLine)[colNum:])
	linePrefix = reg.ReplaceAllString(linePrefix, " ")
	lineSuffix = reg.ReplaceAllString(lineSuffix, " ")

	prefixTokens := strings.Fields(linePrefix)
	suffixTokens := strings.Fields(lineSuffix)

	hoverPrefix := ""
	if len(prefixTokens) > 0 {
		hoverPrefix = prefixTokens[len(prefixTokens)-1]
	}
	hoverSuffix := ""
	if len(suffixTokens) > 0 {
		hoverSuffix = suffixTokens[0]
	}

	hoverWord := hoverPrefix + hoverSuffix

	// find hover word started index
	hoverPrefixIndex := colNum - len([]rune(hoverPrefix)) - 1
	if hoverPrefixIndex <= 0 {
		return currentLine, "", hoverWord, nil
	}
	if currentLine[hoverPrefixIndex] == '.' {
		callerPrefix := currentLine[:hoverPrefixIndex]
		lineTokens := strings.Fields(callerPrefix)
		if len(lineTokens) > 0 {
			caller := lineTokens[len(lineTokens)-1]
			log.Info("hover on [", hoverWord, "] with caller [", caller, "]")
			return currentLine, caller, hoverWord, nil
		}
	}
	log.Info("hover on [", hoverWord, "]")
	return currentLine, "", hoverWord, nil
}

// parseCallerImports 解析hover相关imports
func parseCallerImports(context definition.HoverContext) []string {
	log.Info("Started to parse caller imports..")

	// class is caller or hover item itself
	caller := context.Caller
	if caller == "" {
		caller = context.Keyword
	}

	var callerMap = make(map[string][]string)
	for _, i := range context.Imports {
		if i.Module == "" {
			continue
		}
		for _, alias := range i.Alias {
			callerMap[strings.ToLower(alias.Alias)] = []string{strings.ToLower(i.Module)}
		}
		for _, name := range i.Names {
			callerMap[strings.ToLower(name)] = []string{strings.ToLower(i.Module)}
		}
	}

	var res []string
	res = parseCallerImportHelper(callerMap, caller, res, 6)
	log.Info("Related modules: ", res)

	return res
}

// parseCallerImportHelper 逐层解析调用关系
func parseCallerImportHelper(callerMap map[string][]string, caller string, res []string, tiers int) []string {
	if tiers < 0 {
		return res
	}
	modules, ok := callerMap[caller]
	if !ok {
		// no more related imports
		return res
	}

	for _, module := range modules {
		res = append(res, strings.ToLower(module))
		res = parseCallerImportHelper(callerMap, module, res, tiers-1)
	}
	return res
}

func checkParams(currentLine string, keyword string) bool {
	hasParam := false
	lineTokens := strings.Split(currentLine, keyword)
	if len(lineTokens) > 0 {
		for _, token := range lineTokens {
			if len(token) == 0 {
				continue
			}
			if token[0:1] == "(" {
				log.Info("hasParam: ", token)
				hasParam = true
			}
		}
	}
	return hasParam
}

func removeDuplicateValues(slices []string) []string {
	keys := make(map[string]bool)
	var list []string

	// If the key(values of the slice) is not equal
	// to the already present value in new slice (list)
	// then we append it. else we jump on another element.
	for _, entry := range slices {
		if _, value := keys[entry]; !value {
			keys[entry] = true
			list = append(list, entry)
		}
	}
	return list
}

//func slsReport(start time.Time, keyword string, language string, layout string, callerImports []string, hitNums int) {
//    logData := map[string]string{}
//    logData["layout"] = layout
//    logData["keywords"] = keyword
//    logData["packages"] = strings.Join(callerImports, ",")
//    logData["language"] = language
//    logData["num_recommended"] = strconv.Itoa(hitNums)
//    sls.Report(true, int(time.Since(start).Milliseconds()), sls.ApiDocRecommend, "", logData)
//}
