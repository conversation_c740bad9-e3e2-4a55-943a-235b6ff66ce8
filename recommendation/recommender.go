package recommendation

import (
	"cosy/definition"
	"time"
)

const (
	ReadWriteTimeout = 2 * time.Second // 网络请求超时时间
)

// Recommender interface
type Recommender interface {
	Initialize() error
	Recommend(params definition.RecommendParams) definition.Recommendation
}

type EmptyRecommender struct{}

func (s *EmptyRecommender) Initialize() error {
	return nil
}

func (s *EmptyRecommender) Recommend(params definition.RecommendParams) definition.Recommendation {
	return definition.Recommendation{}
}
