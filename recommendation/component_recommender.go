package recommendation

import (
	"cosy/definition"
	"cosy/log"
	"cosy/ls"
	"cosy/search"
	"cosy/util"
	"strconv"
	"strings"
	"sync"
	"unicode"
)

const (
	ComponentOverviewLayout = "component/overview"
	ComponentDetailLayout   = "component/detail"
	AllComponentLayout      = "component/all"
)

// ComponentRecommender suggests components fit in current context
// It calls force-ai to achieve this
type ComponentRecommender struct {
	searcher *search.ComponentSearcher
}

// NewComponentRecommender returns a ComponentRecommender with  a ComponentSearcher
func NewComponentRecommender() *ComponentRecommender {
	return &ComponentRecommender{
		searcher: search.NewComponentSearcher(),
	}
}

func (r *ComponentRecommender) Initialize() error {
	return nil
}

func (r *ComponentRecommender) Recommend(params definition.RecommendParams) definition.Recommendation {
	switch params.Method {
	case definition.ListAllComponent:
		return r.ListAllComponents()
	default:
		return r.RecommendComponent(params)
	}
}

func (r *ComponentRecommender) RecommendComponent(params definition.RecommendParams) definition.Recommendation {
	log.Info("Start to call ComponentRecommender.. ")
	// Parse the context first
	scriptType := util.GetFileType(string(params.TextDocument.URI))
	tsservice := ls.GetTsService()
	if tsservice == nil {
		return definition.Recommendation{}
	}
	componentContext := tsservice.ParseComponentContext(params.FileContent, scriptType, params.Position)
	var keywords []string
	var froms []string
	var layout string
	// Use fusion by default
	from := "@alifd/next"
	if componentContext.InComponent {
		keywords = append(keywords, componentContext.ComponentName)
		froms = append(froms, componentContext.ImportFrom)
		layout = ComponentDetailLayout
		if componentContext.ImportFrom != "" {
			from = componentContext.ImportFrom
		}
	} else {
		// If the cursor is not in a component, set the layout to ComponentOverview and do searching on imported names
		for i, name := range componentContext.ImportedNames {
			// skip impossible names
			if name == "" || unicode.IsLower(rune(name[0])) {
				continue
			}
			keywords = append(keywords, name)
			froms = append(froms, componentContext.ImportedNamesFrom[i])
		}
		layout = ComponentOverviewLayout
	}
	if len(froms) != len(keywords) {
		log.Info("ERROR: the number of froms doesn't equal to the number of keywords")
		return definition.Recommendation{}
	}

	log.Info("search keys:", keywords, ", in package:", from)

	// Do search
	var searchResults []definition.Documentation
	var wg sync.WaitGroup
	wg.Add(len(keywords))
	var mutex sync.Mutex
	for i, keyword := range keywords {
		go func(key string, from string) {
			defer wg.Done()
			searchHits := r.searcher.DoSearch(definition.SearchQuery{
				Keyword: key,
				Package: from,
			}, layout, 100, 0)
			if layout == ComponentOverviewLayout {
				// By default, we use prefix search on ES
				// But in overview layout, the searched result should be identical with keyword(imported names)
				for _, hit := range searchHits {
					if hit.ComponentDoc.Name == key {
						mutex.Lock()
						searchResults = append(searchResults, hit)
						mutex.Unlock()
					}
				}
			} else {
				mutex.Lock()
				searchResults = append(searchResults, searchHits...)
				mutex.Unlock()
			}
		}(keyword, froms[i])
	}
	wg.Wait()

	// Add extracted demo code and props
	for _, result := range searchResults {
		log.Info("BuildResult: \n", result)
		for i, demo := range result.Demo {
			demo.Code, demo.Props = parseDemoCode(demo.RunnableCode, demo.Component)
			result.Demo[i] = demo
		}
	}

	//logData := map[string]string{}
	//logData["layout"] = layout
	//logData["num_recommended"] = strconv.Itoa(len(searchResults))
	//sls.Report(true, int(time.Since(start).Milliseconds()), sls.ComponentRecommend, "", logData)

	log.Info("Search hits: ")
	var items []definition.RecommendationItem
	for _, result := range searchResults {
		log.Info("BuildResult ", result.ApiDoc.ApiDocument.ApiFullPath)
		items = append(items, definition.RecommendationItem{
			Type: "component",
			Item: result,
		})
	}

	return definition.Recommendation{Layout: layout, Recommendation: items}
}

// ListAllComponents returns all components in ES which are imported by current project
func (r *ComponentRecommender) ListAllComponents() definition.Recommendation {
	// Do search
	var searchResults []definition.Documentation

	searcher := search.NewComponentSearcher()

	// If got no results, use @alifd/next as the default component source, search again
	searchHits := searcher.DoSearch(definition.SearchQuery{
		Package: "@alifd/next",
	}, AllComponentLayout, 100, 0)
	searchResults = append(searchResults, searchHits...)
	log.Infof("List %d components", len(searchResults))

	var items []definition.RecommendationItem
	for _, result := range searchResults {
		items = append(items, definition.RecommendationItem{
			Type: "component",
			Item: result,
		})
	}

	//logData := map[string]string{}
	//logData["layout"] = AllComponentLayout
	//logData["num_recommended"] = strconv.Itoa(len(items))
	//sls.Report(true, 0, sls.ComponentRecommend, "", logData)

	return definition.Recommendation{
		Layout:         AllComponentLayout,
		Recommendation: items,
	}
}

// parseDemoCode extracts component usage code and existing props for from runnable code
func parseDemoCode(rawCode, component string) (code string, props []definition.ComponentProp) {
	tsservice := ls.GetTsService()
	if tsservice == nil {
		return code, props
	}
	components := tsservice.ParseComponentsForFile(rawCode, "tsx")
	propKeys := map[string]bool{}
	for _, c := range components {
		// Keep only component for current demo
		if c.Name != component && strings.TrimRightFunc(c.Name, unicode.IsDigit) != component {
			// "Button" and "Button2" are both component Button
			continue
		}
		// Add all appeared props
		for _, prop := range c.Props {
			// A prop is added for only once
			if !propKeys[prop.Name] {
				propKeys[prop.Name] = true
				props = append(props, prop)
			}
		}
	}

	// Build component code
	code = "<" + component + "\n"
	for i, prop := range props {
		valueStr, ok := prop.Value.(string)
		if ok && valueStr != "" {
			code = code + prop.Name + "=${" + strconv.Itoa(i) + ":" + valueStr + "}\n"
		} else {
			code = code + prop.Name + "=$" + strconv.Itoa(i) + "\n"
		}
	}
	code = code + "/>"
	return code, props
}
