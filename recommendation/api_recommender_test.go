package recommendation

import (
	"cosy/definition"
	"cosy/log"
	"encoding/json"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestApiDocRecommender_Search(t *testing.T) {

}

func TestApiDocRecommender_Search1(t *testing.T) {
	r := NewApiDocRecommender()
	re := r.searcher.Search(definition.SearchParams{
		Method: "placeholder",
	})
	log.Info(re)
}

/** 生成query */
func TestApiDocRequestGenerator(t *testing.T) {
	key := "getRawBody"
	classArrayStr := "getrawbody raw-body getformbody body/form body"
	language := "JS"

	requires := strings.Fields(classArrayStr)

	classArrayStr = "["
	for _, from := range requires {
		classArrayStr += "\""
		classArrayStr += from
		classArrayStr += "\", "
	}
	classArrayStr = strings.TrimRight(classArrayStr, ", ")
	classArrayStr += "]"

	query := definition.SearchQuery{
		Keyword:  key,
		Package:  classArrayStr,
		Language: language,
		FullPath: classArrayStr,
	}
	queryStr, _ := json.Marshal(query)

	requestJson, _ := json.Marshal(&definition.RequestModel{
		Query:     string(queryStr),
		PageSize:  100,
		PageIndex: 0,
		NodeType:  "api-fuzzy-search",
	})
	log.Info(string(requestJson))
}

func Test_getCurrentHoverContext(t *testing.T) {
	content := "public static void main(String arg)"
	_, caller, hoverWord, err := getCurrentHoverContext(content, 0, 26)
	assert.Empty(t, caller)
	assert.Equal(t, "String", hoverWord)
	assert.Nil(t, err)
}
