package systemrule

import (
	"context"
	"cosy/definition"
	"github.com/stretchr/testify/assert"
	"os"
	"path/filepath"
	"testing"
)

func Test_getSystemRuleInfo_suceess(t *testing.T) {
	// init data
	tempDir, err := os.MkdirTemp("", "Test_getSystemRuleInfo_suceess")
	if err != nil {
		t.Fatalf("Failed to create temporary directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	os.MkdirAll(filepath.Join(tempDir, ".lingma", "rules"), 0755)
	filePath := filepath.Join(tempDir, ".lingma", "rules", PROJECT_RULE_FILE_NAME)
	err = os.WriteFile(filePath, []byte("test project-rule info"), 0644)
	if err != nil {
		t.Fatalf("Failed to write file: %v", err)
	}

	// mock ctx
	workspaceFolder := definition.WorkspaceFolder{
		URI: tempDir,
	}
	workspaceFolders := make([]definition.WorkspaceFolder, 0)
	workspaceFolders = append(workspaceFolders, workspaceFolder)
	workspaceInfo := definition.WorkspaceInfo{
		WorkspaceFolders: workspaceFolders,
	}

	ctx := context.WithValue(context.Background(), definition.ContextKeyWorkspace, workspaceInfo)

	systemRuleInfo := GetSystemRuleInfo(ctx, "", FreeInput)
	assert.Equal(t, "test project-rule info", systemRuleInfo)
}

func Test_getSystemRuleInfo_file_not_exist(t *testing.T) {
	// init data
	tempDir, err := os.MkdirTemp("", "Test_getSystemRuleInfo_file_not_exist")
	if err != nil {
		t.Fatalf("Failed to create temporary directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// mock ctx
	workspaceFolder := definition.WorkspaceFolder{
		URI: tempDir,
	}
	workspaceFolders := make([]definition.WorkspaceFolder, 0)
	workspaceFolders = append(workspaceFolders, workspaceFolder)
	workspaceInfo := definition.WorkspaceInfo{
		WorkspaceFolders: workspaceFolders,
	}

	ctx := context.WithValue(context.Background(), definition.ContextKeyWorkspace, workspaceInfo)

	systemRuleInfo := GetSystemRuleInfo(ctx, "", FreeInput)
	assert.Equal(t, "", systemRuleInfo)
}

func Test_AppendSystemRules_success(t *testing.T) {
	// init data
	tempDir, err := os.MkdirTemp("", "Test_getSystemRuleInfo_suceess")
	if err != nil {
		t.Fatalf("Failed to create temporary directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	os.MkdirAll(filepath.Join(tempDir, ".lingma", "rules"), 0755)
	filePath := filepath.Join(tempDir, ".lingma", "rules", PROJECT_RULE_FILE_NAME)
	err = os.WriteFile(filePath, []byte("test project-rule info"), 0644)
	if err != nil {
		t.Fatalf("Failed to write file: %v", err)
	}

	// mock ctx
	workspaceFolder := definition.WorkspaceFolder{
		URI: tempDir,
	}
	workspaceFolders := make([]definition.WorkspaceFolder, 0)
	workspaceFolders = append(workspaceFolders, workspaceFolder)
	workspaceInfo := definition.WorkspaceInfo{
		WorkspaceFolders: workspaceFolders,
	}

	ctx := context.WithValue(context.Background(), definition.ContextKeyWorkspace, workspaceInfo)

	req := AppendSystemRuleReq{
		OriginSystemPrompt:  "test originSystemPrompt",
		IsEnableProjectRule: true,
	}
	appendSystemRules := AppendSystemRules(ctx, req)
	assert.Equal(t, "test originSystemPrompt\n\n附加要求：必须遵循以下原则：\ntest project-rule info", appendSystemRules)
}

func Test_AppendSystemRules_with_not_enable_rules(t *testing.T) {
	ctx := context.WithValue(context.Background(), definition.ContextKeyWorkspace, "")
	req := AppendSystemRuleReq{
		OriginSystemPrompt:  "test originSystemPrompt",
		IsEnableProjectRule: false,
	}

	appendSystemRules := AppendSystemRules(ctx, req)
	assert.Equal(t, "test originSystemPrompt", appendSystemRules)
}

func Test_AppendSystemRules_with_rules_not_exists(t *testing.T) {
	// init data
	tempDir, err := os.MkdirTemp("", "Test_getSystemRuleInfo_file_not_exist")
	if err != nil {
		t.Fatalf("Failed to create temporary directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// mock ctx
	workspaceFolder := definition.WorkspaceFolder{
		URI: tempDir,
	}
	workspaceFolders := make([]definition.WorkspaceFolder, 0)
	workspaceFolders = append(workspaceFolders, workspaceFolder)
	workspaceInfo := definition.WorkspaceInfo{
		WorkspaceFolders: workspaceFolders,
	}

	ctx := context.WithValue(context.Background(), definition.ContextKeyWorkspace, workspaceInfo)
	req := AppendSystemRuleReq{
		OriginSystemPrompt:  "test originSystemPrompt",
		IsEnableProjectRule: false,
	}

	appendSystemRules := AppendSystemRules(ctx, req)
	assert.Equal(t, "test originSystemPrompt", appendSystemRules)
}
