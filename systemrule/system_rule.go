package systemrule

import (
	"context"
	"cosy/chat/agents/support"
	"cosy/definition"
	"cosy/log"
	"cosy/sls"
	"cosy/util"
	"path/filepath"
	"strings"
)

const maxSystemRuleSize = 10000 // 10000个字符

const PROJECT_RULE_FILE_NAME = "project_rule.md"

const (
	FreeInput     = "free_input"
	SystemCommand = "system_command"
	AiDev         = "ai_dev"
	AiAgent       = "ai_agent"
	CommitMsg     = "commit_msg"
)

type AppendSystemRuleReq struct {
	OriginSystemPrompt string

	SessionId string

	RequestId string

	IsEnableProjectRule bool

	Scene string
}

func AppendSystemRules(ctx context.Context, req AppendSystemRuleReq) string {
	if !req.IsEnableProjectRule {
		return req.OriginSystemPrompt
	}

	// 获取用户自定义规则
	systemRuleInfo := GetSystemRuleInfo(ctx, req.RequestId, req.Scene)

	//组装返回结果
	if systemRuleInfo == "" {
		return req.OriginSystemPrompt
	}

	log.Info("success fetch and append system rules")
	builder := strings.Builder{}
	builder.WriteString(req.OriginSystemPrompt)
	builder.WriteString("\n\n以下为附加要求，必须遵循以下原则：\n")
	builder.WriteString(systemRuleInfo)
	return builder.String()
}

func GetSystemRuleInfo(ctx context.Context, requestId string, scene string) string {

	// 获取projectRules
	workspace := ""
	workspaceCtxValue := ctx.Value(definition.ContextKeyWorkspace)
	if workspaceCtxValue != nil {
		workspaceInfo := workspaceCtxValue.(definition.WorkspaceInfo)
		if workspaceRootPath, ok := workspaceInfo.GetWorkspaceFolder(); ok {
			workspace = workspaceRootPath
		}
	}
	if workspace == "" {
		log.Error("can not get workspace folder")
		return ""
	}

	rulePath := filepath.Join(workspace, ".lingma", "rules", PROJECT_RULE_FILE_NAME)
	exists, err := util.Exists(rulePath)
	if !exists || err != nil {
		return ""
	}

	content, err := util.ReadLimitedChars(rulePath, maxSystemRuleSize)
	if err != nil {
		log.Error(err)
		return ""
	}

	// 上报使用埋点
	go reportSystemRuleFetchEvent(ctx, requestId, scene)

	return content
}

// 判断系统规则是否存在
func IsSystemRuleExists(ctx context.Context) bool {
	workspace := ""
	workspaceCtxValue := ctx.Value(definition.ContextKeyWorkspace)
	if workspaceCtxValue != nil {
		workspaceInfo := workspaceCtxValue.(definition.WorkspaceInfo)
		if workspaceRootPath, ok := workspaceInfo.GetWorkspaceFolder(); ok {
			workspace = workspaceRootPath
		}
	}
	if workspace == "" {
		log.Error("can not get workspace folder")
		return false
	}
	rulePath := filepath.Join(workspace, ".lingma", "rules", PROJECT_RULE_FILE_NAME)
	exists, err := util.Exists(rulePath)
	if err != nil {
		log.Error("check systemRule exists has error", err)
		return false
	}
	return exists
}

func FetchWorkspaceLanguages(chatContext definition.FreeInputChatContext) string {
	workspaceLanguages := chatContext.WorkspaceLanguages
	workspaceLanguageStr := strings.Join(workspaceLanguages, ",")
	return workspaceLanguageStr
}

func FetchPreferredLanguage(chatContext definition.FreeInputChatContext) string {
	preferredLanguage := chatContext.PreferredLanguage
	return support.ConvertLanguageCodeToFullName(preferredLanguage)
}

func reportSystemRuleFetchEvent(ctx context.Context, requestId string, scene string) {
	statisticsData := make(map[string]string)
	statisticsData["request_id"] = requestId
	if ideConfig := ctx.Value(definition.ContextKeyIdeConfig); ideConfig != nil {
		ide, ok := ideConfig.(*definition.IdeConfig)
		if ok {
			statisticsData["ide_type"] = ide.IdePlatform
			statisticsData["ide_version"] = ide.IdeVersion
			statisticsData["plugin_version"] = ide.PluginVersion
		}
	}

	statisticsData["fetch_scene"] = scene
	sls.Report(definition.FetchSystemRule, requestId, statisticsData)
}
