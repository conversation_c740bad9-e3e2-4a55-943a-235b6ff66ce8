package dataImport

import (
	"os"
	"testing"
)

// 重要:测试时请按需修改以下信息
var workspaceDir = "/Users/<USER>/codeup/lingma-project-rule-template1"
var importFilePath = "/Users/<USER>/codeup/lingma-export-test/export_data.tar.zst"

func Test_importData(t *testing.T) {
	os.Setenv("COSY_HOME", "/Users/<USER>/codeup/lingma-import-test")
	err := ImportData(workspaceDir, importFilePath)
	if err != nil {
		t.Fatal(err)
	}
}
