package dataImport

import (
	executor_import "cosy/dataImport/executor"
	codebase_executor "cosy/dataImport/executor/codebase"
	memory_executor "cosy/dataImport/executor/memory"
	wiki_executor "cosy/dataImport/executor/wiki"
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	"cosy/util/compress"
	"fmt"
	"os"
	"path/filepath"
)

// ImportData
// 导入数据
// 1.根据importFilePath制定的压缩包的文件路径将压缩文件进行解压
// 2.根据解压的文件调用导入执行器执行导入逻辑
func ImportData(workspaceDir string, importFilePath string) error {
	// 参数合法检查
	if workspaceDir == "" || importFilePath == "" {
		return fmt.Errorf("all of workspaceDir, importFilePath must be provided")
	}

	// 创建临时导入空间
	lingmaHome := util.GetCosyHomePath()
	tempDir := filepath.Join(lingmaHome, "cache", "import")
	err := os.MkdirAll(tempDir, 0755)
	if err != nil {
		return err
	}
	defer os.RemoveAll(tempDir)

	// 将压缩包解压到tempDir中
	err = compress.DecompressZstdTar(importFilePath, tempDir)
	if err != nil {
		return err
	}

	// 开始按照业务类型依次导入
	for _, bizType := range definition.NeedImportDataBizTypes {
		executor := getImportExecutor(bizType, workspaceDir)
		if executor == nil {
			log.Errorf("Failed to find %s executor.\n", bizType)
			continue
		}
		err := executor.Execute(tempDir)
		if err != nil {
			log.Errorf("Failed to execute %s: %v\n", bizType, err)
			continue
		}
	}
	return nil
}

func getImportExecutor(bizType string, workspaceDir string) executor_import.ImportExecutor {
	switch bizType {
	case definition.Codebase:
		return codebase_executor.NewCodebaseImportExecutor(workspaceDir)
	case definition.Memory:
		return memory_executor.NewMemoryImportExecutor(workspaceDir)
	case definition.Wiki:
		return wiki_executor.NewWikiImportExecutor(workspaceDir)
	}
	return nil
}
