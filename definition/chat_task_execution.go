package definition

type AgentClass string

const (
	AgentClassRemoteAgent AgentClass = "RemoteAgent"
	AgentClassLocalAgent  AgentClass = "LocalAgent"
)

type ExecuteTaskParams struct {
	Id                 string     `json:"id"`
	AgentClass         AgentClass `json:"agentClass,omitempty"` // Agent 类型，支持值：[RemoteAgent, LocalAgent]
	ExecutionSessionId *string    `json:"executionSessionId,omitempty"`
	ExecutionRequestId *string    `json:"executionRequestId,omitempty"`
	SourceBranch       string     `json:"sourceBranch"`
	RawConfig          string     `json:"rawConfig"`
}

type ExecuteTaskResponse struct {
	BaseResponse
	Data Execution `json:"data"` // 返回 Agent 的相关信息，暂时只包含 AgentId
}

type UpdateExecutionParams struct {
	SourceBranch string `json:"sourceBranch"`
	HeadCommitId string `json:"headCommitId"`
}

type UpdateExecutionResponse struct {
	BaseResponse
}
