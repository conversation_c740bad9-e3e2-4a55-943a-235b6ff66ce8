package definition

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"os"
	"sync"
	"sync/atomic"
	"time"
)

const (
	TaskStatusPending = "pending" // 任务等待调度
	TaskStatusRunning = "running" // 任务正在执行
	TaskStatusSkip    = "skip"    // 任务跳过，已建立索引
	TaskStatusFinish  = "finish"  // 任务正常成功
	TaskStatusFailed  = "failed"  // 任务正常失败，例如切块失败，Embedding失败
	TaskStatusDiscard = "discard" // 丢弃的任务
)

const (
	ActionNodeOperatorAdd    = "add"
	ActionNodeOperatorUpdate = "update"
	ActionNodeOperatorDelete = "delete"

	ServerFileStatusNotSynced = "NOT_SYNCED"
	ServerFileStatusSynced    = "SYNCED"
	ServerFileStatusPending   = "PENDING"

	ActionNodeStatusPending            = "pending"         // 初始化，服务端排队处理
	ActionLeafNodeStatusProcessSuccess = "process_success" // 文件在服务端处理成功的状态
	ActionNodeStatusNodePartialSynced  = "partial_synced"  // 目录节点中存在disacard的叶节点，因此需要重新计算该节点hash并更新
	ActionNodeStatusNodeSynced         = "synced"          // 该文件对应的mtree节点同步成功
	ActionNodeStatusDiscard            = "discard"         // 该文件节点/目录节点同步失败

	MaxServerSyncDuration = 1 * time.Hour
)

const (
	CtxKeyChatIndexEnable = "ctx_key_chat_index_enable"

	// VectorFullIndexSource 表示全库仓库的索引的建立，但仍不清楚是什么样的全量索引
	VectorFullIndexSource = "vector_full_index"

	// VectorInitialFullIndexSource 表示新仓库的索引建立
	VectorInitialFullIndexSource = "vector_initial_full_index"

	// VectorSubsequentFullIndexSource 表示非初次仓库的全量索引建立
	VectorSubsequentFullIndexSource = "vector_subsequent_full_index"

	// VectorFileChangeIndexSource 表示文件变动导致的索引触发，从mtree而来
	VectorFileChangeIndexSource = "vector_file_change_index"
	// DefaultEmbeddingBatchSize 默认每次向embedding服务发送的embedding数量
	// 这里改成1了，模型那边的要求
	DefaultEmbeddingBatchSize = 1

	DefaultChunkSize          = 500
	DefaultTextIndexBatchSize = 100

	// ScanRepoInterval 扫描仓库的间隔时间
	ScanRepoInterval = 1 * time.Second

	// DefaultAsyncRedundantUploadFileCount
	// 异步上传文件时，做一些文件的冗余上传
	// 默认冗余200个，避免超大库将所有文件传到服务器
	DefaultAsyncRedundantUploadFileCount = 200

	// DefaultAsyncMaxWaiting 客户端异步索引时，超过这个时间则放弃这轮，开启下一轮同步
	DefaultAsyncMaxWaiting = 3 * time.Hour

	// DefaultAsyncFetchEmbedChunksTimeInterval 获取Embed chunks时的时间间隔
	DefaultAsyncFetchEmbedChunksTimeInterval = 3 * time.Second

	DefaultSyncServerNodeContinuousNoUpdateLimit = 5
	DefaultSyncServerNodeTooFewNumThreshold      = 0.1

	MockBigRepoAutoIndexEnvKey = "MOCK_BIG_REPO_AUTO_INDEX"

	DefaultMaxServerIndexTime   = 50 * time.Minute
	DefaultCompensationInterval = 60 * time.Minute

	// 增量索引最多并行执行的数量，超过这个数量则直接放弃该次文件变更事件
	DefaultMaxParallelIncrementalBuildingNumber = 30

	DefaultAutoIndexFileNumThreshold = 10000
)

var taskIds *atomic.Int32

func init() {
	taskIds = &atomic.Int32{}
	taskIds.Store(0)
}

type CodebaseConfig struct {
	Mutex                       sync.Mutex
	MaxClientStorageFileNum     int
	MaxServerStorageFileNum     int
	ClientVectorIndexEnable     bool
	ServerVectorIndexEnable     bool
	ClientAsyncFileNumThreshold int
	ClientAsyncGrayUserLastNum  int
	MaxAutoIndexFileNum         int
}

type AsyncTask struct {
	FileId   string
	FileSize int // 文件大小，单位字节
	VirtualFile
}

func NewAsyncTaskWithVirtualFile(virtualFile VirtualFile) *AsyncTask {
	content, err := virtualFile.GetFileContent()
	if err != nil {
		return nil
	}
	return &AsyncTask{
		FileId:      GetFileId(content),
		FileSize:    len(content),
		VirtualFile: virtualFile,
	}
}

func NewAsyncTask(virtualFile VirtualFile, fileId string, fileSize int) *AsyncTask {
	return &AsyncTask{
		FileId:      fileId,
		FileSize:    fileSize,
		VirtualFile: virtualFile,
	}
}

// Task 一个最小的可执行单元
// 调优先级（每次选出最高优先级的任务进行执行）
// 透传参数
// 透传进度、透传结果、透传错误
// 优先级可以随时调整
type Task struct {
	Id                  int
	WorkspacePath       string // workspacePath
	Source              string
	Status              string // pending/running/finish/failed
	Priority            int    // 优先级，初始时每个workspace的priority都是一样的，会根据任务是否失败次数来调整优先级
	FailedTimes         int    // 已经失败的次数，失败3次的任务不再执行
	ShareChunkHitCnt    int    // 命中共享chunk数量
	DoEmbeddingChunkCnt int    // 实际执行Embedding的chunk数量
	VirtualFile
}

// TaskWrapper
// 建立索引过程中，需要用到切块和Task中的相关内容，因此需要一个wrapper
type TaskWrapper struct {
	Task   *Task           // 原始Task
	Chunks []*StorageChunk // Task对应文件的切块
}

// MergedTaskWrapper
// 索引建立过程中，需要合并多个TaskWrapper，为了减少embedding的请求次数
type MergedTaskWrapper struct {
	Wrappers []*TaskWrapper
}

type VirtualFileWrapper struct {
	VirtualFiles  []VirtualFile
	WorkspacePath string
	Source        string
}

// VectorBatchTask 一批相同WorkspaceId的Task一起执行
type VectorBatchTask struct {
	WorkspacePath string
	Tasks         []*Task
	Source        string
}

func NewVectorBatchTaskWithFilePaths(workspacePath string, filePaths []string, source string) VectorBatchTask {
	var tasks []*Task
	for _, filePath := range filePaths {
		// 这里要过滤掉所有的目录filePath
		fileInfo, err := os.Stat(filePath)
		if err != nil || (fileInfo != nil && fileInfo.IsDir()) {
			// 文件不存在或无法访问
			// 当前文件为目录
			continue
		}
		virtualFile := NewVirtualFile(filePath)
		task := NewTask(virtualFile, source, workspacePath, 0)
		tasks = append(tasks, task)
	}

	return NewVectorBatchTask(workspacePath, tasks)
}

func NewVectorBatchTask(workspacePath string, tasks []*Task) VectorBatchTask {
	source := VectorInitialFullIndexSource
	for _, task := range tasks {
		if task.Source == VectorSubsequentFullIndexSource {
			source = VectorSubsequentFullIndexSource
		} else if task.Source == VectorFileChangeIndexSource {
			source = VectorFileChangeIndexSource
			break
		}
	}
	return VectorBatchTask{
		WorkspacePath: workspacePath,
		Tasks:         tasks,
		Source:        source,
	}
}

func NewTask(virtualFile VirtualFile, source string, workspacePath string, priority int) *Task {
	return &Task{
		Id:                  int(taskIds.Add(1)),
		Source:              source,
		WorkspacePath:       workspacePath,
		Status:              TaskStatusPending,
		Priority:            priority,
		FailedTimes:         0,
		VirtualFile:         virtualFile,
		ShareChunkHitCnt:    0,
		DoEmbeddingChunkCnt: 0,
	}
}

type MTreeActionNodeMeta struct {
	RelativePath string `json:"relativePath"`
	Hash         string `json:"hash"`
	Type         string `json:"type"`
}

type MTreeActionNode struct {
	MTreeActionNodeMeta
	Children []MTreeActionNodeMeta `json:"childrenMetas,omitempty"`
}

type MTreePairNodes struct {
	New       *MTreeActionNode `json:"new"`
	Old       *MTreeActionNode `json:"old"`
	IsPartial bool             `json:"-"` // 代表这个节点是否为部分同步
}

type DiffNodeActions struct {
	AddNodes      []*MTreePairNodes      // 添加的非叶子节点
	UpdateNodes   []*MTreePairNodes      // 更新的非叶子节点
	DeleteNodes   []*MTreePairNodes      // 删除的非叶子节点
	LeafNodes     []*MTreeActionNodeMeta // 待check文件状态的节点
	ServerFileNum int
}

type LeafActionNodes struct {
	MTreeActionNodeMeta
	NodeStatus string
}

type MTreePairActionNodes struct {
	Node       *MTreePairNodes
	Operator   string
	NodeStatus string
}

type FileStatusV2 struct {
	Digest       string `json:"digest"`
	RelativePath string `json:"relativePath"`
	Status       string `json:"status,omitempty"`
}

func GetLeafNodeKey(relativePath string, sha256 string) string {
	return fmt.Sprintf("%s:%s", relativePath, sha256)
}

// 获取新的待同步节点，用于处理部分同步的情况
func GetNewToSyncNode(node *MTreeActionNode, discardFilePath []string) *MTreeActionNode {
	newNode := MTreeActionNode{
		MTreeActionNodeMeta: MTreeActionNodeMeta{
			RelativePath: node.RelativePath,
			Type:         node.Type,
		},
		Children: nil,
	}

	newChildren := make([]MTreeActionNodeMeta, 0)
	for _, child := range node.Children {
		found := false
		for _, discardPath := range discardFilePath {
			if child.RelativePath == discardPath {
				found = true
				break
			}
		}
		if !found {
			newChildren = append(newChildren, child)
		}
	}

	newNode.Children = newChildren
	childHashes := ""
	for _, c := range newNode.Children {
		childHashes += c.Hash
	}
	h := sha256.Sum256([]byte(childHashes))
	newNode.Hash = hex.EncodeToString(h[:])
	return &newNode
}

// CodebaseIndexSetting 代码库索引设置
type CodebaseIndexSetting struct {
	AutoIndex      bool   `json:"autoIndex"`
	Progress       string `json:"progress"`       // 索引进度，none/running/finish
	FileNumber     int    `json:"fileNumber"`     // 仓库文件数量
	UserTrigger    bool   `json:"userTrigger"`    // 是否由用户触发索引
	IsFinished     bool   `json:"isFinished"`     // 是否已经至少完成一次索引
	IsCanceled     bool   `json:"isCanceled"`     // 是否处于取消状态
	IsDeleted      bool   `json:"isDeleted"`      // 是否处于已删除状态
	GraphProgress  string `json:"graphProgress"`  // 图谱索引进度，none/running/finish
	VectorProgress string `json:"vectorProgress"` // 向量索引进度，none/running/finish
}

type CodebaseSettingCommonParams struct {
	WorkspacePath string `json:"workspacePath"`
}

type CodebaseDisplaySetting struct {
	WorkspacePath string `json:"workspacePath"`
	IsIllegal     bool   `json:"isIllegal"`
	AutoIndex     bool   `json:"autoIndex"`
	Progress      string `json:"progress"` // 索引进度，none/running/finish
	FileNumber    int    `json:"fileNumber"`
}

type UpdateStatusParams struct {
	WorkspacePath string `json:"workspacePath"`
	AutoIndex     bool   `json:"autoIndex"`
}

type UpdateProgressParams struct {
	WorkspacePath string `json:"workspacePath"`
	Progress      string `json:"progress"`
}
