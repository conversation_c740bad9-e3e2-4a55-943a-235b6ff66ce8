package definition

const (
	// StatusPending 表示任务已创建但尚未开始。
	ChatTaskStatusPending = "Pending"

	// StatusDesigning 表示任务正处于设计阶段。
	ChatTaskStatusDesigning = "Designing"

	// StatusPlanning 表示任务正处于计划阶段。
	ChatTaskStatusPlanning = "Planning"

	// StatusActionRequired 表示任务需要用户操作。
	ChatTaskStatusActionRequired = "ActionRequired"

	// StatusSummarizing 表示任务正处于总结阶段。
	ChatTaskStatusSummarizing = "Summarizing"

	// StatusPaused 表示任务已暂停。
	ChatTaskStatusPaused = "Paused"

	// StatusAwaitingConfirmation 表示任务等待用户确认。
	ChatTaskStatusAwaitingConfirmation = "AwaitingConfirmation"

	// StatusPreparing 表示任务正在准备执行环境。
	ChatTaskStatusPreparing = "Preparing"

	// StatusRunning 表示任务正在执行中。
	ChatTaskStatusRunning = "Running"

	// StatusAccepted 表示已经接受。
	ChatTaskStatusAccepted = "Accepted"

	// StatusRejected 表示任务被拒绝。
	ChatTaskStatusRejected = "Rejected"

	// StatusCancelled 表示任务被取消。
	ChatTaskStatusCancelled = "Cancelled"

	// task event type
	ChatTaskTypeStatusChange           = "TaskStatusChange"
	ChatTaskTypeRuntimeSSHRelay        = "RuntimeSSHRelay"
	ChatTaskTypeActionFlowUpdate       = "ActionFlowUpdate"
	ChatTaskTypeTaskMessage            = "TaskMessage"
	ChatTaskTypeSandboxBootStageChange = "SandboxBootStageChange"
	ChatTaskTypeSandboxBootLog         = "SandboxBootLog"
	ChatTaskTypeUserTaskStatsEvent     = "UserTaskStatsEvent"
	ChatTaskTypeInProgressTaskUpdate   = "InProgressTaskUpdate"

	// 禁用整体超时
	WatchTaskEventSSETimeout = 0
)

// 服务端响应结构
type BaseResponse struct {
	RequestId string `json:"requestId"` // remoteAgent返回的请求id，不是端侧发起请求的requestId
	Code      int    `json:"code"`      // 响应状态码
	ErrorCode string `json:"errorCode"` // 在 code 非 200 的时候，请求失败的错误码
	Message   string `json:"message"`   // Error.TaskNotFound
}

// 客户端响应结构
type Response struct {
	Success      bool        `json:"success"`
	ErrorCode    string      `json:"errorCode"`
	ErrorMessage string      `json:"errorMessage"`
	Data         interface{} `json:"data"`
}

func NewSuccessResponse(data interface{}) *Response {
	return &Response{
		Success: true,
		Data:    data,
	}
}

func NewErrorResponse(errorCode string, errorMessage string, data any) *Response {
	return &Response{
		Success:      false,
		ErrorCode:    errorCode,
		ErrorMessage: errorMessage,
		Data:         data,
	}
}

type AllocateWorkspaceParams struct {
	MachineId     string   `json:"machineId"`
	FilePath      string   `json:"filePath"`
	VcsType       string   `json:"vcsType"`
	VcsUrls       []string `json:"vcsUrls"`
	WorkspaceInfo string   `json:"workspaceInfo"`
}

type AllocateWorkspaceResponse struct {
	BaseResponse
	Data Workspace `json:"data"`
}

type CreateChatTaskParams struct {
	Name      string `json:"name"`
	Project   string `json:"project"`
	MachineId string `json:"machineId"`

	ExecutionMode ExecutionMode `json:"executionMode"` // 执行类型， 支持值： [Direct, WithDesign]
	// 用户输入
	Query string `json:"query"`

	// 对于ExecutionMode为Direct的时候，创建任务的时候 直接创建执行
	ExecutionConfig *ExecuteTaskParams      `json:"executionConfig"`
	WorkspaceConfig AllocateWorkspaceParams `json:"workspaceConfig"`
}

type CreateChatTaskResponse struct {
	BaseResponse
	Data RemoteChatTask `json:"data"` // 返回 Agent 的相关信息，暂时只包含 AgentId
}

type UpdateChatTaskParams struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

type UpdateChatTaskStatusParams struct {
	Id        string `json:"id"`
	SessionID string `json:"sessionId"`
	Status    string `json:"status"`
}

type DeleteChatTaskParams struct {
	Id string `json:"id"`
}

type CancelChatTaskParams struct {
	Id string `json:"id"`
}

type ResumeChatTaskParams struct {
	Id string `json:"id"`
}

type GetChatTaskParams struct {
	Id string `json:"id"`
}

type GetChatTaskResponse struct {
	BaseResponse
	Data RemoteChatTask `json:"data"` // 返回 Agent 的相关信息，暂时只包含 AgentId
}
type GetTaskBootLogParams struct {
	Id string `json:"id"`
}

type GetChatTaskBootLogResponse struct {
	BaseResponse
	Data string `json:"data"`
}

type ListChatTaskParams struct {
	Project            string `json:"project,omitempty"`
	ExcludedProject    string `json:"excludedProject,omitempty"`
	InProgress         *bool  `json:"inProgress"`
	NameLike           string `json:"nameLike,omitempty"`
	Page               int    `json:"page"`
	PageSize           int    `json:"pageSize"`
	Version            int    `json:"version"`
	Statuses           string `json:"statuses,omitempty"`
	ExecutionSessionId string `json:"executionSessionId,omitempty"`
}

type ListChatTaskResponse struct {
	BaseResponse
	Data ListChatTaskResult `json:"data"`
}

type ListChatTaskResult struct {
	PageNumber int               `json:"pageNumber"`
	PageSize   int               `json:"pageSize"`
	TotalSize  int               `json:"totalSize"`
	Items      []*RemoteChatTask `json:"items"`
}

type SyncTaskStatusRequest struct {
	Id     string `json:"id"`
	Status string `json:"status"`
}

type SyncSandboxBootStageChangeRequest struct {
	Id        string `json:"id"`
	NewStage  string `json:"newStage"`
	NewStatus string `json:"newStatus"`
}

type SyncSandboxBootLogRequest struct {
	Id     string `json:"id"`
	Output string `json:"output"`
}

type TaskEvent struct {
	Id    int64       `json:"id"`
	Event string      `json:"event"`
	Data  interface{} `json:"data"`
}

type TaskStatusChangeData struct {
	TaskId    string `json:"taskId"`
	OldStatus string `json:"oldStatus"`
	NewStatus string `json:"newStatus"`
	Timestamp int64  `json:"timestamp"`
}

type SandboxBootStageChangeData struct {
	TaskId      string `json:"taskId"`
	ExecutionId string `json:"executionId"`
	NewStage    string `json:"newStage"`
	NewStatus   string `json:"newStatus"`
	Timestamp   int64  `json:"timestamp"`
}

type SandboxBootLogData struct {
	TaskId      string `json:"taskId"`
	ExecutionId string `json:"executionId"`
	Output      string `json:"output"` // 增量日志内容
	Timestamp   int64  `json:"timestamp"`
}

type Workspace struct {
	WorkspaceId string `json:"workspaceId"`
	FilePath    string `json:"filePath"`
}

type TaskDesign struct {
	DesignFile       string `json:"designFile,omitempty"`
	DesignData       string `json:"designData,omitempty"`
	UserQuery        string `json:"userQuery,omitempty"`
	UserRequirements string `json:"userRequirements,omitempty"`

	// UpdatedAtTimestamp 更新时间戳
	UpdatedAtTimestamp int64 `json:"updatedAtTimestamp,omitempty"`
}
type RemoteChatTask struct {
	TaskId             string        `json:"taskId"`
	Name               string        `json:"name"`
	WorkspaceId        string        `json:"workspaceId"`
	MachineId          string        `json:"machineId"`
	FilePath           string        `json:"filePath"`
	PrevStatus         string        `json:"prevStatus"`
	Status             string        `json:"status"`
	CreatedAtTimestamp int64         `json:"createdAtTimestamp"`
	EndAtTimestamp     int64         `json:"endAtTimestamp"`
	Execution          *Execution    `json:"execution"`
	UpdatedAtTimestamp int64         `json:"updatedAtTimestamp"`
	Workspace          Workspace     `json:"workspace"`
	ExecutionMode      ExecutionMode `json:"executionMode"`
	DesignSessionId    string        `json:"designSessionId"`
	ExecutionSessionId string        `json:"executionSessionId"`
	ExecutionRequestId string        `json:"executionRequestId"`
	Design             *TaskDesign   `json:"design,omitempty"`
}

type ExecutionMode string

const (
	ExecutionModelDirect     ExecutionMode = "Direct"
	ExecutionModelWithDesign ExecutionMode = "WithDesign"
)

type Execution struct {
	ExecutionId         string     `json:"executionId"`
	AgentClass          AgentClass `json:"agentClass"`
	RawConfig           string     `json:"rawConfig"`
	SourceBranch        string     `json:"sourceBranch"`
	HeadCommitId        string     `json:"headCommitId"`
	RuntimeInfo         Runtime    `json:"runtime"`
	BootStatus          string     `json:"bootStatus"`
	StartedAt           int64      `json:"startedAt"`
	FinishedAt          int64      `json:"finishedAt"`
	CompletedAt         int64      `json:"completedAt"`
	ActionCount         int        `json:"actionCount"`
	FinishedActionCount int        `json:"finishedActionCount"`
}

type Runtime struct {
	WsServerEndpoint string `json:"wsServerEndpoint"`
	IntranetAddress  string `json:"intranetAddress"`
	SSHRelayAddress  string `json:"sshRelayAddress"`
}

type ChatTask struct {
	Id                  string        `json:"id"`
	WorkspaceId         string        `json:"workspaceId"`
	MachineId           string        `json:"machineId"`
	Name                string        `json:"name"`
	PrevStatus          string        `json:"prevStatus"`
	Status              string        `json:"status"`
	CreateTime          int64         `json:"createTime"`
	SourceBranch        string        `json:"sourceBranch"`
	HeadCommitId        string        `json:"headCommitId"`
	ExecuteStartTime    int64         `json:"executeStartTime"`
	ExecuteEndTime      int64         `json:"executeEndTime"`
	EndTime             int64         `json:"endTime"`
	SSHRelayAddress     string        `json:"sshRelayAddress"`
	BootStatus          string        `json:"bootStatus"`
	FinishedActionCount int           `json:"finishedActionCount"`
	TotalActionCount    int           `json:"totalActionCount"`
	RawConfig           string        `json:"rawConfig"`
	UpdatedAtTimestamp  int64         `json:"updatedAtTimestamp"`
	FilePath            string        `json:"filePath"`
	DesignFile          string        `json:"designFile"`
	UserRequirements    string        `json:"userRequirements"`
	AgentClass          AgentClass    `json:"agentClass"`
	ExecutionMode       ExecutionMode `json:"executionMode"`
	DesignSessionId     string        `json:"designSessionId"`
	ExecutionSessionId  string        `json:"executionSessionId"`
	ExecutionRequestId  string        `json:"executionRequestId"`
	Query               string        `json:"query"`
}

// ChatTaskBasicCacheInfo 缓存chat task的基本信息
type ChatTaskBasicCacheInfo struct {
	Id                 string `json:"id"`
	WorkspaceId        string `json:"workspaceId"`
	MachineId          string `json:"machineId"`
	Name               string `json:"name"`
	DesignSessionId    string `json:"designSessionId"`
	ExecutionSessionId string `json:"executionSessionId"`
	ExecutionRequestId string `json:"executionRequestId"`
	Query              string `json:"query"`
}

func (task *ChatTask) IsRemoteAgent() bool {
	return task.AgentClass == AgentClassRemoteAgent
}

func (task *ChatTask) IsTaskWithDesign() bool {
	return task.ExecutionMode == ExecutionModelWithDesign
}

type GetTaskWebsocketEndpointParams struct {
	TaskId string `json:"taskId"`
}

type BootStatusDetail struct {
	CurrentStage string `json:"current_stage"`
	Status       string `json:"status"`
	Progress     struct {
		Prepare struct {
			Status string `json:"status"`
		} `json:"prepare"`
		Run struct {
			Status string `json:"status"`
		} `json:"run"`
		Sync struct {
			Status string `json:"status"`
		} `json:"sync"`
	} `json:"progress"`
}

type UserChatTaskStats struct {
	InProgress           int `json:"inProgress"`
	ActionRequired       int `json:"actionRequired"`
	AwaitingConfirmation int `json:"awaitingConfirmation"`
	Running              int `json:"running"`
}

type GetUserChatTaskStatsResponse struct {
	BaseResponse
	Data UserChatTaskStats `json:"data"`
}

type GetUserChatTaskQuotasRequest struct {
	Resource string `json:"resource"`
}

type GetUserChatTaskQuotasResponse struct {
	BaseResponse
	Data UserChatTaskQuotas `json:"data"`
}

type UserChatTaskQuotas struct {
	Resource    string `json:"resource"`    // 对应资源 quota, 目前只支持 task.user.running
	Description string `json:"description"` // 描述，如 用户支持运行中任务数的配额
	Limit       int64  `json:"limit"`       // 配额的上限
	Usage       int64  `json:"usage"`       // 已经使用的数量
	Unit        string `json:"unit"`        // quota 单位
}

type RawConfig struct {
	ChatContext         BaseChatContext     `json:"chatContext"`
	PluginPayloadConfig PluginPayloadConfig `json:"pluginPayloadConfig"`
}

type TaskInfo struct {
	TaskId     string `json:"id"`
	DesignPath string `json:"designPath"`
}

type TaskSessionInfo struct {
	SessionId string
	Mode      string
	TaskInfo  *TaskInfo
}
