package definition

// AuthStatusInit 授权初始化
const AuthStatusInit = 1

// AuthStatusSuccess 授权成功
const AuthStatusSuccess = 2

// AuthStatusExpired 授权状态过期
const AuthStatusExpired = 3

// AuthStatusError 授权异常
const AuthStatusError = 4

// AuthStatusNetworkError 网络错误
const AuthStatusNetworkError = 5

// AuthIpBannedError IP被禁用错误
const AuthIpBannedError = 6

// AuthAppDisabledError App被禁用错误
const AuthAppDisabledError = 7

// WhitelistNone 白名单未通过
const WhitelistNone = 1

// WhitelistWait 申请中，已废弃
const WhitelistWait = 2
const WhitelistPass = 3
const WhitelistUnknown = 4
const WhitelistNoLicence = 5
const WhitelistNoQuota = 6

const (
	// LoginTypeAccessKeySecretKey ak/sk登录
	LoginTypeAccessKeySecretKey = "ak_sk"

	// LoginTypeNameOnly 专有云仅凭name登录
	LoginTypeNameOnly = "name_only"

	// LoginTypePersonalToken 云效PersonalToken登录
	LoginTypePersonalToken = "personal_token"

	// LoginTypeWeb 网页登录
	LoginTypeWeb = "web"

	// LoginTypeCustom custom
	LoginTypeCustom = "custom"

	// 新版设备登录
	LoginTypeDeviceToken = "device_token"
)

const (
	LoginDedicatedTypeDedicated = "dedicated"

	LoginDedicatedTypeStandard = "standard"
)

type AccountErrorMsg struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

const (
	// UserTypePersonalStandard 个人标准版
	UserTypePersonalStandard = "personal_standard"

	// UserTypePersonalProfessionalTrial 个人专业版试用 仅 Qoder 有
	UserTypePersonalProfessionalTrial = "personal_professional_trial"

	// UserTypePersonalProfessional 个人专业版 目前未实现
	UserTypePersonalProfessional = "personal_professional"

	// UserTypeEnterpriseStandard 公有云-企业标准版
	UserTypeEnterpriseStandard = "enterprise_standard"

	// UserTypeEnterpriseDedicated 公有云-企业专属版
	UserTypeEnterpriseDedicated = "enterprise_professional"
)

type UserStatusResponse struct {
	Name                      string `json:"name"`
	Id                        string `json:"id"`
	AccountId                 string `json:"accountId"`
	StaffId                   string `json:"staffId"`
	Token                     string `json:"token"`
	Quota                     int    `json:"quota"`
	WhitelistStatus           string `json:"whitelistStatus"`
	OrgId                     string `json:"orgId"`   //企业id
	OrgName                   string `json:"orgName"` //企业名称
	YxUid                     string `json:"yxUid"`   //云效uid
	AvatarUrl                 string `json:"avatarUrl"`
	SecurityOauthToken        string `json:"securityOauthToken"`
	RefreshToken              string `json:"refreshToken"`
	ExpireTime                int64  `json:"expireTime"`
	IsSubAccount              bool   `json:"isSubAccount"` //since:"v2.1.2"
	Email                     string `json:"email"`
	UserType                  string `json:"userType"`
	UserTag                   string `json:"userTag"`
	IsPrivacyPolicyModifiable bool   `json:"isPrivacyPolicyModifiable"`
}

type LoginParams struct {
	// LoginType 登录方式：aliyun, ak_sk, name_only, personal_token
	LoginType string `json:"loginType"`
	UserName  string `json:"userName"`
	AccessKey string `json:"accessKey"`
	SecretKey string `json:"secretKey"`
	// 云效personalToken
	PersonalToken string `json:"personalToken"`
	// 阿里云用户id
	UserId string `json:"userId"`
	// 云效企业id
	OrgId    string         `json:"orgId"`
	AuthInfo CustomAuthInfo `json:"authInfo"`
	// 国际版intl，国内版cn
	RegionEnv string `json:"regionEnv"`
	//是否进行了专属版登录
	//  ○ standard	标准版登录
	//  ○ dedicated	专属版
	LoginDedicatedType string `json:"loginDedicatedType"`
}

// CustomAuthInfo 专有云定制授权方式
type CustomAuthInfo struct {
	UserName string `json:"userName,omitempty"`
	OrgId    string `json:"orgId,omitempty"`
}

type SwitchRegionParams struct {
	RegionEnv string `json:"regionEnv"`
}

func (a *CustomAuthInfo) IsValid() bool {
	return a.UserName != ""
}

type AuthStatusParams struct {
	// 阿里云用户id
	UserId string `json:"userId"`
	// 阿里云ak/sk
	Ak string `json:"accessKey"`
	Sk string `json:"secretKey"`
}

type GrantInfoQueryParams struct {
	// 阿里云用户id，可选
	UserId string `json:"userId"`
	// 云效personalToken，可选
	PersonalToken string `json:"personalToken"`
	// 阿里云ak/sk
	Ak string `json:"accessKey"`
	Sk string `json:"secretKey"`
	// 国际版intl，国内版cn
	RegionEnv string `json:"regionEnv"`
	//  ○ standard	标准版登录
	//  ○ dedicated	专属版
	LoginDedicatedType string `json:"loginDedicatedType"`
}

type SwitchAccountParams struct {
	// 阿里云用户id，可选
	UserId string `json:"userId"`
	OrgId  string `json:"orgId,omitempty"`
}

type SwitchAccountInfoResult struct {
	BaseResult
}

// AuthInfoQueryParams 查询用户授权信息
type AuthInfoQueryParams struct {
	// 阿里云用户id，可选
	UserId string `json:"userId"`
	// 阿里云ak/sk，可选
	Ak string `json:"accessKey"`
	Sk string `json:"secretKey"`
}

type AuthStatusResult struct {
	Status    int    `json:"status"`    // 1 - 未登录, 2 - 已登录, 3 - 登录已超期, 4 - 登录失效（user文件被破坏或篡改），5 - 网络错误
	Name      string `json:"name"`      // 用户名
	Id        string `json:"id"`        // 用户ID
	AccountId string `json:"accountId"` // 主账号ID，已废弃，勿用
	Token     string `json:"token"`     // 身份Token
	Quota     int    `json:"quota"`     // 剩余配额（天数）
	UserType  string `json:"userType"`
	// 是否为白名单用户
	// - 1 未加白
	// - 2 等待审批
	// - 3 已加白
	// - 4 检查出错
	// - 5 检查出错
	WhitelistStatus    int    `json:"whitelist"`
	OrgId              string `json:"orgId"`   //企业id
	OrgName            string `json:"orgName"` //企业名称
	YxUid              string `json:"yxUid"`   //云效uid
	StaffId            string `json:"staffId"` //工号id，企业版才有
	AvatarUrl          string `json:"avatarUrl"`
	MessageId          string `json:"messageId"` // UUID用于IDE端消息去重
	Email              string `json:"email"`
	SecurityOauthToken string `json:"securityOauthToken"`
	RefreshToken       string `json:"refreshToken"`
	ExpireTime         int64  `json:"expireTime"`
	IsSubAccount       bool   `json:"isSubAccount"` //since:"v2.1.2"
	// 用户类型标签（设置页标签文本，由 go 返回）
	UserTag string `json:"userTag"`
	// 是否同意隐私政策
	PrivacyPolicyAgreed bool `json:"privacyPolicyAgreed"`
	// 是否可修改隐私政策
	IsPrivacyPolicyModifiable bool `json:"isPrivacyPolicyModifiable"`
	//○ cloud（公有云）
	//○  vpc（专有云）
	//○  vip(国内专属版)
	//○  individual_intl（国际个人版）
	//○ dedicated_intl(国际专属版)
	CloudType string `json:"cloudType"`
}

type LoginStartResult struct {
	Url     string `json:"url"`
	Success bool   `json:"success"`
}

type LogoutResult struct {
	Success bool `json:"success"`
}

type QuotaCache struct {
	ExpireTime      int64  `json:"expireTime"`
	UserId          string `json:"userId"`
	Token           string `json:"token"`
	Quota           int    `json:"quota"`
	Status          int    `json:"status"`
	WhitelistStatus int    `json:"whitelist"`
}

type LoginOrgInfo struct {
	OrgId   string `json:"orgId"`   //企业id
	OrgName string `json:"orgName"` //企业名称
	YxUid   string `json:"yxUid"`   //云效用户id
}

type GrantAccountInfoWrap struct {
	AccountInfo []GrantAccountInfo `json:"accountInfo"` // 包裹了GrantAccountInfo
	BaseResult
}

type GrantAccountInfo struct {
	OrgId        string `json:"orgId"`   //企业id
	OrgName      string `json:"orgName"` //企业名称
	YxUid        string `json:"yxUid"`   //云效用户id
	Uid          string `json:"userId"`
	Name         string `json:"userName"`
	GrantType    string `json:"grantType"` // 类型：personal/organization
	IsSubAccount bool   `json:"isSubAccount"`
	Email        string `json:"email"`
}

type LoginUserInfo struct {
	Aid                string           `json:"aid"`
	Uid                string           `json:"uid"`
	Name               string           `json:"name"`
	SecurityOauthToken string           `json:"securityOauthToken"`
	RefreshToken       string           `json:"refreshToken"`
	ExpireTime         int64            `json:"expireTime"`
	AuthStatus         AuthStatusResult `json:"authStatus"`
}

type LoginAuthUserInfo struct {
	Aid   string `json:"aid"`
	Uid   string `json:"uid"`
	Name  string `json:"name"`
	OrgId string `json:"orgId"`
}

type LoginInfoContext struct {
	UserInfo           LoginUserInfo      //登录用户信息
	GrantAccountInfos  []GrantAccountInfo //授权列表
	AuthStatus         AuthStatusResult   //授权状态
	IsSelectAccount    bool               //最终渲染时是否经历过选择账号，影响前端文案渲染
	SelectOrg          *LoginOrgInfo      //选择登录的企业
	SelectLoginAccount *GrantAccountInfo  //选择登录的账号
	LoginStep          string             //登录阶段 final 最终渲染/select-org 选择企业
}

type AccessKeyLoginRequest struct {
	AccessKey string `json:"accessKey"`
	SecretKey string `json:"secretKey"`
	// 阿里云sts token
	SecurityToken string `json:"securityToken"`
	// 云效企业id
	OrgId string `json:"orgId"`

	LoginExtraParam *LoginExtraParam
}

type UserLoginRequest struct {
	UserId        string `json:"userId"`
	OrgId         string `json:"orgId"` // 云效企业id
	LoginUserInfo LoginUserInfo
}

type FcCredentialConfig struct {
	Default AliyunCredentialConfig `yaml:"default"`
}

// AliyunCredentialConfig 阿里云通ak/sk格式
type AliyunCredentialConfig struct {
	AccessKeyID     string `json:"accessKeyID" yaml:"AccessKeyID"`
	AccessKeySecret string `json:"accessKeySecret" yaml:"AccessKeySecret"`
	SecurityToken   string `json:"securityToken" yaml:"SecurityToken"`
	Expiration      string `json:"expiration" yaml:"Expiration"`
	AccountId       string `json:"accountId" yaml:"AccountID"`
}

type LoginRequestResult struct {
	LoginUrl        string `json:"loginUrl"`
	Nonce           string `json:"nonce"`
	Verifier        string `json:"verifier"`
	Challenge       string `json:"challenge"`
	ChallengeMethod string `json:"challengeMethod"`
	ErrorCode       string `json:"errorCode"`
	ErrorMsg        string `json:"errorMsg"`
	Success         bool   `json:"success"`
}

type LoginAuthCallbackParam struct {
	Nonce       string `json:"nonce"`
	Auth        string `json:"auth"`
	TokenString string `json:"tokenString"`
}

type LoginAuthCallbackResult struct {
	Success   bool   `json:"success"`
	ErrorCode string `json:"errorCode"`
	ErrorMsg  string `json:"errorMsg"`
}

type AuthParseResult struct {
	SecurityOauthToken string
	RefreshToken       string
	ExpireTime         int64
}

type EnvSwitchResult struct {
	Result bool `json:"result"`
	BaseResult
}

const (
	// UserSourceCommon 通用
	UserSourceCommon = "common"

	// UserSourceFc fc函数计算平台
	UserSourceFc = "fc"

	// UserSourcePai pai平台
	UserSourcePai = "pai"
)

type LoginExtraParam struct {
	UserSourceChannel string
}

// V1PollDeviceTokenRequest represents the request parameters for polling device token
type V1PollDeviceTokenRequest struct {
	Nonce               string `json:"nonce"`
	CodeVerifier        string `json:"code_verifier"`
	CodeChallengeMethod string `json:"code_challenge_method"`
}

// DeviceLoginParams represents the parameters for device login
type DeviceLoginParams struct {
	Token        string `json:"token"`
	RefreshToken string `json:"refreshToken"`
	ExpiresIn    string `json:"expiresIn"`
	UserID       string `json:"userId"`
	Username     string `json:"username"`
}
