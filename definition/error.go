package definition

import (
	"errors"
	"fmt"
)

type TokenExpiredError struct {
	Msg string
}

type NoVisibleKnowledgeBaseError struct {
	Msg string
}

const (
	// NotificationCodeQuotaExhausted 补全/问答次数额度用完了
	NotificationCodeQuotaExhausted string = "110"

	// NotificationCodeOrderExpired 订单到期了
	NotificationCodeOrderExpired string = "111"

	// NotificationCodeOrderExpiredSwitchAccount 订单到期切换账号提示
	NotificationCodeOrderExpiredSwitchAccount string = "601"

	// NotificationPopDataPolicySign 通知签署数据协议
	NotificationPopDataPolicySign = "pop_data_policy_sign"

	// NotificationReportCrash 崩溃上报
	NotificationReportCrash = "pop_report_crash"
)

const (
	// UserAccountExceed 用户触发限流
	UserAccountExceed string = "110"

	// UserAccountExpire 企业订单过期, 用户已过期（包括：被移出企业
	UserAccountExpire string = "111"

	AkSkWrongErrorCode     = "ak_sk_wrong_error"
	TokenWrongErrorCode    = "token_wrong_error"
	NetworkErrorCode       = "network_error"
	ServerErrorCode        = "server_error"
	UnknownErrorCode       = "unknown_error"
	EndpointEmptyErrorCode = "endpoint_empty_error"

	// 协议签署错误
	DataPolicySignErrorCode = "data_policy_sign_error"

	// ImageUploadErrorCode 图片上传异常
	ImageUploadErrorCode                 = "image_upload_error"
	ImageUploadTypeNotSupportedErrorCode = "not_support_image_type"
	// LogCollectionErrorCode 日志收集异常
	LogCollectionErrorCode = "log_collection_error"
	// UploadFileErrorCode 文件上传失败
	UploadFileErrorCode = "upload_file_error"

	// 工作区文件过期
	WorkingSpaceFileOutdatedErrorCode = "working_space_file_outdated_error"
	// 数据库查询失败
	SqlQueryErrorCode = "sql_query_error"
	// 数据库操作失败
	SqlExecErrorCode = "sql_exec_error"
	// 批量操作未完全成功
	BatchOperateErrorCode = "batch_operate_error"

	// 不支持的场景
	UnSupportedErrorCode = "unsupported_error"
	// 数据损坏
	DataCorruptErrorCode = "data_corrupt_error"
	// 日志文件不存在
	LogFileNotExistErrorCode = "log_file_not_exist_error"
	// 参数不全
	ParamErrorCode = "param_error"

	// 文件编辑相关错误码
	ApplyUnSupportedErrorCode       = "apply_unsupported_error"
	ApplyTimeoutErrorCode           = "apply_timeout_error"
	ApplyUnknownErrorCode           = "apply_unknown_error"
	GetContentErrorCode             = "get_content_error" // 获取文件内容失败
	CreateWorkingSpaceFileErrorCode = "create_working_space_file_error"
	UserCancelErrorCode             = "user_cancel_error"
	ModelRequestErrorCode           = "model_request_error"
	ContentMatchErrorCode           = "content_match_error"
	ArgumentParseErrorCode          = "argument_parse_error"
	PathNotExistErrorCode           = "path_not_exist_error"
	PathInvalidErrorCode            = "path_invalid_error"
	ContentTooLongErrorCode         = "content_too_long_error"
	ToolInvokeErrorCode             = "tool_invoke_error"
	DeleteFileErrorCode             = "delete_file_error"
	FileWhitelistErrorCode          = "file_whitelist_error"

	GenerateTokenLimitErrorCode = "generate_token_limit_error"

	ChatGeneratingTimeoutErrorCode = "generating_timeout_error"

	UserNotLoginErrorCode = "user_not_login_error"

	LockErrorCode = "lock_error"
)

var (
	DataPolicySignError = newApiError(DataPolicySignErrorCode, "Data policy sign error")
)

type TimeoutError struct {
	Msg  string
	Code int
}

func (e *TimeoutError) Error() string {
	return "timeout"
}

func (e *TokenExpiredError) Error() string {
	return e.Msg
}

// 语法处理处理相关错误信息
var (
	ErrNotSupportLanguage  = errors.New("not support language")
	ErrNotSupportIndexType = errors.New("not support index type")
)

type ApiError struct {
	Code    string `json:"error_code"`
	Message string `json:"message"`
}

func (a *ApiError) Error() string {
	return fmt.Sprintf("%s: %s", a.Code, a.Message)
}

func newApiError(code string, message string) ApiError {
	return ApiError{
		Code:    code,
		Message: message,
	}
}

func (e *NoVisibleKnowledgeBaseError) Error() string {
	return e.Msg
}

// LlmErrorResponse represents the structure of the error response.
type LlmErrorResponse struct {
	Error struct {
		Code    string      `json:"code"`
		Param   interface{} `json:"param"`
		Message string      `json:"message"`
		Type    string      `json:"type"`
	} `json:"error"`

	ID string `json:"id"`

	RequestID string `json:"request_id"`
}

type LlmProxyErrorResponse struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	RequestID string `json:"request_id"`
	Type      string `json:"type"`
	Details   string `json:"details"`
}
