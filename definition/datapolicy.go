package definition

const (
	// 注意：当前隐私策略会影响数据回流的开关逻辑，因此如果此处有改动需要评估对数据回流的影响。

	// SignStatusAgree 同意
	SignStatusAgree = "AGREE"

	// SignStatusDisagree 未同意
	SignStatusDisagree = "DISAGREE"

	// SignStatusNotified 已通知过
	SignStatusNotified = "NOTIFIED"

	// SignStatusNoRecord 未记录
	SignStatusNoRecord = "NO_RECORD"

	// 协议更新的时间
	DataPolicyUpdateDate = "2024-08-20T00:00:00+08:00"
)

type SignStatusResult struct {
	BaseResult
	//签署状态
	ResultBody SignStatusResultBody `json:"result"`
}

type SignStatusResultBody struct {
	//签署状态
	SignStatus string `json:"signStatus"`
}
