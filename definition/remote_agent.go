package definition

type GetRemoteAgentRecordsRequest struct {
	TaskId     string `json:"taskId"`
	SessionId  string `json:"sessionId"` // 会话 ID
	PageSize   int    `json:"pageSize"`
	PageNumber int    `json:"pageNumber"`
}

type GetRemoteAgentMessagesRequest struct {
	TaskId     string `json:"taskId"`
	SessionId  string `json:"sessionId"`
	PageSize   int    `json:"pageSize"`
	PageNumber int    `json:"pageNumber"`
}
