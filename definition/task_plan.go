package definition

// TaskTree represents plan
type TaskTree struct {
	SessionId    string `json:"sessionId" db:"session_id"`        // Associated session Id
	TaskTreeJSON string `json:"taskTreeJson" db:"task_tree_json"` // JSON representation of the task tree
	GmtCreate    int64  `json:"gmtCreate" db:"gmt_create"`        // Creation timestamp
	GmtModified  int64  `json:"gmtModified" db:"gmt_modified"`    // Last modification timestamp
}
