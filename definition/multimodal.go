package definition

const (
	// TargetCompressSize 目标压缩文件大小 3MB
	TargetCompressSize int64 = 3 * 1024 * 1024

	// Claude 有 8000 像素的限制，设置 8000 * 0.9 = 7200 提供安全边界
	MaxImagePixels = 7200

	// CompressImageStep
	// 0.7意味着每次缩小30%
	CompressImageStep float32 = 0.7

	// MinCompressScale
	// 最小压缩比例
	// 当图片过大，0.1 * 0.7 * 0.7 = 0.049
	MinCompressScale float32 = 0.048
)

type UploadImageRequest struct {
	Base64Image string `json:"base64Image"`
}

type UploadImageResult struct {
	Url string `json:"url"`
}

// UploadImageResponse {"success":false,"traceId":"","msgCode":500,"msgInfo":"Internal Server Error","message":"Internal Server Error"}
type UploadImageResponse struct {
	Result UploadImageResult `json:"result"`
	//RequestId string            `json:"request_id"`
	RequestId string `json:"requestId"`
	Success   bool   `json:"success"`
	Message   string `json:"message"`
}
