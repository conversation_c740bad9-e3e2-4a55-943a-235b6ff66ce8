package definition

import "strings"

const (
	LocaleEn = "en"

	LocaleZh = "zh"

	LocalZhCn = "zh-cn"
)

func GetPreferredLanguageDesc(preferredLanguage string) string {
	if strings.Contains(preferredLanguage, "zh") {
		return "Chinese(中文)"
	}
	if strings.Contains(preferredLanguage, "en") {
		return "English(English)"
	}
	if strings.Contains(preferredLanguage, "es") {
		return "Spanish(Español)"
	}
	if strings.Contains(preferredLanguage, "fr") {
		return "French(Français)"
	}
	if strings.Contains(preferredLanguage, "pt") {
		return "Portuguese(Português)"
	}
	if strings.Contains(preferredLanguage, "ja") {
		return "Japanese(日本語)"
	}
	if strings.Contains(preferredLanguage, "de") {
		return "German(<PERSON>uts<PERSON>)"
	}
	if strings.Contains(preferredLanguage, "ko") {
		return "Korean(한국어)"
	}

	return preferredLanguage
}
