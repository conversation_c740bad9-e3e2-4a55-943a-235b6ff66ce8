package definition

import "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"

// ToolConfirmRequest 工具执行确认请求
type ToolConfirmRequest struct {
	SessionId  string `json:"sessionId"`
	RequestId  string `json:"requestId"`
	ToolCallId string `json:"toolCallId"`
	Approval   bool   `json:"approval"`
}

type ToolInvokeRequest struct {
	RequestId  string         `json:"requestId"`
	ToolCallId string         `json:"toolCallId"`
	Name       string         `json:"name"`
	Parameters map[string]any `json:"parameters"`
	Async      bool           `json:"async"`
}

type ToolInvokeResponse struct {
	ToolCallId   string         `json:"toolCallId"`
	Name         string         `json:"name"`
	Success      bool           `json:"success"`
	ErrorMessage string         `json:"errorMessage"`
	Result       map[string]any `json:"result"`
}

const (
	ToolInputExtraToolCallId     = "tool_call_id"
	ToolInputExtraToolName       = "tool_name"
	ToolInputExtraSyncFunc       = "sync_func"
	ToolInputGetCtxForClientFunc = "get_ctx_for_client_func"
)

const (

	//开始解析
	StartToolParseEvent = "start"

	//结束解析
	EndToolParseEvent = "end"

	//增量解析
	DeltaParsingToolParsingEvent = "deltaParsing"
)

type ToolParseEvent struct {
	Type     string
	ToolCall *definition.ToolCall
	Extra    map[string]any
}

type StreamingContentType string

const (
	StreamingContentTypeContent = "content"

	StreamingContentTypeReasoning = "reasoning"
)

const (
	ToolParseEventExtraDeltaArgs = "delta_args" // 当前包的增量args
	ToolParseEventExtraWholeArgs = "whole_args" // 截止到当前包的全量的args
)
