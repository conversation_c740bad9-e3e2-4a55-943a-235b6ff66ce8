package definition

import (
	"encoding/json"
	"fmt"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	agentLlms "code.alibaba-inc.com/cosy/lingma-agent-graph/llms"
)

const (
	KeyChainTimeRecorder = "key_chain_time_recorder"
)

// RemoteChatAsk The request struct that sends to remote server
type RemoteChatAsk struct {
	RequestId               string                     `json:"request_id"`     //请求id，历史原因，卡片id等同于requestId相同
	RequestSetId            string                     `json:"request_set_id"` //问答集合id
	ChatRecordId            string                     `json:"chat_record_id"` //问答卡片id
	Stream                  bool                       `json:"stream"`
	ChatTask                string                     `json:"chat_task,omitempty"`
	ChatContext             interface{}                `json:"chat_context,omitempty"`
	ImageUrls               []string                   `json:"image_urls"`               //多模态场景下，图片链接
	ChatHistory             []ChatHistoryItem          `json:"chat_history,omitempty"`   //废弃了！老版chat service中会话历史
	PromptHistory           []AgentPromptHistoryItem   `json:"prompt_history,omitempty"` //agent模式下，会话历史
	IsReply                 bool                       `json:"is_reply"`
	IsRetry                 bool                       `json:"is_retry"`
	SessionId               string                     `json:"session_id"`
	CodeLanguage            string                     `json:"code_language"`
	Source                  int                        `json:"source"`
	Version                 string                     `json:"version,omitempty"`                    //会话本地化后为2
	ChatPrompt              string                     `json:"chat_prompt"`                          //agent模式下使用，直接使用端侧的prompt
	CustomSystemRoleContent string                     `json:"custom_system_role_content,omitempty"` //废弃了！老版约定的chat ask接口的系统提示词，服务端要求
	SystemRoleContent       string                     `json:"system_role_content,omitempty"`        //新版ai agent下覆盖系统提示词
	Parameters              map[string]interface{}     `json:"parameters"`
	UserType                string                     `json:"aliyun_user_type"`
	TaskDefinitionType      string                     `json:"task_definition_type,omitempty"`
	SessionType             string                     `json:"session_type,omitempty"` //agent类型
	AgentId                 string                     `json:"agent_id,omitempty"`     //约定的agent标识，用于区分模型路由
	TaskId                  string                     `json:"task_id,omitempty"`      //约定的agent下面具体的task标识，用于区分模型路由
	ModelConfig             ModelConfig                `json:"model_config"`
	Messages                []*agentDefinition.Message `json:"messages,omitempty"` //新版openAI标准格式
	Tools                   []agentLlms.Tool           `json:"tools"`              //新版openAI工具调用
	Mode                    string                     `json:"mode,omitempty"`     //新版common agent下，模式
	Passkey                 string                     `json:"passkey,omitempty"`
	LineUpType              string                     `json:"line_up_type,omitempty"` // 资源限制，排队类型
	LineUpId                string                     `json:"line_up_id,omitempty"`   // 资源限制，排队ID
}

type ChatResponse struct {
	Headers         map[string][]string `json:"headers"`
	Body            string              `json:"body"`
	StatusCodeValue int                 `json:"statusCodeValue"`
	StatusCode      string              `json:"statusCode"`
}

type ChatBody struct {
	Output        interface{} `json:"output"`
	Usage         LlmUsage    `json:"usage"`
	RequestId     string      `json:"request_id"`
	decodedOutput interface{}
}

// NewChatBody 解析 JSON 格式的聊天消息体并返回 ChatBody 结构。
// 参数：
//   - body: 包含聊天消息的 JSON 字符串。
//
// 返回值：
//   - ChatBody: 解析后的聊天消息体。
//   - error: 如果解析过程中发生错误，则返回非空错误。
//
// 该函数首先尝试将输入字符串反序列化为 ChatBody 结构。然后，它会进一步尝试将其中的输出部分分别按照 OpenAI 输出格式和旧版输出格式进行解析。
// 如果 OpenAI 输出格式有效，则使用该格式；否则，使用旧版输出格式。
func NewChatBody(body string) (ChatBody, error) {
	chatBody := ChatBody{}
	if err := json.Unmarshal([]byte(body), &chatBody); err != nil {
		return ChatBody{}, fmt.Errorf("unmarshal to Chat body: %v", err)
	}
	if chatBody.Output == nil {
		return ChatBody{}, fmt.Errorf("no valid chat output. body text: %s", body)
	}

	var output []byte
	output, _ = json.Marshal(chatBody.Output)

	// 尝试按 OpenAI 的输出格式解析
	openAIOutput, valid, err := parseOpenAIOutput(output)
	if err != nil {
		return ChatBody{}, err
	}
	if valid {
		chatBody.decodedOutput = openAIOutput
		return chatBody, nil
	}
	// 尝试按旧版输出格式解析
	obsoleteOutput := &ObsoleteOutput{}
	if err := json.Unmarshal(output, &obsoleteOutput); err != nil {
		return ChatBody{}, fmt.Errorf("unmarshal to Obsolete Output: %v", err)
	}
	chatBody.decodedOutput = obsoleteOutput
	return chatBody, nil
}

func parseOpenAIOutput(output []byte) (Output, bool, error) {
	// 尝试按 OpenAI 的输出格式解析
	openAIOutput := &OpenAIOutput{}
	if err := json.Unmarshal(output, &openAIOutput); err != nil {
		return nil, false, fmt.Errorf("unmarshal to OpenAI Output: %v", err)
	}
	if openAIOutput.Choices != nil {
		for i := range openAIOutput.Choices {
			choice := &openAIOutput.Choices[i]
			if _, ok := choice.Message.Content.(string); ok {
				//ignore
			} else {
				var structContent []OpenAIOutputMessageContent
				contentStr, _ := json.Marshal(choice.Message.Content)
				if err := json.Unmarshal(contentStr, &structContent); err != nil {
					return nil, false, fmt.Errorf("unmarshal to OpenAI Output Message Content: %v", err)
				}
				choice.Message.Content = structContent
			}
		}
	}
	return openAIOutput, openAIOutput.isValid(), nil
}

func (c *ChatBody) GetOutputText() string {
	// 如果 decodedOutput 为空，返回空字符串
	if c.decodedOutput == nil {
		return ""
	}
	outputText := ""
	// 尝试将 decodedOutput 断言为 *OpenAIOutput 类型
	if output, ok := c.decodedOutput.(*OpenAIOutput); ok {
		// 如果 Choices 非空且第一个 Choice 的 Message.Content 非空，
		// 返回第一个 Content 的 Text 字段，否则返回空字符串
		if len(output.Choices) > 0 {
			outputContent := output.Choices[0].Message.Content
			if content, ok := outputContent.(string); ok {
				outputText = content
			} else if contentArray, ok := outputContent.([]OpenAIOutputMessageContent); ok {
				outputText = contentArray[0].Text
			}
		}
	} else if obsoleteOutput, ok := c.decodedOutput.(*ObsoleteOutput); ok {
		// 尝试将 decodedOutput 断言为 *ObsoleteOutput 类型
		// 返回 ObsoleteOutput 的 Text 字段
		outputText = obsoleteOutput.Text
	}
	if outputText != "" {
		outputText = trimLastIllegalReplaceChar(outputText, 1)
	}
	return outputText
}

func (c *ChatBody) GetReasoningText() string {
	// 如果 decodedOutput 为空，返回空字符串
	if c.decodedOutput == nil {
		return ""
	}
	reasoningText := ""
	// 尝试将 decodedOutput 断言为 *OpenAIOutput 类型
	if output, ok := c.decodedOutput.(*OpenAIOutput); ok {
		// 如果 Choices 非空且第一个 Choice 的 Message.Content 非空，
		// 返回第一个 Content 的 Text 字段，否则返回空字符串
		if len(output.Choices) > 0 {
			reasoningText = output.Choices[0].Message.ReasoningContent
		}
	} else if obsoleteOutput, ok := c.decodedOutput.(*ObsoleteOutput); ok {
		// 尝试将 decodedOutput 断言为 *ObsoleteOutput 类型
		// 返回 ObsoleteOutput 的 Text 字段
		reasoningText = obsoleteOutput.ReasoningText
	}
	if reasoningText != "" {
		reasoningText = trimLastIllegalReplaceChar(reasoningText, 1)
	}
	return reasoningText
}

type Output interface {
	isValid() bool
}

// OpenAIOutput 表示来自 OpenAI API 的输出响应。
// 包含一个 Choices 列表，每个元素代表一个可能的回复选项。
type OpenAIOutput struct {
	Choices []OpenAIOutputChoice `json:"choices"`
}

// isValid 检查 OpenAIOutput 是否有效。
// 有效的条件是 Choices 字段非空且至少包含一个元素。
func (o *OpenAIOutput) isValid() bool {
	if o.Choices == nil || len(o.Choices) == 0 {
		return false
	}
	choice0 := o.Choices[0]
	if choice0.Message.Content == nil {
		return false
	}
	if contentArray, ok := choice0.Message.Content.([]OpenAIOutputMessageContent); ok {
		if len(contentArray) <= 0 {
			return false
		}
	}
	return true
}

// OpenAIOutputChoice 表示 OpenAI API 输出中的单个选择项。
// 每个选择项包含一条消息和完成原因。
type OpenAIOutputChoice struct {
	Message      OpenAIOutputMessage `json:"message"`       // 回复的消息内容
	FinishReason string              `json:"finish_reason"` // 完成的原因
}

// OpenAIOutputMessage 表示 OpenAI API 输出的消息内容。
// 消息包含多个文本块和发送者的角色。
type OpenAIOutputMessage struct {
	Content          any    `json:"content"`           // 消息内容，多模态情况下是数组，其他场景为字符串
	ReasoningContent string `json:"reasoning_content"` //推理过程，可选
	Role             string `json:"role"`              // 发送者角色
}

type OpenAIOutputMessageContent struct {
	Text string `json:"text"` // 文本内容
}

// ObsoleteOutput 表示已废弃的输出格式。
// 该结构体用于向后兼容旧版本的 API 响应。
type ObsoleteOutput struct {
	FinishReason  string `json:"finish_reason"`  // 完成的原因
	Text          string `json:"text"`           // 文本内容
	ReasoningText string `json:"reasoning_text"` // 推理文本内容
}

// isValid 检查 ObsoleteOutput 是否有效。
// 有效的条件是 FinishReason 和 Text 字段均不为空。
func (o *ObsoleteOutput) isValid() bool {
	return o.FinishReason != "" && len(o.Text) > 0
}

// 移除最后的非法字符，最多移除 $checkCount 个
func trimLastIllegalReplaceChar(s string, checkCount int) string {
	if len(s) == 0 {
		return s
	}
	runes := []rune(s)
	validText := s
	c := 0 //最多校验3个rune
	for i := len(runes) - 1; i >= 0; i-- {
		c += 1
		if c > checkCount {
			break
		}
		if IllegalReplaceChar == runes[i] {
			validText = string(runes[:i])
			continue
		} else {
			validText = string(runes[:i+1])
			break
		}

	}
	return validText
}

// IllegalReplaceChar 在Unicode标准中，码点U+FFFD（十进制为65533）被定义为“REPLACEMENT CHARACTER”（替换字符），通常显示为一个问号（?）或者一个方块。这个字符主要用于表示数据传输或处理过程中遇到的错误，特别是当系统无法正确解析或显示某个特定的Unicode字符时
const IllegalReplaceChar = rune(65533)

type ChatAnswer struct {
	RequestId  string `json:"requestId"`
	SessionId  string `json:"sessionId"`
	Text       string `json:"text"`
	Overwrite  bool   `json:"overwrite"`
	IsFiltered bool   `json:"isFiltered"`
	Timestamp  int64  `json:"timestamp"`
	// 问答回答扩展属性
	// - intentionType 意图类型 chat | unittest | develop
	// - sessionType agent类型	chat | developer
	Extra map[string]any `json:"extra"`
}

// ChatThink 问答推理文本
type ChatThink struct {
	RequestId string `json:"requestId"`
	SessionId string `json:"sessionId"`
	Text      string `json:"text"`
	Timestamp int64  `json:"timestamp"`
	Step      string `json:"step"`
	// 问答回答扩展属性
	// - intentionType 意图类型 chat | unittest | develop
	// - sessionType agent类型	chat | developer
	Extra map[string]any `json:"extra"`
}

// ChatStart 排队结束后，回答开始前
type ChatStart struct {
	RequestId   string `json:"requestId"`
	SessionId   string `json:"sessionId"`
	SessionType string `json:"sessionType"`
}

// ChatNotification 在问答流前推送消息
type ChatNotification struct {
	IsExceedQuota bool   `json:"isExceedQuota"`
	NextResetAt   int64  `json:"nextResetAt"`
	RequestId     string `json:"requestId"`
	SessionId     string `json:"sessionId"`
	SessionType   string `json:"sessionType"`
}

type ChatFinish struct {
	RequestId string `json:"requestId"`
	SessionId string `json:"sessionId"`
	Reason    string `json:"reason"`

	FullAnswer string `json:"fullAnswer"`
	//历史原因，定义了int类型
	StatusCode int `json:"statusCode"`
	// - intentionType 意图类型 chat | unittest | develop
	// - sessionType agent类型	chat | developer
	Extra map[string]any `json:"extra"`
}

type ChatFilterTimeout struct {
	RequestId  string `json:"requestId"`
	SessionId  string `json:"sessionId"`
	StatusCode int    `json:"statusCode"`
}

type ChatFilteringStatus struct {
	RequestId string `json:"requestId"`
	SessionId string `json:"sessionId"`
}

// RemoteChatReplyRequest The request struct that sends to remote server
type RemoteChatReplyRequest struct {
	RequestId string `json:"request_id"`
	SessionId string `json:"session_id"`
}

// RemoteChatLikeRequest The chat like request struct that sends to remote server
type RemoteChatLikeRequest struct {
	RequestId string `json:"request_id"`
	SessionId string `json:"session_id"`
	Like      int    `json:"like"`
}

// RemoteChatSystemEventRequest The chat system event request struct that sends to remote server
type RemoteChatSystemEventRequest struct {
	RequestId   string `json:"request_id"`
	SessionId   string `json:"session_id"`
	SystemEvent string `json:"system_event"`
}

// ChatProcessStep 问答处理阶段
type ChatProcessStep struct {
	SessionId   string `json:"sessionId"`
	RequestId   string `json:"requestId"`
	Step        string `json:"step"`
	Description string `json:"description"`
	Status      string `json:"status"`
	Result      any    `json:"result"`
	Message     string `json:"message"`
}

type ChatAskFeature struct {
	Id    string         `json:"id"`
	Name  string         `json:"name"`
	Type  string         `json:"type"`
	Extra map[string]any `json:"extra"`
}

type TestAgentProcessStep struct {
	SessionId    string `json:"sessionId"`
	RequestId    string `json:"requestId"`
	RequestSetId string `json:"requestSetId"`
	Step         string `json:"step"`
	Description  string `json:"description"`
	Status       string `json:"status"`
	Result       any    `json:"result"`
	Message      string `json:"message"`
}

const (
	// 根据描述生成代码
	DESCRIPTION_GENERATE_CODE string = "DESCRIPTION_GENERATE_CODE"
	// 生成测试用例
	GENERATE_TESTCASE string = "GENERATE_TESTCASE"
	// 解释代码
	EXPLAIN_CODE string = "EXPLAIN_CODE"
	// 根据代码生成注释
	CODE_GENERATE_COMMENT string = "CODE_GENERATE_COMMENT"
	// 优化代码
	OPTIMIZE_CODE string = "OPTIMIZE_CODE"
	// 回复任务
	REPLY_TASK string = "REPLY_TASK"
	// 重试任务
	RETRY_TASK string = "RETRY_TASK"
	// 行内编辑任务
	CHAT_TASK_INLINE_CHAT string = "INLINE_EDIT"

	// 自由输入任务类型
	FREE_INPUT string = "FREE_INPUT"
	// 生成终端命令
	TERMINAL_COMMAND_GENERATION string = "TERMINAL_COMMAND_GENERATION"
	// 终端解释命令
	TERMINAL_EXPLAIN_FIX string = "TERMINAL_EXPLAIN_FIX"
	ERROR_INFO_ASK       string = "ERROR_INFO_ASK"
	// 清理会话类型
	CLEAR_CONTEXT string = "SESSION_HISTORY_CLEAR"
	// 帮助
	HELP string = "HELP"

	// 代码文档搜索翻译
	DOCUMENT_TRANSLATE = "DOCUMENT_TRANSLATE"
	// 代码文档搜索智能生成
	SEARCH_TITLE_ASK = "SEARCH_TITLE_ASK"
	// Code Problem
	CODE_PROBLEM_SOLVE string = "CODE_PROBLEM_SOLVE"
	// AI DEVELOPER
	// 单测agent修复环境检查依赖项建议
	AI_DEVELOPER_TEST_AGENT_FIX_ENV string = "AI_DEVELOPER_TEST_AGENT_FIX_ENV"
	// COMMON_AGENT_TASK 这里的key只给reply策略用
	COMMON_AGENT_TASK string = "COMMON_AGENT_TASK"
	// CONTINUE_TASK agent场景下继续任务
	CONTINUE_TASK string = "CONTINUE_TASK"
	// 使用 Design 的 Quest 模式
	QUEST_DESIGN string = "QUEST_DESIGN"
	// 简单指令的 Quest 模式
	QUEST_PROCEED string = "QUEST_PROCEED"
	// quest模式的用户需求命名
	QUEST_REQUIREMENT_NAMEING string = "QUEST_REQUIREMENT_NAMEING"
	// quest模式的用户输入的prompt优化或总结
	QUEST_PROMPT_OPTIMIZE string = "QUEST_PROMPT_OPTIMIZE"
)

const (
	// ChatStepStart 问答预处理开始
	ChatStepStart = "step_start"

	// ChatStepCollectingWorkspaceTree 收集项目空间树
	ChatStepCollectingWorkspaceTree = "step_collecting_workspace_tree"

	// ChatStepRefineQuery 需求分析
	ChatStepRefineQuery = "step_refine_query"

	// ChatStepDeterminingCodebase 库内检索
	ChatStepDeterminingCodebase = "step_determining_codebase"

	// ChatStepDeterminingContextCodebase 库内检索(codebase上下文文案，与上面ChatStepDeterminingCodebase旧版workspace区分开来)
	ChatStepDeterminingContextCodebase = "step_determining_context_codebase"

	// ChatStepRetrieveRelevantInfo 知识库rag
	ChatStepRetrieveRelevantInfo = "step_retrieve_relevant_info"

	// ChatStepEnd 问答预处理结束
	ChatStepEnd = "step_end"

	// ChatStepStatusDoing 进行中
	ChatStepStatusDoing = "doing"

	// ChatStepStatusDone 已完成
	ChatStepStatusDone = "done"

	// ChatStepStatusError 异常
	ChatStepStatusError = "error"
)

const (
	// TaskDefinitionTypeSystem 系统预制任务
	TaskDefinitionTypeSystem = "system"

	// TaskDefinitionTypeCustom 用户自定义任务
	TaskDefinitionTypeCustom = "custom"
)

const (
	// SessionTypeChat 问答agent
	SessionTypeChat = "chat"

	// SessionTypeDeveloper AI Developer
	SessionTypeDeveloper = "developer"

	// Deprecated SessionTypeCoder 通用agent
	SessionTypeCoder = "coder"

	// SessionTypeAssistant 通用研发agent
	SessionTypeAssistant = "assistant"

	SessionTypeInline = "inline"

	// SessionTypeQuest Quest模式
	SessionTypeQuest = "quest"
)

const (
	// ChatAskService 服务端拼prompt的问答模型
	ChatAskService = "chat_ask"

	// AgentChatAskService agent直接问答模式
	AgentChatAskService = "agent_chat_ask"
)

const (
	// AIDeveloperIntentDetectChat 通用问答
	AIDeveloperIntentDetectChat = "common"

	// AIDeveloperIntentDetectUnittest AI Developer
	AIDeveloperIntentDetectUnittest = "unittest"

	// AIDeveloperIntentDetectDev dev场景
	AIDeveloperIntentDetectDev = "dev"

	// AIDeveloperIntentDetectUI2FeCode ui_2_fe_code场景
	AIDeveloperIntentDetectUI2FeCode = "ui_to_code"

	// AIDeveloperIntentDetectCommonAgent 通用agent场景
	AIDeveloperIntentDetectCommonAgent = "common_agent"
)

const (
	// AgentNameCommonChat 通用问答
	AgentNameCommonChat = "common"

	// AgentNameDev dev场景的agentId
	AgentNameDev = "dev_agent"

	//AgentNameUnittest unittest场景的agentId
	AgentNameUnittest = "test_agent"

	// AgentNameCommonDev 通用研发问答
	AgentNameCommonDev = "common_dev_agent"
)

const (
	AgentAIDeveloper = "ai_developer"

	// AgentTestAgent test_agent
	AgentTestAgent = "test_agent"

	// AgentIdAIChat ai_chat通用问答
	AgentIdAIChat = "ai_chat"
)

const (
	AgentTaskDiffGen = "diff_gen"

	AgentTaskDiffSolution = "diff_solution"

	AgentTaskVlDiffSolution = "vl_diff_solution"

	AgentTaskVlUItoCode = "ui_to_code"

	AgentTaskDiffApplyCopy = "diff_apply_copy"

	AgentTaskDiffApplyReApplyFailed = "diff_apply_reapply_failed"

	AgentTaskDiffApply = "diff_apply"

	AgentTaskDiffApplyWithChunk = "diff_apply_chunk"

	AgentTaskDiffApplyWithSearchReplace = "diff_apply_search_replace"

	AgentTaskTestPlan = "plan"

	AgentTaskTestGenerate = "testcase_generate"

	AgentTaskTestAgentPlanning = "test_agent_planning"

	AgentTaskIntentDetect = "intent_detect"

	// 多模态的意图识别
	AgentTaskMultimodalIntentDetect = "vl_intent_detect"

	AgentTaskChatSummary = "chat_summary"

	AgentTaskCommonChat = "common_chat"

	//多模态
	AgentTaskVlChat = "vl_chat"

	AgentTaskQuestionRefine   = "question_refine"
	AgentTaskMemoryGeneration = "memory_generation"
	AgentTaskMemoryRerank     = "memory_rerank"

	//问答场景下识别出的code任务
	AgentTaskChatCoder = "coder_chat"

	//问答场景下识别出的是重试任务
	AgentTaskChatRetry = "retry_chat"

	// wiki生成的通用任务
	AgentTaskDeepWikiGenerate = "deepwiki_generate"

	// wiki生成的目录任务
	AgentTaskDeepWikiCatalogGenerate = "deepwiki_catalog_generate"

	// wiki生成的内容任务
	AgentTaskDeepWikiContentGenerate = "deepwiki_content_generate"
)

// CustomCommandExtra 自定义指令的extra区域
type CustomCommandExtra struct {
	Identifier  string `json:"identifier"`
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
}

type CustomContextProviderExtra struct {
	Identifier    string                      `json:"identifier"`
	Name          string                      `json:"name"`
	ComponentType string                      `json:"componentType"`
	SelectedItem  ContextProviderSelectedItem `json:"selectedItem"`
	//解析出来的上下文列表
	ParsedContextItems []ParsedContextItem `json:"parsedContextItems"`
}

type ContextProviderExtraItem struct {
	Identifier string `json:"identifier"`
	Name       string `json:"name"`
	SourceType string `json:"sourceType"`
}

// CustomModelExtra 制定模型
type CustomModelExtra struct {
	Key string `json:"key"` //模型唯一key
}

type ContextProviderSelectedItem struct {
	Name       string `json:"name"`
	Identifier string `json:"identifier"`
	Query      string `json:"query"`
	// - fileType	 文件类型，文件类型/image
	// - imgUrl	图片时的URL
	Extra map[string]any `json:"extra"`
	//插件侧传入的上下文项内容
	Content string `json:"content"`
}

type ContextProviderQueryItem struct {
	Query string `json:"query"`
}

const (
	// ChatExtraKeyCommand 自定义指令
	ChatExtraKeyCommand = "command"

	// ChatExtraKeyContext 自定义上下文
	ChatExtraKeyContext = "context"

	// ChatExtraKeyTaskDefinitionType 指令类型，系统/自定义
	ChatExtraKeyTaskDefinitionType = "taskDefinitionType"

	// ChatExtraKeyModelConfig 模型配置
	ChatExtraKeyModelConfig = "modelConfig"

	// ChatExtraKeyQuestTaskInfo quest 任务信息
	ChatExtraKeyQuestTaskInfo = "taskInfo"
)

const (
	// ChatFinishStatusCodeSuccess 正常
	ChatFinishStatusCodeSuccess = 200

	ChatFinishStatusCodeAgentPanic = 500

	// ChatFinishStatusCodeTimeout 超时
	ChatFinishStatusCodeTimeout = 408

	// ChatFinishStatusCodeRetryError 超时
	ChatFinishStatusCodeRetryError = 409

	// ChatFinishStatusCodeFilterBlocked 安全过滤拦截了
	ChatFinishStatusCodeFilterBlocked = 601

	// ChatFinishStatusCodeCommandNotFound 指令不存在
	ChatFinishStatusCodeCommandNotFound = 602

	// ChatFinishStatusCodeCommandExecuteError 指令执行异常
	ChatFinishStatusCodeCommandExecuteError = 603

	// AgentQuotaExhausted agent quota超限
	AgentQuotaExhausted = 604

	// WithAppendThirdPartyReference 获取问答引用时，是否获取三方包中的引用
	WithAppendThirdPartyReference = true
)

const (
	// AcceptChunkIdSeparator 字符长度==缓存流式问答的内容的段数，将内容拼接并检测Wrapper并过滤后，才能发给插件端
	// 这里如此设计的原因是，单字符如果出现在文档中，会导致被过滤
	// 两字符连续在回答中出现的概率极低，同时截取回答段数适中，不会被用户察觉
	// 同时考虑到两个字符需要拦截两个回答的部分，不至于由于网络问题让端侧超时
	AcceptChunkIdSeparator = "▓"

	// RAGReportKeyEnableWorkspaceRag 因为不能引用chains.definition下的内容，所以这里重写一份一模一样的
	RAGReportKeyEnableWorkspaceRag = "key_enable_workspace_rag"

	// RAGReportKeyEnableKnowledgeRag 因为不能引用chains.definition下的内容，所以这里重写一份一模一样的
	RAGReportKeyEnableKnowledgeRag = "key_enable_knowledge_rag"

	// RAGReportKeyWorkspaceRequirementAnalysisTimeCostMs workspace中需求分析消耗的总时间
	RAGReportKeyWorkspaceRequirementAnalysisTimeCostMs = "workspace_requirement_analysis_time_cost_ms"

	// RAGReportKeyWorkspaceRequirementAnalysisTokenCost workspace中需求分析消耗的总token
	RAGReportKeyWorkspaceRequirementAnalysisTokenCost = "workspace_requirement_analysis_token_cost"

	// RAGReportKeyWorkspaceRetrievalTimeCostMs workspace中检索消耗的总时间
	RAGReportKeyWorkspaceRetrievalTimeCostMs = "workspace_retrieval_time_cost_ms"

	// RAGReportKeyWorkspaceRetrievalTokenCost workspace中检索消耗的总token
	RAGReportKeyWorkspaceRetrievalTokenCost = "workspace_retrieval_token_cost"

	// RAGReportKeyKnowledgeRequirementAnalysisTimeCostMs knowledge中需求分析消耗的总时间
	RAGReportKeyKnowledgeRequirementAnalysisTimeCostMs = "knowledge_requirement_analysis_time_cost_ms"

	// RAGReportKeyKnowledgeRequirementAnalysisTokenCost knowledge中需求分析消耗的总token
	RAGReportKeyKnowledgeRequirementAnalysisTokenCost = "knowledge_requirement_analysis_token_cost"

	// RAGReportKeyKnowledgeDocRetrievalTimeCostMs knowledge中文档检索消耗的总时间
	RAGReportKeyKnowledgeDocRetrievalTimeCostMs = "knowledge_doc_retrieval_time_cost_ms"

	// RAGReportKeyKnowledgeDocRetrievalTokenCost knowledge中文档检索消耗的总token
	RAGReportKeyKnowledgeDocRetrievalTokenCost = "knowledge_doc_retrieval_token_cost"

	// RAGReportKeyKnowledgeDocRetrievalChunkList knowledge中文档检索的chunk列表
	RAGReportKeyKnowledgeDocRetrievalChunkList = "knowledge_doc_retrieval_chunk_list"

	// IntentionDetectTimeCostMs 意图检测消耗的时间
	IntentionDetectTimeCostMs = "intention_detect_time_cost_ms"

	// CoderIntentionDetectResult coder代码生成意图检测结果
	CoderIntentionDetectResult = "coder_intention_detect_result"

	// UIToCodeIntentionDetectResult ui界面生成意图检测结果
	UIToCodeIntentionDetectResult = "ui_to_code_intention_detect_result"

	// RAGReportKeyChatStatistics Rag统计信息，用于从input向askParam传递
	RAGReportKeyChatStatistics = "rag_report_chat_statistics"

	// RAGReportKeyRetrievalDoc 文档信息，用于从input向askParam传递
	RAGReportKeyRetrievalDoc = "rag_report_retrieval_doc"

	// RAGReportEnableWorkspace 启用本地rag检索
	RAGReportEnableWorkspace = "workspace"

	// RAGReportEnableTeamDocs 启用知识库检索
	RAGReportEnableTeamDocs = "team_docs"
)

const (
	SessionModeChat = "chat"

	SessionModeEdit = "edit"

	SessionModeAgent = "agent"

	SessionModeDesign = "design"

	SessionModeLongRunning = "long_running"

	SessionModeLongRunningSub = "long_running_sub"

	SessionModeQuest = "quest"
)

type RagReportKnowledgeBaseDocChunk struct {
	KnowledgeBaseId string  `json:"kb_id"`
	ChunkId         string  `json:"chunk_id"`
	FileId          string  `json:"file_id"`
	RecallScore     float32 `json:"recall_score"`
	LLMAccept       bool    `json:"llm_accept"`
}

// ChatAskExtraParams 这个作为参数时需要传指针，因为AcceptChunkIds需要作为返回值
type ChatAskExtraParams struct {
	IsFiltered          bool
	IsBlocked           bool
	FilterStatus        string
	EnableWorkspaceRag  bool
	EnableKnowledgeRag  bool
	AcceptChunkIds      []int  // 返回值，这个值是用来返回的，初始化设置为""
	IntentionType       string //AI developer模式下意图识别类型
	ParsedUserQuery     string //解析了自定义上下文后的用户问题
	OriginalCode        string
	IsEnableProjectRule bool     //是否启用库内系统规则
	ImageUrls           []string `json:"imageUrls"`
	CommonAgentName     string   // agentName，主要用于非agent链路判断区分走AI Chat or AI Develop
}

type DiffBlock struct {
	Language   string
	FilePath   string
	Identifier string
	//完整代码
	CodeText string
}

type ChatSummaryRequest struct {
	ChatPrompt        string         `json:"chat_prompt"`
	RequestId         string         `json:"request_id"`
	Stream            bool           `json:"stream"`
	Parameters        map[string]any `json:"parameters"`
	SystemRoleContent string         `json:"system_role_content,omitempty"`
	Version           string         `json:"version,omitempty"` //会话本地化后为2
	AgentId           string         `json:"agent_id"`
	TaskId            string         `json:"task_id"`
	SessionType       string         `json:"session_type,omitempty"` //agent类型
}

// 处理单轮对话内的短期记忆，即agent自我迭代过程中的记忆
const (
	CreateMemoryAction = "create"
	UpdateMemoryAction = "update"
	DeleteMemoryAction = "delete"
)

const (
	// MemoryAutoSource 自动生成
	MemoryAutoSource = "auto"
	// MemoryUserSource 用户要求记住
	MemoryUserSource = "user"
	// MemoryInitSource 初始化记忆
	MemoryInitSource = "init"

	// MemoryGlobalScope 全局范围
	MemoryGlobalScope   = "global"
	MemoryGlobalScopeId = "Global"

	// MemoryWorkspaceScope 工程级范围的记忆
	MemoryWorkspaceScope = "workspace"

	// MemoryActiveState 活跃记忆
	MemoryActiveState = "active"
	// MemoryArchivedState 归档记忆
	MemoryArchivedState = "archived"
	// MemoryForgottenState 遗忘记忆
	MemoryForgottenState = "forgotten"

	// MemoryCategoryUnknown 未知类型
	MemoryCategoryUnknown = "unknown"
	// MemoryCategoryUserPrefer 用户偏好记忆
	MemoryCategoryUserPrefer = "user_prefer"
	// MemoryCategoryProjectInfo 项目信息记忆
	MemoryCategoryProjectInfo = "project_info"
	// MemoryCategoryProjectSpecification 项目规范记忆
	MemoryCategoryProjectSpecification = "project_specification"
	// MemoryCategoryCodeStandard 代码规范记忆, 废弃，仅用于对老记忆的遗忘
	MemoryCategoryCodeStandard = "code_standard"
	// MemoryCategoryDesignPattern 设计模式记忆, 废弃，仅用于对老记忆的遗忘
	MemoryCategoryDesignPattern = "design_pattern"
	// MemoryCategoryExperienceLessons 经验教训记忆
	MemoryCategoryExperienceLessons = "experience_lessons"
	// MemoryCategoryTaskReferenceFiles 任务相关文件记忆
	MemoryCategoryTaskReferenceFiles = "history_task_reference_files"
	// MemoryCategoryTaskWorkflow 任务执行步骤记忆
	MemoryCategoryTaskWorkflow = "history_task_workflow"

	// MemoryTimelinessForever 记忆失效性-永久
	MemoryTimelinessForever = "FOREVER"
	// MemoryTimelinessFixedTime 记忆失效性-固定时间
	MemoryTimelinessFixedTime = "FIXED_TIME"
	// MemoryTimelinessCanForget 记忆失效性-可遗忘
	MemoryTimelinessCanForget = "CAN_FORGET"
)

var (
	// MemoryCategoryMap 记忆类型及其重要度
	MemoryCategoryMap = map[string]float64{
		MemoryCategoryUnknown:              1.0,
		MemoryCategoryUserPrefer:           1.4,
		MemoryCategoryProjectSpecification: 1.3,
		MemoryCategoryProjectInfo:          1.0,
		MemoryCategoryCodeStandard:         1.2,
		MemoryCategoryDesignPattern:        1.0,
		MemoryCategoryExperienceLessons:    1.5,
		MemoryCategoryTaskReferenceFiles:   1.0,
		MemoryCategoryTaskWorkflow:         1.0,
	}
)

// MemoryCategoryScoreDiscountRate 记忆类型重要度衰减率
const MemoryCategoryScoreDiscountRate = 0.01

const (
	LongTermMemoryType  = "long_term_memory"
	ShortTermMemoryType = "short_term_memory"
)

// CreateMemoryRequest 创建记忆请求
type CreateMemoryRequest struct {
	Id       string `json:"id"`
	Title    string `json:"title"`
	Source   string `json:"source"`
	Scope    string `json:"scope"`
	Keywords string `json:"keywords"`
	Category string `json:"category"`
	Content  string `json:"content"`
	Action   string `json:"action"`
}

const (
	FinishReasonStop          = "stop"
	FinishReasonLength        = "length"
	FinishReasonContentFilter = "content_filter"
	FinishReasonToolCalls     = "tool_calls"
	FinishReasonErrorFinish   = "error_finish"
)

type ChatMessageStage struct {
	SessionId string `json:"sessionId"` // 会话id
	RequestId string `json:"requestId"` // 对话id
	MessageId string `json:"messageId"` // 消息唯一id
	Timestamp int64  `json:"timestamp"`
	Stage     string `json:"stage"` // 阶段 START-message流开始
}

const ChatNoticeNotifyTypeDisplayTask = "DisplayTask"
const ChatNoticeProcessTypeResume = "Resume"

type ChatNotifier struct {
	SessionId   string      `json:"sessionId"`   // 会话id
	RequestId   string      `json:"requestId"`   // 对话id
	NotifyType  string      `json:"notifyType"`  // 通知 类型
	ProcessType string      `json:"processType"` // 处理 类型
	Data        interface{} `json:"data"`        // 通知内容/接收的内容
}
