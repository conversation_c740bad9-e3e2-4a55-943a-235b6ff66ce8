package definition

type LogFeedbackParams struct {
	Feedback      string   `json:"feedback"`
	PluginLogFile string   `json:"pluginLogFile"`
	IdeLogDir     string   `json:"ideLogDir"` //可选，ide日志目录
	ImageUris     []string `json:"imageUris"`
}

type LogFeedbackBody struct {
	FeedbackId string `json:"feedbackId"`
}

type LogFeedbackResult struct {
	BaseResult
	Result LogFeedbackBody `json:"result"`
}

type FeedbackIssueParam struct {
	UserID        string `json:"user_id"`
	UserName      string `json:"user_name"`
	IDEType       string `json:"ide_type"`
	PluginVersion string `json:"plugin_version"`
	Description   string `json:"description"`
	ProcessState  int    `json:"process_state"`
	ProcessResult string `json:"process_result"`
	Processor     string `json:"processor"`
}

// WikiIndexParams wiki索引操作参数
type WikiIndexParams struct {
	WorkspacePath string `json:"workspacePath"`
}

// WikiStartGenerateParams wiki手动生成参数
type WikiStartGenerateParams struct {
}

// WikiPauseGenerateParams wiki手动暂停参数
type WikiPauseGenerateParams struct {
}

// WikiGetCatalogParams wiki获取目录参数
type WikiGetCatalogParams struct {
}
