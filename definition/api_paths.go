package definition

import (
	"fmt"
	"net/url"
	"strings"
)

// URLType URL类型枚举
type URLType int

const (
	URLTypeUnknown URLType = iota
	URLTypeCenter
	URLTypeInference
	URLTypeDataCodebase
	URLTypeDataRemoteAgent
)

// APIVersion API版本
type APIVersion string

const (
	APIVersionV1 APIVersion = "v1"
	APIVersionV2 APIVersion = "v2"
	APIVersionV3 APIVersion = "v3"
)

// APIModule API模块
type APIModule string

const (
	APIModuleSystem      APIModule = "system"      // 系统模块
	APIModuleAuth        APIModule = "auth"        // 认证模块
	APIModuleChat        APIModule = "chat"        // 聊天模块
	APIModuleCodebase    APIModule = "codebase"    // 代码库模块
	APIModuleUser        APIModule = "user"        // 用户模块
	APIModuleConfig      APIModule = "config"      // 配置模块
	APIModuleRemoteAgent APIModule = "remoteAgent" // 远程代理模块
	APIModuleExtension   APIModule = "extension"   // 扩展模块
	APIModuleMCP         APIModule = "mcp"         // MCP模块
	APIModuleFeedback    APIModule = "feedback"    // 反馈模块
	APIModuleSearch      APIModule = "search"      // 搜索模块
	APIModuleFile        APIModule = "file"        // 文件模块
	APIModuleImage       APIModule = "image"       // 图片模块
	APIModuleModel       APIModule = "model"       // 模型模块
	APIModuleWiki        APIModule = "wiki"        // Wiki模块
)

// APIPath API路径定义
type APIPath struct {
	Path         string     `json:"path"`         // 路径
	Version      APIVersion `json:"version"`      // API版本
	Module       APIModule  `json:"module"`       // 所属模块
	Description  string     `json:"description"`  // 描述
	URLType      URLType    `json:"urlType"`      // URL类型（中心节点、推理节点、数据节点等）
	SinceVersion string     `json:"sinceVersion"` // 端侧版本（从哪个版本开始引进）
}

// APIPathRegistry API路径注册表
type APIPathRegistry struct {
	paths map[string]*APIPath
}

// NewAPIPathRegistry 创建新的API路径注册表
func NewAPIPathRegistry() *APIPathRegistry {
	registry := &APIPathRegistry{
		paths: make(map[string]*APIPath),
	}
	registry.initDefaultPaths()
	return registry
}

// Register 注册API路径
func (r *APIPathRegistry) Register(name string, path *APIPath) {
	r.paths[name] = path
}

// Get 获取API路径
func (r *APIPathRegistry) Get(name string) *APIPath {
	return r.paths[name]
}

// GetPath 获取路径字符串
func (r *APIPathRegistry) GetPath(name string) string {
	if path := r.Get(name); path != nil {
		return path.Path
	}
	return ""
}

// GetByModule 根据模块获取所有路径
func (r *APIPathRegistry) GetByModule(module APIModule) []*APIPath {
	var result []*APIPath
	for _, path := range r.paths {
		if path.Module == module {
			result = append(result, path)
		}
	}
	return result
}

// GetByVersion 根据版本获取所有路径
func (r *APIPathRegistry) GetByVersion(version APIVersion) []*APIPath {
	var result []*APIPath
	for _, path := range r.paths {
		if path.Version == version {
			result = append(result, path)
		}
	}
	return result
}

// GetAllPaths 获取所有路径
func (r *APIPathRegistry) GetAllPaths() map[string]*APIPath {
	return r.paths
}

// GetMatchedPath 判断传入的URL是否被注册过
// 支持精确匹配和RESTful API模式匹配（如 /api/v2/tasks/123 匹配 /api/v2/tasks/%s）
// 会自动预处理URL，删除域名、查询参数等，只保留路径部分进行匹配
func (r *APIPathRegistry) GetMatchedPath(inputURL string) *APIPath {
	// 预处理URL，提取纯净的路径部分
	cleanPath := r.cleanURL(inputURL)

	// 1. 首先尝试精确匹配
	for _, path := range r.paths {
		if path.Path == cleanPath {
			return path
		}
	}

	// 2. 尝试RESTful API模式匹配
	for _, path := range r.paths {
		if r.matchPattern(path.Path, cleanPath) {
			return path
		}
	}

	return nil
}

// matchPattern 检查URL是否匹配RESTful模式
// pattern: 注册的路径模板（如 "/api/v2/tasks/%s"）
// url: 要检查的实际URL（如 "/api/v2/tasks/123"）
func (r *APIPathRegistry) matchPattern(pattern, url string) bool {
	// 如果模式不包含占位符，跳过
	if !strings.Contains(pattern, "%") {
		return false
	}

	// 分割路径段，去除前后的斜杠
	patternParts := strings.Split(strings.Trim(pattern, "/"), "/")
	urlParts := strings.Split(strings.Trim(url, "/"), "/")

	// 如果段数不匹配，直接返回false
	if len(patternParts) != len(urlParts) {
		return false
	}

	// 逐段比较
	for i, patternPart := range patternParts {
		urlPart := urlParts[i]

		// 如果是占位符（包含%），跳过检查，任何值都可以匹配
		if strings.Contains(patternPart, "%") {
			continue
		}

		// 如果不是占位符，必须精确匹配
		if patternPart != urlPart {
			return false
		}
	}

	return true
}

// cleanURL 清理URL，删除域名、查询参数、fragment等，只保留路径部分
func (r *APIPathRegistry) cleanURL(inputURL string) string {
	// 如果是空字符串，直接返回
	if inputURL == "" {
		return ""
	}

	// 尝试解析为完整URL（包含协议和域名）
	if parsedURL, err := url.Parse(inputURL); err == nil {
		// 如果成功解析且包含scheme（协议），说明是完整URL，提取Path部分
		if parsedURL.Scheme != "" {
			path := strings.TrimPrefix(parsedURL.Path, UrlPathAlgoPrefix)
			return path
		}
	}

	// 如果不是完整URL，尝试作为路径处理
	// 查找查询参数的位置
	if queryIndex := strings.Index(inputURL, "?"); queryIndex != -1 {
		inputURL = inputURL[:queryIndex]
	}

	// 查找fragment的位置
	if fragmentIndex := strings.Index(inputURL, "#"); fragmentIndex != -1 {
		inputURL = inputURL[:fragmentIndex]
	}

	inputURL = strings.TrimPrefix(inputURL, UrlPathAlgoPrefix)

	return inputURL
}

// FormatPath 格式化路径（支持参数替换）
func (r *APIPathRegistry) FormatPath(name string, args ...interface{}) string {
	path := r.GetPath(name)
	if path == "" {
		return ""
	}
	return fmt.Sprintf(path, args...)
}

// initDefaultPaths 初始化默认路径
func (r *APIPathRegistry) initDefaultPaths() {
	// 系统模块
	r.Register("UrlPathAlgoPrefix", &APIPath{
		Path:         UrlPathAlgoPrefix,
		Version:      "",
		Module:       APIModuleSystem,
		Description:  "URL中的前缀",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathReportHeartbeat", &APIPath{
		Path:         UrlPathReportHeartbeat,
		Version:      APIVersionV1,
		Module:       APIModuleSystem,
		Description:  "心跳上报接口",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathReportHeartbeatTelemetry", &APIPath{
		Path:         UrlPathReportHeartbeatTelemetry,
		Version:      APIVersionV2,
		Module:       APIModuleSystem,
		Description:  "心跳上报接口新版",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathReportTracking", &APIPath{
		Path:         UrlPathReportTracking,
		Version:      APIVersionV1,
		Module:       APIModuleSystem,
		Description:  "上报业务埋点接口",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathPing", &APIPath{
		Path:         UrlPathPing,
		Version:      APIVersionV1,
		Module:       APIModuleSystem,
		Description:  "端侧ping接口",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathServerMetaUriPath", &APIPath{
		Path:         UrlPathServerMetaUriPath,
		Version:      APIVersionV1,
		Module:       APIModuleSystem,
		Description:  "服务器版本信息",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	// 认证模块
	r.Register("UrlPathLogin", &APIPath{
		Path:         UrlPathLogin,
		Version:      "",
		Module:       APIModuleAuth,
		Description:  "插件登录首跳URL",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathAuthLogin", &APIPath{
		Path:         UrlPathAuthLogin,
		Version:      "",
		Module:       APIModuleAuth,
		Description:  "服务端登录URL",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathLogout", &APIPath{
		Path:         UrlPathLogout,
		Version:      "",
		Module:       APIModuleAuth,
		Description:  "灵码插件登出URL",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathLogoutReport", &APIPath{
		Path:         UrlPathLogoutReport,
		Version:      APIVersionV3,
		Module:       APIModuleAuth,
		Description:  "登录URL",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathLoginReport", &APIPath{
		Path:         UrlPathLoginReport,
		Version:      APIVersionV3,
		Module:       APIModuleAuth,
		Description:  "登录URL",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathCustomLoginAuth", &APIPath{
		Path:        UrlPathCustomLoginAuth,
		Version:     APIVersionV2,
		Module:      APIModuleAuth,
		Description: "自定义登录认证",
		URLType:     URLTypeCenter,
	})

	r.Register("UrlRefreshToken", &APIPath{
		Path:         UrlRefreshToken,
		Version:      APIVersionV3,
		Module:       APIModuleAuth,
		Description:  "刷新令牌",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathModelServiceInvoke", &APIPath{
		Path:         UrlPathModelServiceInvoke,
		Version:      APIVersionV1,
		Module:       APIModuleModel,
		Description:  "通用大模型调用service",
		URLType:      URLTypeInference,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathRerankServiceRerank", &APIPath{
		Path:         UrlPathRerankServiceInvoke,
		Version:      APIVersionV2,
		Module:       APIModuleModel,
		Description:  "rerank接口",
		URLType:      URLTypeInference,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlRequestRemoteToken", &APIPath{
		Path:         UrlRequestRemoteToken,
		Version:      APIVersionV3,
		Module:       APIModuleAuth,
		Description:  "获取远程令牌",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathPollDeviceTokenUrl", &APIPath{
		Path:         UrlPathPollDeviceTokenUrl,
		Version:      APIVersionV3,
		Module:       APIModuleAuth,
		Description:  "轮询设备令牌",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	// 聊天模块
	r.Register("UrlPathDeleteChatByRequestId", &APIPath{
		Path:         UrlPathDeleteChatByRequestId,
		Version:      APIVersionV2,
		Module:       APIModuleChat,
		Description:  "根据requestId删除chat record",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathAgentQueueStatus", &APIPath{
		Path:         UrlPathAgentQueueStatus,
		Version:      APIVersionV2,
		Module:       APIModuleChat,
		Description:  "Agent队列状态",
		URLType:      URLTypeInference,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathAskFinishUrl", &APIPath{
		Path:         UrlPathAskFinishUrl,
		Version:      APIVersionV2,
		Module:       APIModuleChat,
		Description:  "问答完成",
		URLType:      URLTypeInference,
		SinceVersion: CosyVersion_3_0_0,
	})

	// 代码库模块
	r.Register("UrlPathCodebaseEmbedding", &APIPath{
		Path:         UrlPathCodebaseEmbeddingLimitEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleCodebase,
		Description:  "Codebase embedding接口（企业标准版、企业个人版、个人版）",
		URLType:      URLTypeDataCodebase,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathCodebaseEmbeddingK2", &APIPath{
		Path:         UrlPathCodebaseEmbeddingUnlimitEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleCodebase,
		Description:  "Codebase embedding接口（专属版、专有云）",
		URLType:      URLTypeDataCodebase,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathCodebaseFileCheckStatus", &APIPath{
		Path:         UrlPathCodebaseFileCheckStatus,
		Version:      APIVersionV2,
		Module:       APIModuleCodebase,
		Description:  "Codebase文件状态检查",
		URLType:      URLTypeDataCodebase,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathCodebaseFileCheckStatusV2", &APIPath{
		Path:         UrlPathCodebaseFileCheckStatusV2,
		Version:      APIVersionV2,
		Module:       APIModuleCodebase,
		Description:  "Codebase文件状态检查V2",
		URLType:      URLTypeDataCodebase,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathCodebaseFileUpload", &APIPath{
		Path:         UrlPathCodebaseFileUpload,
		Version:      APIVersionV2,
		Module:       APIModuleCodebase,
		Description:  "Codebase文件上传",
		URLType:      URLTypeDataCodebase,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathCodebaseFileGetChunks", &APIPath{
		Path:         UrlPathCodebaseFileGetChunks,
		Version:      APIVersionV2,
		Module:       APIModuleCodebase,
		Description:  "Codebase文件获取块",
		URLType:      URLTypeDataCodebase,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathCodebaseSyncInitCodebase", &APIPath{
		Path:         UrlPathCodebaseSyncInitCodebase,
		Version:      APIVersionV2,
		Module:       APIModuleCodebase,
		Description:  "Codebase同步初始化",
		URLType:      URLTypeDataCodebase,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathCodebaseSyncGetMerkleNode", &APIPath{
		Path:         UrlPathCodebaseSyncGetMerkleNode,
		Version:      APIVersionV2,
		Module:       APIModuleCodebase,
		Description:  "Codebase同步获取Merkle节点",
		URLType:      URLTypeDataCodebase,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathCodebaseSyncBatchGetMerkleNodes", &APIPath{
		Path:         UrlPathCodebaseSyncBatchGetMerkleNodes,
		Version:      APIVersionV2,
		Module:       APIModuleCodebase,
		Description:  "Codebase同步批量获取Merkle节点",
		URLType:      URLTypeDataCodebase,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathCodebaseSyncUpdateMerkleNodes", &APIPath{
		Path:         UrlPathCodebaseSyncUpdateMerkleNodes,
		Version:      APIVersionV2,
		Module:       APIModuleCodebase,
		Description:  "Codebase同步更新Merkle节点",
		URLType:      URLTypeDataCodebase,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathCodebaseSearchRetrieveChunks", &APIPath{
		Path:         UrlPathCodebaseSearchRetrieveChunks,
		Version:      APIVersionV2,
		Module:       APIModuleCodebase,
		Description:  "Codebase向量检索接口",
		URLType:      URLTypeDataCodebase,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathCodebaseOperationDeleteCodebase", &APIPath{
		Path:         UrlPathCodebaseOperationDeleteCodebase,
		Version:      APIVersionV2,
		Module:       APIModuleCodebase,
		Description:  "Codebase删除接口",
		URLType:      URLTypeDataCodebase,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathCodebasePing", &APIPath{
		Path:         UrlPathCodebasePing,
		Version:      APIVersionV2,
		Module:       APIModuleCodebase,
		Description:  "Codebase健康检查接口",
		URLType:      URLTypeDataCodebase,
		SinceVersion: CosyVersion_3_0_0,
	})

	// 用户模块
	r.Register("UrlPathGrantAuthInfo", &APIPath{
		Path:         UrlPathGrantAuthInfo,
		Version:      APIVersionV3,
		Module:       APIModuleUser,
		Description:  "查询有权限的企业列表",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathQueryAuthStatus", &APIPath{
		Path:         UrlPathQueryAuthStatus,
		Version:      APIVersionV3,
		Module:       APIModuleUser,
		Description:  "查询授权状态",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathUserDataNodeEndpoint", &APIPath{
		Path:         UrlPathUserDataNodeEndpoint,
		Version:      APIVersionV3,
		Module:       APIModuleUser,
		Description:  "获取用户数据节点的endpoint",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	// 配置模块
	r.Register("UrlPathGetDataPolicy", &APIPath{
		Path:         UrlPathGetDataPolicy,
		Version:      APIVersionV2,
		Module:       APIModuleConfig,
		Description:  "查询隐私协议签署状态",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathUpdateDataPolicy", &APIPath{
		Path:         UrlPathUpdateDataPolicy,
		Version:      APIVersionV2,
		Module:       APIModuleConfig,
		Description:  "更新隐私协议签署状态",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	// 远程代理模块
	r.Register("AllocateWorkspaceEndpoint", &APIPath{
		Path:         AllocateWorkspaceEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "分配工作空间",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("CreateTaskEndpoint", &APIPath{
		Path:         CreateTaskEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "创建任务",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UpdateTaskEndpoint", &APIPath{
		Path:         UpdateTaskEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "更新任务",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("GetTaskEndpoint", &APIPath{
		Path:         GetTaskEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "获取任务",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("ListTasksEndpoint", &APIPath{
		Path:         ListTasksEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "列出任务",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("ListAllTasksEndpoint", &APIPath{
		Path:         ListAllTasksEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "列出所有任务",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("WatchTaskEventsEndpoint", &APIPath{
		Path:         WatchTaskEventsEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "监听任务事件",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UpdateTaskStatusEndpoint", &APIPath{
		Path:         UpdateTaskStatusEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "更新任务状态",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("DeleteTaskEndpoint", &APIPath{
		Path:         DeleteTaskEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "删除任务",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("CancelTaskEndpoint", &APIPath{
		Path:         CancelTaskEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "取消任务",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("ResumeTaskEndpoint", &APIPath{
		Path:         ResumeTaskEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "激活任务",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("ExecuteTaskEndpoint", &APIPath{
		Path:         ExecuteTaskEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "执行任务",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("GetUserChatTaskStatsEndpoint", &APIPath{
		Path:         GetUserChatTaskStatsEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "获取用户聊天任务统计",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("WatchUserEventsEndpoint", &APIPath{
		Path:         WatchUserEventsEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "监听用户事件",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("GetTaskBootLogEndpoint", &APIPath{
		Path:         GetTaskBootLogEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "获取任务启动日志",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("GetUserQuotasEndpoint", &APIPath{
		Path:         GetUserQuotasEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "用户任务额度API",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("TaskFileChangeEndpoint", &APIPath{
		Path:         TaskFileChangeEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "任务文件变更API",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("ActionFlowEndpoint", &APIPath{
		Path:         ActionFlowEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "任务action flow端点",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("DesignEndpoint", &APIPath{
		Path:         DesignEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "任务设计端点",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("MessageEndpoint", &APIPath{
		Path:         MessageEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "任务消息API",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("ReportEndpoint", &APIPath{
		Path:         ReportEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "任务报告API",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("ExecutionSessionEndpoint", &APIPath{
		Path:         ExecutionSessionEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "执行远程会话API",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("RemoteSessionRecordEndpoint", &APIPath{
		Path:         RemoteSessionRecordEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "远程会话records API",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("SessionMessagesEndpoint", &APIPath{
		Path:         SessionMessagesEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleRemoteAgent,
		Description:  "会话消息API",
		URLType:      URLTypeDataRemoteAgent,
		SinceVersion: CosyVersion_3_0_0,
	})

	// 扩展模块
	r.Register("UrlPathScriptDownloadApi", &APIPath{
		Path:         UrlPathScriptDownloadApi,
		Version:      APIVersionV2,
		Module:       APIModuleExtension,
		Description:  "脚本下载API",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathExtensionNodeVmDownload", &APIPath{
		Path:         UrlPathExtensionNodeVmDownload,
		Version:      APIVersionV1,
		Module:       APIModuleExtension,
		Description:  "扩展节点VM下载",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathPullServerConfigUrl", &APIPath{
		Path:         UrlPathPullServerConfigUrl,
		Version:      APIVersionV2,
		Module:       APIModuleExtension,
		Description:  "拉取服务器配置",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	// MCP模块
	r.Register("UrlPathMcpListRecommendUrl", &APIPath{
		Path:         UrlPathMcpListRecommendUrl,
		Version:      APIVersionV1,
		Module:       APIModuleMCP,
		Description:  "MCP推荐服务器列表",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathMcpSearchServerUrl", &APIPath{
		Path:         UrlPathMcpSearchServerUrl,
		Version:      APIVersionV1,
		Module:       APIModuleMCP,
		Description:  "MCP搜索服务器",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathMcpGetServerInfo", &APIPath{
		Path:         UrlPathMcpGetServerInfo,
		Version:      APIVersionV1,
		Module:       APIModuleMCP,
		Description:  "MCP获取服务器信息",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	// 反馈模块
	r.Register("UrlPathFeedBackApiUrl", &APIPath{
		Path:         UrlPathFeedBackApiUrl,
		Version:      APIVersionV2,
		Module:       APIModuleFeedback,
		Description:  "反馈API",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	// 搜索模块
	r.Register("UrlPathToolWebSearchUrl", &APIPath{
		Path:         UrlPathToolWebSearchUrl,
		Version:      APIVersionV1,
		Module:       APIModuleSearch,
		Description:  "统一搜索",
		URLType:      URLTypeInference,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathSerpSearchUrl", &APIPath{
		Path:         UrlPathSerpSearchUrl,
		Version:      APIVersionV1,
		Module:       APIModuleSearch,
		Description:  "SerpApi搜索",
		URLType:      URLTypeInference,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathRefineQueryEndpoint", &APIPath{
		Path:         UrlPathRefineQueryEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleSearch,
		Description:  "优化查询",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathQueryCodeEndpoint", &APIPath{
		Path:         UrlPathQueryCodeEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleSearch,
		Description:  "查询代码",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathQueryDocEndpoint", &APIPath{
		Path:         UrlPathQueryDocEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleSearch,
		Description:  "查询文档扩展",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathQueryDocListURI", &APIPath{
		Path:         UrlPathQueryDocListURI,
		Version:      APIVersionV2,
		Module:       APIModuleSearch,
		Description:  "查询知识库列表",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	// 文件模块
	r.Register("UrlPathUploadFileEndpoint", &APIPath{
		Path:         UrlPathUploadFileEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleFile,
		Description:  "文件上传",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	// 图片模块
	r.Register("UrlPathUploadImageEndpoint", &APIPath{
		Path:         UrlPathUploadImageEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleImage,
		Description:  "图片上传",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	// 模型模块
	r.Register("UrlPathModelQueryUrl", &APIPath{
		Path:         UrlPathModelQueryUrl,
		Version:      APIVersionV2,
		Module:       APIModuleModel,
		Description:  "模型查询",
		URLType:      URLTypeInference,
		SinceVersion: CosyVersion_3_0_0,
	})

	// Wiki模块
	r.Register("UrlPathWikiLineUpUrl", &APIPath{
		Path:         UrlPathWikiLineUpUrl,
		Version:      APIVersionV2,
		Module:       APIModuleWiki,
		Description:  "Wiki队列",
		URLType:      URLTypeInference,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathWikiReleaseLineUpUrl", &APIPath{
		Path:         UrlPathWikiReleaseLineUpUrl,
		Version:      APIVersionV2,
		Module:       APIModuleWiki,
		Description:  "Wiki完成",
		URLType:      URLTypeInference,
		SinceVersion: CosyVersion_3_0_0,
	})

	// 其他模块
	r.Register("UrlPathQueryRegionEndpoints", &APIPath{
		Path:         UrlPathQueryRegionEndpoints,
		Version:      APIVersionV2,
		Module:       APIModuleSystem,
		Description:  "查询region endpoint",
		URLType:      URLTypeCenter,
		SinceVersion: CosyVersion_3_0_0,
	})

	r.Register("UrlPathQuotaEndpoint", &APIPath{
		Path:         UrlPathQuotaEndpoint,
		Version:      APIVersionV2,
		Module:       APIModuleSystem,
		Description:  "配额端点",
		URLType:      URLTypeInference,
		SinceVersion: CosyVersion_3_0_0,
	})
}

// 全局API路径注册表实例
var GlobalAPIPathRegistry = NewAPIPathRegistry()

// 为了保持向后兼容性，提供原有的常量定义
// 这些常量现在从注册表中获取值

// 系统级别
const UrlPathAlgoPrefix = "/algo"
const UrlPathReportHeartbeat = "/api/v1/heartbeat"
const UrlPathReportHeartbeatTelemetry = "/telemetry/api/v1/heartbeat"
const UrlPathReportTracking = "/api/v1/tracking"
const UrlPathPing = "/api/v1/ping"
const UrlPathServerMetaUriPath = "/api/v1/server/version"

// 登录授权相关
// 插件登录首跳URL
const UrlPathLogin = "/device/selectAccounts"

// 服务端登录URL
const UrlPathAuthLogin = "/users/login"

// 灵码插件登出URL
const UrlPathLogout = "/users/logout"
const UrlPathLogoutReport = "/api/v3/user/logout"
const UrlPathLoginReport = "/api/v3/user/login"
const UrlPathCustomLoginAuth = "/api/v2/user/customLoginAuth"
const UrlRefreshToken = "/api/v3/user/refresh_token"
const UrlRequestRemoteToken = "/api/v3/user/remoteToken"

// 大模型通用service invoke path
const UrlPathModelServiceInvoke = "/api/v2/service/pro/%s/%s"
const UrlPathRerankServiceInvoke = "/api/v2/service/pro/rerank"

// agent和问答
const UrlPathDeleteChatByRequestId = "/api/v2/service/chat/delete/record:byRequestId"
const UrlPathAgentQueueStatus = "/api/v2/service/ask/queue/status"
const UrlPathAskFinishUrl = "/api/v2/service/ask/finish"
const UrlPathQuotaEndpoint = "/api/v2/service/agent/quota"
const UrlPathRefineQueryEndpoint = "/api/v2/service/refineQuery"
const UrlPathToolWebSearchUrl = "/api/v1/webSearch/unifiedSearch"
const UrlPathSerpSearchUrl = "/api/v1/webSearch/serpApiSearch"

// mcp
const UrlPathMcpListRecommendUrl = "/api/v1/mcp/modelscope/server/recommend"
const UrlPathMcpSearchServerUrl = "/api/v1/mcp/modelscope/server/search"
const UrlPathMcpGetServerInfo = "/api/v1/mcp/modelscope/server/info?serverId=%s"

// 多region ha
const UrlPathQueryRegionEndpoints = "/api/v2/service/region/endpoints"
const UrlPathUserDataNodeEndpoint = "/api/v3/user/region"

// device token
const UrlPathPollDeviceTokenUrl = "/api/v3/user/oauth2/deviceToken/poll"

const UrlPathCodebaseFileCheckStatus = "/api/v2/service/codebase/file/checkStatus"
const UrlPathCodebaseFileCheckStatusV2 = "/api/v2/service/codebase/file/checkStatusV2"
const UrlPathCodebaseFileUpload = "/api/v2/service/codebase/file/upload"
const UrlPathCodebaseFileGetChunks = "/api/v2/service/codebase/file/getChunks"
const UrlPathCodebaseSyncInitCodebase = "/api/v2/service/codebase/sync/initCodebase"
const UrlPathCodebaseSyncGetMerkleNode = "/api/v2/service/codebase/sync/getMerkleNode"
const UrlPathCodebaseSyncBatchGetMerkleNodes = "/api/v2/service/codebase/sync/batchGetMerkleNodes"
const UrlPathCodebaseSyncUpdateMerkleNodes = "/api/v2/service/codebase/sync/updateMerkleNodes"
const UrlPathCodebaseSearchRetrieveChunks = "/api/v2/service/codebase/search/retrieveChunks"
const UrlPathCodebaseOperationDeleteCodebase = "/api/v2/service/codebase/operation/deleteCodebase"
const UrlPathCodebasePing = "/api/v2/service/codebase/ping"

// Codebase embedding 接口
// 这是专属的 codebase embedding 服务
// NOTE: 对于用户量非常大的场景，需要单独部署一个 embedding 服务，否则会影响其他服务
// 企业标准版、企业个人版、个人版等均使用这个 endpoint
// 限流，使用令牌桶
const UrlPathCodebaseEmbeddingLimitEndpoint = "/api/v2/service/codebase/embedding"

// 对于用户量不是特别大的场景，使用这个 endpoint，专属版、专有云使用该 endpoint
// 不限流，不使用令牌桶
const UrlPathCodebaseEmbeddingUnlimitEndpoint = "/api/v2/service/codebase/embedding_k2"

// 灵码知识库相关
const UrlPathQueryDocEndpoint = "/api/v2/service/queryDocExt"
const UrlPathQueryCodeEndpoint = "/api/v2/service/queryCode"
const UrlPathQueryDocListURI = "/api/v2/service/queryKBList"

// 用户相关
const UrlPathGrantAuthInfo = "/api/v3/user/grantAuthInfos"
const UrlPathQueryAuthStatus = "/api/v3/user/status"

// 配置相关
const UrlPathGetDataPolicy = "/api/v2/config/getDataPolicy"
const UrlPathUpdateDataPolicy = "/api/v2/config/updateDataPolicy"

// 远程代理相关
const AllocateWorkspaceEndpoint = "/api/v2/remoteAgent/qoder/workspaces/allocate"
const CreateTaskEndpoint = "/api/v2/remoteAgent/qoder/tasks"
const UpdateTaskEndpoint = "/api/v2/remoteAgent/qoder/tasks/%s"
const GetTaskEndpoint = "/api/v2/remoteAgent/qoder/tasks/%s"
const ListTasksEndpoint = "/api/v2/remoteAgent/qoder/tasks"
const ListAllTasksEndpoint = "/api/v2/remoteAgent/qoder/tasks"
const WatchTaskEventsEndpoint = "/api/v2/remoteAgent/sse/qoder/tasks/%s/events/stream"
const UpdateTaskStatusEndpoint = "/api/v2/remoteAgent/qoder/tasks/%s/status"
const DeleteTaskEndpoint = "/api/v2/remoteAgent/qoder/tasks/%s"
const CancelTaskEndpoint = "/api/v2/remoteAgent/qoder/tasks/%s/cancel"
const ResumeTaskEndpoint = "/api/v2/remoteAgent/qoder/tasks/%s/resume"
const ExecuteTaskEndpoint = "/api/v2/remoteAgent/qoder/tasks/%s/executions"
const GetUserChatTaskStatsEndpoint = "/api/v2/remoteAgent/qoder/tasks/user-stats"
const WatchUserEventsEndpoint = "/api/v2/remoteAgent/sse/qoder/user/events/stream"
const GetTaskBootLogEndpoint = "/api/v2/remoteAgent/qoder/tasks/%s/executions/boot-log"
const GetUserQuotasEndpoint = "/api/v2/remoteAgent/qoder/quotas"
const TaskFileChangeEndpoint = "/api/v2/remoteAgent/qoder/tasks/%s/file-changes"
const ActionFlowEndpoint = "/api/v2/remoteAgent/qoder/tasks/%s/action-flow"
const DesignEndpoint = "/api/v2/remoteAgent/qoder/tasks/%s/design"
const MessageEndpoint = "/api/v2/remoteAgent/qoder/tasks/%s/messages"
const ReportEndpoint = "/api/v2/remoteAgent/qoder/tasks/%s/reports"
const ExecutionSessionEndpoint = "/api/v2/remoteAgent/qoder/tasks/%s/%s/sessions"
const RemoteSessionRecordEndpoint = "/api/v2/remoteAgent/qoder/sessions/%s/records"
const SessionMessagesEndpoint = "/api/v2/remoteAgent/qoder/sessions/%s/messages"

// 扩展执行引擎
const UrlPathExtensionNodeVmDownload = "/api/v1/extension/vm/download"
const UrlPathPullServerConfigUrl = "/api/v2/extension/config/pull"
const UrlPathScriptDownloadApi = "/api/v2/extension/script/download"

// deepwiki相关
const UrlPathWikiLineUpUrl = "/api/v2/service/wiki/queue"
const UrlPathWikiReleaseLineUpUrl = "/api/v2/service/wiki/finish"

// 其他API
const UrlPathFeedBackApiUrl = "/api/v2/feedback/dislike"
const UrlPathModelQueryUrl = "/api/v2/model/list"
const UrlPathUploadImageEndpoint = "/api/v2/image/upload"
const UrlPathUploadFileEndpoint = "/api/v2/file/diagnose/upload"
