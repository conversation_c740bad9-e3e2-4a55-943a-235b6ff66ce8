package definition

// 参考格式
//
//		{
//	  "repo": "repo3",
//	  "ra": "qts2"
//	}
type UserDataRegion struct {
	UserId string `json:"userId"`
	Repo   string `json:"repo"` //codebase region
	Ra     string `json:"ra"`   //remote agent region
}

func (u *UserDataRegion) IsEmpty() bool {
	return u.Repo == "" && u.Ra == ""
}

// 部分为空
func (u *UserDataRegion) IsPartialEmpty() bool {
	return u.Repo != "" && u.Ra == "" || u.Repo == "" && u.Ra != ""
}

type UserRegionResponse struct {
	BaseResult
	Result bool `json:"result"`
}

type UpdateUserRegionResponse struct {
	BaseResult
	Result bool `json:"result"`
}
