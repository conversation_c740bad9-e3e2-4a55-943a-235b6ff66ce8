package config

import (
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/util"
	_ "embed"
	"encoding/json"
	"io"
	"net/url"
	"os"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/viper"
)

//go:embed env/env_cn.json
var cnEnvConfigJSON string

//go:embed env/env_intl.json
var intlEnvConfigJSON string

//go:embed env/env_qoder.json
var qoderEnvConfigJSON string

// 国内版配置
var CnEnvConfig EnvConfig

// 国际版配置
var IntlEnvConfig EnvConfig

// qoder版配置
var QoderEnvConfig EnvConfig

func initAuthExtConfig() {
	if !OnPremiseMode { // 非专有云模式不初始化
		return
	}
	path := util.GetAuthExtCMDPath()
	if len(path) == 0 {
		AuthExtConf.Enabled = false
	} else {
		AuthExtConf.Enabled = util.PathExists(path)
	}
}

func InitLocalConfig() {
	// 再读取配置文件
	initLocalModelConfig()

	initEmbeddedConfig()

	//加载环境配置文件
	loadEnvConfig()

	//全局配置
	initGlobalConfig()

	//截断配置
	initTruncateConfig()

	//处理remote配置
	ConfigureRemoteConfig()

	initAuthExtConfig()

}

func initLocalModelConfig() {
	// Load local config
	cosyCacheDir := util.GetCosyCachePath()
	configFileName := "config"
	viper.SetConfigName(configFileName)
	viper.SetConfigType("json")
	viper.AddConfigPath(cosyCacheDir)

	// Find and read the config file
	err := viper.ReadInConfig()
	if err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			// If no config file here, create a new one
			_ = os.MkdirAll(cosyCacheDir, 0755)
			err := os.WriteFile(filepath.Join(cosyCacheDir, definition.ConfigFile), []byte(""), 0644)
			if err != nil {
				log.Errorf("Cannot create local model config: %v", err)
			}

			// Create new viper config instance
			newViper := viper.New()
			newViper.SetConfigName(configFileName)
			newViper.SetConfigType("json")
			newViper.AddConfigPath(cosyCacheDir)
			// Write config
			writeErr := newViper.WriteConfig()
			if writeErr != nil {
				log.Errorf("Cannot save current local config: %v", err)
			}
		}
	}
	// Set current env
	viper.Set("env", "local")
	// Set default beam search config
	viper.SetDefault("beam_search_config", definition.BeamConfig{
		BeamSize:       3,
		TopK:           3,
		TokenThreshold: 0.1,
		MaxLength:      10,
	})
	// Load local beam search config
	var beamConfig ViperBeamConfig
	if err := viper.UnmarshalKey("beam_search_config", &beamConfig); err != nil || beamConfig.BeamSize == 0 {
		beamConfig = ViperBeamConfig{
			BeamSize:       3,
			TopK:           3,
			TokenThreshold: 0.1,
			MaxLength:      10,
		}
	}
	beamConfig = checkInferenceConfig(beamConfig)
	global.LocalBeamConfig = definition.BeamConfig{
		BeamSize:       beamConfig.BeamSize,
		TopK:           beamConfig.TopK,
		TokenThreshold: beamConfig.TokenThreshold,
		MaxLength:      beamConfig.MaxLength,
	}
}

// 加载全局配置
func initGlobalConfig() {
	var debug bool
	if err := viper.UnmarshalKey("debug", &debug); err == nil {
		if global.IsReleaseVersion() {
			global.DebugMode = false
		} else {
			global.DebugMode = debug
		}
	}
	if IsRemoteAgentMode() {
		global.DebugMode = true
	}
	var regionEnv string
	if err := viper.UnmarshalKey("region_env", &regionEnv); err == nil {
		if regionEnv == "" {
			RegionEnv = definition.RegionCn
		} else {
			RegionEnv = regionEnv
		}
	}

	var remoteConfig RemoteConfig
	if err := viper.UnmarshalKey("remote_config", &remoteConfig); err == nil {
		// Support overwrite configuration, for debugging
		copyFields("remote_config", &remoteConfig, &GlobalModelRemoteConfig)
	}

	var globalConfig ModelConfig
	if err := viper.UnmarshalKey("global_config", &globalConfig); err == nil {
		// Support overwrite configuration, for debugging
		copyFields("global_config", &globalConfig, &GlobalModelConfig)
	}

	var extensionConfigPullInterval int
	if err := viper.UnmarshalKey("extension_config_pull_interval", &extensionConfigPullInterval); err == nil {
		if extensionConfigPullInterval != 0 {
			global.ExtensionConfigPullInterval = extensionConfigPullInterval
		}
	}

	var regionConfig RegionConfig
	if err := viper.UnmarshalKey("region_config", &regionConfig); err == nil {
		// Support overwrite configuration, for debugging
		GlobalRegionConfig = regionConfig
	}
}

// 初始化截断配置
func initTruncateConfig() {
	if global.IsQoderProduct() {
		//先用公有云配置吧
		GlobalTruncateConfig.ChunkLimits = DefaultTruncateConfig.ChunkLimits

		return
	}

	GlobalTruncateConfig.ChunkLimits = DefaultTruncateConfig.ChunkLimits
	if OnPremiseMode {
		// 专有云支持外部配置化
		if len(GlobalTruncateConfig.ChunkLimits) > 0 {
			for key, val := range GlobalTruncateConfig.ChunkLimits {
				if key == "" || val <= 0 {
					continue
				}
				GlobalTruncateConfig.ChunkLimits[key] = val
			}
		}
	}
}

func copyFields(parentKey string, from, to interface{}) {
	fromValue := reflect.ValueOf(from).Elem()
	toValue := reflect.ValueOf(to).Elem()
	for i := 0; i < fromValue.NumField(); i++ {
		fieldName := fromValue.Type().Field(i).Name
		fieldTag := fromValue.Type().Field(i).Tag.Get("mapstructure")
		toFieldValue := toValue.FieldByName(fieldName)
		if viper.IsSet(parentKey+"."+fieldTag) && toFieldValue.IsValid() && toFieldValue.CanSet() {
			switch fromValue.Type().Field(i).Type.Name() {
			case "string":
				toFieldValue.SetString(fromValue.Field(i).String())
			case "bool":
				toFieldValue.SetBool(fromValue.Field(i).Bool())
			case "int":
				toFieldValue.SetInt(fromValue.Field(i).Int())
			default:
				field := fromValue.Type().Field(i)
				go func() {
					// 等待logger初始化完成
					time.Sleep(1 * time.Second)
					log.Debugf("Skipped unknown local config item: %s [%s]", field.Name, field.Type.Name())
				}()
			}
		}
	}
}

func checkInferenceConfig(localConfig ViperBeamConfig) ViperBeamConfig {
	if localConfig.BeamSize < 1 || localConfig.BeamSize > 5 {
		localConfig.BeamSize = 3
	}
	if localConfig.TopK < 1 || localConfig.TopK > 5 {
		localConfig.TopK = 3
	}
	if localConfig.TokenThreshold < 0.001 || localConfig.TokenThreshold > 0.999 {
		localConfig.TokenThreshold = 0.1
	}
	if localConfig.MaxLength < 5 || localConfig.MaxLength > 20 {
		localConfig.MaxLength = 10
	}
	return localConfig
}

func UpdateBeamConfig(mode string) {
	inferenceMode := definition.InferenceMode(mode)
	if inferenceMode != definition.InferenceModeAuto && inferenceMode != definition.InferenceModeSpeed && inferenceMode != definition.InferenceModeLength {
		return
	}
	// Set local inference mode
	global.LocalInferenceMode = inferenceMode
	global.LocalBeamConfig = GetLocalBeamConfig(global.LocalInferenceMode)
}

// 新增bin目录下env.json
func loadEnvConfig() {
	cosyEnvConfigFile := filepath.Join(util.GetCosyProcessPath(), "bin", "env.json")
	file, err := os.Open(cosyEnvConfigFile)
	if err != nil {
		if os.IsNotExist(err) {
			log.Info("Env config file not exists, skipped")
			return
		}
		log.Warnf("Failed to open env config file: %s", err.Error())
		return
	}
	defer file.Close()

	byteValue, _ := io.ReadAll(file)
	err = json.Unmarshal(byteValue, BundledEnvConfig)
	if err != nil {
		log.Warnf("Failed to read env config file: %s", err.Error())
		return
	}
	fixEnvConfigEncoding(BundledEnvConfig)

	envConfig := BundledEnvConfig

	log.Debugf("load external env config. path: %s, content: %s", cosyEnvConfigFile, string(byteValue))

	if envConfig.OnPremise != "" {
		if boolValue, err := strconv.ParseBool(envConfig.OnPremise); err == nil {
			OnPremiseMode = boolValue
		}
	}

	if envConfig.RegionConfig != nil {
		//使用固定的region服务
		BundledRegionConfig = envConfig.RegionConfig
	}

	if envConfig.TestAgentTrackConfig.DoTestAgentTrack != "" {
		if boolValue, err := strconv.ParseBool(envConfig.TestAgentTrackConfig.DoTestAgentTrack); err == nil {
			DoTestAgentTrack = boolValue
		}
	}
	if envConfig.TestAgentTrackConfig.EvalOutPutLogFileDir != "" {
		EvalOutPutLogFileDir = envConfig.TestAgentTrackConfig.EvalOutPutLogFileDir
	}
}

func configureWithCustomizedEndpoint(endpoint string) {
	//自定义endpoint
	parsedUrl, err := url.Parse(endpoint)
	if err != nil {
		log.Warnf("endpoint is error. endpoint=%s, err=%v", GlobalModelConfig.Endpoint, err)
		return
	}
	if parsedUrl.Path != "" && parsedUrl.Path != "/" {
		log.Warnf("endpoint is bad. endpoint=%s", GlobalModelConfig.Endpoint)
		return
	}
	if parsedUrl.Scheme != "http" && parsedUrl.Scheme != "https" {
		log.Warnf("endpoint is bad. endpoint=%s", GlobalModelConfig.Endpoint)
		return
	}
	if strings.HasSuffix(endpoint, "/") {
		endpoint = endpoint[:len(endpoint)-1]
	}
	Remote.BigModelEndpoint = endpoint + "/algo"
	Remote.LoginUrl = endpoint + definition.UrlPathLogin
	Remote.AuthLoginUrl = endpoint + definition.UrlPathAuthLogin
	Remote.AuthLogoutUrl = endpoint + definition.UrlPathLogout
	//清空环境切换cookie
	Remote.BigModelCookie = ""
	Remote.BigModelHost = ""
}

func initEmbeddedConfig() {
	err := util.UnmarshalToObject(cnEnvConfigJSON, &CnEnvConfig)
	if err != nil {
		log.Errorf("Failed to read embedded cn region env config file: %v", err)
		return
	}

	err = util.UnmarshalToObject(intlEnvConfigJSON, &IntlEnvConfig)
	if err != nil {
		log.Errorf("Failed to read embedded intl region env config file: %v", err)
		return
	}
	err = util.UnmarshalToObject(qoderEnvConfigJSON, &QoderEnvConfig)
	if err != nil {
		log.Errorf("Failed to read embedded qoder env config file: %v", err)
		return
	}

}

func GetLocalBeamConfig(mode definition.InferenceMode) definition.BeamConfig {
	// Set beam config
	switch mode {
	case definition.InferenceModeSpeed:
		return definition.BeamConfig{
			BeamSize:       2,
			TopK:           2,
			TokenThreshold: 0.1,
			MaxLength:      5,
		}
	case definition.InferenceModeLength:
		return definition.BeamConfig{
			BeamSize:       3,
			TopK:           3,
			TokenThreshold: 0.1,
			MaxLength:      15,
		}
	default:
		// Load saved beam config in auto mode from local config
		var localConfig ViperBeamConfig
		if err := viper.UnmarshalKey("beam_search_config", &localConfig); err != nil || localConfig.BeamSize == 0 {
			localConfig = ViperBeamConfig{
				BeamSize:       3,
				TopK:           3,
				TokenThreshold: 0.1,
				MaxLength:      10,
			}
		}
		localConfig = checkInferenceConfig(localConfig)
		return definition.BeamConfig{
			BeamSize:       localConfig.BeamSize,
			TopK:           localConfig.TopK,
			TokenThreshold: localConfig.TokenThreshold,
			MaxLength:      localConfig.MaxLength,
		}
	}
}

func UpdateGlobalConfig(configParams definition.GlobalConfigParam) definition.ConfigUpdateResult {
	// 兼容插件未更新的情况，为空使用原来的值
	if configParams.McpAutoRun == "" {
		configParams.McpAutoRun = GlobalModelConfig.McpAutoRun
	}
	if configParams.WebToolsExecutionMode == "" {
		configParams.WebToolsExecutionMode = GlobalModelConfig.WebToolsExecutionMode
	}
	if configParams.AskModeUseTools == "" {
		configParams.AskModeUseTools = GlobalModelConfig.AskModeUseTools
	}
	if configParams.TerminalRunMode == "" {
		configParams.TerminalRunMode = GlobalModelConfig.TerminalRunMode
	}
	if configParams.CommandDenyList == "" {
		configParams.CommandDenyList = GlobalModelConfig.CommandDenyList
	}
	if configParams.QuestModeEnable == "" {
		configParams.QuestModeEnable = GlobalModelConfig.QuestModeEnable
	}
	if configParams.PreferredLanguage == "" {
		configParams.PreferredLanguage = GlobalModelConfig.PreferredLanguage
	} else {
		global.PreferredLanguage = configParams.PreferredLanguage
	}
	if configParams.ShowQuotaExceeded == "" {
		configParams.ShowQuotaExceeded = GlobalModelConfig.ShowQuotaExceeded
	}

	if !checkProxyValid(configParams) {
		log.Warnf("Update global config fail, proxy confg format is invalid, should start with 'http' or 'socks'. proxyUrl: " + configParams.HttpProxy)
		return definition.ConfigUpdateResult{
			Success: false,
		}
	}
	bytes0, err0 := json.Marshal(configParams)
	bytes1, err1 := json.Marshal(GlobalModelConfig)
	if err0 != nil || err1 != nil {
		return definition.ConfigUpdateResult{
			Success: false,
		}
	}
	if string(bytes0) != string(bytes1) {
		GlobalModelConfig.HttpProxy = configParams.HttpProxy
		GlobalModelConfig.ProxyMode = configParams.ProxyMode
		GlobalModelConfig.CommandAllowList = configParams.CommandAllowList
		GlobalModelConfig.McpAutoRun = configParams.McpAutoRun
		GlobalModelConfig.WebToolsExecutionMode = configParams.WebToolsExecutionMode
		GlobalModelConfig.AskModeUseTools = configParams.AskModeUseTools
		GlobalModelConfig.TerminalRunMode = configParams.TerminalRunMode
		GlobalModelConfig.CommandDenyList = configParams.CommandDenyList
		GlobalModelConfig.QuestModeEnable = configParams.QuestModeEnable
		GlobalModelConfig.PreferredLanguage = configParams.PreferredLanguage
		GlobalModelConfig.ShowQuotaExceeded = configParams.ShowQuotaExceeded
		saveConfig := map[string]string{
			"http_proxy":               GlobalModelConfig.HttpProxy,
			"proxy_mode":               GlobalModelConfig.ProxyMode,
			"endpoint":                 GlobalModelConfig.Endpoint,
			"command_allow_list":       GlobalModelConfig.CommandAllowList,
			"mcp_auto_run":             GlobalModelConfig.McpAutoRun,
			"web_tools_execution_mode": GlobalModelConfig.WebToolsExecutionMode,
			"ask_mode_use_tools":       GlobalModelConfig.AskModeUseTools,
			"terminal_run_mode":        GlobalModelConfig.TerminalRunMode,
			"command_deny_list":        GlobalModelConfig.CommandDenyList,
			"quest_mode_enable":        GlobalModelConfig.QuestModeEnable,
			"preferred_language":       GlobalModelConfig.PreferredLanguage,
			"show_quota_exceeded":      GlobalModelConfig.ShowQuotaExceeded,
		}
		viper.Set("global_config", saveConfig)

		err := viper.WriteConfig()
		if err == nil {
			log.Debugf("Update global config success. global config: " + util.ToJsonStr(GlobalModelConfig))
			return definition.ConfigUpdateResult{
				Success: true,
			}
		}
	}
	return definition.ConfigUpdateResult{
		Success: false,
	}
}

// AppendCommandAllowListConfig 向全局配置的命令白名单中添加新命令。
func AppendCommandAllowListConfig(configParams definition.ConfigCommandListParam) definition.ConfigUpdateResult {
	// 调用util.splitCommands分割得到列表
	commands := util.SplitCommands(configParams.CommandString)

	// 合并新增的命令到全局命令白名单，去重并按字典序排序
	allowList := mergeCommandList(commands, GlobalModelConfig.CommandAllowList)

	// 更新全局命令白名单
	configParam := GetGlobalConfig()
	configParam.CommandAllowList = strings.Join(allowList, ",")
	return UpdateGlobalConfig(configParam)
}

// RemoveCommandAllowListConfig 从全局配置中移除指定的命令白名单
func RemoveCommandAllowListConfig(configParams definition.ConfigCommandListParam) definition.ConfigUpdateResult {
	// 调用util.splitCommands分割得到列表
	commands := util.SplitCommands(configParams.CommandString)

	// 在全局命令白名单中移除命令
	allowList := removeCommandList(commands, GlobalModelConfig.CommandAllowList)

	// 更新全局命令白名单
	configParam := GetGlobalConfig()
	configParam.CommandAllowList = strings.Join(allowList, ",")
	return UpdateGlobalConfig(configParam)
}

// AppendCommandDenyListConfig 向全局配置的命令黑名单中添加新命令。
func AppendCommandDenyListConfig(configParams definition.ConfigCommandListParam) definition.ConfigUpdateResult {
	// 调用util.splitCommands分割得到列表
	commands := util.SplitCommands(configParams.CommandString)

	// 合并新增的命令到全局命令白名单，去重并按字典序排序
	allowList := mergeCommandList(commands, GlobalModelConfig.CommandDenyList)

	// 更新全局命令白名单
	configParam := GetGlobalConfig()
	configParam.CommandDenyList = strings.Join(allowList, ",")
	return UpdateGlobalConfig(configParam)
}

// RemoveCommandDenyListConfig 从全局配置中移除指定的命令黑名单
func RemoveCommandDenyListConfig(configParams definition.ConfigCommandListParam) definition.ConfigUpdateResult {
	// 调用util.splitCommands分割得到列表
	commands := util.SplitCommands(configParams.CommandString)

	// 在全局命令白名单中移除命令
	allowList := removeCommandList(commands, GlobalModelConfig.CommandDenyList)

	// 更新全局命令白名单
	configParam := GetGlobalConfig()
	configParam.CommandDenyList = strings.Join(allowList, ",")
	return UpdateGlobalConfig(configParam)
}

func mergeCommandList(commands []string, oldCommands string) []string {
	mergedList := make([]string, 0)
	duplicateMap := make(map[string]bool)

	// 处理旧命令
	if oldCommands != "" {
		for _, cmd := range strings.FieldsFunc(oldCommands, func(r rune) bool { return r == ',' }) {
			// 直接添加旧命令
			mergedList = append(mergedList, cmd)
			trimmedCmd := strings.TrimSpace(cmd)
			parts := strings.Fields(trimmedCmd)
			if len(parts) > 0 {
				// 适配 IsCommandInWhiteList 方法，仅检查第一部分
				duplicateMap[parts[0]] = true
			}
		}
	}

	// 处理新命令
	for _, cmd := range commands {
		trimmedCmd := strings.TrimSpace(cmd)
		parts := strings.Fields(trimmedCmd)
		if len(parts) > 0 && !duplicateMap[parts[0]] {
			duplicateMap[parts[0]] = true
			mergedList = append(mergedList, parts[0])
		}
	}

	return mergedList
}

func removeCommandList(commands []string, oldCommands string) []string {
	finalList := make([]string, 0)
	removeMap := make(map[string]bool)

	// 构建要移除的命令集合
	for _, cmd := range commands {
		trimmedCmd := strings.TrimSpace(cmd)
		parts := strings.Fields(trimmedCmd)
		if len(parts) > 0 {
			removeMap[parts[0]] = true
		}
	}

	// 处理旧命令，排除需要删除的部分
	if oldCommands != "" {
		for _, cmd := range strings.FieldsFunc(oldCommands, func(r rune) bool { return r == ',' }) {
			trimmedCmd := strings.TrimSpace(cmd)
			parts := strings.Fields(trimmedCmd)
			if len(parts) > 0 {
				if !removeMap[parts[0]] {
					finalList = append(finalList, parts[0])
				}
			}
		}
	}

	return finalList
}

func UpdateEndpointConfig(configParams definition.ConfigEndpointParam) definition.ConfigUpdateResult {
	if configParams.Endpoint == GlobalModelConfig.Endpoint {
		return definition.ConfigUpdateResult{
			Success: false,
		}
	}
	GlobalModelConfig.Endpoint = configParams.Endpoint

	saveConfig := map[string]string{
		"http_proxy":         GlobalModelConfig.HttpProxy,
		"proxy_mode":         GlobalModelConfig.ProxyMode,
		"endpoint":           GlobalModelConfig.Endpoint,
		"command_allow_list": GlobalModelConfig.CommandAllowList,
	}
	viper.Set("global_config", saveConfig)

	err := viper.WriteConfig()
	if err != nil {
		log.Errorf("Update endpoint fail. error: %v", err)
		return definition.ConfigUpdateResult{
			Success: false,
		}
	}
	log.Info("Update endpoint success. endpoint config: " + configParams.Endpoint)
	return definition.ConfigUpdateResult{
		Success: true,
	}
}

func SetShowQuotaExceeded(showQuotaExceeded string) {
	configParam := GetGlobalConfig()
	configParam.ShowQuotaExceeded = showQuotaExceeded
	UpdateGlobalConfig(configParam)
}

func GetShowQuotaExceeded() bool {
	configParam := GetGlobalConfig()
	if configParam.ShowQuotaExceeded == "false" {
		return false
	}
	if configParam.ShowQuotaExceeded == "true" {
		// 首次超限，初始化为 true
		return true
	}
	lastTime, err := time.Parse(time.RFC3339, configParam.ShowQuotaExceeded)
	if err != nil {
		return true
	}
	//// todo temp test
	//return time.Since(lastTime).Minutes() > 5
	today := time.Now().Truncate(24 * time.Hour)
	return lastTime.Before(today)
}

func ConfigureRemoteConfig() {
	if global.IsQoderProduct() {
		//qoder场景
		Remote = QoderEnvConfig.Remote

		if BundledEnvConfig.Remote.LoginUrl != "" {
			//外置env.json也包含了有效的 endpoint设置情况下，优先加载外部
			Remote = BundledEnvConfig.Remote

			log.Infof("Using qoder config.")
		}

		return
	}

	if OnPremiseMode {
		//私有化版本
		Remote = BundledEnvConfig.Remote

		log.Infof("Using on-premise config.")
		return
	}
	if global.CustomizedEndpoint != "" {
		//启动时设置了Endpoint
		configureWithCustomizedEndpoint(global.CustomizedEndpoint)
		return
	}
	if GlobalModelConfig.Endpoint != "" {
		//设置了专属版Endpoint
		configureWithCustomizedEndpoint(GlobalModelConfig.Endpoint)
		return
	}
	if BundledEnvConfig.Remote.LoginUrl != "" {
		//配置了有效的Endpoint，覆盖默认配置
		Remote = BundledEnvConfig.Remote

		return
	}

	if RegionEnv == definition.RegionIntl {
		Remote = IntlEnvConfig.Remote
	} else {
		Remote = CnEnvConfig.Remote
	}
	Remote.withExtra(&GlobalModelRemoteConfig)
}

func checkProxyValid(configParams definition.GlobalConfigParam) bool {
	if configParams.ProxyMode != "manual" {
		return true
	}
	//手动模式下校验是否有协议前缀
	if configParams.HttpProxy == "" {
		//支持不配置代理
		return true
	}
	if strings.HasPrefix(configParams.HttpProxy, "http") || strings.HasPrefix(configParams.HttpProxy, "socks") {
		return true
	}
	return false
}

func GetGlobalConfig() definition.GlobalConfigParam {
	return definition.GlobalConfigParam{
		ProxyMode:             GlobalModelConfig.ProxyMode,
		HttpProxy:             GlobalModelConfig.HttpProxy,
		CommandAllowList:      GlobalModelConfig.CommandAllowList,
		McpAutoRun:            GlobalModelConfig.McpAutoRun,
		WebToolsExecutionMode: GlobalModelConfig.WebToolsExecutionMode,
		AskModeUseTools:       GlobalModelConfig.AskModeUseTools,
		TerminalRunMode:       GlobalModelConfig.TerminalRunMode,
		CommandDenyList:       GlobalModelConfig.CommandDenyList,
		QuestModeEnable:       GlobalModelConfig.QuestModeEnable,
		PreferredLanguage:     GlobalModelConfig.PreferredLanguage,
		ShowQuotaExceeded:     GlobalModelConfig.ShowQuotaExceeded,
	}
}

func fixEnvConfigEncoding(envConfig *EnvConfig) {
	if envConfig.Remote.MessageEncode == "" {
		envConfig.Remote.MessageEncode = "1"
	}
	if envConfig.Remote.LoginEncode == "" {
		envConfig.Remote.LoginEncode = "2"
	}
}

func GetAskModeUseTools() bool {
	rlt, err := strconv.ParseBool(GlobalModelConfig.AskModeUseTools)
	if err != nil {
		log.Errorf("GetAskModeUseTools fail. error: %v", err)
		return true
	}
	return rlt
}
