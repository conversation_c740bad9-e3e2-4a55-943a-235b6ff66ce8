#!/usr/bin/env zsh

# 本地lingma dev编译脚本

project_path=$(cd "$(dirname "${0}")"; pwd)
echo "project_path: ${project_path}"

# 构建专属客户端
# -X main.dedicated=true
extra_flags=""

source "${project_path}/script/parse_build_args.sh" "$@"
if [ "${OUT_DIR}" = "" ]; then
  # 本地构建时将.lingma作为默认输出目录
  OUT_DIR="/Applications/Qoder.app/Contents/Resources/app/resources/bin"
fi
echo "Building binaries for local env"

# Get version first
version=$BUILD_VERSION
if [ "${version}" = "" ]; then
  version=$(cat DEV_VERSION)
fi

if [ "${version}" != "" ]; then
  echo ">> Set CosyVersion to: ${version} <<"
  echo ">> Extra flags before: ${extra_flags} <<"

  extra_flags="${extra_flags} -X cosy/global.CosyVersion=${version}"
fi

buildTargetProduct=qoder
buildTargetForm="${COSY_TARGET_FORM:-ide}"

extra_flags="${extra_flags} -X main.productType=${buildTargetProduct}"
extra_flags="${extra_flags} -X main.buildFormType=${buildTargetForm}"

echo ">> Extra flags: ${extra_flags} <<"

# Create target folder
bin_dir=$OUT_DIR
echo "Target dir: ""$bin_dir"
mkdir -p "$bin_dir/"{x86_64_darwin,aarch64_darwin}

arch=$(uname -m)
echo "Building x86_64_darwin"
CGO_ENABLED=1 GOOS=darwin GOARCH=amd64 go build -tags "dev,qoder" -gcflags="all=-N -l" -ldflags="${extra_flags}" -o "$bin_dir"/x86_64_darwin/Qoder
if [ "${arch}" = "arm64" ]; then
  echo "Building aarch64_darwin"
  CGO_ENABLED=1 GOOS=darwin GOARCH=arm64 go build -tags "dev,qoder" -gcflags="all=-N -l" -ldflags="${extra_flags}" -o "$bin_dir"/aarch64_darwin/Qoder
fi

if [ "$?" != "0"  ]; then
    echo "ERROR：build failed. errorCode: $?"
    exit 1
fi

echo "Done"
