package handlers

import (
	"context"
	"cosy/chat/agents/support"
	"cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/log"
	"cosy/longruntask"
	"cosy/util/session"
	"cosy/websocket"
)

func SendAgentNoticeHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.AgentStateInNoticeParam](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}

	stateInNotice := support.StateInNotice{
		SessionId: params.SessionId,
		RequestId: params.RequestId,
		Type:      params.NoticeType,
	}
	result := definition.NewSuccessResponse(nil)
	errMsg := ""
	err = support.ProcessStateInNotice(ctx, params.RequestId, stateInNotice)
	if err != nil {
		log.Debugf("Failed to send notice to agent, sessionId=%s, requestId=%s, err=%+v", params.SessionId, params.RequestId, err)
		unifiedError, ok := cosyErrors.IsUnifiedError(err)
		if ok && unifiedError.Code == cosyErrors.AgentNotExist {
			// TODO 需要看下这种异常怎么处理
			// agent已经不存活了，暂时把任务状态置为ActionRequired
			if session.IsQuestLongRunningSession(params.SessionId) {
				taskId := session.GetTaskIdBySessionId(params.SessionId)
				taskResp := longruntask.GetTaskManager().GetChatTask(ctx, taskId)
				if taskResp.Success {
					taskInfo, _ := taskResp.Data.(*definition.ChatTask)
					if taskInfo != nil && (taskInfo.Status == definition.ChatTaskStatusRunning || taskInfo.Status == definition.ChatTaskStatusPaused) {
						longruntask.UpdateChatTaskStatus(ctx, taskId, params.SessionId, definition.ChatTaskStatusActionRequired, nil)
					}
				}
			}
			result = definition.NewErrorResponse("AgentNotExist", errMsg, nil)
		} else {
			errMsg = err.Error()
			result = definition.NewErrorResponse("InternalError", errMsg, nil)
		}
	}
	reply(ctx, req, result)
	return nil
}
