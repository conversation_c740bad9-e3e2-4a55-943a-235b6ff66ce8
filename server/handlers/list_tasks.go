package handlers

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/websocket"
)

func ListTasksHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.ListChatTaskParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}

	log.Infof("[chat][listTask] params=%v", params)
	// 获取任务列表
	response := longruntask.ListProjectTasks(ctx, params)

	reply(ctx, req, response)
	return nil
}
