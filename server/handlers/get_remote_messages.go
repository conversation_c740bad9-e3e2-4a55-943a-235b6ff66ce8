package handlers

import (
	"context"
	"cosy/chat/agents/coder/common"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
)

type PagedAgentToolCallResult struct {
	ErrorMessage string                       `json:"errorMessage"`
	Successful   bool                         `json:"successful"`
	ToolResults  []common.ToolCallSyncRequest `json:"toolResults"`
	PageNumber   int                          `json:"pageNumber"`
	PageSize     int                          `json:"pageSize"`
	TotalSize    int                          `json:"totalSize"`
}

func GetRemoteMessagesHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.GetRemoteAgentMessagesRequest](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}
	agentToolCallResult := PagedAgentToolCallResult{}
	messages, err := longruntask.GetTaskManager().GetSessionMessages(ctx, params)
	if err != nil {
		log.Errorf("Query session messages failed. SessionId: %s, TaskId: %s", params.SessionId, params.TaskId)
		return err
	}

	agentToolCallResult.TotalSize = len(messages)
	agentToolCallResult.PageNumber = 1
	agentToolCallResult.PageSize = len(messages)
	toolCallIds := make([]string, 0)

	for _, message := range messages {
		if message.Role == "tool" && message.ToolResult != "" {
			toolCallResult := common.ToolCallSyncRequest{}
			if err := json.Unmarshal([]byte(message.ToolResult), &toolCallResult); err != nil {
				log.Errorf("Error unmarshalling tool result for message ID: %s, err: %s", message.Id, err.Error())
			}
			toolCallIds = append(toolCallIds, toolCallResult.ToolCallId)
			agentToolCallResult.ToolResults = append(agentToolCallResult.ToolResults, toolCallResult)
		}
	}

	confirmToolCalls := toolCommon.GetConfirmToolCallBySession(params.SessionId)
	if len(confirmToolCalls) != 0 {
		for _, toolCall := range confirmToolCalls {
			if util.Contains(toolCallIds, toolCall.ID) {
				continue
			}
			var parameters map[string]interface{}
			err := json.Unmarshal([]byte(toolCall.Function.Arguments), &parameters)
			if err != nil {
				parameters = make(map[string]interface{})
			}
			agentToolCallResult.ToolResults = append(agentToolCallResult.ToolResults, common.ToolCallSyncRequest{
				ToolCallId:     toolCall.ID,
				ToolCallStatus: common.ToolCallStatusPending,
				Parameters:     parameters,
			})
		}
	}
	reply(ctx, req, definition.NewSuccessResponse(agentToolCallResult))
	return nil
}
