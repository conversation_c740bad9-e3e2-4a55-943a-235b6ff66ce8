package handlers

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/websocket"
)

func UpdateTaskHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.UpdateChatTaskParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}

	log.Info("[chat][updateTask] params=%v", params)
	// 更新任务
	response := longruntask.GetTaskManager().UpdateChatTask(ctx, params.Id, params)

	reply(ctx, req, response)
	return nil
}
