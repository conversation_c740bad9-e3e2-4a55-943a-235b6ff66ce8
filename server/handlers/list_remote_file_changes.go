package handlers

import (
	"context"
	"cosy/chat/service"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/util"
	"cosy/util/session"
	"cosy/websocket"
	"fmt"
	"strings"
)

func ListRemoteFileChangesHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.ListRemoteFileChangesRequestParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}
	if params.SessionId == "" {
		err := fmt.Errorf("SessionId is required for remote/fileChanges/list")
		log.Error(err)
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}
	if taskId := session.GetTaskIdBySessionId(params.SessionId); taskId != "" {
		// https://devops.aliyun-inc.com/projex/bug/AICODE-14485# 《【Qoder】【Quest】丢弃的任务，Action flow页面文件变更列表为空》
		// 如果任务状态是 Accepted 或者 Rejected，从云上拉FileChanges，本地工作区可能已经清理快照数据
		response := longruntask.GetTaskManager().GetChatTask(ctx, taskId)
		if response.Success {
			taskData := response.Data.(*definition.ChatTask)
			if taskData.Status == definition.ChatTaskStatusAccepted || taskData.Status == definition.ChatTaskStatusRejected {
				fileChangesResp, err := getTaskFileChanges(ctx, req, taskId, 1, 100)
				if err != nil {
					return err
				}
				var infos []definition.RemoteFileChangeInfo
				for _, fileChange := range fileChangesResp.FileChanges {
					infos = append(infos, definition.RemoteFileChangeInfo{
						SessionId:     params.SessionId,
						FileId:        fileChange.FilePath,
						Mode:          fileChange.EditMode,
						BeforeContent: fileChange.OriginalContent,
						AfterContent:  fileChange.ModifiedContent,
					})
				}
				reply(ctx, req, definition.NewSuccessResponse(definition.ListRemoteFileChangesResponse{
					FileChanges: infos,
				}))
				return nil
			}
		}
	}
	fileVOList, err := service.WorkingSpaceServiceManager.ListFileChangesBySessionId(ctx, params.SessionId)
	if err != nil {
		// 可能获取某个文件有错误，不应影响整体数据返回
		log.Error(err)
		if len(fileVOList) == 0 {
			// 没有数据就返回错误
			reply(ctx, req, definition.NewErrorResponse("InternalError", err.Error(), nil))
			return err
		}
		// 若有数据则返回数据
	}
	data := definition.ListRemoteFileChangesResponse{
		FileChanges: make([]definition.RemoteFileChangeInfo, 0),
	}
	var filteredFileVOList []definition.RemoteFileChangeInfo
	workDir := util.GetWorkspaceUriFromCtx(ctx)
	for _, f := range fileVOList {
		if f.BeforeContent == f.AfterContent {
			// 过滤掉 前后改动相同的文件
			continue
		}
		if strings.HasPrefix(f.FileId, workDir) {
			// 只返回相对路径
			f.FileId = strings.Replace(f.FileId, workDir, ".", 1)
		}
		filteredFileVOList = append(filteredFileVOList, *definition.ParseRemoteFileChangeInfo(&f))
	}
	if len(filteredFileVOList) > 0 {
		data.FileChanges = filteredFileVOList
	}
	reply(ctx, req, definition.NewSuccessResponse(data))
	return nil
}
