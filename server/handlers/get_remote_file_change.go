package handlers

import (
	"context"
	"cosy/chat/service"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/util"
	"cosy/util/session"
	"cosy/websocket"
	"fmt"
	"path/filepath"
)

func GetRemoteFileChangeHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.GetRemoteFileChangesRequestParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}
	if params.SessionId == "" {
		err := fmt.Errorf("SessionId is required for remote/fileChanges/get")
		log.Error(err)
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}
	if params.FileId == "" {
		err := fmt.Errorf("FileId is required for remote/fileChanges/get")
		log.Error(err)
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}
	filePath := params.FileId
	workDir := util.GetWorkspaceUriFromCtx(ctx)
	if !filepath.IsAbs(filePath) {
		filePath = filepath.Join(workDir, filePath)
	}
	if taskId := session.GetTaskIdBySessionId(params.SessionId); taskId != "" {
		// https://devops.aliyun-inc.com/projex/bug/AICODE-14485# 《【Qoder】【Quest】丢弃的任务，Action flow页面文件变更列表为空》
		// 如果任务状态是 Accepted 或者 Rejected，从云上拉FileChanges，本地工作区可能已经清理快照数据
		response := longruntask.GetTaskManager().GetChatTask(ctx, taskId)
		if response.Success {
			taskData := response.Data.(*definition.ChatTask)
			if taskData.Status == definition.ChatTaskStatusAccepted || taskData.Status == definition.ChatTaskStatusRejected {
				fileChangesResp, err := getTaskFileChanges(ctx, req, taskId, 1, 100)
				if err != nil {
					return err
				}
				for _, fc := range fileChangesResp.FileChanges {
					if filePath == filepath.Join(workDir, fc.FilePath) {
						reply(ctx, req, definition.NewSuccessResponse(definition.GetRemoteFileChangesResponse{
							FileChange: &definition.RemoteFileChangeInfo{
								SessionId:     params.SessionId,
								FileId:        fc.FilePath,
								Mode:          fc.EditMode,
								BeforeContent: fc.OriginalContent,
								AfterContent:  fc.ModifiedContent,
							},
						}))
						return nil
					}
				}
				err = fmt.Errorf("file change %s not found", filePath)
				reply(ctx, req, definition.NewErrorResponse("NotFound", err.Error(), &definition.RemoteFileChangeInfo{}))
				return err
			}
		}
	}
	var result any
	fileVO, err := service.WorkingSpaceServiceManager.GetFileChangeBySessionId(ctx, params.SessionId, filePath)
	if err != nil {
		// 可能获取某个文件有错误，不应影响整体数据返回
		log.Error(err)
		result = definition.NewErrorResponse("InternalError", err.Error(), &definition.RemoteFileChangeInfo{})
	} else {
		result = definition.NewSuccessResponse(definition.GetRemoteFileChangesResponse{
			FileChange: definition.ParseRemoteFileChangeInfo(fileVO),
		})
	}

	reply(ctx, req, result)
	return nil
}
