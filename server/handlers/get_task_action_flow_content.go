package handlers

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/websocket"
)

func GetTaskActionFlowContentHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.GetActionFlowContentRequestParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}

	taskMgr := longruntask.GetTaskManager()
	resp, err := taskMgr.GetActionFlow(ctx, params.TaskId)
	if err != nil {
		log.Errorf("Failed to get actionFlow: %v", err)
	}

	result := definition.Response{
		Success:      err == nil && resp.Code == 200,
		ErrorCode:    resp.ErrorCode,
		ErrorMessage: resp.Message,
		Data:         resp.Data,
	}

	reply(ctx, req, result)
	return nil
}
