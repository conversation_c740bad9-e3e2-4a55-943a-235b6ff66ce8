package handlers

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/websocket"
)

func UpdateTaskDesignHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.UpdateDesignRequestParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}

	taskMgr := longruntask.GetTaskManager()
	resp, err := taskMgr.SyncDesignDoc(ctx, params.TaskId, &longruntask.SyncDesignContentParams{
		DesignData: params.DesignContent,
	})
	if err != nil {
		log.Errorf("Failed to update design doc: %v", err)
	}

	result := definition.Response{
		Success:      err == nil && resp.Code == 200,
		ErrorCode:    resp.<PERSON>,
		ErrorMessage: resp.Message,
		Data:         resp.Data,
	}

	reply(ctx, req, result)
	return nil
}
