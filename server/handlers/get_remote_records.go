package handlers

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/websocket"
	"sort"
)

func GetRemoteRecordsHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.GetRemoteAgentRecordsRequest](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}

	resp, err := longruntask.GetTaskManager().GetSessionRecords(ctx, params)
	if err != nil {
		log.Errorf("Query session records failed. SessionId: %s, TaskId: %s", params.SessionId, params.TaskId)
		reply(ctx, req, definition.NewErrorResponse("InternalError", err.Error(), nil))
	}
	sort.Slice(resp, func(i, j int) bool {
		return resp[i].GmtCreate < resp[j].GmtCreate
	})

	pagedResult := longruntask.GetSessionRecordsResp{
		PageNumber: 1,
		PageSize:   len(resp),
		TotalSize:  len(resp),
		Items:      resp,
	}

	reply(ctx, req, definition.NewSuccessResponse(pagedResult))
	return nil
}
