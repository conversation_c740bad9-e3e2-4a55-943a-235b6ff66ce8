package handlers

import (
	"context"
	"cosy/chat/agents/quest/summary"
	"cosy/config"
	"cosy/definition"
	"cosy/locale"
	"cosy/log"
	"cosy/longruntask"
	"cosy/util"
	"cosy/websocket"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"time"
)

func CreateTaskHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.CreateChatTaskParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}

	log.Info("[chat][createTask] params=%v", params)
	taskName, designFile, err := createTaskNameAndDesignFile(ctx, params)
	params.Name = taskName
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("CreateTaskNameAndDesignFileFailed", err.Error(), nil))
		return err
	}
	// 创建任务
	response := longruntask.CreateChatTask(ctx, params)
	if !response.Success {
		reply(ctx, req, response)
		log.Error("[chat][createTask] CreateTask Failed, response=%v", response)
		return errors.New("CreateTask failed")
	}

	taskInfo := response.Data.(*definition.ChatTask)
	taskInfo.Name = taskName
	taskInfo.DesignFile = designFile
	taskInfo.Query = params.Query

	// 异步更新task名称和设计文档
	go updateTaskAsyncWithTimeout(ctx, taskInfo.Id, taskName, params.Query, taskInfo.DesignFile)

	reply(ctx, req, response)
	return nil
}

func createTaskNameAndDesignFile(ctx context.Context, createParams *definition.CreateChatTaskParams) (string, string, error) {
	taskName, fileName, err := summary.GetNamingInfoByUserQuery(ctx, createParams)
	if err != nil {
		log.Error("[chat][createTask] CreateTask Failed, error:", err)
		return "", "", errors.New(locale.Localize("error_code_generate_task_name_failed", getLanguage()))
	}

	if len(taskName) == 0 {
		log.Error("[chat][createTask] CreateTask Failed, the task name is empty")
		return "", "", errors.New(locale.Localize("error_code_generate_task_name_failed", getLanguage()))
	}
	if createParams.ExecutionMode == definition.ExecutionModelDirect {
		return taskName, "", nil
	}

	if len(fileName) == 0 {
		log.Error("[chat][createTask] CreateTask Failed, the design file name is empty")
		return "", "", errors.New(locale.Localize("error_code_generate_design_file_name_failed", getLanguage()))
	}
	designFile, err := createDesignFile(ctx, createParams.Project, fileName)
	if err != nil {
		log.Error("[chat][createTask] CreateDesignFile Failed, error:", err)
		return "", "", errors.New(locale.Localize("error_code_create_design_file_failed", getLanguage()))
	}
	return taskName, designFile, nil
}

// updateTaskAsyncWithTimeout 异步更新任务信息，带超时控制
func updateTaskAsyncWithTimeout(ctx context.Context, taskId, taskName, userQuery, designFile string) {
	// 创建带超时的子上下文
	updateCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 使用 defer 捕获 panic
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("updateTaskAsyncWithTimeout panic for taskId %s: %v", taskId, r)
		}
	}()

	// 同步设计文档
	taskManager := longruntask.GetTaskManager()
	if _, err := taskManager.SyncDesignDoc(updateCtx, taskId, &longruntask.SyncDesignContentParams{
		UserQuery:  userQuery,
		DesignFile: designFile,
	}); err != nil {
		log.Errorf("Failed to update design doc. taskId:%s, err:%s", taskId, err.Error())
	}
}

func createDesignFile(ctx context.Context, filePath, fileName string) (string, error) {
	// 标准化路径，处理Windows下的路径问题
	normalizedPath := util.NormalizePathForWindows(filePath)
	designFilePath := filepath.Join(normalizedPath, ".qoder", "quests")
	if err := os.MkdirAll(designFilePath, 0755); err != nil {
		log.Error("[chat][createDesignFile] CreateDesignFile Failed, error:%v", err)
		return "", errors.New(locale.Localize("error_code_create_design_file_failed", getLanguage()))
	}

	designFile := filepath.Join(designFilePath, fmt.Sprintf("%s.md", fileName))

	// FIXME 需要改为序号
	if _, err := os.Stat(designFile); !os.IsNotExist(err) {
		designFile = filepath.Join(designFilePath, fmt.Sprintf("%s-%d.md", fileName, time.Now().Unix()))
	}

	file, err := os.OpenFile(designFile, os.O_CREATE|os.O_WRONLY|os.O_EXCL, 0644)
	if err != nil {
		log.Error("[chat][createDesignFile] CreateDesignFile Failed, error:%v", err)
		return "", errors.New(locale.Localize("error_code_create_design_file_failed", getLanguage()))

	}

	if closeErr := file.Close(); closeErr != nil {
		log.Warnf("Failed to close design file %s: %v", designFile, closeErr)
	}

	return designFile, nil
}
func getLanguage() string {
	originParams := config.GetGlobalConfig()
	// 应该采用Display Language, 展示未发现cosy中获取该配置的方法，FIXME
	if len(originParams.PreferredLanguage) > 0 {
		return originParams.PreferredLanguage
	}
	// 默认采用英文
	return definition.LocaleEn
}
