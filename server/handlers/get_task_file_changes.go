package handlers

import (
	"context"
	"cosy/definition"
	cosyError "cosy/errors"
	"cosy/log"
	"cosy/longruntask"
	"cosy/websocket"
	"errors"
	"fmt"
	"net/url"
)

func GetTaskFileChangesHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.GetFileChangesContentRequestParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}
	resp, err := getTaskFileChanges(ctx, req, params.TaskId, params.PageNumber, params.PageSize)
	if err != nil {
		return err
	}
	reply(ctx, req, definition.NewSuccessResponse(resp))
	return nil
}

func getTaskFileChanges(ctx context.Context, req *websocket.WireRequest, taskId string, pageNumber int, pageSize int) (*longruntask.PagedFileChangesResp, error) {
	taskMgr := longruntask.GetTaskManager()
	resp, err := taskMgr.GetFileChanges(ctx, taskId, pageSize, pageNumber)
	if err != nil {
		log.Errorf("Failed to get fileChanges: %v", err)
		var urlError *url.Error
		if errors.As(err, &urlError) && urlError.Timeout() {
			reply(ctx, req, definition.NewErrorResponse(fmt.Sprintf("%d", cosyError.SystemError), "Sorry, something went wrong. Please try again", nil))
			return nil, err
		}
		reply(ctx, req, definition.NewErrorResponse("InternalError", err.Error(), nil))
		return nil, err
	}

	return resp, nil
}
