package longruntask

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/log"
	"cosy/remote"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTaskManager_SyncDesignDoc(t *testing.T) {
	tests := []struct {
		TaskId     string
		DesignData string
	}{
		{
			TaskId:     "task-d1ii1jo6psgaesl57rog",
			DesignData: "Just For Test",
		},
	}
	config.InitConfig()
	client.InitClients()
	remote.InitRegionFailoverService()
	config.Remote.BigModelEndpoint = "http://test-api-ap-southeast-1.qoder.ai/algo"
	taskManager := GetTaskManager()
	for _, tt := range tests {
		resp, err := taskManager.SyncDesignDoc(context.Background(), tt.TaskId, &SyncDesignContentParams{
			DesignData: tt.DesignData,
		})
		if err != nil {
			assert.Fail(t, "error", err.Error())
		}
		log.Infof("%+v", resp)
	}
}

func TestTaskManager_LoadDesignDoc(t *testing.T) {
	tests := []struct {
		TaskId     string
		DesignData string
	}{
		{
			TaskId: "task-d1ii1jo6psgaesl57rog",
		},
	}
	config.InitConfig()
	client.InitClients()
	remote.InitRegionFailoverService()
	config.Remote.BigModelEndpoint = "http://test-api-ap-southeast-1.qoder.ai/algo"
	taskManager := GetTaskManager()
	for _, tt := range tests {
		resp, err := taskManager.GetDesignDoc(context.Background(), tt.TaskId)
		if err != nil {
			assert.Fail(t, "error", err.Error())
		}
		log.Infof("%+v", resp)
	}
}
