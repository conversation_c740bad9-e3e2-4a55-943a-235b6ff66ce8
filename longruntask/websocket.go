package longruntask

import (
	"context"
	"cosy/definition"
	"cosy/util/cache"
	"cosy/websocket"
	"fmt"
)

// ChatSessionClientCache 缓存sessionId与Client映射
var ChatSessionClientCache = cache.NewLruCache(100)

func notifyToClient(sessionId string, method string, payload any) error {
	v, ok := ChatSessionClientCache.Get(sessionId)
	if !ok {
		return fmt.Errorf("no session found for sessionId %s", sessionId)
	}
	ctx := context.WithValue(context.Background(), websocket.ClientCtxKey, v)
	return websocket.WsInst.NotifyClient(ctx, method, payload)
}

func NotificationForFileChanged(fileVO *definition.WorkingSpaceFileVO) error {
	return notifyToClient(fileVO.SessionId, "notification/file_changed", fileVO)
}
