package longruntask

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestUpdateFileChange(t *testing.T) {
	client.InitClients()
	type args struct {
		taskId     string
		fileChange *definition.UpdateFileChangeParam
	}
	tests := []struct {
		name    string
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "test",
			args: args{
				taskId: "task-d1iflt7bhmk6f17kj0p0",
				fileChange: &definition.UpdateFileChangeParam{
					FilePath:        "/Users/<USER>/code/owl/requirements.txt",
					OriginalContent: "",
					ModifiedContent: "123",
					EditMode:        "ADD",
				},
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.wantErr(t, UpdateFileChange(tt.args.taskId, tt.args.fileChange), fmt.Sprintf("UpdateFileChange(%v, %v)", tt.args.taskId, tt.args.fileChange))
		})
	}
}

func TestTaskManager_GetFileChanges(t *testing.T) {
	tests := []struct {
		TaskId     string
		PageSize   int
		PageNumber int
	}{
		{
			TaskId:     "task-d1ii1jo6psgaesl57rog",
			PageSize:   10,
			PageNumber: 1,
		},
	}
	config.InitConfig()
	client.InitClients()
	remote.InitRegionFailoverService()

	config.Remote.BigModelEndpoint = "http://test-api-ap-southeast-1.qoder.ai/algo"
	taskManager := GetTaskManager()
	for _, tt := range tests {
		resp, err := taskManager.GetFileChanges(context.Background(), tt.TaskId, tt.PageSize, tt.PageNumber)
		if err != nil {
			assert.Fail(t, "error", err.Error())
		}
		log.Infof("%+v", resp)
	}
}
