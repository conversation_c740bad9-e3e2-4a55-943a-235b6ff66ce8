package longruntask

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	cosyError "cosy/errors"
	"cosy/log"
	"cosy/remote"
	"cosy/sse"
	"cosy/user"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"runtime"
	"sync/atomic"
	"time"
)

const (
	MaxRetryCount         = 5
	UserChangedOrNotLogin = "user_changed_or_not_login"
)

var retryCount = 0

var userTaskStateRunning atomic.Bool

// GetUserChatTaskStats 获取用户任务统计信息
func GetUserChatTaskStats(ctx context.Context) *definition.Response {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return buildErrorResponse(cosyError.QuestUserNotLoginError, "user not login")
	}
	requestUrl := util.BuildUrlQuery(definition.GetUserChatTaskStatsEndpoint, map[string]any{
		"Encode": config.Remote.MessageEncode,
	})

	req, err := remote.BuildBigModelAuthRequest(http.MethodGet, requestUrl, nil)
	if err != nil {
		log.Warnf("Build request error, error=%v", err)
		return buildErrorResponse("", err.Error())
	}
	resp, err := client.GetRemoteAgentClient().Do(req)
	if err != nil {
		log.Warnf("do big model auth request error when get user chat task stats, the err: %v", err)
		var urlError *url.Error
		if errors.As(err, &urlError) && urlError.Timeout() {
			return buildSystemErrorResponse()
		}
		return buildErrorResponse("", err.Error())
	}
	defer resp.Body.Close()

	// 读取响应
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Warnf("read response body error when get user chat task stats, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	// 解析响应
	var result definition.GetUserChatTaskStatsResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		if resp.StatusCode != http.StatusOK {
			log.Warnf("Failed to get user chat task stats, url=%s, status=%s", req.URL, resp.Status)
			return buildErrorResponse("", fmt.Errorf("failed to get user chat task stats, status=%s", resp.Status).Error())
		}
		log.Warnf("unmarshal response body error get user chat task stats, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	response := &definition.Response{
		Success:      result.Code == 200,
		ErrorCode:    result.ErrorCode,
		ErrorMessage: result.Message,
		Data:         result.Data,
	}
	return response
}

func WatchingUserTaskStatsEvents(ctx context.Context) {
	if config.IsRemoteAgentMode() {
		// 远程模式下，不需要监听用户任务统计事件
		return
	}
	go watchEvents(ctx)
}

func watchEvents(ctx context.Context) {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return
	}
	requestUrl := util.BuildUrlQuery(definition.WatchUserEventsEndpoint, map[string]any{
		"UserId": userInfo.Uid,
		"Encode": config.Remote.MessageEncode,
	})
	req, requestError := remote.BuildBigModelAuthRequest(http.MethodGet, requestUrl, nil)
	if requestError != nil {
		log.Warnf("Build request error, url=%s, error=%v", requestUrl, requestError)
		return
	}
	sseClient := sse.NewSseRemoteAgentClient(map[string]string{})
	// 定义处理函数
	handleFun := handleEvent(ctx)
	// 使用原子操作的CompareAndSwap确保只有一个连接处于运行状态
	if !userTaskStateRunning.CompareAndSwap(false, true) {
		log.Warnf("watch user task stats event: already running")
		return
	}
	// 确保函数退出时重置running状态
	defer func() {
		userTaskStateRunning.Store(false)
		retryCount = 0
		log.Debugf("watch user task stats event: exit")
	}()

	// 使用for循环实现完整的重试机制，包含整个SSE生命周期
	for retryCount <= MaxRetryCount {
		log.Debugf("watch user task stats event: attempting to establish SSE connection, retry count: %d, url:%s", retryCount, req.URL)

		// 发送请求&读取数据，这个函数会一直阻塞直到连接断开或发生错误
		err := sseClient.SubscribeWithContext(ctx, time.Duration(definition.WatchTaskEventSSETimeout)*time.Second, req, handleFun, func(req *http.Request, rsp *http.Response) {
			log.Warnf("watch user task stats event: timeout")
		}, nil)

		// 无论是连接失败还是连接后的异常断开，都会到达这里
		if err != nil {
			log.Warnf("watch user task stats event: err=%v", err)
			if UserChangedOrNotLogin == err.Error() {
				log.Warnf("watch user task stats event: user not login or changed, try exit sse")
				return
			}
			retryCount++
			if retryCount <= MaxRetryCount {
				// sleep 1 second before retry
				time.Sleep(time.Duration(1) * time.Second)
			} else {
				return
			}
		} else {
			// 正常结束（比如上下文取消），不需要重试
			log.Debugf("watch user task stats event: ended normally")
			return
		}
	}
}

func checkUserInfoValidInReq(req *http.Request) bool {
	userIdInUrl := req.URL.Query().Get("UserId")
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil || (userIdInUrl != "" && userIdInUrl != userInfo.Uid) {
		return false
	}
	return true
}

func handleEvent(ctx context.Context) func(req *http.Request, rsp *http.Response, msg *sse.Event, closeChan chan error) {
	return func(req *http.Request, rsp *http.Response, msg *sse.Event, closeChan chan error) {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("watch user task stats events triggered panic")

				// 获取当前堆栈信息
				stack := make([]byte, 4096)
				stack = stack[:runtime.Stack(stack, false)]
				log.Errorf("recover from crash. err: %+v, stack: %s", r, stack)
			}
		}()
		log.Debugf("watch user task stats events. type=%s, data=%s，requestURL=%s", msg.Event, string(msg.Data), req.URL)
		// 有数据过来，重新连接的次数重置为0
		retryCount = 0
		// 校验当前sse的用户信息是否还有效
		valid := checkUserInfoValidInReq(req)
		if !valid {
			log.Warnf("watch user task stats events. user not login or user changed")
			closeChan <- errors.New(UserChangedOrNotLogin)
			return
		}
		switch string(msg.Event) {
		case definition.ChatTaskTypeUserTaskStatsEvent:
			var userChatTaskStats = definition.UserChatTaskStats{}
			err := json.Unmarshal(msg.Data, &userChatTaskStats)
			if err != nil {
				log.Warnf("Unmarshal sse msg data error: ", err)
				return
			}
			// 同步状态变更的消息给ide
			syncUserChatTaskStats(ctx, &userChatTaskStats)
			return
		case definition.ChatTaskTypeInProgressTaskUpdate:
			inProgressTask := &definition.RemoteChatTask{}
			err := json.Unmarshal(msg.Data, inProgressTask)
			if err != nil {
				log.Warnf("Unmarshal sse msg data error: ", err)
				return
			}
			syncInProgressTaskEventStats(ctx, inProgressTask)
			return

		}
	}
}

func syncUserChatTaskStats(ctx context.Context, stats *definition.UserChatTaskStats) {
	log.Debugf("syncUserChatTaskStats, status=%s", util.ToJsonStr(stats))

	// 检查websocket是否有链接的客户端，有才发送
	if websocket.WsInst == nil {
		log.Warn("syncUserChatTaskStats webSocket instance is not initialized")
		return
	}
	clients := websocket.WsInst.GetClientList()
	hasClients := len(clients) > 0
	if hasClients {
		websocket.SendBroadcastWithTimeout(ctx, "user/task/stats/sync", stats, nil)
	}
}

func syncInProgressTaskEventStats(ctx context.Context, task *definition.RemoteChatTask) {
	log.Debugf("syncInProgressTaskEventStats, status=%s", util.ToJsonStr(task))

	// 检查websocket是否有链接的客户端，有才发送
	if websocket.WsInst == nil {
		log.Warn("syncInProgressTaskEventStats webSocket instance is not initialized")
		return
	}
	clients := websocket.WsInst.GetClientList()
	hasClients := len(clients) > 0
	if hasClients {
		websocket.SendBroadcastWithTimeout(ctx, "user/inprogress/task/sync", TransRemoteTaskToTask(task), nil)
	}
}

func GetUserChatTaskQuotas(ctx context.Context, resource string) *definition.Response {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return buildErrorResponse("", "user not login")
	}
	requestUrl := util.BuildUrlQuery(definition.GetUserQuotasEndpoint, map[string]any{
		"resource": resource,
		"Encode":   config.Remote.MessageEncode,
	})
	req, err := remote.BuildBigModelAuthRequest(http.MethodGet, requestUrl, nil)
	if err != nil {
		log.Warnf("Build request error, error=%v", err)
		return buildErrorResponse("", err.Error())
	}
	resp, err := client.GetRemoteAgentClient().Do(req)
	if err != nil {
		log.Warnf("do big model auth request error when get user chat task quotas, the err: %v", err)
		var urlError *url.Error
		if errors.As(err, &urlError) && urlError.Timeout() {
			return buildSystemErrorResponse()
		}
		return buildErrorResponse("", err.Error())
	}
	defer resp.Body.Close()

	// 读取响应
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Warnf("read response body error when get user chat task quotas, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	// 解析响应
	var result definition.GetUserChatTaskQuotasResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		if resp.StatusCode != http.StatusOK {
			log.Warnf("Failed to get user chat task quotas, url=%s, status=%s", req.URL, resp.Status)
			return buildErrorResponse("", fmt.Errorf("failed to get user chat task stats, status=%s", resp.Status).Error())
		}
		log.Warnf("unmarshal response body error get user chat task quotas, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	response := &definition.Response{
		Success:      result.Code == 200,
		ErrorCode:    result.ErrorCode,
		ErrorMessage: result.Message,
		Data:         result.Data,
	}
	return response
}
