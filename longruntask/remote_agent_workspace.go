package longruntask

import (
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	cosyError "cosy/errors"
	"cosy/locale"
	"cosy/log"
	"cosy/remote"
	"cosy/user"
	"cosy/util"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"path/filepath"
	"time"

	"github.com/go-git/go-git/v5"
)

// 先查询workspace
func GetOrInitWorkspace(userUid string, projectPath string) (string, error) {
	if projectPath == "" {
		return "", nil
	}

	// 获取gitRemotes
	gitRemotes, err := getGitRemotes(projectPath)
	if err != nil {
		log.Errorf("get git remotes error, the err: %v", err)
	}

	// 获取本机mac
	mac, err := GetMachineId()
	if err != nil {
		log.Errorf("get mac error, the err: %v", err)
	}

	// 如果缓存中没有的话，从服务器allocate一个
	workspaceId, err := allocateWorkspace(projectPath, gitRemotes, mac)
	if err != nil {
		return "", err
	}
	return workspaceId, nil
}

func allocateWorkspace(workspaceUri string, gitRemotes []string, mac string) (string, error) {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return cosyError.QuestUserNotLoginError, errors.New("user not login")
	}

	workspaceInfo, err := json.Marshal(map[string]interface{}{
		"projectPath": workspaceUri,
	})
	if err != nil {
		return "", err
	}
	createWorkspaceParams := definition.AllocateWorkspaceParams{
		MachineId:     mac,
		FilePath:      workspaceUri,
		VcsType:       "Git",
		VcsUrls:       gitRemotes,
		WorkspaceInfo: string(workspaceInfo),
	}

	payload := remote.HttpPayload{
		Payload:       util.ToJsonStr(createWorkspaceParams),
		EncodeVersion: config.Remote.MessageEncode,
	}

	req, err := retryBuildRequest(http.MethodPost, definition.AllocateWorkspaceEndpoint, payload)
	if err != nil {
		if errors.Is(err, cosyError.PathNoRouteError) {
			return "", errors.New(locale.Localize("error_code_init_server_config_failed", definition.LocaleEn))
		}
		log.Warnf("Build request error, error=%v", err)
		return "", err
	}
	beginTimeStamp := time.Now()
	resp, err := client.GetRemoteAgentClient().Do(req)
	elapsedTime := time.Since(beginTimeStamp).Seconds()
	log.Infof("allocate workspace time %fs", elapsedTime)
	if err != nil {
		log.Errorf("do big model auth request error when allocate workspace, the err: %v", err)
		return "", err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		log.Warnf("Failed to allocate workspace, url=%s, status=%s", req.URL, resp.Status)
		return "", fmt.Errorf("failed to allocate workspace, status=%s", resp.Status)
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when allocate workspace, the err: %v", err)
		return "", err
	}

	var result definition.AllocateWorkspaceResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		log.Errorf("unmarshal response body error when allocate workspace, the err: %v", err)
		return "", err
	}
	return result.Data.WorkspaceId, nil
}

func getGitRemotes(workspacePath string) ([]string, error) {

	// 在Windows电脑下，ide给的path 可能为：/c:/Users/<USER>/， 此时golang的filepath不能正确处理
	normalizedPath := util.NormalizePathForWindows(workspacePath)
	// 确保路径是绝对路径
	absPath, err := filepath.Abs(normalizedPath)
	if err != nil {
		return nil, fmt.Errorf("failed to get absolute path: %w", err)
	}

	// 打开仓库
	repo, err := git.PlainOpen(absPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open git repository: %w", err)
	}

	// 获取远程仓库信息
	remotes, err := repo.Remotes()
	if err != nil {
		return nil, fmt.Errorf("failed to get remotes: %w", err)
	}

	if len(remotes) == 0 {
		// 返回空数组
		return []string{}, nil
	}

	for _, remote := range remotes {
		config := remote.Config()
		if len(config.URLs) > 0 {
			return config.URLs, nil
		}
	}
	return nil, fmt.Errorf("failed to get remotes: remotes is empty")
}

// 获取本机地址
func GetMachineId() (string, error) {
	return util.GetMachineId(true), nil
}

// retryBuildRequest 重试构建请求，如果是PathNoRouteError错误，每隔1s重试，5次超时
func retryBuildRequest(httpMethod string, urlPath string, requestBody interface{}) (*http.Request, error) {
	const maxRetries = 10
	const retryInterval = 500 * time.Millisecond

	var req *http.Request
	var err error

	for i := 0; i < maxRetries; i++ {
		req, err = remote.BuildBigModelAuthRequest(httpMethod, urlPath, requestBody)
		if err != nil {
			// 检查是否是PathNoRouteError
			if errors.Is(err, cosyError.PathNoRouteError) {
				log.Warnf("PathNoRouteError encountered, retry attempt %d/%d", i+1, maxRetries)
				if i < maxRetries-1 { // 如果不是最后一次重试，则等待
					time.Sleep(retryInterval)
					continue
				}
				// 最后一次重试失败，返回错误
				return nil, err
			}
			// 非PathNoRouteError，直接返回
			return nil, err
		}
		// 成功构建请求，返回
		return req, nil
	}

	// 不应该到达这里
	return req, nil
}
