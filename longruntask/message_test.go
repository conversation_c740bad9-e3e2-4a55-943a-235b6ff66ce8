package longruntask

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/log"
	"cosy/remote"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTaskManager_GetMessages(t *testing.T) {
	tests := []struct {
		TaskId     string
		PageSize   int
		PageNumber int
	}{
		{
			TaskId:     "task-d1ii1jo6psgaesl57rog",
			PageSize:   10,
			PageNumber: 1,
		},
	}
	config.InitConfig()
	client.InitClients()
	remote.InitRegionFailoverService()

	config.Remote.BigModelEndpoint = "http://test-api-ap-southeast-1.qoder.ai/algo"
	taskManager := GetTaskManager()
	for _, tt := range tests {
		resp, err := taskManager.GetMessages(context.Background(), tt.TaskId, tt.PageSize, tt.PageNumber)
		if err != nil {
			assert.Fail(t, "error", err.Error())
		}
		log.Infof("%+v", resp)
	}
}
