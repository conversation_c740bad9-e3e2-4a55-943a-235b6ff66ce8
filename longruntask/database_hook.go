/*
Package longruntask provides database hooks with asynchronous channel-based processing.

This file has been refactored to use a channel-based architecture:
- Hook functions now queue data to channels instead of direct synchronous calls
- A background listener goroutine processes data from channels and performs sync operations
- Provides better performance and resilience by decoupling data collection from sync operations
- Uses context.WithTimeout instead of time.After to avoid GC issues in Go 1.20+

Usage:
1. Call StartSyncListener() during application startup
2. Hook functions will automatically queue data to channels
3. Call StopSyncListener() during application shutdown for graceful cleanup
*/
package longruntask

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/stable"
	"cosy/util/session"
	"sync"
	"time"

	"golang.org/x/exp/slices"
)

var syncToServerSessionModes = []string{
	definition.SessionModeDesign,
	definition.SessionModeLongRunning,
	definition.SessionModeLongRunningSub,
}

// 定义channel数据结构
type SessionSyncData struct {
	Session *definition.ChatSession
}

type RecordSyncData struct {
	Record *definition.ChatRecord
}

type MessageSyncData struct {
	Message *definition.ChatMessage
}

type MessageUpdateSyncData struct {
	Id         string
	SessionId  string
	ToolResult string
}

// 全局channels
var (
	sessionChannel       = make(chan SessionSyncData, 1000)
	recordChannel        = make(chan RecordSyncData, 1000)
	messageChannel       = make(chan MessageSyncData, 1000)
	messageUpdateChannel = make(chan MessageUpdateSyncData, 1000)

	// 监听器控制
	stopListener = make(chan struct{})
	listenerWg   sync.WaitGroup
	listenerOnce sync.Once
)

// StartSyncListener 启动数据库同步监听器
// 应在应用启动时调用，用于启动后台协程监听channels中的数据并进行同步
func StartSyncListener() {
	listenerOnce.Do(func() {
		listenerWg.Add(1)
		stable.GoSafe(context.Background(), func() {
			defer listenerWg.Done()
			log.Info("Database sync listener started")

			mgr := GetTaskManager()

			for {
				select {
				case <-stopListener:
					log.Info("Database sync listener stopped")
					return

				case sessionData := <-sessionChannel:
					if sessionData.Session != nil {
						syncSessionToServer(mgr, sessionData.Session)
					}

				case recordData := <-recordChannel:
					if recordData.Record != nil {
						syncRecordToServer(mgr, recordData.Record)
					}

				case messageData := <-messageChannel:
					if messageData.Message != nil {
						syncMessageToServer(mgr, messageData.Message)
					}

				case updateData := <-messageUpdateChannel:
					syncMessageUpdateToServer(mgr, updateData.Id, updateData.SessionId, updateData.ToolResult)
				}
			}
		}, "questTaskReporter")
	})
}

// StopSyncListener 停止数据库同步监听器
// 应在应用关闭时调用，用于优雅地停止后台监听协程
func StopSyncListener() {
	close(stopListener)
	listenerWg.Wait()
	log.Info("Database sync listener shutdown completed")
}

// 内部同步函数 - Session
func syncSessionToServer(mgr *TaskManager, session *definition.ChatSession) {
	if !IsSessionNeedSyncToServer(session) {
		return
	}

	taskId, err := GetTaskIdBySessionId(context.Background(), session.SessionId)
	if err != nil {
		log.Warnf("Get task id failed, ignore sync, err:%s", err.Error())
		return
	}

	if _, err := mgr.CreateExecutionSession(context.Background(), taskId, *session); err != nil {
		log.Warnf("Create execution session failed, ignore sync. err:%s", err.Error())
		return
	}

	log.Debugf("Session sync completed for sessionId: %s", session.SessionId)
}

// 内部同步函数 - Record
func syncRecordToServer(mgr *TaskManager, record *definition.ChatRecord) {
	if !session.IsQuestTaskSession(record.SessionId) {
		return
	}

	if _, err := mgr.CreateChatRecord(context.Background(), record.SessionId, *record); err != nil {
		log.Warnf("Create chat record failed, ignore sync. err:%s", err.Error())
		return
	}

	log.Debugf("Record sync completed for sessionId: %s", record.SessionId)
}

// 内部同步函数 - Message
func syncMessageToServer(mgr *TaskManager, message *definition.ChatMessage) {
	if !session.IsQuestTaskSession(message.SessionId) {
		return
	}

	if _, err := mgr.UpdateSessionMessages(context.Background(), message.SessionId, *message); err != nil {
		log.Warnf("Update session messages failed, ignore sync. err:%s", err.Error())
		return
	}

	log.Debugf("Message sync completed for sessionId: %s", message.SessionId)
}

// 内部同步函数 - Message Update
func syncMessageUpdateToServer(mgr *TaskManager, id, sessionId, toolResult string) {
	if !session.IsQuestTaskSession(sessionId) {
		return
	}

	if _, err := mgr.UpdateSessionMessagesToolResult(context.Background(), id, sessionId, toolResult); err != nil {
		log.Warnf("Update session messages tool result failed, ignore sync. err:%s", err.Error())
		return
	}

	log.Debugf("Message update sync completed for sessionId: %s", sessionId)
}

func IsSessionNeedSyncToServer(session *definition.ChatSession) bool {
	// 通过sessionType判断
	if session == nil {
		return false
	}
	return session.SessionType == "quest" && slices.Contains(syncToServerSessionModes, session.Mode)
}
func SyncToServerForChatSessionCreateOrUpdate(session *definition.ChatSession) error {
	if session == nil {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	select {
	case sessionChannel <- SessionSyncData{Session: session}:
		log.Debugf("Session data queued for sync: %s", session.SessionId)
	case <-ctx.Done():
		log.Warnf("Failed to queue session data for sync (timeout): %s", session.SessionId)
	}

	return nil
}

func SyncToServerForChatRecordCreatedOrUpdated(record *definition.ChatRecord) error {
	if record == nil {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	select {
	case recordChannel <- RecordSyncData{Record: record}:
		log.Debugf("Record data queued for sync: %s", record.SessionId)
	case <-ctx.Done():
		log.Warnf("Failed to queue record data for sync (timeout): %s", record.SessionId)
	}

	return nil
}

func SyncToServerForChatMessageCreated(message *definition.ChatMessage) error {
	if message == nil {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	select {
	case messageChannel <- MessageSyncData{Message: message}:
		log.Debugf("Message data queued for sync: %s", message.SessionId)
	case <-ctx.Done():
		log.Warnf("Failed to queue message data for sync (timeout): %s", message.SessionId)
	}

	return nil
}

func SyncToServerForChatMessageUpdated(id, sessionId, toolResult string) error {
	if sessionId == "" || id == "" {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	select {
	case messageUpdateChannel <- MessageUpdateSyncData{
		Id:         id,
		SessionId:  sessionId,
		ToolResult: toolResult,
	}:
		log.Debugf("Message update data queued for sync: %s", sessionId)
	case <-ctx.Done():
		log.Warnf("Failed to queue message update data for sync (timeout): %s", sessionId)
	}

	return nil
}
