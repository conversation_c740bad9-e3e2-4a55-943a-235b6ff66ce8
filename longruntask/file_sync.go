package longruntask

import (
	"context"
	"cosy/config"
	"cosy/definition"
	dataexport "cosy/export"
	"cosy/log"
	"cosy/remote"
	"cosy/user"
	"cosy/util"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"code.alibaba-inc.com/cosy/filesync/pkg/filesync"
)

func SyncWorkspace(ctx context.Context, taskId string, localWorkspacePath string, remoteWorkspacePath string) error {
	log.Infof("starting to sync workspace to remote for task: %s, local: %s, remote: %s", taskId, localWorkspacePath, remoteWorkspacePath)
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return errors.New("user not login")
	}
	taskResp := GetTaskManager().GetChatTask(ctx, taskId)
	if !taskResp.Success {
		return errors.New(taskResp.ErrorMessage)
	}
	taskInfo := taskResp.Data.(*definition.ChatTask)
	sshAddress := taskInfo.SSHRelayAddress
	if sshAddress == "" {
		return errors.New("ssh address is empty")
	}

	gitignorePath := filepath.Join(localWorkspacePath, ".gitignore")
	sshPassword, err := remote.CalculateSSHPassword(taskId)
	if err != nil {
		log.Warn("CalculateSSHPassword error: %s", err)
		return err
	}
	opts := &filesync.SyncOptions{
		SyncMode:          filesync.SyncModeFull,
		OverwritePolicy:   filesync.OverwritePolicyAlways,
		GitIgnoreFilePath: gitignorePath,
	}
	cfg := &filesync.Config{
		Endpoint:      sshAddress,
		Timeout:       10 * time.Second,
		TransportType: filesync.TransportSSH,
		SSHMode:       filesync.SSHModeSCP, // 使用SCP模式
		Auth: &filesync.AuthConfig{
			Username: userInfo.Uid,
			Password: sshPassword,
		},
		Logger: log.GetLogger().Desugar(),
	}
	cli, err := filesync.NewSyncClient(cfg)
	if err != nil {
		log.Errorf("failed to create file sync client: %v", err)
		return err
	}
	if err := cli.Connect(ctx); err != nil {
		log.Errorf("failed to connect remote: %v", err)
		return err
	}
	defer cli.Disconnect()

	pushCtx, cancel := context.WithTimeout(ctx, 10*time.Minute)
	defer cancel()

	var localPaths []string
	var remotePaths []string
	addFileSync := func(localPath string, remotePath string) {
		localPaths = append(localPaths, localPath)
		remotePaths = append(remotePaths, remotePath)
	}
	// 同步代码库
	addFileSync(localWorkspacePath, remoteWorkspacePath)
	// 同步VSCode 插件配置
	if userHomePath, err := os.UserHomeDir(); err == nil {
		vscodeExtensionPath := filepath.Join(userHomePath, ".vscode", "extensions", "extensions.json")
		if fileInfo, err := os.Stat(vscodeExtensionPath); err == nil && !fileInfo.IsDir() {
			addFileSync(vscodeExtensionPath, "/root/.vscode/extensions/extensions.json")
		}
	}

	// 同步记忆、代码索引
	if result, err := dataexport.ExportData(dataexport.ExportDataParams{WorkspaceDir: localWorkspacePath}); err != nil {
		log.Errorf("export data failed, workspace dir: %s, %v", localWorkspacePath, err)
	} else {
		addFileSync(result.DataFilePath, "/data/user-memory.tar.zst")
		defer os.Remove(result.DataFilePath)
	}
	// 同步沙箱Token
	if tokenFilePath, err := generateRemoteTokenFile(taskId); err != nil {
		log.Errorf("generate remote token file error: %v", err)
		return err
	} else {
		// 将沙箱Token同步进对应位置
		addFileSync(tokenFilePath, config.GetRemoteAgentUserInfoPath())
		defer os.Remove(tokenFilePath)
	}

	if tempFile, err := os.CreateTemp(util.GetCosyHomePath(), fmt.Sprintf("workspace-sync-%s-*", taskId)); err != nil {
		log.Errorf("create temp file error when create workspace-ready, the err: %v", err)
		return err
	} else {
		// 将本地工作目录信息写入
		_, _ = tempFile.WriteString(localWorkspacePath)
		tempFilePath := tempFile.Name()
		defer os.Remove(tempFilePath)
		// 同步结束标志位
		addFileSync(tempFilePath, "/data/.workspace-ready")
	}

	resultCh, progressCh, err := cli.Push(pushCtx, localPaths, remotePaths, opts)
	if err != nil {
		log.Errorf("failed to push files: %v", err)
		return err
	}
	for {
		select {
		case result, ok := <-resultCh:
			if !ok {
				log.Warnf("resultCh closed")
				return nil
			}
			if result.Success {
				log.Infof("successfully synced workspace to remote for task: %s, local: %s, remote: %s", taskId, localWorkspacePath, remoteWorkspacePath)
				return nil
			}
			log.Errorf("push files failed, localPath: %s, remotePath: %s, errors: %v", localWorkspacePath, remoteWorkspacePath, result.Errors)
			var errs []error
			for _, e := range result.Errors {
				errs = append(errs, &e)
			}
			return errors.Join(errs...)
		case progress, ok := <-progressCh:
			if ok {
				log.Infof("push files progress: %v, current: %s, %d/%d", progress.Stage, progress.CurrentFile, progress.ProcessedFiles, progress.TotalFiles)
			}
		case <-pushCtx.Done():
			log.Warnf("push files canceled: %v", pushCtx.Err())
			return pushCtx.Err()
		}
	}
}

// CheckIfWorkspaceSynced 检查工作空间是否已经同步完成，若BootStatus.CurrentStage==run 则认为是已同步
func CheckIfWorkspaceSynced(ctx context.Context, taskId string) bool {
	resp := GetTaskManager().GetChatTask(ctx, taskId)
	taskData := resp.Data.(*definition.ChatTask)
	if taskData.BootStatus == "" {
		return false
	}
	var detail definition.BootStatusDetail
	if err := json.Unmarshal([]byte(taskData.BootStatus), &detail); err != nil {
		log.Errorf("unmarshal boot status error, the err: %v", err)
		return false
	}
	switch detail.CurrentStage {
	case "prepare", "sync":
		return false
	case "run":
		return true
	}
	return false
}

func generateRemoteTokenFile(taskId string) (string, error) {
	resp, err := remote.RequestRemoteToken()
	if err != nil {
		log.Errorf("request remote token error: %v", err)
		return "", err
	}
	u := user.RemoteAgentUserInfo{
		Id:                 resp.Id,
		Name:               resp.Name,
		Quota:              resp.Quota,
		WhitelistStatus:    resp.WhitelistStatus,
		SecurityOauthToken: resp.SecurityOauthToken,
		RefreshToken:       resp.RefreshToken,
		ExpireTime:         resp.ExpireTime,
		IsSubAccount:       resp.IsSubAccount,
	}
	userBytes, err := json.Marshal(u)
	if err != nil {
		log.Errorf("marshal user info error: %v", err)
		return "", err
	}
	tempFile, err := os.CreateTemp(util.GetCosyHomePath(), fmt.Sprintf("remote-agent-user-info-%s-*", taskId))
	if err != nil {
		log.Errorf("create temp file error when create remote-agent-user-info, the err: %v", err)
		return "", err
	}
	if _, err = tempFile.Write(userBytes); err != nil {
		log.Errorf("write temp file error when create remote-agent-user-info, the err: %v", err)
		return "", err
	}
	return tempFile.Name(), nil
}
