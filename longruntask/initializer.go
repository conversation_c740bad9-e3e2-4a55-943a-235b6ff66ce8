package longruntask

import (
	"context"
	"cosy/config"
	"cosy/log"
	"time"
)

// SSE连接检查的间隔时间，1分钟
const SSECheckInterval = 1 * time.Minute

var (
	// SSE连接检查的上下文和取消函数
	sseCtx    context.Context
	sseCancel context.CancelFunc
	// SSE连接检查的定时器
	sseCheckTimer *time.Timer
)

// InitializeUserTaskSSEConnections 初始化用户任务统计的sse链接
func InitializeUserTaskSSEConnections() {
	log.Info("Initializing user task stats SSE connections")

	// 如果是远程Agent模式，不需要初始化SSE连接
	if config.IsRemoteAgentMode() {
		return
	}

	// 创建带取消的上下文
	sseCtx, sseCancel = context.WithCancel(context.Background())

	// 启动定时检查
	// 如果已经有一个检查器在运行，先停止它
	if sseCheckTimer != nil {
		sseCheckTimer.Stop()
	}

	// 创建定时器
	sseCheckTimer = time.NewTimer(SSECheckInterval)

	// 执行检查
	checkUserTaskSSEConnections()

	// 启动定时检查协程
	go func() {
		for {
			select {
			case <-sseCtx.Done():
				// 上下文取消，停止检查
				if sseCheckTimer != nil {
					sseCheckTimer.Stop()
				}
				log.Info("User task stats SSE connections checker stopped")
				return
			case <-sseCheckTimer.C:
				// 执行检查
				checkUserTaskSSEConnections()
				// 重置定时器
				sseCheckTimer.Reset(SSECheckInterval)
			}
		}
	}()
}

// checkUserTaskSSEConnections 检查所有SSE连接状态
func checkUserTaskSSEConnections() {
	log.Debug("Checking user task stats SSE connections...")

	// 检查用户任务统计SSE连接
	if !userTaskStateRunning.Load() {
		log.Info("User task stats SSE connection is not running, trying to connect")
		// 尝试重新连接
		WatchingUserTaskStatsEvents(sseCtx)
	} else {
		log.Debug("User task stats SSE connection is running")
	}
}

// StopSSEConnections 停止所有SSE连接
func StopSSEConnections() {
	log.Info("Stopping all user task stats SSE connections")

	// 停止所有SSE连接相关的资源
	if sseCancel != nil {
		sseCancel()
	}

	// 停止定时器
	if sseCheckTimer != nil {
		sseCheckTimer.Stop()
		sseCheckTimer = nil
	}
}
