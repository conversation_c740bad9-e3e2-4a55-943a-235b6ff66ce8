package longruntask

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/log"
	"cosy/remote"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTaskManager_GetReport(t *testing.T) {
	tests := []struct {
		TaskId string
	}{
		{
			TaskId: "task-d1ii1jo6psgaesl57rog",
		},
	}
	config.InitConfig()
	client.InitClients()
	remote.InitRegionFailoverService()

	config.Remote.BigModelEndpoint = "http://test-api-ap-southeast-1.qoder.ai/algo"
	taskManager := GetTaskManager()
	for _, tt := range tests {
		resp, err := taskManager.GetReport(context.Background(), tt.TaskId)
		if err != nil {
			assert.Fail(t, "error", err.Error())
		}
		log.Infof("%+v", resp)
	}
}

func TestTaskManager_UpdateReport(t *testing.T) {
	tests := []struct {
		TaskId  string
		Summary string
	}{
		{
			TaskId:  "task-d1ii1jo6psgaesl57rog",
			Summary: "Test summary for task progress report",
		},
	}
	config.InitConfig()
	client.InitClients()
	remote.InitRegionFailoverService()

	config.Remote.BigModelEndpoint = "http://test-api-ap-southeast-1.qoder.ai/algo"
	taskManager := GetTaskManager()
	for _, tt := range tests {
		resp, err := taskManager.UpdateReport(context.Background(), tt.TaskId, tt.Summary)
		if err != nil {
			assert.Fail(t, "error", err.Error())
		}
		log.Infof("%+v", resp)
	}
}
