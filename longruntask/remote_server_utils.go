package longruntask

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	cosyError "cosy/errors"
	"cosy/locale"
	"cosy/log"
	"cosy/remote"
	"cosy/user"
	"cosy/util"
	"encoding/json"
	"errors"
	"io"
	"net/url"
	"time"
)

type ResponseHandlerFn func(bodyBytes []byte, operationName string) (*definition.Response, error)

// RemoteAgentRequestOptions 定义远程代理请求的配置选项
type RemoteAgentRequestOptions struct {
	Method            string            // HTTP方法
	Endpoint          string            // 请求端点
	Payload           interface{}       // 请求payload，可以是HttpPayload或nil
	OperationName     string            // 操作名称，用于日志记录
	RequireAuth       bool              // 是否需要用户登录检查，默认true
	ResponseHandlerFn ResponseHandlerFn // 自定义响应解析器
}

func defaultResponseHandler(bodyBytes []byte, operationName string) (*definition.Response, error) {
	// 首先尝试解析为BaseResponse
	var result definition.BaseResponse
	err := json.Unmarshal(bodyBytes, &result)
	if err != nil {
		log.Errorf("unmarshal response body error when %s, the err: %v", operationName, err)
		return nil, err
	}

	return &definition.Response{
		Success:      result.Code == 200,
		ErrorCode:    result.ErrorCode,
		ErrorMessage: result.Message,
	}, nil
}

// CallRemoteAgent 通用的远程代理请求方法
func CallRemoteAgent(ctx context.Context, options RemoteAgentRequestOptions) *definition.Response {
	// 1. 用户登录检查
	if options.RequireAuth {
		userInfo := user.GetCachedUserInfo()
		if userInfo == nil {
			return buildErrorResponse(cosyError.QuestUserNotLoginError, "user not login")
		}
	}

	// 2. 构建请求payload
	var payload interface{}
	if options.Payload != nil {
		if httpPayload, ok := options.Payload.(remote.HttpPayload); ok {
			payload = httpPayload
		} else {
			// 如果不是HttpPayload类型，则包装成HttpPayload
			payload = remote.HttpPayload{
				Payload:       util.ToJsonStr(options.Payload),
				EncodeVersion: config.Remote.MessageEncode,
			}
		}
	}

	// 3. 构建认证请求
	req, err := remote.BuildBigModelAuthRequest(options.Method, options.Endpoint, payload)
	if err != nil {
		log.Warnf("Build request error for %s, url=%s, error=%v", options.OperationName, options.Endpoint, err)
		if errors.Is(err, cosyError.PathNoRouteError) {
			return buildErrorResponse("", locale.Localize("error_code_init_server_config_failed", definition.LocaleEn))
		}
		return buildErrorResponse("", err.Error())
	}

	// 4. 执行请求并计时
	beginTimeStamp := time.Now()
	resp, err := client.GetRemoteAgentClient().Do(req)
	elapsedTime := time.Since(beginTimeStamp).Milliseconds()
	log.Infof("%s time %d ms", options.OperationName, elapsedTime)

	if err != nil {
		log.Errorf("do big model auth request error when %s, the err: %v", options.OperationName, err)
		var urlError *url.Error
		if errors.As(err, &urlError) && urlError.Timeout() {
			return buildSystemErrorResponse()
		}
		return buildErrorResponse("", err.Error())
	}
	defer resp.Body.Close()

	// 5. 读取响应
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when %s, the err: %v", options.OperationName, err)
		return buildErrorResponse("RequestServerFailed", locale.Localize("error_code_request_server_failed", getLanguage()))
	}

	// 6. 使用自定义解析器或默认解析
	var r *definition.Response
	var handleErr error
	if options.ResponseHandlerFn != nil {
		r, handleErr = options.ResponseHandlerFn(bodyBytes, options.OperationName)
	} else {
		r, handleErr = defaultResponseHandler(bodyBytes, options.OperationName)
	}

	if handleErr != nil {
		log.Warnf("Failed to %s, url=%s, status=%s, responseBody:%s, err=%v", options.OperationName, resp.Request.URL.String(), resp.Status, string(bodyBytes), handleErr)
		return buildErrorResponse("", locale.Localize("error_code_request_server_failed", getLanguage()))
	}
	return r
}

func getLanguage() string {
	originParams := config.GetGlobalConfig()
	// 应该采用Display Language, 展示未发现cosy中获取该配置的方法，FIXME
	if len(originParams.PreferredLanguage) > 0 {
		return originParams.PreferredLanguage
	}
	// 默认采用英文
	return definition.LocaleEn
}
