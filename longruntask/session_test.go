package longruntask

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTaskManager_CreateExecutionSession(t *testing.T) {
	tests := []struct {
		TaskId      string
		Params      definition.ChatSession
		SessionType string
	}{
		{
			TaskId: "task-d1ii1jo6psgaesl57rog",
			Params: definition.ChatSession{
				OrgID:        "",
				UserID:       "d4a29f8b-8730-41cf-8da5-ad0c54b41ef5",
				UserName:     "d4a29f8b-8730-41cf-8da5-ad0c54b41ef5",
				SessionId:    "dfa651ac-3955-4111-96f8-b9f27860403a",
				SessionTitle: "你好，你是谁",
				ProjectId:    "/Users/<USER>/code/owl",
				ProjectURI:   "/Users/<USER>/code/owl",
				ProjectName:  "owl",
				GmtCreate:    1751100369020,
				GmtModified:  1751100369020,
				SessionType:  "assistant",
				Mode:         "agent",
				Version:      "2",
			},
			SessionType: "executions",
		},
		{
			TaskId: "task-d1ii1jo6psgaesl57rog",
			Params: definition.ChatSession{
				OrgID:        "",
				UserID:       "d4a29f8b-8730-41cf-8da5-ad0c54b41ef5",
				UserName:     "d4a29f8b-8730-41cf-8da5-ad0c54b41ef5",
				SessionId:    "94f8243f-9423-4ffb-a49b-c4fcb9c69fec",
				SessionTitle: "你好，你是谁",
				ProjectId:    "/Users/<USER>/code/owl",
				ProjectURI:   "/Users/<USER>/code/owl",
				ProjectName:  "owl",
				GmtCreate:    1751100369020,
				GmtModified:  1751100369020,
				SessionType:  "assistant",
				Mode:         "agent",
				Version:      "2",
			},
			SessionType: "designs",
		},
	}
	config.InitConfig()
	client.InitClients()
	remote.InitRegionFailoverService()

	config.Remote.BigModelEndpoint = "http://test-api-ap-southeast-1.qoder.ai/algo"
	taskManager := GetTaskManager()
	for _, tt := range tests {
		resp, err := taskManager.CreateSession(context.Background(), tt.TaskId, tt.SessionType, tt.Params)
		if err != nil {
			assert.Fail(t, "error", err.Error())
		}
		log.Infof("%+v", resp)
	}
}

func TestTaskManager_CreateSessionRecord(t *testing.T) {
	tests := []struct {
		SessionId string
		Params    definition.ChatRecord
	}{
		{
			SessionId: "dfa651ac-3955-4111-96f8-b9f27860403a",
			Params: definition.ChatRecord{
				RequestId:         "0204bd4d-01b4-48b6-bb68-ba68c6759c20",
				SessionId:         "dfa651ac-3955-4111-96f8-b9f27860403a",
				ChatTask:          "FREE_INPUT",
				ChatContext:       "{\"text\":\"你好，你是谁\",\"workspaceLanguages\":[\"python\"],\"language\":\"\",\"localeLang\":\"zh\",\"preferredLanguage\":\"zh\",\"features\":[],\"activeFilePath\":\"/Users/<USER>/code/owl/.gitignore\",\"chatPrompt\":\"\",\"imageUrls\":null}",
				SystemRoleContent: "",
				Question:          "你好，你是谁",
				Answer:            "",
				LikeStatus:        0,
				GmtCreate:         1751100369024,
				GmtModified:       1751100369024,
				FinishStatus:      0,
				FilterStatus:      "",
				ErrorResult:       "",
				CodeLanguage:      ".gitignore (GitIgnore)",
				Extra:             "{\"context\":[],\"modelConfig\":{\"key\":\"dashscope_qwen_plus_20250428_thinking\"}}",
				SessionType:       "assistant",
				Summary:           "",
				IntentionType:     "common_agent",
				ReasoningContent:  "",
				Mode:              "agent",
				ChatPrompt:        "",
			},
		},
	}
	config.InitConfig()
	client.InitClients()
	remote.InitRegionFailoverService()

	config.Remote.BigModelEndpoint = "http://test-api-ap-southeast-1.qoder.ai/algo"
	taskManager := GetTaskManager()
	for _, tt := range tests {
		resp, err := taskManager.CreateChatRecord(context.Background(), tt.SessionId, tt.Params)
		if err != nil {
			assert.Fail(t, "error", err.Error())
		}
		log.Infof("%+v", resp)
	}
}

func TestTaskManager_GetSessionRecords(t *testing.T) {
	tests := []struct {
		SessionId  string
		PageSize   int
		PageNumber int
	}{
		{
			SessionId:  "dfa651ac-3955-4111-96f8-b9f27860403a",
			PageSize:   10,
			PageNumber: 1,
		},
	}
	config.InitConfig()
	client.InitClients()
	remote.InitRegionFailoverService()

	config.Remote.BigModelEndpoint = "http://test-api-ap-southeast-1.qoder.ai/algo"
	taskManager := GetTaskManager()
	for _, tt := range tests {
		resp, err := taskManager.getSessionRecordsSinglePage(context.Background(), tt.SessionId, tt.PageSize, tt.PageNumber)
		if err != nil {
			assert.Fail(t, "error", err.Error())
		}
		log.Infof("%+v", resp)
	}
}

func TestTaskManager_GetSessionMessages(t *testing.T) {
	tests := []struct {
		SessionId  string
		PageSize   int
		PageNumber int
	}{
		{
			SessionId:  "d1mi0178oeq4re7kp8k0",
			PageSize:   10,
			PageNumber: 1,
		},
	}
	config.InitConfig()
	client.InitClients()
	remote.InitRegionFailoverService()

	config.Remote.BigModelEndpoint = "http://test-api-ap-southeast-1.qoder.ai/algo"
	taskManager := GetTaskManager()
	for _, tt := range tests {
		resp, err := taskManager.getSessionMessagesSinglePage(context.Background(), tt.SessionId, "", tt.PageSize, tt.PageNumber)
		if err != nil {
			assert.Fail(t, "error", err.Error())
		}
		log.Infof("%+v", resp)
	}
}
