package longruntask

import (
	"context"
	"cosy/definition"
	"cosy/util/cache"
	"fmt"
	"sync"
)

var executionSessionIdTaskIdCacheLock sync.RWMutex
var executionSessionIdTaskIdCache = cache.NewLruCache(20)
var finishedTaskInfoCache = cache.NewLruCache(20)
var fileChangesCache = cache.NewLruCache(10)

func SetExecutionSessionIdTaskIdCache(sessionId, taskId string) {
	executionSessionIdTaskIdCacheLock.Lock()
	defer executionSessionIdTaskIdCacheLock.Unlock()
	executionSessionIdTaskIdCache.Add(sessionId, taskId)
}

func DeleteExecutionSessionIdTaskIdCacheBySessionId(sessionId string) {
	executionSessionIdTaskIdCacheLock.Lock()
	defer executionSessionIdTaskIdCacheLock.Unlock()
	executionSessionIdTaskIdCache.Remove(sessionId)
}

func GetExecutionSessionIdTaskIdCache(sessionId string) (string, bool) {
	executionSessionIdTaskIdCacheLock.RLock()
	defer executionSessionIdTaskIdCacheLock.RUnlock()
	if v, ok := executionSessionIdTaskIdCache.Get(sessionId); ok {
		return v.(string), true
	}

	return "", false
}

func SetFinishedTaskInfoCache(taskId string, taskInfo *definition.ChatTask) {
	finishedTaskInfoCache.Add(taskId, taskInfo)
}

func GetFinishedTaskInfoCache(taskId string) (*definition.ChatTask, bool) {
	if v, ok := finishedTaskInfoCache.Get(taskId); ok {
		return v.(*definition.ChatTask), true
	}

	return nil, false
}

func IsTaskFinished(status string) bool {
	return status == definition.ChatTaskStatusRejected ||
		status == definition.ChatTaskStatusAccepted ||
		status == definition.ChatTaskStatusCancelled
}

func fileChangesCacheKey(taskId string, pageNumber int, pageSize int) string {
	return taskId + ":" + string(rune(pageNumber)) + ":" + string(rune(pageSize))
}

func SetFinishedTaskFileChangesCache(taskId string, pageNumber int, pageSize int, fileChanges *PagedFileChangesResp) {
	fileChangesCache.Add(fileChangesCacheKey(taskId, pageNumber, pageSize), fileChanges)
}

func GetFinishedFileChangesCache(taskId string, pageNumber int, pageSize int) (*PagedFileChangesResp, bool) {
	if v, ok := fileChangesCache.Get(fileChangesCacheKey(taskId, pageNumber, pageSize)); ok {
		return v.(*PagedFileChangesResp), true
	}

	return nil, false
}
func (t *TaskManager) StoreChatTaskCache(ctx context.Context, chatTask definition.ChatTask) (bool, error) {
	if chatTask.DesignSessionId == "" && chatTask.ExecutionSessionId == "" {
		return false, fmt.Errorf("cache chat task failed. Execution and design sessionId cannot be both empty")
	}

	// 只缓存基本信息
	basicInfo := definition.ChatTaskBasicCacheInfo{
		Id:                 chatTask.Id,
		WorkspaceId:        chatTask.WorkspaceId,
		MachineId:          chatTask.MachineId,
		Name:               chatTask.Name,
		DesignSessionId:    chatTask.DesignSessionId,
		ExecutionSessionId: chatTask.ExecutionSessionId,
		ExecutionRequestId: chatTask.ExecutionRequestId,
		Query:              chatTask.Query,
	}

	if chatTask.DesignSessionId != "" {
		t.taskCache.Store(chatTask.DesignSessionId, basicInfo)
	}

	if chatTask.ExecutionSessionId != "" {
		t.taskCache.Store(chatTask.ExecutionSessionId, basicInfo)
		SetExecutionSessionIdTaskIdCache(chatTask.ExecutionSessionId, chatTask.Id)
	}

	return true, nil
}

func (t *TaskManager) LoadChatTaskCache(ctx context.Context, sessionId string) (definition.ChatTaskBasicCacheInfo, error) {
	var basicInfo definition.ChatTaskBasicCacheInfo

	if sessionId == "" {
		return basicInfo, fmt.Errorf("sessionId cannot be empty")
	}

	task, exists := t.taskCache.Load(sessionId)
	if !exists {
		return basicInfo, fmt.Errorf("no cached task found for sessionId: %s", sessionId)
	}

	basicInfo, ok := task.(definition.ChatTaskBasicCacheInfo)
	if !ok {
		return basicInfo, fmt.Errorf("cached value is not a ChatTaskBasicCacheInfo for sessionId: %s", sessionId)
	}

	return basicInfo, nil
}
