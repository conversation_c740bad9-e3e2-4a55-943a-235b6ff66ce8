package longruntask

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/remote"
	"cosy/util"
	"fmt"
	"net/http"
	"sync"
)

type TaskManager struct {
	httpClient *http.Client
	taskCache  sync.Map
}

var TaskMgr *TaskManager

func InitTaskManager() {
	TaskMgr = &TaskManager{
		httpClient: client.GetRemoteAgentClient(),
		taskCache:  sync.Map{},
	}
}

func GetTaskManager() *TaskManager {
	if TaskMgr == nil {
		InitTaskManager()
	}
	return TaskMgr
}

type SyncDesignContentParams struct {
	DesignData       string `json:"designData,omitempty"`
	DesignFile       string `json:"designFile,omitempty"`
	UserQuery        string `json:"userQuery,omitempty"`
	UserRequirements string `json:"userRequirements,omitempty"`
}

type TaskServiceCommonResponse struct {
	Code      int            `json:"code"`
	ErrorCode string         `json:"errorCode"`
	Message   string         `json:"message"`
	RequestId string         `json:"requestId"`
	Data      map[string]any `json:"data"`
}

type FileChangesResponse struct {
	Code      int                  `json:"code"`
	ErrorCode string               `json:"errorCode"`
	Message   string               `json:"message"`
	RequestId string               `json:"requestId"`
	Data      PagedFileChangesResp `json:"data"`
}

func (t *TaskManager) SyncDesignDoc(ctx context.Context, taskId string, params *SyncDesignContentParams) (TaskServiceCommonResponse, error) {
	commonResp := TaskServiceCommonResponse{}
	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(params),
		EncodeVersion: config.Remote.MessageEncode,
	}
	endPoint := fmt.Sprintf(definition.DesignEndpoint, taskId)
	if err := t.commonTaskCall(ctx, http.MethodPost, endPoint, httpPayload, &commonResp); err != nil {
		return commonResp, err
	}

	return commonResp, nil
}

func (t *TaskManager) GetDesignDoc(ctx context.Context, taskId string) (TaskServiceCommonResponse, error) {
	commonResp := TaskServiceCommonResponse{}

	endPoint := fmt.Sprintf(definition.DesignEndpoint, taskId)
	if err := t.commonTaskCall(ctx, http.MethodGet, endPoint, nil, &commonResp); err != nil {
		return commonResp, err
	}

	return commonResp, nil
}
