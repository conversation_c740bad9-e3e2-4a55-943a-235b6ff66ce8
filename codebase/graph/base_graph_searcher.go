package graph

import (
	"context"
	"cosy/ide"
	"cosy/indexing/chat_indexing"
	"cosy/log"
	"cosy/sls"
	"cosy/util"
	"cosy/util/graph"
	"errors"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage"
	"github.com/google/uuid"

	"code.alibaba-inc.com/cosy/lingma-codebase-graph/searcher"
)

const (
	Builtin = "builtin"
	Ide     = "ide"
	Unknown = "unknown"
)

type BaseGraphSearcher struct {
	graphFileIndexer *chat_indexing.GraphFileIndexer
}

func NewBaseGraphSearcher(graphFileIndexer *chat_indexing.GraphFileIndexer) *BaseGraphSearcher {
	return &BaseGraphSearcher{
		graphFileIndexer: graphFileIndexer,
	}
}

// 通用分发函数，减少重复逻辑
func dispatchGraphOp[P any, R any](
	s *BaseGraphSearcher,
	ctx context.Context,
	params P,
	filePath string,
	eventType string,
	builtinChecker func(string) storage.GraphStore,
	ideChecker func(string) bool,
	builtinImpl func(context.Context, P, storage.GraphStore) (R, error),
	ideImpl func(context.Context, P) (R, error),
	resultCount func(R) int,
	getWorkspacePath func(P) string,
) (R, error) {
	var zero R
	if filePath == "" {
		err := errors.New("invalid params")
		go s.addGraphSearcherSlsLog(ctx, getWorkspacePath(params), "", eventType, Unknown, params, 0, 0, err)
		return zero, err
	}
	language := util.GetLanguageByFilePath(filePath)
	if graphStore := builtinChecker(filePath); graphStore != nil {
		startTime := time.Now().UnixMicro()
		res, err := builtinImpl(ctx, params, graphStore)
		cost := time.Now().UnixMicro() - startTime
		go s.addGraphSearcherSlsLog(ctx, getWorkspacePath(params), language, eventType, Builtin, params, resultCount(res), cost, err)
		return res, err
	}
	if ideChecker(filePath) {
		startTime := time.Now().UnixMicro()
		res, err := ideImpl(ctx, params)
		cost := time.Now().UnixMicro() - startTime
		go s.addGraphSearcherSlsLog(ctx, getWorkspacePath(params), language, eventType, Ide, params, resultCount(res), cost, err)
		return res, err
	}
	err := errors.New("both builtin and ide are disable")
	go s.addGraphSearcherSlsLog(ctx, getWorkspacePath(params), language, eventType, Unknown, params, 0, 0, err)
	return zero, err
}

func (s *BaseGraphSearcher) LocateNode(ctx context.Context, params LocateNodeQuery) ([]Node, error) {
	return dispatchGraphOp(
		s,
		ctx, params, params.FilePath, sls.EventTypeChatCodebaseGraphLocateResult,
		s.judgeCanUseBuiltinNode, graph.JudgeCanUseIdeNode,
		s.locateNodeByBuiltin, s.locateNodeByIde,
		func(res []Node) int { return len(res) },
		func(p LocateNodeQuery) string { return p.WorkspacePath },
	)
}

func (s *BaseGraphSearcher) TravelGraph(ctx context.Context, params TravelGraphQuery) (GraphPath, error) {
	return dispatchGraphOp(
		s,
		ctx, params, params.FilePath, sls.EventTypeChatCodebaseGraphTravelResult,
		s.judgeCanUseBuiltinGraph, graph.JudgeCanUseIdeGraph,
		s.travelGraphByBuiltin, s.travelGraphByIde,
		func(res GraphPath) int { return len(res.Paths) },
		func(p TravelGraphQuery) string { return p.WorkspacePath },
	)
}

func (s *BaseGraphSearcher) ExpandGraph(ctx context.Context, params ExpandGraphQuery) (Graph, error) {
	return dispatchGraphOp(
		s,
		ctx, params, params.FilePath, sls.EventTypeChatCodebaseGraphExpandResult,
		s.judgeCanUseBuiltinGraph, graph.JudgeCanUseIdeGraph,
		s.expandGraphByBuiltin, s.expandGraphByIde,
		func(res Graph) int { return len(res.Edges) },
		func(p ExpandGraphQuery) string { return p.WorkspacePath },
	)
}

func (s *BaseGraphSearcher) MergeGraph(ctx context.Context, graphs []Graph) (Graph, error) {
	res := Graph{
		Edges: []Edge{},
		Nodes: make(map[string]Node),
	}

	nodeKey := make(map[string]bool)
	relationKey := make(map[string]bool)
	for _, graph := range graphs {
		for _, edge := range graph.Edges {
			key := edge.SourceId + edge.EdgeType + edge.TargetId
			if relationKey[key] {
				continue
			}
			res.Edges = append(res.Edges, edge)
			relationKey[key] = true
		}
		for _, node := range graph.Nodes {
			if nodeKey[node.NodeId] {
				continue
			}
			res.Nodes[node.NodeId] = node
			nodeKey[node.NodeId] = true
		}
	}
	return res, nil
}

func (s *BaseGraphSearcher) locateNodeByBuiltin(ctx context.Context, params LocateNodeQuery, graphStore storage.GraphStore) ([]Node, error) {
	res := []Node{}
	retriever := searcher.NewBaseGraphRetriever(graphStore)
	nodes, err := retriever.FindNode(ctx, searcher.FindNodeQuery{
		FilePath:    params.FilePath,
		StartLine:   params.StartLine,
		EndLine:     params.EndLine,
		StartOffset: -1,
		EndOffset:   -1,
	})
	if err == nil && len(nodes) > 0 {
		for _, node := range nodes {
			res = append(res, convertFromBuiltinSymbol(node))
		}
	}
	return res, err
}

func (s *BaseGraphSearcher) locateNodeByIde(ctx context.Context, params LocateNodeQuery) ([]Node, error) {
	res := []Node{}

	// 尝试使用 IDE 行号定位
	ideQuery := ide.IdeSearchSymbolRequest{
		WorkspacePath:  params.WorkspacePath,
		Filepath:       params.FilePath,
		MaxResultCount: DefaultSymbolMaxCount,
		StartLine:      params.StartLine,
		EndLine:        params.EndLine,
	}
	ideResponse, err := ide.NewIdeSearcher().SearchSymbolByIde(ctx, ideQuery)
	if err == nil && len(ideResponse.Symbols) > 0 {
		for _, sym := range ideResponse.Symbols {
			res = append(res, convertFromIdeSymbol(sym))
		}
	}
	return res, err
}

func (s *BaseGraphSearcher) travelGraphByBuiltin(ctx context.Context, params TravelGraphQuery, graphStore storage.GraphStore) (GraphPath, error) {
	res := GraphPath{
		Nodes: map[string]Node{},
		Paths: [][]string{},
	}
	retriever := searcher.NewBaseGraphRetriever(graphStore)
	builtinNodes, err := retriever.FindNode(ctx, searcher.FindNodeQuery{
		FilePath:    params.FilePath,
		StartLine:   -1,
		EndLine:     -1,
		StartOffset: params.StartOffset,
		EndOffset:   params.EndOffset,
	})

	if err != nil {
		return res, err
	}

	if len(builtinNodes) == 0 {
		return res, errors.New("node not found")
	}

	// 选择最合适的节点
	bestNode := s.findBestMatchingNode(builtinNodes, params.StartOffset, params.EndOffset)

	graphPaths, err := retriever.TraverseGraph(ctx, bestNode.NodeId, 1, []string{}, []string{}, params.Reverse)
	if err != nil {
		return res, err
	}
	for _, path := range graphPaths {
		for _, node := range path.Nodes {
			res.Nodes[node.NodeId] = convertFromBuiltinSymbol(node)
		}
		res.Paths = append(res.Paths, path.Paths)
	}
	return res, nil
}

// findBestMatchingNode 根据匹配度选择最合适的节点
func (s *BaseGraphSearcher) findBestMatchingNode(nodes []storage.GraphNode, targetStart, targetEnd int32) storage.GraphNode {
	if len(nodes) == 1 {
		return nodes[0]
	}

	bestNode := nodes[0]
	bestScore := s.calculateMatchScore(nodes[0], targetStart, targetEnd)

	for i := 1; i < len(nodes); i++ {
		score := s.calculateMatchScore(nodes[i], targetStart, targetEnd)
		if score > bestScore {
			bestScore = score
			bestNode = nodes[i]
		}
	}

	return bestNode
}

// calculateMatchScore 计算节点与目标范围的匹配度
func (s *BaseGraphSearcher) calculateMatchScore(node storage.GraphNode, targetStart, targetEnd int32) float64 {
	nodeStart := node.Position.StartOffset
	nodeEnd := node.Position.EndOffset

	// 计算重叠区域
	overlapStart := nodeStart
	if targetStart > nodeStart {
		overlapStart = targetStart
	}
	overlapEnd := nodeEnd
	if targetEnd < nodeEnd {
		overlapEnd = targetEnd
	}

	// 如果没有重叠，返回很低的分数
	if overlapStart >= overlapEnd {
		return 0.0
	}

	// 计算各种指标
	overlapSize := float64(overlapEnd - overlapStart)
	targetSize := float64(targetEnd - targetStart)
	nodeSize := float64(nodeEnd - nodeStart)

	// 重叠比例（相对于目标范围）
	overlapRatio := overlapSize / targetSize

	// 节点大小相对于目标范围的比例
	sizeRatio := nodeSize / targetSize

	// 计算匹配度分数
	var score float64

	// 如果节点完全包含目标范围
	if nodeStart <= targetStart && nodeEnd >= targetEnd {
		// 完全包含的情况下，节点越小越好（减少无效空间）
		// 使用反比关系，节点越接近目标大小，分数越高
		if sizeRatio <= 1.0 {
			score = 1.0 // 完美匹配
		} else {
			// 节点越大，分数越低，但仍然给予较高分数因为完全包含
			score = 0.8/sizeRatio + 0.2
		}
	} else if targetStart <= nodeStart && targetEnd >= nodeEnd {
		// 目标范围完全包含节点
		score = 0.6 * overlapRatio
	} else {
		// 部分重叠的情况
		score = 0.4 * overlapRatio
	}

	// 对于重叠比例高的情况给予额外奖励
	if overlapRatio > 0.8 {
		score += 0.1
	}

	return score
}

func (s *BaseGraphSearcher) travelGraphByIde(ctx context.Context, params TravelGraphQuery) (GraphPath, error) {
	res := GraphPath{
		Nodes: map[string]Node{},
		Paths: [][]string{},
	}

	count := ide.RelationshipLimit{
		Extend:     DefaultRelationMaxCount,
		Implement:  DefaultRelationMaxCount,
		MethodCall: DefaultRelationMaxCount,
		Reference:  DefaultRelationMaxCount,
	}
	if params.Reverse {
		count = ide.RelationshipLimit{
			ExtendBy:     DefaultRelationMaxCount,
			ImplementBy:  DefaultRelationMaxCount,
			MethodCallBy: DefaultRelationMaxCount,
			ReferenceBy:  DefaultRelationMaxCount,
		}
	}

	ideSubGraph, err := ide.NewIdeSearcher().SearchRelationByIde(ctx, ide.IdeSearchRelationRequest{
		WorkspacePath:     params.WorkspacePath,
		Filepath:          params.FilePath,
		StartOffset:       params.StartOffset,
		EndOffset:         params.EndOffset,
		RelationshipLimit: count,
	})
	if err == nil {
		resGraph := convertFromIdeGraph(ideSubGraph)
		for _, edge := range resGraph.Edges {
			res.Paths = append(res.Paths, []string{edge.SourceId, edge.EdgeType, edge.TargetId})
		}
		for _, node := range resGraph.Nodes {
			res.Nodes[node.NodeId] = node
		}
		return res, nil
	}
	return res, err
}

func (s *BaseGraphSearcher) expandGraphByBuiltin(ctx context.Context, params ExpandGraphQuery, graphStore storage.GraphStore) (Graph, error) {
	res := []Graph{}
	retriever := searcher.NewBaseGraphRetriever(graphStore)
	builtinNodes, err := retriever.FindNode(ctx, searcher.FindNodeQuery{
		FilePath:    params.FilePath,
		StartLine:   params.StartLine,
		EndLine:     params.EndLine,
		StartOffset: -1,
		EndOffset:   -1,
	})
	if err == nil {
		if len(builtinNodes) != 0 {
			for _, builtinNode := range builtinNodes {
				subgraph, err := retriever.ExtractSubgraph(ctx, builtinNode.NodeId, 1, []string{}, []string{})
				if err == nil {
					tempGraph := Graph{
						Nodes: make(map[string]Node),
						Edges: make([]Edge, 0, len(subgraph.Edges)),
					}
					for _, node := range subgraph.Nodes {
						tempGraph.Nodes[node.NodeId] = convertFromBuiltinSymbol(node)
					}
					for _, edge := range subgraph.Edges {
						tempGraph.Edges = append(tempGraph.Edges, convertFromBuiltinEdge(edge))
					}
					res = append(res, tempGraph)
				} else {
					log.Warn("[codebase-graph] extract subgraph err when expand graph", err)
				}
			}
		} else {
			log.Warn("[codebase-graph] can not locate node when expand graph", params)
		}
	} else {
		log.Warn("[codebase-graph] locate node err when expand graph", err)
	}

	return s.MergeGraph(ctx, res)
}

func (s *BaseGraphSearcher) expandGraphByIde(ctx context.Context, params ExpandGraphQuery) (Graph, error) {
	res := []Graph{}
	ideSearcher := ide.NewIdeSearcher()
	ideNodes, err := ideSearcher.SearchSymbolByIde(ctx, ide.IdeSearchSymbolRequest{
		WorkspacePath:  params.WorkspacePath,
		Filepath:       params.FilePath,
		StartLine:      params.StartLine,
		EndLine:        params.EndLine,
		MaxResultCount: DefaultSymbolMaxCount,
	})
	if err != nil {
		log.Warn("[codebase-graph] search symbol by ide err when locate node", err)
		return Graph{}, err
	}
	if len(ideNodes.Symbols) == 0 {
		log.Warn("can not locate node when expand graph", params)
	}

	for _, ideNode := range ideNodes.Symbols {
		symbol := convertFromIdeSymbol(ideNode)
		// 二/三方包
		if !strings.HasPrefix(ideNode.Filepath, params.WorkspacePath) {
			tempGraph := Graph{
				Nodes: make(map[string]Node),
				Edges: []Edge{},
			}
			tempGraph.Nodes[symbol.NodeId] = symbol
			res = append(res, tempGraph)
			continue
		}

		ideSubGraph, err := ide.NewIdeSearcher().SearchRelationByIde(ctx, ide.IdeSearchRelationRequest{
			WorkspacePath: params.WorkspacePath,
			Filepath:      params.FilePath,
			StartOffset:   ideNode.StartOffset,
			EndOffset:     ideNode.EndOffset,
			RelationshipLimit: ide.RelationshipLimit{
				Extend:       DefaultRelationMaxCount,
				Implement:    DefaultRelationMaxCount,
				MethodCall:   DefaultRelationMaxCount,
				Reference:    DefaultRelationMaxCount,
				ExtendBy:     DefaultRelationMaxCount,
				ImplementBy:  DefaultRelationMaxCount,
				MethodCallBy: DefaultRelationMaxCount,
				ReferenceBy:  DefaultRelationMaxCount,
			},
		})

		if err != nil {
			log.Warn("[codebase-graph] search relation by ide err when expand graph", err)
			continue
		}
		res = append(res, convertFromIdeGraph(ideSubGraph))
	}

	return s.MergeGraph(ctx, res)
}

func (s *BaseGraphSearcher) judgeCanUseBuiltinNode(fileAbsPath string) storage.GraphStore {
	if !graph.GetGlobalGraphSwitch() {
		log.Infof("[codebase-graph] graph indexing is disabled")
		return nil
	}
	if s.graphFileIndexer == nil {
		log.Warn("[codebase-graph] graph indexer is nil")
		return nil
	}

	graphStore := s.graphFileIndexer.GetGraphStore("")
	ext := strings.ToLower(filepath.Ext(fileAbsPath))
	if _, ok := graph.GetAcceptSearchNodeLanguageExt()[ext]; ok {
		if graphStore != nil {
			record, err := graphStore.FindWorkspaceRecord(nil, nil)
			if err != nil {
				log.Warn("[codebase-graph] find workspace record err when judge can use builtin node", err)
				return nil
			}
			if len(record) < 2 {
				log.Info("[codebase-graph] builtin graph is not ready", fileAbsPath)
				return nil
			}
			return graphStore
		} else {
			log.Warn("[codebase-graph] can not find graphStore", fileAbsPath)
			return nil
		}
	}
	return nil
}

func (s *BaseGraphSearcher) judgeCanUseBuiltinGraph(fileAbsPath string) storage.GraphStore {
	if !graph.GetGlobalGraphSwitch() {
		log.Infof("[codebase-graph] graph indexing is disabled")
		return nil
	}
	if s.graphFileIndexer == nil {
		log.Warn("[codebase-graph] graph indexer is nil")
		return nil
	}
	graphStore := s.graphFileIndexer.GetGraphStore("")
	ext := strings.ToLower(filepath.Ext(fileAbsPath))
	if _, ok := graph.GetAcceptSearchRelationLanguageExt()[ext]; ok {
		if graphStore != nil {
			record, err := graphStore.FindWorkspaceRecord(nil, nil)
			if err != nil {
				log.Warn("[codebase-graph] find workspace record err when judge can use builtin node", err)
				return nil
			}
			if len(record) < 3 {
				log.Info("[codebase-graph] builtin graph is not ready", fileAbsPath)
				return nil
			}
			return graphStore
		} else {
			log.Warn("[codebase-graph] can not find graphStore", fileAbsPath)
			return nil
		}
	}
	return nil
}

func (s *BaseGraphSearcher) addGraphSearcherSlsLog(ctx context.Context, workspace, language, eventType, graphType string, params interface{}, resultCount int, cost int64, err error) {
	requestId := uuid.NewString()

	eventData := map[string]string{
		"workspace":   workspace,
		"params":      util.ToJsonStr(params),
		"graphType":   graphType,
		"success":     strconv.FormatBool(err == nil),
		"resultCount": strconv.Itoa(resultCount),
		"language":    language,
		"cost":        strconv.FormatInt(cost, 10),
	}
	if err != nil {
		eventData["error"] = err.Error()
	}

	sls.Report(eventType, requestId, eventData)
}
