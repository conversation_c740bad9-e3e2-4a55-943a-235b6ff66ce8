package recommend

import (
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/searcher"
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage"
	"context"
	"cosy/chat/chains/common"
	"cosy/ide"
	"cosy/indexing/chat_indexing"
	"cosy/log"
	"cosy/sls"
	"cosy/user"
	"cosy/util/graph"
	"errors"
	"github.com/google/uuid"
	"path/filepath"
	"runtime/debug"
	"strings"
)

type BaseRecommender struct {
	graphFileIndexer *chat_indexing.GraphFileIndexer
}

func NewBaseRecommender(graphFileIndexer *chat_indexing.GraphFileIndexer) *BaseRecommender {
	return &BaseRecommender{
		graphFileIndexer: graphFileIndexer,
	}
}

func (r *BaseRecommender) RecommendFile(ctx context.Context, params *RecommendParams) ([]RecommendationFile, error) {
	var panicErr error
	defer func() {
		if r := recover(); r != nil {
			panicStr := string(debug.Stack())
			data := make(map[string]string)
			data["panic_msg"] = panicStr
			data["function"] = "RecommendFile"
			userType := user.GetUserType()
			data["user_type"] = userType
			log.Error("[codebase-graph] recommend recover panic", panicStr)
			sls.Report(sls.EventTypeChatCodebaseGraphPanic, uuid.NewString(), data)
			panicErr = errors.New("panic recovered in RecommendFile")
		}
	}()

	requestId, b := ctx.Value(common.KeyRequestId).(string)
	if !b {
		requestId = uuid.NewString()
	}
	var files []RecommendationFile
	cacheKey := AssembleCacheKey(params.WorkspacePath, params.FilePath)
	if info, found := RecommenderCache.Get(cacheKey); found {
		files = info.([]RecommendationFile)
		log.Debug("["+requestId+"] "+"find recommend file by cache success: ", files)
	} else {
		var err error
		files, err = r.doRecommendFile(ctx, params)
		if err == nil {
			RecommenderCache.SetDefault(cacheKey, files)
			log.Debug("["+requestId+"] "+"find recommend file by fileContent success: ", files)
		} else {
			log.Error("["+requestId+"] "+"find recommend file by fileContent failed: ", err)
		}
	}
	if panicErr != nil {
		log.Error("["+requestId+"] "+"find recommend file by fileContent panic: ", panicErr)
		return nil, panicErr
	}

	return files, nil
}

func (r *BaseRecommender) doRecommendFile(ctx context.Context, params *RecommendParams) ([]RecommendationFile, error) {
	if graphStore := r.judgeCanUseBuiltinGraph(params.FilePath); graphStore != nil {
		res, err := r.recommendByBuiltin(ctx, params, graphStore)
		return res, err
	}
	if graph.JudgeCanUseIdeGraph(params.FilePath) {
		res, err := r.recommendByIde(ctx, params)
		return res, err
	}

	return []RecommendationFile{}, NotSupportErr
}

func (recommender *BaseRecommender) recommendByBuiltin(ctx context.Context, params *RecommendParams, graphStore storage.GraphStore) ([]RecommendationFile, error) {
	res := []RecommendationFile{}
	retriever := searcher.NewBaseGraphRetriever(graphStore)
	builtinNodes, err := retriever.FindNode(ctx, searcher.FindNodeQuery{
		FilePath:    params.FilePath,
		StartLine:   -1,
		EndLine:     -1,
		StartOffset: -1,
		EndOffset:   -1,
	})
	subMap := make(map[string]int)
	if err == nil {
		if len(builtinNodes) != 0 {
			for _, builtinNode := range builtinNodes {
				subgraph, err := retriever.ExtractSubgraph(ctx, builtinNode.NodeId, 1, []string{}, []string{})
				if err == nil {
					for _, node := range subgraph.Nodes {
						if count, ok := subMap[node.FileAbsPath]; ok {
							subMap[node.FileAbsPath] = count + 1
						} else {
							subMap[node.FileAbsPath] = 1
						}
					}
				} else {
					log.Warn("[codebase-graph] extract subgraph err when expand graph", err)
				}
			}
		} else {
			log.Warn("[codebase-graph] can not locate node when expand graph", params)
		}
	} else {
		log.Warn("[codebase-graph] locate node err when expand graph", err)
	}

	for filePath, count := range subMap {
		res = append(res, RecommendationFile{
			FilePath: filePath,
			Score:    float64(count),
		})
	}
	return SortAndTrimByScore(res, params.MaxCount), nil
}

func (r *BaseRecommender) recommendByIde(ctx context.Context, params *RecommendParams) ([]RecommendationFile, error) {
	res := []RecommendationFile{}
	ideSearcher := ide.NewIdeSearcher()
	ideNodes, err := ideSearcher.SearchSymbolByIde(ctx, ide.IdeSearchSymbolRequest{
		WorkspacePath: params.WorkspacePath,
		Filepath:      params.FilePath,
		StartLine:     0,
		EndLine:       0,
	})
	if err != nil {
		log.Warn("[codebase-graph] search symbol by ide err when locate node", err)
		return res, err
	}
	if len(ideNodes.Symbols) == 0 {
		log.Warn("can not locate node when expand graph", params)
		return res, nil
	}
	subMap := make(map[string]int)
	for _, ideNode := range ideNodes.Symbols {
		ideSubGraph, err := ide.NewIdeSearcher().SearchRelationByIde(ctx, ide.IdeSearchRelationRequest{
			WorkspacePath: params.WorkspacePath,
			Filepath:      params.FilePath,
			StartOffset:   ideNode.StartOffset,
			EndOffset:     ideNode.EndOffset,
			RelationshipLimit: ide.RelationshipLimit{
				Extend:       5,
				Implement:    5,
				MethodCall:   10,
				Reference:    10,
				ExtendBy:     5,
				ImplementBy:  5,
				MethodCallBy: 10,
				ReferenceBy:  10,
			},
		})

		if err != nil {
			log.Warn("[codebase-graph] search relation by ide err when expand graph", err)
			continue
		}
		for _, node := range ideSubGraph.Relationships.ExtendBy {
			if count, ok := subMap[node.Filepath]; ok {
				subMap[node.Filepath] = count + 1
			} else {
				subMap[node.Filepath] = 1
			}
		}
	}

	for filePath, count := range subMap {
		res = append(res, RecommendationFile{
			FilePath: filePath,
			Score:    float64(count),
		})
	}
	return SortAndTrimByScore(res, params.MaxCount), nil
}

func (r *BaseRecommender) judgeCanUseBuiltinGraph(fileAbsPath string) storage.GraphStore {
	if !graph.GetGlobalGraphSwitch() {
		log.Infof("[codebase-graph] graph indexing is disabled")
		return nil
	}
	if r.graphFileIndexer == nil {
		log.Warn("[codebase-graph] graph indexer is nil")
		return nil
	}
	graphStore := r.graphFileIndexer.GetGraphStore("")
	ext := strings.ToLower(filepath.Ext(fileAbsPath))
	if _, ok := graph.GetAcceptSearchRelationLanguageExt()[ext]; ok {
		if graphStore != nil {
			record, err := graphStore.FindWorkspaceRecord(nil, nil)
			if err != nil {
				log.Warn("[codebase-graph] find workspace record err when judge can use builtin node", err)
				return nil
			}
			if len(record) < 3 {
				log.Info("[codebase-graph] builtin graph is not ready", fileAbsPath)
				return nil
			}
			return graphStore
		} else {
			log.Warn("[codebase-graph] can not find graphStore", fileAbsPath)
			return nil
		}
	}
	return nil
}
