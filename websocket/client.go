package websocket

import (
	"cosy/log"
	"encoding/json"
	"strings"
	"time"

	"github.com/gorilla/websocket"
)

var (
	newline = []byte{'\n'}
)

const (
	// Time allowed to write a message to the peer.
	writeWait = 10 * time.Second

	// Time allowed to read the next pong message from the peer.
	pongWait = 6 * time.Minute

	// Send pings to peer with this period. Must be less than pongWait.
	pingPeriod = 5 * time.Minute

	// Maxmum message size allowed from peer(10485760 bytes = 10M at most)
	// The client side should have to control the message size, otherwise the websocket would quit if the message is too big
	maxMessageSize = 10 * 1024 * 1024

	// The retry limit for connection err
	errRetryLimit = 10
)

// Client is a websocket client connection connected to server
type Client struct {
	conn *websocket.Conn
	// send is the channel which receives server messages
	// Those messages will be sent to the client
	send chan []byte

	closed bool
}

// NewClient initialize a client to communicate which connects to given websocket server
func NewClient(c *websocket.Conn) *Client {
	return &Client{
		conn: c,
		// Capacity of the channel: 512
		// It means we can have at most 512 outgoing messages in the sending queue for each client
		send:   make(chan []byte, 512),
		closed: false,
	}
}

// ReadPump pumps messages from the websocket connection to the server.
//
// The application runs ReadPump in a per-connection goroutine. The application
// ensures that there is at most one reader on a connection by executing all
// reads from this goroutine.
func (c *Client) ReadPump() {
	defer func() {
		WsInst.Unregister(c)
		_ = c.conn.Close()
	}()
	c.conn.SetReadLimit(maxMessageSize)
	_ = c.conn.SetReadDeadline(time.Now().Add(pongWait))
	c.conn.SetPongHandler(func(string) error {
		_ = c.conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})
	retry := 0
	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Infof("Connection closed: %v", err)
			} else if strings.Contains(err.Error(), "i/o timeout") || strings.Contains(err.Error(), "read limit exceed") {
				retry = handleRecoverableErr(retry, err, c)
				if retry < errRetryLimit {
					continue
				}
			} else {
				log.Warnf("Unknown connection error: %v", err)
			}
			break
		}

		// Forward message to server, the forwarded message contains the client pointer and message content
		WsInst.Forward(ClientMessage{c, message})
		retry = 0
	}
}

// WritePump pumps messages from the server to the websocket connection.
//
// A goroutine running WritePump is started for each connection. The
// application ensures that there is at most one writer to a connection by
// executing all writes from this goroutine.
func (c *Client) WritePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		_ = c.conn.Close()
	}()
	for {
		select {
		case message, ok := <-c.send: // The server sends replies or server requests to c.send
			_ = c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				// The server closed the channel.
				_ = c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			_, err = w.Write(message)
			if err != nil {
				log.Error("Cannot write message: ", err)
			}

			if err = w.Close(); err != nil {
				return
			}
		case <-ticker.C:
			_ = c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

func MarshalToRaw(obj interface{}) (*json.RawMessage, error) {
	data, err := json.Marshal(obj)
	if err != nil {
		return nil, err
	}
	raw := json.RawMessage(data)
	return &raw, nil
}

func handleRecoverableErr(retry int, err error, c *Client) int {
	retry += 1
	if retry >= errRetryLimit {
		log.Errorf("Connection err reach max retry limit, close soon: %v", err)
	} else {
		log.Warnf("Connection error, try recovering connection: err: %v", err)
		_ = c.conn.SetReadDeadline(time.Now().Add(pongWait))
	}
	return retry
}

func (c *Client) Close() {
	c.closed = true
	close(c.send)
}

func (c *Client) IsClosed() bool {
	return c.closed
}
