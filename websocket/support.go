package websocket

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"time"
)

// Server runs as a local service which accepts websocket connections from multiple clients(IDEs)
// It parses and forwards messages from clients to handler, as well as sends messages to clients
type CommServer interface {
	Unregister(c *Client)
	Register(c *Client)
	Forward(message ClientMessage)
	Run(timer *time.Timer)
	GetClientList() []*Client
	GetHandler() Handler
	RemoveClients(clients []*Client)
	NotifyClient(ctx context.Context, method string, params interface{}) (err error)
	NotifyToClient(ctx context.Context, client *Client, method string, params interface{}) (err error)
	BroadcastClient(ctx context.Context, method string, params, result interface{})
	RequestClient(ctx context.Context, method string, params, result interface{}, timeout time.Duration) (err error)
	RequestToWsClientWithTimeout(ctx context.Context, client *Client, method string, params, result interface{}, timeout time.Duration) (err error)
	Reply(ctx context.Context, request *WireRequest, response interface{}, responseErr error) error
	GetIdeInfo(client *Client) (definition.IdeConfig, error)
}

func logBroadCastParams(method string, params interface{}) {
	log.Debugf("Broadcasting to socket [%s]", method)
	if method == "auth/report" {
		log.Debugf("params: %+v", params)
	}
}
