package websocket

import (
	"context"
	"cosy/log"
	"cosy/sls"
	"errors"
	"time"
)

// SendRequestWithTimeout sends a request back to IDE side, fetches result from resultPointer
func SendRequestWithTimeout(ctx context.Context, method string, param interface{}, resultPointer interface{}, timeout time.Duration) error {
	if ctx == nil {
		return errors.New("context is nil")
	}

	s := time.Now()
	sls.AddLanguageServiceTrigger(method)
	done := make(chan int, 1)
	var err error

	// Send the request in a goroutine
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Error("Recovered from panic:", r)
			}
		}()

		//log.Debugf("Sending to socket [%s]", method)
		//if method == "chat/finish" {
		//	go stable.ReportChatFinishError("chat/finish", ctx, param)
		//}
		err = WsInst.RequestClient(ctx, method, param, resultPointer, timeout)
		done <- 1
	}()

	// BuildSuccess or timeout
	select {
	case <-done:
		if err != nil {
			log.Errorf("Failed to call %s: , error: %v", method, err)
			return err
		}
		// BuildSuccess
		sls.AddLanguageServiceRt(method, int(time.Since(s).Milliseconds()))
		//log.Debugf("Calling %s costs: %d ms", method, time.Since(s).Milliseconds())
	case <-time.After(timeout):
		sls.AddLanguageServiceTimeout(method)
		return errors.New("timeout")
	case <-ctx.Done():
		log.Warn("Upstream task is cancelled, cancel request")
		return ctx.Err()
	}
	return nil
}

func SendBroadcastWithTimeout(ctx context.Context, method string, param interface{}, resultPointer interface{}) {
	go func() {
		logBroadCastParams(method, param)
		if WsInst != nil {
			WsInst.BroadcastClient(ctx, method, param, resultPointer)
		}
	}()
}
