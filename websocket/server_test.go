package websocket

import (
	"context"
	"cosy/log"
	"encoding/json"
	"errors"
	"testing"
	"time"

	gorillaWs "github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
)

func TestServer_Run(t *testing.T) {
	handler := EmptyHandler{}
	server := NewWebsocketServer(&handler)
	go server.Run(time.NewTimer(30 * time.Second))

	// Test client register
	client := NewClient(&gorillaWs.Conn{})
	server.Register <- client
	// Check register result
	for {
		select {
		case <-time.After(3 * time.Second):
			assert.Fail(t, "Client is not registered")
		default:
			_, exist := server.clients.Load(client)
			if exist == false {
				continue
			}
		}
		_, exist := server.clients.Load(client)
		if exist == true {
			break
		}
	}

	// Test forward
	clientMessage := ClientMessage{
		client:  client,
		message: []byte("message with no header"),
	}
	server.forward <- clientMessage
	clientMessage2 := ClientMessage{
		client:  client,
		message: []byte("Content-Length: 76\r\n\r\n{\"jsonrpc\":\"2.0\",\"method\":\"textDocument/completion\",\"params\":{},\"id\":\"2458\"}"),
	}
	server.forward <- clientMessage2
	clientMessage3 := ClientMessage{
		client:  client,
		message: []byte("Content-Length: 76{}"),
	}
	server.forward <- clientMessage3
	clientMessage4 := ClientMessage{
		client:  client,
		message: []byte("Content-Length: 76\r\n\r\n?"),
	}
	server.forward <- clientMessage4
	clientMessage5 := ClientMessage{
		client:  client,
		message: []byte("Content-Length: 76\r\n\r\n{\"jsonrpc\":\"2.0\"}"),
	}
	server.forward <- clientMessage5
	anotherClient := NewClient(&gorillaWs.Conn{})
	server.forward <- ClientMessage{
		client:  anotherClient,
		message: []byte("Content-Length: 76\r\n\r\n{\"jsonrpc\":\"2.0\"}"),
	}

	// Test response
	rchan := make(chan *WireResponse)
	server.pending[ID{Number: 11}] = rchan
	responseMsg := ClientMessage{
		client:  client,
		message: []byte("Content-Length: 44\r\n\r\n{\"jsonrpc\":\"2.0\",\"id\":11,\"params\":{}}"),
	}
	server.forward <- responseMsg
	select {
	case <-time.After(3 * time.Second):
		assert.Fail(t, "Cannot forward response")
	case <-rchan:
		log.Info("BuildSuccess to send response!")
		break
	}

	// Test unregister
	server.unregister <- client
	// Check register result
	for {
		select {
		case <-time.After(3 * time.Second):
			assert.Fail(t, "Client is not registered")
		default:
			_, exist := server.clients.Load(client)
			if exist == true {
				continue
			}
		}
		_, exist := server.clients.Load(client)
		if exist == false {
			break
		}
	}
}
func TestServer_ReplyClient(t *testing.T) {
	server, client := mockServerAndClient()

	// Build request
	request := &WireRequest{
		ID: &ID{Number: 123},
	}
	// Initialize ctx
	ctx := context.Background()
	err := server.Reply(ctx, request, nil, nil)
	assert.NotEqual(t, nil, err)

	ctx = context.WithValue(ctx, ClientCtxKey, client)

	// 1. Normal response
	response := "result"
	err = server.Reply(ctx, request, &response, nil)
	assert.Equal(t, nil, err)

	select {
	case <-time.After(3 * time.Second):
		assert.Fail(t, "Client doesn't receive message")
	case data := <-client.send:
		assert.Contains(t, string(data), "\"id\":123")
	}

	// 2. Error response
	err = server.Reply(ctx, request, nil, errors.New("error!"))
	assert.Equal(t, nil, err)

	select {
	case <-time.After(3 * time.Second):
		assert.Fail(t, "Client doesn't receive message")
	case data := <-client.send:
		assert.Contains(t, string(data), "error!")
	}
}
func TestServer_RequestClient(t *testing.T) {
	server, client := mockServerAndClient()
	// Initialize ctx
	ctx := context.Background()
	err := server.RequestClient(ctx, "method", nil, nil, ClientTimeout)
	assert.NotEqual(t, nil, err)
	ctx = context.WithValue(ctx, ClientCtxKey, client)
	go func() {
		var result string
		err = server.RequestClient(ctx, "method", nil, &result, ClientTimeout)
		assert.Equal(t, nil, err)
	}()

	select {
	case <-client.send:
		for {
			id := ID{Number: 1}
			server.pendingMu.Lock()
			rchan := server.pending[id]
			if rchan == nil {
				continue
			}
			server.pendingMu.Unlock()
			res := "result"
			resBytes, _ := json.Marshal(res)
			rawMsg := json.RawMessage(resBytes)
			rchan <- &WireResponse{
				Result: &rawMsg,
			}
			break
		}
		// mock response
	case <-time.After(3 * time.Second):
		assert.Fail(t, "Client doesn't receive request")
	}
}

func TestServer_NotifyClient(t *testing.T) {
	server, client := mockServerAndClient()

	// Initialize ctx
	ctx := context.Background()
	err := server.NotifyClient(ctx, "method", nil)
	assert.NotEqual(t, nil, err)

	ctx = context.WithValue(ctx, ClientCtxKey, client)
	err = server.NotifyClient(ctx, "method", nil)
	assert.Equal(t, nil, err)

	select {
	case <-time.After(3 * time.Second):
		assert.Fail(t, "Client doesn't receive notification")
	case <-client.send:
		break
	}
}

func mockServerAndClient() (*Server, *Client) {
	handler := EmptyHandler{}
	server := NewWebsocketServer(&handler)
	client := NewClient(&gorillaWs.Conn{})
	server.clients.Store(client, true)
	return server, client
}
