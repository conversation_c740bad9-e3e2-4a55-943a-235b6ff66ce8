package websocket

import (
	"cosy/log"
	"net/http"
	"net/url"
	"testing"
	"time"

	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
)

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
}

// 临时保存原始的 WsInst
var originalWsInst *Server

func TestClient(t *testing.T) {
	// 创建测试用的 Server
	handler := EmptyHandler{}
	server := &Server{
		unregister: make(chan *Client),
		forward:    make(chan ClientMessage),
		handler:    &handler,
		pending:    make(map[ID]chan *WireResponse),
	}

	// 临时替换全局 WsInst
	WsInst = server

	// 测试结束后恢复原始 WsInst
	defer func() {
		WsInst = originalWsInst
	}()

	// Everytime a new websocket connection is set, call this function to register a new client
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		conn, err := upgrader.Upgrade(w, r, nil)
		if err != nil {
			log.Error(err)
			return
		}
		client := NewClient(conn)

		go client.WritePump()
		go client.ReadPump()
	})

	go func() {
		if err := http.ListenAndServe("localhost:34359", nil); err != nil {
			log.Error("http err:", err)
		}
	}()

	// Initialize a client
	u := url.URL{Scheme: "ws", Host: "localhost:34359", Path: "/"}
	for {
		c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
		if err != nil {
			log.Info("dial:", err)
			continue
		}
		defer c.Close()

		select {
		case <-time.After(3 * time.Second):
			assert.Fail(t, "No registered client")
		}

		err = c.WriteMessage(websocket.TextMessage, []byte("message"))
		if err != nil {
			log.Info(err)
		}
		select {
		case msg := <-server.forward:
			assert.Equal(t, "message", string(msg.message))
			msg.client.send <- []byte("response")
		case <-time.After(3 * time.Second):
			assert.Fail(t, "No forwarded message received")
		}

		message_type, reply, err := c.ReadMessage()
		assert.Equal(t, nil, err)
		assert.Equal(t, websocket.TextMessage, message_type)
		assert.Equal(t, "response", string(reply))
		break
	}

}
