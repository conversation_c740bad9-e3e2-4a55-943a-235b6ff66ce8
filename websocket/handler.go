package websocket

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"errors"
)

// Handler is the interface used to hook into the message handling of a rpc connection.
type Handler interface {
	Deliver(ctx context.Context, r *WireRequest) (err error)
	UnregisterClient(client *Client)
	GetIdeInfo(client *Client) (definition.IdeConfig, error)
}

type EmptyHandler struct{}

func (h *EmptyHandler) Deliver(ctx context.Context, r *WireRequest) {
	log.Info("Delivering ", r.Method)
}

func (h *EmptyHandler) UnregisterClient(client *Client) {}

func (h *EmptyHandler) GetIdeInfo(client *Client) (definition.IdeConfig, error) {
	return definition.IdeConfig{}, errors.New("not found")
}
