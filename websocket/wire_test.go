// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package websocket

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestID(t *testing.T) {
	Id := ID{Number: 10}
	assert.Equal(t, "#10", Id.String())
	bytes, err := Id.MarshalJSON()
	assert.Nil(t, err)
	assert.Equal(t, "10", string(bytes))

	Id = ID{Name: "10"}
	assert.Equal(t, "\"10\"", Id.String())
	bytes, err = Id.MarshalJSON()
	assert.<PERSON>l(t, err)
	assert.Equal(t, "\"10\"", string(bytes))
}

func TestVersionTag(t *testing.T) {
	version := []byte("\"2.0\"")
	versionTag := VersionTag{}
	err := json.Unmarshal(version, &versionTag)
	assert.Nil(t, err)

	version = []byte("\"1.0\"")
	err = versionTag.UnmarshalJSON(version)
	assert.NotNil(t, err)

	// It's considered as a number, not a string, so UnmarshalJSON returns error
	version = []byte("2.0")
	err = versionTag.UnmarshalJSON([]byte(version))
	assert.NotNil(t, err)
}

func TestError(t *testing.T) {
	err := Error{
		Code:    1,
		Message: "error message",
		Data:    nil,
	}

	assert.Equal(t, "error message", err.Error())
}
