#!/usr/bin/env zsh

no_question="N"
if [ "${1}" = "--yes" ]; then
  no_question="Y"
fi

version=$(grep 'cosy.core.version' "${HOME}/.lingma/bin/config.json" | grep -o '[0-9]\+\(\.[0-9]\+\)\+')
if [ "${2}" != "" ]; then
    version="${2}"
fi

process=$(ps -eo 'pid,lstart,command' | grep 'Lingma start' | grep -v 'grep' | head -1)
if [ "${process}" = "" ]; then
  echo "No lingma process found"
else
  if [ "${no_question}" = "Y" ]; then
    check="y"
  else
    version=$(echo "${process}" | grep -o '/[0-9]\+\(\.[0-9]\+\)\+/' | sed 's/^.//;s/.$//' | tail -1)
    binary=$(echo "${process}" | grep -o " /Users/<USER>/<PERSON><PERSON> start" | sed -e 's/^ //' -e 's/ start$//')
    ptime=$(echo "${process}" | sed -e 's/ *[0-9]* *[a-zA-Z]* *\([a-zA-Z]*\) *\([0-9]*\) *\([0-9:]*\) *\([0-9]*\) .*/\4\-\1\-\2 \3/' \
            -e 's/Jan/01/' -e 's/Feb/02/' -e 's/Mar/03/' -e 's/Apr/04/' -e 's/May/05/' -e 's/Jun/06/' -e 's/Jul/07/' \
            -e 's/Aug/08/' -e 's/Sep/09/' -e 's/Oct/10/' -e 's/Nov/11/' -e 's/Dec/12/' -e 's/-\([0-9]\) /\-0\1 /')
    ctime=$(stat -f '%SB' -t '%Y-%m-%d %H:%M:%S' "${binary}")
    echo "Lingma version ${version//\//} is running"
    echo "Process start at ${ptime}"
    echo "Binary create at ${ctime}"
    printf "Kill it ? (y/n) "
    read -r check
  fi
  if [ "${check:0:1}" = "y" ]; then
    for pid in $(ps -eo 'pid,comm' | grep '/Lingma$' | awk '{print $1}'); do
      echo "Killing process ${pid}"
      kill -15 ${pid}
    done
  else
    exit 0
  fi
fi

if [ "${no_question}" = "Y" ]; then
  check="y"
else
  printf "Start a new one ? (y/n) "
  read -r check
fi
if [ "${check:0:1}" = "y" ]; then
  if [ "$(uname -m)" = "arm64" ]; then
    arch_os="aarch64_darwin"
  else
    arch_os="x86_64_darwin"
  fi
  binary="${HOME}/.lingma/bin/${version}/${arch_os}/Lingma"
  mkdir -p "${HOME}/.lingma/logs"
  nohup "${binary}" start > /dev/null 2>&1 &
  echo "Lingma started. version=$version"
fi
