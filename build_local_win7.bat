@echo off
setlocal enabledelayedexpansion

:: 使用环境变量作为默认值
set "trial_edition=%TRIAL_EDITION%"
set "expire_time=%EXPIRE_TIME%"
set "server_url=%SERVER_URL%"
set "server_proxy=%SERVER_PROXY%"
set "cosy_version=%COSY_VERSION%"
set "cosy_building_tags=%COSY_BUILDING_TAGS%"

set "extra_param=%EXTRA_PARAM%"

:: 遍历所有命令行参数
:parse_args
if "%1"=="" goto end_parse_args
if "%~1"=="--trial-edition=*" (
    set "trial_edition=%~1"
    set "trial_edition=!trial_edition:--trial-edition=!"
) else if "%~1"=="--expire-time=*" (
    set "expire_time=%~1"
    set "expire_time=!expire_time:--expire-time=!"
) else if "%~1"=="--server-url=*" (
    set "server_url=%~1"
    set "server_url=!server_url:--server-url=!"
) else if "%~1"=="--server-host=*" (
    set "server_host=%~1"
    set "server_host=!server_host:--server-host=!"
) else if "%~1"=="--server-proxy=*" (
    set "server_proxy=%~1"
    set "server_proxy=!server_proxy:--server-proxy=!"
) else (
    set "extra_param=%1"
)
shift
goto parse_args
:end_parse_args

:: 如果没有提供额外参数，并检查对应的环境变量
if "%extra_param%"=="" (
    set "out_path=./out"
) else (
    set "out_path=%extra_param%"
)

echo Using out path: "%out_path%" 
if not exist "%out_path%" mkdir "%out_path%"

:: 输出构建信息
echo Building binaries ...
set "EXTRA_FLAGS="
if not "%trial_edition%"=="" (
    echo Build trial edition: %trial_edition% 
    set "EXTRA_FLAGS=%EXTRA_FLAGS% -X main.trialEdition=%trial_edition%"
)
if not "%server_url%"=="" (
    echo Use server url: %server_url%
    set "EXTRA_FLAGS=%EXTRA_FLAGS% -X main.serverUrl=%server_url%"
)
if not "%server_host%"=="" (
    echo Use server host: %server_host%
    set "EXTRA_FLAGS=%EXTRA_FLAGS% -X main.serverHost=%server_host%"
)
if not "%server_proxy%"=="" (
    echo Use server proxy: %server_proxy%
    set "EXTRA_FLAGS=%EXTRA_FLAGS% -X main.serverProxy=%server_proxy%"
)
if "%cosy_version%"=="" (
    :: 从当前目录的VERSION文件获取版本号
    for /f "delims=" %%a in (VERSION) do set "cosy_version=%%a"
)
if "%cosy_build_tags%" == "" (
  rem 默认走生产构建
  set "BUILDING_TAGS=ne,prod"
  echo Set Building Tags to: %BUILDING_TAGS%
) else (
  set "BUILDING_TAGS=%cosy_build_tags%"
  echo Set Building Tags to: %BUILDING_TAGS%
)

echo Set Cosy version to: %cosy_version%
set "EXTRA_FLAGS=%EXTRA_FLAGS% -X cosy/global.CosyVersion=%cosy_version%"

echo EXTRA_FLAGS: "%EXTRA_FLAGS%"

:: Get version first
echo { > "%out_path%/config.json"
echo     "cosy.core.version": "%cosy_version%" >> "%out_path%/config.json"
echo } >> "%out_path%/config.json"

:: Create target folder
set "bin_dir=!out_path!/%cosy_version%"

echo Building x86_64_windows
go build -tags "%BUILDING_TAGS%" -buildmode=pie -trimpath -ldflags "-s -w %EXTRA_FLAGS%" -o "%bin_dir%/x86_64_windows/LingmaWin7.exe"

echo Done
